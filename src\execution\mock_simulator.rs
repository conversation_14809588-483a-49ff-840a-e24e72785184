use crate::error::{BasiliskError, ExecutionError, Result};
use crate::shared_types::{ArbitrageOpportunity, ExecutionRequest, Opportunity, StrategyType, DexArbitrageData};
use ethers::{
    types::{Address, TransactionRequest, U256},
};
use tracing::{info, warn};

/// Mock simulator that doesn't require Anvil
/// This is useful for development when Anvil is not available
pub struct MockSimulator {
    chain_id: u64,
    rpc_url: String,
}

impl MockSimulator {
    pub fn new(chain_id: u64, rpc_url: String) -> Self {
        Self {
            chain_id,
            rpc_url,
        }
    }

    pub async fn start(&mut self) -> Result<()> {
        info!("Starting mock simulator for chain ID {} (no Anvil required)", self.chain_id);
        Ok(())
    }

    pub async fn stop(&mut self) -> Result<()> {
        info!("Stopping mock simulator");
        Ok(())
    }

    /// Mock simulation that returns reasonable test values
    pub async fn simulate_arbitrage(&self, opportunity: &Opportunity) -> Result<(U256, U256)> {
        match opportunity {
            Opportunity::DexArbitrage { data, .. } => {
                // Mock profitable arbitrage
                let simulated_profit = data.input_amount / U256::from(100); // 1% profit
                let estimated_gas_cost = U256::from(21000) * U256::from(20_000_000_000u64); // 21k gas * 20 gwei
                
                info!("Mock simulation: profit={}, gas_cost={}", simulated_profit, estimated_gas_cost);
                Ok((simulated_profit, estimated_gas_cost))
            },
            _ => {
                warn!("Mock simulation not implemented for opportunity type: {}", opportunity.opportunity_type());
                Ok((U256::zero(), U256::from(21000) * U256::from(20_000_000_000u64)))
            }
        }
    }

    pub async fn simulate_request(&self, request: &ExecutionRequest) -> Result<bool> {
        match request.strategy_type {
            StrategyType::DexDexArbitrage | StrategyType::ZenGeometer => {
                info!("Mock simulation: {:?} strategy approved", request.strategy_type);
                Ok(true) // Always approve for mock
            },
            _ => {
                warn!("Mock simulation: {:?} strategy not implemented", request.strategy_type);
                Ok(false)
            }
        }
    }

    pub async fn simulate_single_swap_profit(
        &self,
        _pool_address: Address,
        _token_in: Address,
        _token_out: Address,
        amount_in: U256,
    ) -> Result<U256> {
        // Mock 0.5% profit on swap
        let amount_out = amount_in + (amount_in / U256::from(200));
        Ok(amount_out)
    }

    pub async fn estimate_gas_cost(&self, _tx_request: &TransactionRequest) -> Result<U256> {
        // Mock gas cost: 21k gas * 20 gwei
        Ok(U256::from(21000) * U256::from(20_000_000_000u64))
    }
}