// Deploy StargateCompassV1 to Base Sepolia testnet
async function main() {
  console.log("🚀 Deploying StargateCompassV1 to Base Sepolia testnet...");
  
  // Base Sepolia testnet addresses (these might be mock/test addresses)
  // For testnet, we can use placeholder addresses or deploy mocks
  const AAVE_PROVIDER = "******************************************"; // Placeholder
  const STARGATE_ROUTER = "******************************************"; // Placeholder
  
  console.log(`Network: ${hre.network.name}`);
  console.log(`Chain ID: ${hre.network.config.chainId}`);
  console.log(`Aave Provider: ${AAVE_PROVIDER}`);
  console.log(`Stargate Router: ${STARGATE_ROUTER}`);
  
  // Get the contract factory
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  
  console.log("📦 Deploying contract...");
  const contract = await StargateCompassV1.deploy(AAVE_PROVIDER, STARGATE_ROUTER);
  
  console.log("⏳ Waiting for deployment confirmation...");
  await contract.waitForDeployment();
  
  const address = await contract.getAddress();
  const deployTx = contract.deploymentTransaction();
  
  console.log("✅ Deployment successful!");
  console.log(`📍 Contract Address: ${address}`);
  console.log(`🔗 Transaction Hash: ${deployTx.hash}`);
  console.log(`⛽ Gas Used: ${deployTx.gasLimit}`);
  console.log(`🔍 Sepolia BaseScan: https://sepolia.basescan.org/address/${address}`);
  
  // Save deployment info
  console.log("\n📋 Update your testnet config:");
  console.log(`stargate_compass_v1 = "${address}"`);
  
  return { address, contract };
}

main()
  .then((result) => {
    console.log("\n🎉 Testnet deployment complete!");
    console.log("🧪 Ready for testing with free testnet ETH!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });