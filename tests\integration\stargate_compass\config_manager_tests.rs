// Unit tests for Configuration Manager
// Tests configuration file discovery, parsing, and validation logic

use super::*;
use super::core::TestReporter as TestReporterTrait;
use std::fs;
use std::path::Path;
use tempfile::TempDir;
use ethers::types::Address;
use std::str::FromStr;

/// Create a temporary directory with test configuration files
fn create_test_config_dir() -> (TempDir, Vec<String>) {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let config_dir = temp_dir.path().join("config");
    fs::create_dir(&config_dir).expect("Failed to create config directory");
    
    let mut created_files = Vec::new();
    
    // Create local.toml with valid configuration
    let local_toml = r#"
[contracts]
stargate_compass_v1 = "******************************************"

[network]
rpc_url = "https://mainnet.base.org"
chain_id = 8453

[execution]
max_gas_price = 50000000000
slippage_tolerance = 0.005
"#;
    
    let local_path = config_dir.join("local.toml");
    fs::write(&local_path, local_toml).expect("Failed to write local.toml");
    created_files.push("local.toml".to_string());
    
    // Create testnet.toml with different address
    let testnet_toml = r#"
[contracts]
stargate_compass = "******************************************"

[network]
rpc_url = "https://sepolia.base.org"
chain_id = 84532

[execution]
max_gas_price = 20000000000
slippage_tolerance = 0.01
"#;
    
    let testnet_path = config_dir.join("testnet.toml");
    fs::write(&testnet_path, testnet_toml).expect("Failed to write testnet.toml");
    created_files.push("testnet.toml".to_string());
    
    // Create invalid.toml with malformed content
    let invalid_toml = r#"
[contracts
stargate_compass_v1 = "invalid_address"
missing_closing_bracket
"#;
    
    let invalid_path = config_dir.join("invalid.toml");
    fs::write(&invalid_path, invalid_toml).expect("Failed to write invalid.toml");
    created_files.push("invalid.toml".to_string());
    
    // Create empty.toml
    let empty_path = config_dir.join("empty.toml");
    fs::write(&empty_path, "").expect("Failed to write empty.toml");
    created_files.push("empty.toml".to_string());
    
    (temp_dir, created_files)
}

#[tokio::test]
async fn test_config_file_discovery() {
    let (temp_dir, created_files) = create_test_config_dir();
    
    // Change to temp directory for testing
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    // Test file discovery
    let discovered_files = ConfigurationManager::discover_config_files().unwrap();
    
    // Should find local.toml and testnet.toml (but not invalid or empty ones in priority order)
    assert!(!discovered_files.is_empty(), "Should discover configuration files");
    
    // Check that local.toml is found (highest priority)
    let local_found = discovered_files.iter()
        .any(|p| p.file_name().unwrap() == "local.toml");
    assert!(local_found, "Should find local.toml");
    
    // Check that testnet.toml is found
    let testnet_found = discovered_files.iter()
        .any(|p| p.file_name().unwrap() == "testnet.toml");
    assert!(testnet_found, "Should find testnet.toml");
    
    // Restore original directory
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_config_file_parsing() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test parsing valid configuration
    let local_path = Path::new("config/local.toml");
    let config = config_manager.parse_config_file(local_path).unwrap();
    
    // Verify structure
    assert!(config.get("contracts").is_some(), "Should have contracts section");
    assert!(config.get("network").is_some(), "Should have network section");
    assert!(config.get("execution").is_some(), "Should have execution section");
    
    // Test parsing invalid configuration
    let invalid_path = Path::new("config/invalid.toml");
    let invalid_result = config_manager.parse_config_file(invalid_path);
    assert!(invalid_result.is_err(), "Should fail to parse invalid TOML");
    
    // Test parsing empty configuration
    let empty_path = Path::new("config/empty.toml");
    let empty_result = config_manager.parse_config_file(empty_path);
    assert!(empty_result.is_err(), "Should fail to parse empty file");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_config_validation() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test validation of valid configuration
    let local_path = Path::new("config/local.toml");
    let config = config_manager.parse_config_file(local_path).unwrap();
    let validation_result = config_manager.validate_config_structure(&config, local_path);
    
    assert!(validation_result.valid, "Valid configuration should pass validation");
    assert!(validation_result.contract_address_valid, "Should have valid contract address");
    assert!(validation_result.missing_keys.is_empty(), "Should not have missing keys");
    assert!(validation_result.invalid_values.is_empty(), "Should not have invalid values");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_contract_address_extraction() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test extracting address from local.toml
    let local_path = Path::new("config/local.toml");
    let config = config_manager.parse_config_file(local_path).unwrap();
    let address = config_manager.extract_contract_address(&config).unwrap();
    
    let expected_address = Address::from_str("******************************************").unwrap();
    assert_eq!(address, expected_address, "Should extract correct address");
    
    // Test extracting address from testnet.toml (different key)
    let testnet_path = Path::new("config/testnet.toml");
    let testnet_config = config_manager.parse_config_file(testnet_path).unwrap();
    let testnet_address = config_manager.extract_contract_address(&testnet_config).unwrap();
    
    let expected_testnet_address = Address::from_str("******************************************").unwrap();
    assert_eq!(testnet_address, expected_testnet_address, "Should extract testnet address");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_configuration_manager_creation() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    // Test successful creation
    let config_manager = ConfigurationManager::new();
    assert!(config_manager.is_ok(), "Should create configuration manager successfully");
    
    let manager = config_manager.unwrap();
    // Note: config_files is private, so we test functionality instead
    let validation = manager.validate_configuration().await.unwrap();
    assert!(!validation.config_files_found.is_empty(), "Should have discovered config files");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_configuration_manager_no_config_dir() {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    // Test creation when no config directory exists
    let config_manager = ConfigurationManager::new();
    assert!(config_manager.is_err(), "Should fail when no config directory exists");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_get_current_contract_address() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test getting current contract address
    let address = config_manager.get_current_contract_address().await;
    assert!(address.is_ok(), "Should be able to get contract address");
    
    let addr = address.unwrap();
    // Should get the address from the first valid config file (local.toml)
    let expected_address = Address::from_str("******************************************").unwrap();
    assert_eq!(addr, expected_address, "Should return correct contract address");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_validate_configuration_comprehensive() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test comprehensive configuration validation
    let validation_result = config_manager.validate_configuration().await.unwrap();
    
    assert!(validation_result.contract_address_valid, "Should find valid contract addresses");
    assert!(!validation_result.config_files_found.is_empty(), "Should find configuration files");
    
    // Should find at least local.toml and testnet.toml
    assert!(validation_result.config_files_found.len() >= 2, "Should find multiple config files");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_find_files_with_contract_addresses() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test finding files with contract addresses
    let files_with_addresses = config_manager.find_files_with_contract_addresses().unwrap();
    
    assert!(!files_with_addresses.is_empty(), "Should find files with contract addresses");
    
    // Should find both local.toml and testnet.toml
    let local_found = files_with_addresses.iter()
        .any(|p| p.file_name().unwrap() == "local.toml");
    let testnet_found = files_with_addresses.iter()
        .any(|p| p.file_name().unwrap() == "testnet.toml");
    
    assert!(local_found, "Should find local.toml with contract address");
    assert!(testnet_found, "Should find testnet.toml with contract address");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_backup_configuration() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test backup functionality
    let backup_result = config_manager.backup_configuration().await;
    assert!(backup_result.is_ok(), "Should be able to backup configuration");
    
    // Check that backup files were created
    let config_dir = Path::new("config");
    let entries = fs::read_dir(config_dir).unwrap();
    let backup_files: Vec<_> = entries
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry.file_name().to_string_lossy().contains(".backup.")
        })
        .collect();
    
    assert!(!backup_files.is_empty(), "Should create backup files");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_get_all_configs() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test getting all configurations
    let all_configs = config_manager.get_all_configs();
    
    // Should succeed for valid files, but may include errors for invalid ones
    // We expect at least the valid files to be parsed
    match all_configs {
        Ok(configs) => {
            assert!(!configs.is_empty(), "Should parse at least some configuration files");
            
            // Check that we can access the parsed configurations
            for (path, config) in &configs {
                println!("Parsed config from: {}", path.display());
                assert!(config.is_table(), "Each config should be a TOML table");
            }
        }
        Err(e) => {
            // This might happen if invalid.toml causes the entire operation to fail
            println!("Expected error due to invalid configuration file: {}", e);
        }
    }
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[test]
fn test_address_validation_utility() {
    // Test valid addresses
    let valid_addresses = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ];
    
    for addr_str in &valid_addresses {
        let result = validate_ethereum_address(addr_str);
        assert!(result.is_ok(), "Should validate address: {}", addr_str);
    }
    
    // Test invalid addresses
    let invalid_addresses = [
        "742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e",  // Missing 0x
        "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4",   // Too short
        "******************************************e", // Too long
        "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4g",  // Invalid character
        "0x",                                           // Just prefix
        "",                                             // Empty
        "not_an_address",                               // Not hex
    ];
    
    for addr_str in &invalid_addresses {
        let result = validate_ethereum_address(addr_str);
        assert!(result.is_err(), "Should reject invalid address: {}", addr_str);
    }
}

#[test]
fn test_config_validation_edge_cases() {
    let config_manager = ConfigurationManager::with_files(vec![]);
    
    // Test empty configuration
    let empty_config = toml::Value::Table(toml::map::Map::new());
    let result = config_manager.validate_config_structure(&empty_config, Path::new("test.toml"));
    
    assert!(!result.valid, "Empty configuration should be invalid");
    assert!(!result.missing_keys.is_empty(), "Should report missing keys");
    
    // Test configuration with missing contracts section
    let mut partial_config = toml::map::Map::new();
    partial_config.insert("network".to_string(), toml::Value::Table(toml::map::Map::new()));
    partial_config.insert("execution".to_string(), toml::Value::Table(toml::map::Map::new()));
    let partial_toml = toml::Value::Table(partial_config);
    
    let partial_result = config_manager.validate_config_structure(&partial_toml, Path::new("partial.toml"));
    assert!(!partial_result.valid, "Configuration without contracts should be invalid");
    assert!(!partial_result.contract_address_valid, "Should not have valid contract address");
}

// Integration test that combines multiple components
#[tokio::test]
async fn test_configuration_manager_integration() {
    let (temp_dir, _) = create_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    // Test the complete workflow
    let config_manager = ConfigurationManager::new().unwrap();
    
    // 1. Validate initial configuration
    let validation = config_manager.validate_configuration().await.unwrap();
    assert!(validation.contract_address_valid, "Initial configuration should be valid");
    
    // 2. Get current address
    let current_address = config_manager.get_current_contract_address().await.unwrap();
    assert_ne!(current_address, Address::zero(), "Should have non-zero address");
    
    // 3. Find files with addresses
    let files_with_addresses = config_manager.find_files_with_contract_addresses().unwrap();
    assert!(!files_with_addresses.is_empty(), "Should find files with addresses");
    
    // 4. Create backups
    let backup_result = config_manager.backup_configuration().await;
    assert!(backup_result.is_ok(), "Should create backups successfully");
    
    println!("✅ Configuration Manager integration test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}