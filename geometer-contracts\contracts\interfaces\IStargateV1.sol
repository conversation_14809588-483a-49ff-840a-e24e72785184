// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

struct LzTxParams {
    uint lzTxGas;
    uint lzTxValue;
    bytes lzTxAirdrop;
}

// A minimal interface for the Stargate V1 Router
interface IStargateRouter {
    function quoteLayerZeroFee(
        uint16 _dstChainId,
        uint8 _functionType,
        bytes calldata _toAddress,
        bytes calldata _transferAndCallPayload,
        LzTxParams calldata _lzTxParams
    ) external view returns (uint nativeFee, uint zroFee);

    function swap(
        uint16 _dstChainId,
        uint8 _srcPoolId,
        uint8 _dstPoolId,
        address payable _refundAddress,
        uint256 _amountLD,
        uint256 _minAmountLD,
        LzTxParams calldata _lzTxParams,
        bytes calldata _to,
        bytes calldata _payload
    ) external payable;
}

// A minimal interface for the Stargate V1 Factory
interface IStargateFactory {
    function getPool(uint8 _poolId) external view returns (address);
}
