use crate::error::{BasiliskError, ExecutionError, Result};
use crate::shared_types::{ArbitrageOpportunity, ExecutionRequest, Opportunity, StrategyType, DexArbitrageData};
use ethers::contract::Contract;
use ethers::abi::Abi;
use ethers::types::transaction::eip2718::TypedTransaction;
use ethers::{
    prelude::*,
    providers::{Http, Provider},
    types::{Address, TransactionRequest, U256},
};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::process::{Child, Command}; // Use tokio::process for async
use tokio::time::sleep;
use tracing::{debug, error, info, warn};

// use crate::contracts::i_uniswap_v2_pair::IUniswapV2Pair;

// Helper function to calculate amount out for Uniswap V2 like pools
// x_1 * y_1 = x_2 * y_2
// (reserve_in + amount_in) * (reserve_out - amount_out) = reserve_in * reserve_out
// amount_out = (amount_in * reserve_out) / (reserve_in + amount_in)
// With fee: amount_out = (amount_in * 0.997 * reserve_out) / (reserve_in + amount_in * 0.997)
fn get_amount_out(amount_in: U256, reserve_in: U256, reserve_out: U256, fee_numerator: U256) -> U256 {
    if amount_in.is_zero() || reserve_in.is_zero() || reserve_out.is_zero() {
        return U256::zero();
    }

    let amount_in_with_fee = amount_in * fee_numerator;
    let numerator = amount_in_with_fee * reserve_out;
    let denominator = reserve_in * U256::from(1000) + amount_in_with_fee;

    numerator / denominator
}

// This is a placeholder implementation that will be expanded in Phase 2
pub struct Simulator {
    chain_id: u64,
    rpc_url: String, // RPC URL for the main network to fork from
    anvil_process: Option<Child>,
    fork_url: Option<String>, // RPC URL of the Anvil fork
}

impl Simulator {
    pub fn new(chain_id: u64, rpc_url: String) -> Self {
        Self {
            chain_id,
            rpc_url,
            anvil_process: None,
            fork_url: None,
        }
    }

    pub async fn start(&mut self) -> Result<()> {
        info!("Starting simulator for chain ID {}", self.chain_id);

        // Start Anvil as a fork of the main network
        self.start_anvil_fork().await?;

        Ok(())
    }

    pub async fn stop(&mut self) -> Result<()> {
        if let Some(mut process) = self.anvil_process.take() {
            info!("Stopping Anvil process");
            // Attempt graceful shutdown first
            if let Err(e) = process.kill().await {
                error!("Failed to send kill signal to Anvil process: {}", e);
            }
            // Wait for the process to actually exit
            if let Err(e) = process.wait().await {
                error!("Failed to wait for Anvil process to exit: {}", e);
            }
        }

        Ok(())
    }

    // Starts an Anvil instance forking the specified network
    async fn start_anvil_fork(&mut self) -> Result<()> {
        info!("Starting Anvil fork of {}", self.rpc_url);

        let mut child = Command::new("anvil")
            .arg("--fork-url")
            .arg(&self.rpc_url)
            .arg("--chain-id")
            .arg(self.chain_id.to_string())
            // .arg("--port") // Optional: specify a port if needed
            // .arg("8545")
            .stdout(std::process::Stdio::piped()) // Capture stdout to potentially read the RPC url
            .stderr(std::process::Stdio::piped()) // Capture stderr for errors
            .spawn()?;

        let stdout = child.stdout.take().ok_or_else(|| BasiliskError::execution_error("Failed to capture Anvil stdout"))?;
        let reader = tokio::io::BufReader::new(stdout);
        use tokio::io::AsyncBufReadExt;
        let mut lines = reader.lines();

        let mut fork_url = None;
        while let Some(line) = lines.next_line().await? {
            if line.contains("Listening on") {
                // Example: "Listening on 127.0.0.1:8545"
                let parts: Vec<&str> = line.split_whitespace().collect();
                if let Some(addr) = parts.last() {
                    fork_url = Some(format!("http://{}", addr));
                    info!("Anvil fork started. RPC URL: {}", fork_url.as_ref().unwrap());
                    break;
                }
            }
        }

        let fork_url = fork_url.ok_or_else(|| BasiliskError::execution_error("Failed to find Anvil RPC URL in stdout"))?;
        self.fork_url = Some(fork_url.clone());
        self.anvil_process = Some(child);

        // Health check: Wait for Anvil to be responsive
        let provider = Provider::<Http>::try_from(fork_url.as_str())
            .map_err(|e| ExecutionError::RpcError(format!("Failed to create provider for health check: {}", e)))?;
        let client = Arc::new(provider);

        let mut retries = 0;
        let max_retries = 10;
        let retry_delay = Duration::from_secs(1);

        while retries < max_retries {
            match client.get_block_number().await {
                Ok(block_number) => {
                    info!("Anvil is responsive. Current block number: {}", block_number);
                    return Ok(());
                }
                Err(e) => {
                    warn!("Anvil not yet responsive (attempt {}/{}) - Error: {}", retries + 1, max_retries, e);
                    retries += 1;
                    sleep(retry_delay).await;
                }
            }
        }

        Err(BasiliskError::execution_error("Anvil did not become responsive within the timeout."))
    }

    // Simulates an arbitrage transaction on the Anvil fork
    pub async fn simulate_arbitrage(&self, opportunity: &Opportunity) -> Result<(U256, U256)> {
        // Ensure we have a fork URL
        let fork_url = self
            .fork_url
            .as_ref()
            .ok_or_else(|| BasiliskError::execution_error("Anvil fork not started"))?;

        // Connect to the fork
        let provider = Provider::<Http>::try_from(fork_url.as_str())
            .map_err(|e| ExecutionError::RpcError(format!("Failed to create provider: {}", e)))?;
        let client = Arc::new(provider);

        match opportunity {
            Opportunity::DexArbitrage { data, .. } => {
                let mut current_amount_in = data.input_amount;
                let mut final_amount_out = U256::zero();

                // Simulate each hop in the arbitrage path
                for i in 0..data.path.len() - 1 {
                    let token_in = data.path[i];
                    let token_out = data.path[i + 1];
                    let pool_address = data.pools[i];

                    current_amount_in = self.simulate_single_swap_profit(
                        pool_address,
                        token_in,
                        token_out,
                        current_amount_in,
                    ).await?;

                    if i == data.path.len() - 2 { // If this is the last hop
                        final_amount_out = current_amount_in;
                    }
                }

                // Final profit is the difference between the final amount and the initial input
                let simulated_profit = if final_amount_out > data.input_amount {
                    final_amount_out - data.input_amount
                } else {
                    U256::zero()
                };

                info!("Simulation successful. Simulated profit: {}", simulated_profit);

                // Estimate gas cost for the transaction
                let estimated_gas_cost = self.estimate_gas_cost(&TransactionRequest {
                    from: Some(Address::zero()), // Dummy sender
                    to: Some(NameOrAddress::Address(data.pools[0])), // First pool in path
                    value: Some(data.input_amount), // Input amount as value for simulation
                    data: None, // No calldata needed for gas estimation
                    gas: None,
                    gas_price: None,
                    nonce: None,
                    chain_id: None,
                }).await?;

                Ok((simulated_profit, estimated_gas_cost))
            },
            _ => Err(BasiliskError::execution_error(&format!("Simulation not implemented for opportunity type: {}", opportunity.opportunity_type()))),
        }
    }

    pub async fn simulate_request(&self, request: &ExecutionRequest) -> Result<bool> {
        let fork_url = self
            .fork_url
            .as_ref()
            .ok_or_else(|| BasiliskError::execution_error("Anvil fork not started"))?;

        let provider = Provider::<Http>::try_from(fork_url.as_str())
            .map_err(|e| ExecutionError::RpcError(format!("Failed to create provider: {}", e)))?;
        let client = Arc::new(provider);

        match request.strategy_type {
            StrategyType::DexDexArbitrage => {
                // For DexDexArbitrage, the payload should contain the necessary data
                // to construct and simulate the transaction.
                // We expect the payload to be a DexArbitrageData struct serialized as JSON.
                let data: DexArbitrageData = serde_json::from_value(request.payload.clone())
                    .map_err(|e| ExecutionError::PreFlightCheckFailed(format!("Failed to parse DexArbitrageData from payload: {}", e)))?;

                if data.path.len() < 2 {
                    return Err(BasiliskError::execution_error("DexArbitrage opportunity path too short for simulation."));
                }

                // Simulate each hop in the arbitrage path to get the final output amount
                let mut current_amount_in = data.input_amount;
                for i in 0..data.path.len() - 1 {
                    let token_in = data.path[i];
                    let token_out = data.path[i + 1];
                    let pool_address = data.pools[i];

                    current_amount_in = self.simulate_single_swap_profit(
                        pool_address,
                        token_in,
                        token_out,
                        current_amount_in,
                    ).await?;
                }

                // Check if the final amount is greater than the initial input (i.e., profitable)
                let is_profitable = current_amount_in > data.input_amount;

                if is_profitable {
                    info!("Execution request simulation successful (profitable).");
                    Ok(true)
                } else {
                    info!("Execution request simulation successful (not profitable).");
                    Ok(false)
                }
            },
            StrategyType::CexDexArbitrage => {
                warn!("CexDexArbitrage simulation not yet implemented - returning false");
                Ok(false)
            },
            StrategyType::Sandwich => {
                warn!("Sandwich simulation not yet implemented - returning false");
                Ok(false)
            },
            StrategyType::Liquidation => {
                warn!("Liquidation simulation not yet implemented - returning false");
                Ok(false)
            },
            StrategyType::ZenGeometer => {
                // ZenGeometer uses the same simulation logic as DexDexArbitrage
                info!("Simulating ZenGeometer strategy (cross-chain arbitrage)");
                
                // For ZenGeometer, we simulate the Degen chain portion of the arbitrage
                // The payload should contain the cross-chain arbitrage data
                let data: DexArbitrageData = serde_json::from_value(request.payload.clone())
                    .map_err(|e| ExecutionError::PreFlightCheckFailed(format!("Failed to parse ZenGeometer data from payload: {}", e)))?;

                if data.path.len() < 2 {
                    return Err(BasiliskError::execution_error("ZenGeometer opportunity path too short for simulation."));
                }

                // Simulate the arbitrage path on Degen chain
                let mut current_amount_in = data.input_amount;
                for i in 0..data.path.len() - 1 {
                    let token_in = data.path[i];
                    let token_out = data.path[i + 1];
                    let pool_address = data.pools[i];

                    current_amount_in = self.simulate_single_swap_profit(
                        pool_address,
                        token_in,
                        token_out,
                        current_amount_in,
                    ).await?;
                }

                // Check if the final amount is greater than the initial input (profitable)
                let is_profitable = current_amount_in > data.input_amount;

                if is_profitable {
                    info!("ZenGeometer simulation successful (profitable cross-chain arbitrage).");
                    Ok(true)
                } else {
                    info!("ZenGeometer simulation successful (not profitable).");
                    Ok(false)
                }
            },
        }
    }

    /// Calculate profit for a two-pool arbitrage path
    /// This is used by the Basilisk Gaze strategy to check for dislocations
    /// NOTE: This is currently a placeholder and needs to be replaced with actual
    /// on-chain DEX simulation logic for robust profit calculation.
    pub async fn simulate_single_swap_profit(
        &self,
        pool_address: ethers::types::Address,
        token_in: ethers::types::Address,
        token_out: ethers::types::Address,
        amount_in: U256,
    ) -> Result<U256> {
        let fork_url = self
            .fork_url
            .as_ref()
            .ok_or_else(|| BasiliskError::execution_error("Anvil fork not started"))?;

        let provider = Provider::<Http>::try_from(fork_url.as_str())
            .map_err(|e| ExecutionError::RpcError(format!("Failed to create provider: {}", e)))?;
        let client = Arc::new(provider);

        // let pair_contract = IUniswapV2Pair::new(pool_address, client.clone());

        // let (reserve0, reserve1, _) = pair_contract.get_reserves().call().await
        //     .map_err(|e| ExecutionError::RpcError(format!("Failed to get reserves for pool {:?}: {}", pool_address, e)))?;
        let (reserve0, reserve1) = (U256::from(1000000), U256::from(1000000)); // Mock reserves

        // let (reserve_in, reserve_out) = if pair_contract.token_0().call().await.map_err(|e| ExecutionError::RpcError(format!("Failed to get token0 for pool {:?}: {}", pool_address, e)))? == token_in {
        let (reserve_in, reserve_out) = if token_in == Address::zero() { // Mock condition
            (reserve0, reserve1)
        } else {
            (reserve1, reserve0)
        };

        // Calculate amount out directly here
        // Convert parameters to the same type (U256) for comparison
        let amount_in_u256 = U256::from(amount_in);
        let reserve_in_u256 = U256::from(reserve_in);
        let reserve_out_u256 = U256::from(reserve_out);
        
        if amount_in_u256.is_zero() || reserve_in_u256.is_zero() || reserve_out_u256.is_zero() {
            return Ok(U256::zero());
        }
        
        // Set fee numerator
        let fee_numerator = U256::from(997); // 0.3% fee = 997/1000
        
        let amount_in_with_fee = amount_in_u256 * fee_numerator;
        let numerator = amount_in_with_fee * reserve_out_u256;
        let denominator = reserve_in_u256 * U256::from(1000) + amount_in_with_fee;
        
        let amount_out = numerator / denominator;

        debug!(
            "Simulated swap: amount_in {}, reserve_in {}, reserve_out {}, amount_out {}",
            amount_in, reserve_in, reserve_out, amount_out
        );

        Ok(amount_out)
    }

    /// Estimate gas cost for a transaction on the Anvil fork
    pub async fn estimate_gas_cost(&self, tx_request: &TransactionRequest) -> Result<U256> {
        let fork_url = self
            .fork_url
            .as_ref()
            .ok_or_else(|| BasiliskError::execution_error("Anvil fork not started"))?;

        let provider = Provider::<Http>::try_from(fork_url.as_str())
            .map_err(|e| ExecutionError::RpcError(format!("Failed to create provider: {}", e)))?;
        let client = Arc::new(provider);

        // Convert TransactionRequest to TypedTransaction for gas estimation
        let typed_tx = TypedTransaction::Legacy(tx_request.clone());
        
        // Use eth_estimateGas to get a realistic gas estimate
        let gas_estimate = client.estimate_gas(&typed_tx, None).await
            .map_err(|e| ExecutionError::GasEstimationFailed { 
                operation: "transaction".to_string(),
                reason: format!("Failed to estimate gas: {}", e) 
            })?;

        info!("Estimated gas for transaction: {}", gas_estimate);

        // Get current gas price from the Anvil fork
        let gas_price = client.get_gas_price().await
            .map_err(|e| ExecutionError::GasEstimationFailed { 
                operation: "gas_price".to_string(),
                reason: format!("Failed to get gas price from Anvil: {}", e) 
            })?;

        info!("Current gas price from Anvil: {} Gwei", gas_price / U256::from(1_000_000_000));

        Ok(gas_estimate * gas_price)
    }
}
