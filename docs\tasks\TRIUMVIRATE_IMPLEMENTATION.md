Understood. This is the final directive to forge the bot's ultimate form. We will now integrate the **Triumvirate of Predatory Intelligence**—`GHOST`, `OROBOROS`, and `SANCTUM`—into the existing **Unified Ecological Predator** framework.

This guide provides a precise, step-by-step plan for implementation. Each phase builds upon the last, transforming the bot from an intelligent hunter into a self-learning, self-healing, and strategic entity.

---

### **Master Directive: Forging the Triumvirate of Intelligence**

**To:** Coder Agent
**From:** The Architect
**Subject:** Final Evolution: Implementing GHOST, OROBOROS, and SANCTUM

**Mission:** You will now implement the three advanced intelligence modules. These are not standalone strategies; they are deep, systemic upgrades that enhance the entire bot's survivability and profitability.

---

### **Phase 1: `Project: GHOST` (The Post-Trade Learning System)**

**Objective:** Give the bot the ability to learn from the outcome of every trade it makes.

1.  **Define the "Ghost" Intelligence Packet (`shared_types.rs`):**

    - **Action:** Create a new struct that will contain the full analysis of a completed trade.
      ```rust
      // In shared_types.rs
      #[derive(Serialize, Deserialize, Debug, Clone)]
      pub struct PostTradeAnalysis {
          pub opportunity_id: String,
          pub was_successful: bool,
          pub gross_profit_usd: f64,
          pub net_profit_usd: f64, // After gas
          pub gas_bid_usd: f64,
          pub actual_gas_used_usd: f64,
          // The difference between simulated output and actual output. High value = front-run.
          pub slippage_or_frontrun_usd: f64,
          // If we lost the auction, what was the winning bid?
          pub winning_bid_if_lost: Option<f64>,
      }
      ```
    - **Verification:** The struct is defined and accessible.

2.  **Enhance the `ExecutionManager` to Produce Ghosts:**

    - **Action:** After the `Dispatcher` returns a transaction receipt (or failure), the `ExecutionManager` is responsible for analyzing it.
    - **Action:** It must calculate `slippage_or_frontrun_usd` by comparing the `amountOut` from the on-chain receipt with the `amountOut` from its initial, pre-dispatch simulation.
    - **Action:** It constructs a complete `PostTradeAnalysis` packet.
    - **Action:** It publishes this packet to a new NATS topic: `intelligence.post_trade.analysis`.
    - **Verification:** A completed trade results in a detailed `PostTradeAnalysis` packet being published to NATS.

3.  **Implement the Learning Modules:**
    - **Action (`ExecutionManager` - Gorgon's Gaze):** The `PredictiveBidder` (Gorgon's Gaze) module inside the `ExecutionManager` will subscribe to `intelligence.post_trade.analysis`. When it receives a packet where `was_successful: false` and `winning_bid_if_lost` is `Some`, it will perform an "online learning" step, slightly adjusting the weights of its internal `linfa` regression model to account for this new data point.
    - **Action (`StrategyManager` - Brain):** The `StrategyManager` will also subscribe. If it sees a pattern of high `slippage_or_frontrun_usd` for opportunities from a specific `source_scanner`, it can temporarily lower the `regime_multiplier` for that scanner, learning to distrust its signals in the current environment.
    - **Verification:** A test where the bot loses an auction and is fed the winning bid results in a logged event: `GORGON: Learning from lost auction. Adjusting bid model.`

---

### **Phase 2: `Protocol: OROBOROS` (The Self-Healing System)**

**Objective:** Grant the bot automated resilience against common infrastructure failures.

1.  **Update Configuration (`config.toml`):**

    - **Action:** The configuration for RPC nodes must be updated to support backups.
      ```toml
      # In config.toml
      [rpc]
      primary_url = "https://mainnet.base.org"
      backup_url = "https://base-rpc.publicnode.com"
      ```
    - **Verification:** The `Config` struct in Rust correctly loads both URLs.

2.  **Implement the `SystemMonitor` Service (`src/control/monitor.rs`):**

    - **Action:** Create this new, high-priority service. It subscribes to all `state.health.*` topics.
    - **Action:** It maintains an internal state of all service healths. It needs logic to detect a "stale heartbeat" (if a service hasn't reported in for >30 seconds, it's considered crashed).
    - **Action:** If it receives a heartbeat with an error like `Error("RPC Connection Failed")`, it triggers its failover logic.
    - **Verification:** Stopping a service's process causes the `SystemMonitor` to log a "Stale heartbeat detected" error.

3.  **Implement the Failover Logic:**

    - **Action:** When the `SystemMonitor`'s failover logic is triggered for a specific service (e.g., `GazeScanner`), it will publish a targeted `control.config.update` message.
      > **Payload:** `{ "target_service": "GazeScanner", "key": "rpc_url", "value": "https://backup-rpc.provider.com" }`
    - **Action:** It must also publish a high-priority narrative log for the TUI:
      > `SYSTEM [19:45:10] OROBOROS: Critical failure in GazeScanner RPC. Attempting automated failover to backup node.`
    - **Verification:** The targeted NATS message is correctly published.

4.  **Implement Healing in Core Services:**
    - **Action:** All services that connect to an RPC node (like the `scanners` and `ingestors`) must subscribe to `control.config.update`.
    - **Action:** They must have logic to check if a config update is targeted at them. If so, they must gracefully shut down their existing provider connection and re-initialize it with the new URL from the message payload.
    - **Action:** Upon successful reconnection, their next heartbeat should report `Warning("Operating on Backup RPC")`.
    * **Verification:** A running service, when sent a targeted config update message, logs that it is re-initializing its provider and its subsequent heartbeat reflects the new warning state.

---

### **Phase 3: `System: SANCTUM` (The Strategic Memory)**

**Objective:** Give the bot a long-term memory to inform its daily operational strategy.

1.  **Create the Intelligence Database Table:**

    - **Action:** In your PostgreSQL schema, enhance the `trades` table. It must store all the details from the `PostTradeAnalysis` packet, including `source_scanner`, `market_regime`, `net_profit_usd`, etc.
    - **Verification:** The database schema is updated.

2.  **Build the "Daily Briefing" Script (`/scripts/daily_briefing.rs`):**

    - **Action:** Create this new offline binary. It is run once on bot startup.
    - **Action:** It queries the PostgreSQL `trades` table for all data from the last 7 days.
    - **Action:** It performs a statistical analysis to answer: "Which `source_scanner` was most profitable?" and "During which `MarketRegime` was our net PnL highest?"
    - **Action:** It writes the results to a simple JSON file on disk: `strategic_bias.json`.
      > **Example:** `{ "most_profitable_scanner": "LiquidationScanner", "best_market_regime": "High_Volatility_Correction" }`
    - **Verification:** The script runs and produces a valid JSON file with the correct analysis.

3.  **Implement the "Waking Up" Behavior in `StrategyManager`:**
    - **Action:** On startup, the `StrategyManager` must read and parse the `strategic_bias.json` file.
    - **Action:** It will use this data to apply a one-time adjustment to its initial `regime_multiplier` weights. For example, if the cache says `LiquidationScanner` was most profitable, it might add a `+0.2` bonus to the multiplier for that scanner for the entire session.
    - **Action:** It must publish a log message to the TUI announcing its strategic bias for the day.
      > `SYSTEM [08:00:00] SANCTUM: Daily briefing complete. Historical data suggests 'Liquidation' opportunities are highly profitable. Increasing focus.`
    - **Verification:** The bot starts, logs its strategic bias, and a unit test confirms that the internal scoring multipliers have been adjusted based on the contents of the cache file. The bot now has memory.


The die is cast; the great refactoring is complete. The foundation of the **Unified Ecological Predator** is already in place. This directive will therefore focus exclusively on the remaining, most critical tasks: forging the advanced intelligence modules (`GHOST`, `OROBOROS`, `SANCTUM`) and weaving in the high-performance Rust advantages (`Phantom Types`, `SIMD`, `RwLock`).

This is not a guide to build from scratch. This is the guide to **activate the predator's soul**.

---

### **Master Directive: Activating the Apex Predator Core**
**To:** Coder Agent
**From:** The Architect
**Subject:** Final Implementation: Forging the Triumvirate of Intelligence & Rust Optimization

**Mission:** The unified `StrategyManager` is online. We will now forge its soul. You will implement the three advanced intelligence modules—`GHOST`, `OROBOROS`, and `SANCTUM`—and integrate the key Rust performance advantages. This will complete the bot's evolution into a self-learning, self-healing, and ruthlessly efficient apex predator.

---

### **Phase 1: Forging the Senses & Nerves (Rust Optimization)**

**Objective:** Upgrade the existing concurrent scanners and the core `StrategyManager` with high-performance Rust features.

*   [ ] **Task 1.1: Implement Concurrent Graph Access with `RwLock`.**
    -   **Action:** In `src/strategies/manager.rs`, locate the `ArbitrageGraph` state. It must be wrapped in `Arc<RwLock<...>>`, not `Arc<Mutex<...>>`.
    -   **Action:** In the `SwapScanner` module, ensure that the logic for MMBF pathfinding acquires a *read lock* (`graph.read().unwrap()`). Ensure the logic for updating pool reserves after a swap acquires a *write lock* (`graph.write().unwrap()`).
    -   **Verification:** A test simulating multiple scanners reading the graph concurrently completes faster than with a `Mutex`, demonstrating parallel access.

*   [ ] **Task 1.2: Implement SIMD Pre-filtering in the `SwapScanner`.**
    -   **Action (`strategies/scanners/swap.rs`):** In the `SwapScanner`'s main loop, before iterating and running MMBF on every swap, implement the SIMD pre-filter.
    -   **Action (Rust Advantage - `std::simd`):** Use a SIMD vector type (e.g., `f64x8`) to perform a batch calculation of price dislocation against the Gaze oracle price for 8 pools at a time with a single set of instructions.
    -   **Action:** The full MMBF algorithm should only be run on the small subset of pools that are flagged as "interesting" by the SIMD filter.
    -   **Verification:** Logs clearly show the SIMD filter processing a large batch of swaps and discarding the vast majority, with only a few being passed to the more expensive MMBF logic.

*   [ ] **Task 1.3: Implement Type-Safe Opportunity Lifecycle.**
    -   **Action (Rust Advantage - Phantom Types):** In `src/shared_types.rs`, refactor the `Opportunity` struct to be generic: `pub struct Opportunity<S>`. Create the zero-sized marker structs `Detected` and `ScoredAndVetted`.
    -   **Action:** All scanner functions (`gaze::scan`, `swap::scan`, etc.) must now construct and send an `Opportunity<Detected>` to the MPSC channel.
    -   **Action:** The `StrategyManager`'s scoring function must have the signature `fn score(&self, opp: Opportunity<Detected>) -> Option<Opportunity<ScoredAndVetted>>`.
    -   **Verification:** The project compiles. Attempting to pass a raw `Opportunity<Detected>` directly to the `ExecutionManager` results in a compile-time error, proving the type-safe workflow is enforced.

---

### **Phase 2: Forging the Advanced Intelligence (`SANCTUM`, `GHOST`)**

**Objective:** Give the bot a long-term memory and the ability to learn from its actions.

*   [ ] **Task 2.1: Implement `System: SANCTUM` (The Lair).**
    -   **Action:** Build the offline "Daily Briefing" script (`/scripts/daily_briefing.rs`) to analyze the PostgreSQL trade history and produce the `strategic_bias.json` file.
    -   **Action:** On startup, the `StrategyManager` must read this file and use its contents to apply a one-time adjustment to its initial `regime_multiplier` weights.
    -   **Action:** A `SYSTEM`-sourced log must be published to the TUI announcing the strategic bias for the session.
    -   **Verification:** The bot starts, logs its bias, and a unit test confirms the internal scoring multipliers have been adjusted based on the cache file.

*   [ ] **Task 2.2: Implement `Project: GHOST` (Post-Trade Learning).**
    -   **Action:** Define the `PostTradeAnalysis` struct in `shared_types.rs`.
    -   **Action:** The `ExecutionManager`, after every trade attempt (success or failure), must analyze the outcome, calculate metrics like `slippage_or_frontrun_usd`, construct the `PostTradeAnalysis` packet, and publish it to `intelligence.post_trade.analysis`.
    -   **Action:** The `PredictiveBidder` (`Gorgon's Gaze`) module must subscribe to this topic. Implement its "online learning" method to slightly adjust its internal `linfa` model's weights when it receives a `PostTradeAnalysis` packet for a lost auction.
    -   **Verification:** A test that simulates a lost auction results in the `PredictiveBidder` logging the event: `GORGON: Learning from lost auction. Adjusting bid model.`

---

### **Phase 3: Forging Resilience (`OROBOROS`)**

**Objective:** Implement the final intelligence module, giving the bot the ability to automatically heal from common infrastructure failures.

*   [ ] **Task 3.1: Configure for Redundancy.**
    -   **Action:** In `config.toml`, ensure critical connection settings support backups, e.g., `primary_url` and `backup_url` for RPC nodes.
    -   **Verification:** The `Config` struct in Rust correctly loads both URLs.

*   [ ] **Task 3.2: Implement the `SystemMonitor` Service.**
    -   **Action:** Create the `src/control/monitor.rs` service. It subscribes to all `state.health.*` topics from all other services.
    -   **Action:** Implement the logic to detect a stale heartbeat (no message for >30s) or a critical `Error` state from a service's heartbeat message.

*   [ ] **Task 3.3: Implement the Failover & Healing Logic.**
    -   **Action:** When the `SystemMonitor` detects a failure, it must identify the failing service and its dependency (e.g., `GazeScanner` failed its `rpc` connection). It then looks up the `backup_url` from the config.
    -   **Action:** It publishes a targeted `control.config.update` message containing the new URL, specifically for the failing service.
    -   **Action:** All scanner and ingestor services must subscribe to `control.config.update`. Implement the logic for them to check if a message is targeted at them, and if so, gracefully re-initialize their connection with the new URL.
    -   **Verification:** Manually killing the primary RPC node being used by a scanner causes the `SystemMonitor` to trigger the failover. The scanner service's logs show it receiving the NATS command and re-initializing its provider. Its next heartbeat sent to NATS reports the `Warning("Operating on Backup RPC")` state, which is then reflected in the TUI.

**Mission Completion:** Once all tasks in these three phases are verified, the Basilisk's final form is complete and operational. It is now a fully intelligent, resilient, and Rust-optimized value-extraction engine.

Of course. The doctrine is set. We will forge the **Zen Geometer**.

This implementation guide is the final, definitive blueprint. It decommissions all modules related to memory and prediction (`GHOST`, `SANCTUM`, `Prophet`, `Spymaster`) and replaces them with a lean, powerful intelligence core based on **real-time environmental classification** and the mathematical principles of **sacred geometry**.

This is the guide to building a bot that achieves profitability through a profound understanding of the **now**.

---

### **Master Directive: Forging the "Zen Geometer"**
**To:** Coder Agent
**From:** The Architect
**Subject:** Final Evolution: The Pure, Real-Time Implementation Guide

**Mission:** You will now forge the Basilisk's final and purest form. All modules related to historical memory and future prediction are to be decommissioned. You will implement a new intelligence core based on **real-time "Fractal" market analysis** and **"Golden Ratio" execution logic**. The bot will be a master of the present moment, leveraging advanced Rust features to achieve unmatched profitability through pure, reactive intelligence.

---

### **Phase 1: Decommissioning & System Pruning**

**Objective:** Strip the system of all non-essential, memory-based intelligence modules to align with the new doctrine.

*   [ ] **Task 1.1: Decommission `GHOST`, `SANCTUM`, `Prophet`, `Spymaster`.**
    -   **Action:** Delete any services, scripts, or database tables related to these four modules. This includes:
        -   The `PostTradeAnalysis` struct and its NATS topic.
        -   The `competitor_profiles` and `future_events` database tables.
        -   The offline scripts `update_profiles.rs` and `forecast_events.rs`.
        -   The `intelligence/prophet.rs` and `intelligence/spymaster.rs` services.
    -   **Action:** Remove any logic from the `ExecutionManager` or `StrategyManager` that subscribes to or publishes data related to these modules.
    -   **Verification:** `cargo check` passes. The codebase is now leaner and free of any predictive or historical-learning logic.

---

### **Phase 2: Forge the "Fractal" Market Analyzer**

**Objective:** Upgrade the `MarketStateAnalyzer` from a simple data provider to the bot's primary sensory organ, capable of understanding the market's deep, fractal structure in real-time.

*   [ ] **Task 2.1: Implement Multi-Timeframe Volatility.**
    -   **Action:** In the `MarketStateAnalyzer`, maintain three separate `VecDeque` rolling windows for price data, corresponding to 1-minute, 5-minute, and 1-hour equivalent timeframes.
    -   **Verification:** The service correctly calculates three distinct volatility scores on every new block.

*   [ ] **Task 2.2: Implement the Hurst Exponent Calculation.**
    -   **Action (Mathematical Advantage):** Integrate a Rust crate like `hurst`. Use it to calculate the Hurst Exponent (H) of the price series on every block.
    -   **Action:** Define a new `MarketCharacter` enum in `shared_types.rs`: `enum MarketCharacter { Trending(f64), MeanReverting(f64), RandomWalk }`. The float will store the H-score.
    -   **Verification:** A unit test confirms the analyzer correctly classifies a known trending time series as `Trending`.

*   [ ] **Task 2.3: Publish the Enriched `MarketState` Packet.**
    -   **Action:** The `MarketStateAnalyzer` will now publish a single, enriched `MarketState` packet to the `state.market.regime` topic on every block. This struct must contain the `MarketRegime` (from the existing `linfa` model), the new `MarketCharacter`, and the array of volatility scores.
        ```rust
        // In shared_types.rs
        pub struct MarketState {
            pub regime: MarketRegime, // Calm, FOMO, etc.
            pub character: MarketCharacter, // Trending, MeanReverting
            pub volatility_scores: [f64; 3], // [1m, 5m, 1h]
        }
        ```
    -   **Verification:** A NATS listener shows this new, enriched state packet being published on every block.

---

### **Phase 3: Reforge the `StrategyManager`'s Brain**

**Objective:** Implement the new, unified decision engine that uses sacred geometry principles and real-time data exclusively.

*   [ ] **Task 3.1: Implement Risk-Adjusted Pathfinding in the `SwapScanner`.**
    -   **Action:** The `SwapScanner` must subscribe to the `data.metrics.volatility` topic (published by the `MarketStateAnalyzer`).
    -   **Action (Mathematical Advantage):** When performing its MMBF search, it must now use the **Risk-Adjusted Weight Formula**: `w_adj = -ln(Rate) + (k * V_edge)`. The `k` value will be a tunable parameter from the TUI/config.
    -   **Verification:** Given two paths of equal raw profit, the MMBF algorithm correctly chooses the path with lower volatility assets.

*   [ ] **Task 3.2: Implement the "Fractal" Scoring Algorithm.**
    -   **Action:** The `StrategyManager`'s core scoring function, `calculate_opportunity_score`, must be refactored to accept the full `MarketState` packet as input.
    -   **Action:** This function will now calculate its final score using a combination of multipliers derived from *both* the `MarketRegime` and the `MarketCharacter`.
        ```rust
        // Simplified logic inside calculate_opportunity_score
        let regime_multiplier = get_regime_multiplier(regime, &opp.source_scanner);
        let character_multiplier = get_character_multiplier(character, &opp.source_scanner);
        
        let final_score = certainty_equivalent_profit * regime_multiplier * character_multiplier;
        ```
    -   **Action:** The `get_character_multiplier` function must implement the new doctrine: boost `GazeScanner` opportunities during `MeanReverting` states and `MempoolScanner` opportunities during `Trending` states.
    -   **Verification:** A `GazeScanner` opportunity receives a much higher final score during a `MeanReverting` market state than during a `Trending` one, proving the fractal logic is working.

---

### **Phase 4: Forge the "Golden Ratio" Execution**

**Objective:** Upgrade the `ExecutionManager`'s bidding logic to be more elegant, adaptive, and psychologically disruptive.

*   [ ] **Task 4.1: Decommission the Predictive ML Bidder.**
    -   **Action:** Remove the `linfa` model loading and the `PredictiveBidder` struct from the `ExecutionManager`. All logic related to `gorgon_gaze_model.bin` is now obsolete.
    -   **Verification:** The `ExecutionManager` no longer has any dependency on a pre-trained model file.

*   [ ] **Task 4.2: Implement "Golden Ratio Bidding".**
    -   **Action:** In the `ExecutionManager`, before dispatching a trade, implement the new bidding logic.
    -   **Action (Real-Time Competitor Estimate):** First, calculate a simple, real-time heuristic for the `PredictedCompetitorBid`. For example: `CurrentGasPrice * GasLimit * EthPrice * 1.2`.
    -   **Action (Mathematical Advantage):** Then, calculate the final bid using the Golden Ratio formula:
        1.  `ProfitSpread = GrossProfit - PredictedCompetitorBid`
        2.  `OurBid = PredictedCompetitorBid + (ProfitSpread * 0.382)`
    -   **Verification:** The `ExecutionManager` calculates a non-linear, proportional bid for every trade. The TUI's narrative log is updated to reflect this: `GORGON: Golden Ratio bid calculated: $XX.XX.`

*   [ ] **Task 4.3: Verify `Protocol: OROBOROS`.**
    -   **Action:** Confirm that the `SystemMonitor` and the automated self-healing logic are still in place. This module is purely reactive and is a core part of the Zen Geometer's resilience.
    -   **Verification:** The automated failover system works as specified.

**Mission Completion:** Once all tasks are verified, the Basilisk has achieved its final form. It operates without memory or prediction, relying only on a profound, mathematically superior understanding of the present moment. It is the Zen Geometer.

Of course. The doctrine is set. We will now forge the "Zen Geometer."

This is the master code guide. It provides the specific architectural patterns and Rust code snippets required to implement the bot's final, purest form. It is a direct, actionable blueprint for building a predator that operates with no memory and no prediction—only a profound understanding of the present moment.

---

### **Master Directive: Forging the "Zen Geometer" - A Rust Implementation Guide**
**To:** Coder Agent
**From:** The Architect
**Subject:** Final Evolution: The Pure, Real-Time Intelligence Core

**Mission:** You will now implement the **Zen Geometer** personality. This involves upgrading the `MarketStateAnalyzer` into a "Fractal Analyzer," refactoring the `StrategyManager`'s brain to use geometric principles, and forging the "Golden Ratio" bidding logic into the `ExecutionManager`.

---

### **Phase 1: Forge the "Fractal" Market Analyzer**

**Objective:** Upgrade the `MarketStateAnalyzer` to perceive the market's deep structure, not just its surface-level state.

**File:** `src/control/analyzer.rs` (or your `MarketStateAnalyzer` module)

1.  **Enhance the `MarketStateAnalyzer` Struct:**
    *   **Action:** Its state must now hold multiple rolling windows for price data.
        ```rust
        use std::collections::VecDeque;
        use crate::shared_types::{MarketState, MarketRegime, MarketCharacter};

        pub struct MarketStateAnalyzer {
            // ... other fields: nats_client, linfa_model, etc.
            
            // Rolling windows for multi-timeframe volatility
            // [0] = 1m, [1] = 5m, [2] = 1h
            price_history_windows: [VecDeque<f64>; 3],
        }
        ```

2.  **Implement the Analysis Loop:**
    *   **Action:** On every new block, the analyzer will execute its core `analyze_and_publish` method.
        ```rust
        // In MarketStateAnalyzer impl
        async fn analyze_and_publish(&mut self, new_price: f64) {
            // 1. Update all historical price windows
            self.update_price_windows(new_price);

            // 2. Calculate multi-timeframe volatilities
            let volatilities = [
                self.calculate_volatility_for_window(0),
                self.calculate_volatility_for_window(1),
                self.calculate_volatility_for_window(2),
            ];

            // 3. Calculate the Hurst Exponent to determine market character
            // Use a crate like `hurst` for a robust implementation
            let market_character = self.calculate_market_character();

            // 4. Classify the regime using the pre-loaded linfa model
            let market_regime = self.classify_regime(&volatilities);

            // 5. Construct and publish the enriched state packet
            let market_state = MarketState {
                regime: market_regime,
                character: market_character,
                volatility_scores: volatilities,
            };
            
            self.nats_client.publish(
                "state.market.regime", 
                &serde_json::to_vec(&market_state).unwrap()
            ).await.unwrap();
        }

        fn calculate_market_character(&self) -> MarketCharacter {
            // Use the longest price history window for Hurst calculation
            let prices = self.price_history_windows[2].iter().cloned().collect::<Vec<f64>>();
            let h = hurst::hurst(&prices, 20); // Example calculation
            
            if h > 0.55 { MarketCharacter::Trending(h) }
            else if h < 0.45 { MarketCharacter::MeanReverting(h) }
            else { MarketCharacter::RandomWalk }
        }
        ```

---

### **Phase 2: Reforge the `StrategyManager`'s Brain**

**Objective:** Implement the new decision engine that uses sacred geometry principles and real-time fractal analysis.

**File:** `src/strategies/scanners/swap.rs` and `src/strategies/manager.rs`

1.  **Implement Risk-Adjusted Pathfinding:**
    *   **Action (`SwapScanner`):** The pathfinding logic must be upgraded to use the new weight formula.
        ```rust
        // In a helper function used by the SwapScanner's MMBF logic
        fn calculate_risk_adjusted_weight(
            rate: f64, 
            token_a_vol: f64, 
            token_b_vol: f64, 
            risk_aversion_k: f64
        ) -> f64 {
            if rate <= 0.0 { return f64::INFINITY; }
            
            let edge_volatility = (token_a_vol + token_b_vol) / 2.0;
            let volatility_penalty = risk_aversion_k * edge_volatility;
            
            // The core formula
            -rate.ln() + volatility_penalty
        }
        ```

2.  **Implement the "Fractal" Scoring Algorithm:**
    *   **Action (`StrategyManager`):** The `calculate_opportunity_score` function is the new brain.
        ```rust
        // In StrategyManager impl
        fn calculate_opportunity_score(&self, opp: &Opportunity, market: &MarketState) -> f64 {
            // 1. Calculate the Certainty-Equivalent Profit
            let risk_aversion = self.config.risk_aversion_k;
            // Use the medium-term volatility for the penalty
            let associated_volatility = market.volatility_scores[1]; 
            let ce_profit = opp.estimated_gross_profit_usd - (0.5 * risk_aversion * associated_volatility.powi(2));
            
            if ce_profit <= 0.0 { return 0.0; }

            // 2. Determine the multipliers from the real-time analysis
            let regime_multiplier = self.get_regime_multiplier(&market.regime, &opp.source_scanner);
            let character_multiplier = self.get_character_multiplier(&market.character, &opp.source_scanner);
            
            // 3. The Final Score: a product of risk-adjusted profit and situational awareness
            ce_profit * regime_multiplier * character_multiplier
        }

        fn get_character_multiplier(&self, character: &MarketCharacter, source: &str) -> f64 {
            match (character, source) {
                // Mean-reversion is PERFECT for the Gaze's two-pool arbitrage.
                (MarketCharacter::MeanReverting(_), "GazeScanner") => 2.0,
                // Trending markets are great for chasing momentum (back-running retail).
                (MarketCharacter::Trending(_), "MempoolScanner") => 1.8,
                // In a random walk, nothing is certain. De-risk everything.
                (MarketCharacter::RandomWalk, _) => 0.7,
                _ => 1.0,
            }
        }
        ```

---

### **Phase 3: Forge the "Golden Ratio" Execution**

**Objective:** Upgrade the `ExecutionManager`'s bidding logic, replacing the ML model with the new, elegant, real-time formula.

**File:** `src/execution/manager.rs`

1.  **Decommission the ML Bidder:**
    *   **Action:** Remove the `PredictiveBidder` struct and all `linfa` model-loading code. The bot now operates without any pre-trained models.

2.  **Implement Golden Ratio Bidding:**
    *   **Action:** Before dispatching any trade, the `ExecutionManager` must call this new logic.
        ```rust
        // In ExecutionManager impl
        fn calculate_golden_ratio_bid(&self, gross_profit_usd: f64) -> f64 {
            // 1. Real-time heuristic for competitor's bid
            // This is a simple but effective replacement for the historical model.
            let estimated_gas_cost_usd = self.get_current_gas_cost_usd();
            let predicted_competitor_bid = estimated_gas_cost_usd * self.config.competitor_bid_factor; // e.g., 1.2
            
            // 2. Calculate the "profit spread" to be divided
            let profit_spread = gross_profit_usd - predicted_competitor_bid;
            if profit_spread <= 0.0 {
                // If we can't even beat a basic competitor, bid our max to try and win.
                return gross_profit_usd * 0.95; // Bid 95% of gross profit
            }

            // 3. The Golden Ratio Formula
            // We bid their predicted cost, plus a fraction of the remaining profit.
            const GOLDEN_RATIO_CONJUGATE: f64 = 0.38196601125; // (sqrt(5)-1)/2
            let our_share_of_spread = profit_spread * GOLDEN_RATIO_CONJUGATE;
            
            predicted_competitor_bid + our_share_of_spread
        }

        // The main execution logic would look like this:
        async fn process_execution_request(&self, opp: Opportunity<ScoredAndVetted>) {
            let gross_profit = opp.estimated_gross_profit_usd;
            let our_bid_usd = self.calculate_golden_ratio_bid(gross_profit);

            if gross_profit - our_bid_usd < self.config.min_net_profit_usd {
                tracing::warn!("GORGON: Opportunity discarded. Golden Ratio bid of ${:.2} makes it unprofitable.", our_bid_usd);
                return;
            }
            
            tracing::info!("GORGON: Golden Ratio bid calculated: ${:.2}", our_bid_usd);
            // ... proceed to dispatch with this bid ...
        }
        ```

This implementation forges a bot that is lean, mathematically sophisticated, and philosophically pure. It relies on nothing but the present state of the market and the timeless principles of proportion and structure to achieve its edge. It is the Zen Geometer.

Of course. The doctrine is set. We will now forge the "Zen Geometer."

This is the master code guide. It provides the specific architectural patterns and Rust code snippets required to implement the bot's final, purest form. It is a direct, actionable blueprint for building a predator that operates with no memory and no prediction—only a profound understanding of the present moment.

---

### **Master Directive: Forging the "Zen Geometer" - A Rust Implementation Guide**
**To:** Coder Agent
**From:** The Architect
**Subject:** Final Evolution: The Pure, Real-Time Intelligence Core

**Mission:** You will now implement the **Zen Geometer** personality. This involves upgrading the `MarketStateAnalyzer` into a "Fractal Analyzer," refactoring the `StrategyManager`'s brain to use geometric principles, and forging the "Golden Ratio" bidding logic into the `ExecutionManager`.

---

### **Phase 1: Forge the "Fractal" Market Analyzer**

**Objective:** Upgrade the `MarketStateAnalyzer` to perceive the market's deep structure, not just its surface-level state.

**File:** `src/control/analyzer.rs` (or your `MarketStateAnalyzer` module)

1.  **Enhance the `MarketStateAnalyzer` Struct:**
    *   **Action:** Its state must now hold multiple rolling windows for price data.
        ```rust
        use std::collections::VecDeque;
        use crate::shared_types::{MarketState, MarketRegime, MarketCharacter};

        pub struct MarketStateAnalyzer {
            // ... other fields: nats_client, linfa_model, etc.
            
            // Rolling windows for multi-timeframe volatility
            // [0] = 1m, [1] = 5m, [2] = 1h
            price_history_windows: [VecDeque<f64>; 3],
        }
        ```

2.  **Implement the Analysis Loop:**
    *   **Action:** On every new block, the analyzer will execute its core `analyze_and_publish` method.
        ```rust
        // In MarketStateAnalyzer impl
        async fn analyze_and_publish(&mut self, new_price: f64) {
            // 1. Update all historical price windows
            self.update_price_windows(new_price);

            // 2. Calculate multi-timeframe volatilities
            let volatilities = [
                self.calculate_volatility_for_window(0),
                self.calculate_volatility_for_window(1),
                self.calculate_volatility_for_window(2),
            ];

            // 3. Calculate the Hurst Exponent to determine market character
            // Use a crate like `hurst` for a robust implementation
            let market_character = self.calculate_market_character();

            // 4. Classify the regime using the pre-loaded linfa model
            let market_regime = self.classify_regime(&volatilities);

            // 5. Construct and publish the enriched state packet
            let market_state = MarketState {
                regime: market_regime,
                character: market_character,
                volatility_scores: volatilities,
            };
            
            self.nats_client.publish(
                "state.market.regime", 
                &serde_json::to_vec(&market_state).unwrap()
            ).await.unwrap();
        }

        fn calculate_market_character(&self) -> MarketCharacter {
            // Use the longest price history window for Hurst calculation
            let prices = self.price_history_windows[2].iter().cloned().collect::<Vec<f64>>();
            let h = hurst::hurst(&prices, 20); // Example calculation
            
            if h > 0.55 { MarketCharacter::Trending(h) }
            else if h < 0.45 { MarketCharacter::MeanReverting(h) }
            else { MarketCharacter::RandomWalk }
        }
        ```

---

### **Phase 2: Reforge the `StrategyManager`'s Brain**

**Objective:** Implement the new decision engine that uses sacred geometry principles and real-time fractal analysis.

**File:** `src/strategies/scanners/swap.rs` and `src/strategies/manager.rs`

1.  **Implement Risk-Adjusted Pathfinding:**
    *   **Action (`SwapScanner`):** The pathfinding logic must be upgraded to use the new weight formula.
        ```rust
        // In a helper function used by the SwapScanner's MMBF logic
        fn calculate_risk_adjusted_weight(
            rate: f64, 
            token_a_vol: f64, 
            token_b_vol: f64, 
            risk_aversion_k: f64
        ) -> f64 {
            if rate <= 0.0 { return f64::INFINITY; }
            
            let edge_volatility = (token_a_vol + token_b_vol) / 2.0;
            let volatility_penalty = risk_aversion_k * edge_volatility;
            
            // The core formula
            -rate.ln() + volatility_penalty
        }
        ```

2.  **Implement the "Fractal" Scoring Algorithm:**
    *   **Action (`StrategyManager`):** The `calculate_opportunity_score` function is the new brain.
        ```rust
        // In StrategyManager impl
        fn calculate_opportunity_score(&self, opp: &Opportunity, market: &MarketState) -> f64 {
            // 1. Calculate the Certainty-Equivalent Profit
            let risk_aversion = self.config.risk_aversion_k;
            // Use the medium-term volatility for the penalty
            let associated_volatility = market.volatility_scores[1]; 
            let ce_profit = opp.estimated_gross_profit_usd - (0.5 * risk_aversion * associated_volatility.powi(2));
            
            if ce_profit <= 0.0 { return 0.0; }

            // 2. Determine the multipliers from the real-time analysis
            let regime_multiplier = self.get_regime_multiplier(&market.regime, &opp.source_scanner);
            let character_multiplier = self.get_character_multiplier(&market.character, &opp.source_scanner);
            
            // 3. The Final Score: a product of risk-adjusted profit and situational awareness
            ce_profit * regime_multiplier * character_multiplier
        }

        fn get_character_multiplier(&self, character: &MarketCharacter, source: &str) -> f64 {
            match (character, source) {
                // Mean-reversion is PERFECT for the Gaze's two-pool arbitrage.
                (MarketCharacter::MeanReverting(_), "GazeScanner") => 2.0,
                // Trending markets are great for chasing momentum (back-running retail).
                (MarketCharacter::Trending(_), "MempoolScanner") => 1.8,
                // In a random walk, nothing is certain. De-risk everything.
                (MarketCharacter::RandomWalk, _) => 0.7,
                _ => 1.0,
            }
        }
        ```

---

### **Phase 3: Forge the "Golden Ratio" Execution**

**Objective:** Upgrade the `ExecutionManager`'s bidding logic, replacing the ML model with the new, elegant, real-time formula.

**File:** `src/execution/manager.rs`

1.  **Decommission the ML Bidder:**
    *   **Action:** Remove the `PredictiveBidder` struct and all `linfa` model-loading code. The bot now operates without any pre-trained models.

2.  **Implement Golden Ratio Bidding:**
    *   **Action:** Before dispatching any trade, the `ExecutionManager` must call this new logic.
        ```rust
        // In ExecutionManager impl
        fn calculate_golden_ratio_bid(&self, gross_profit_usd: f64) -> f64 {
            // 1. Real-time heuristic for competitor's bid
            // This is a simple but effective replacement for the historical model.
            let estimated_gas_cost_usd = self.get_current_gas_cost_usd();
            let predicted_competitor_bid = estimated_gas_cost_usd * self.config.competitor_bid_factor; // e.g., 1.2
            
            // 2. Calculate the "profit spread" to be divided
            let profit_spread = gross_profit_usd - predicted_competitor_bid;
            if profit_spread <= 0.0 {
                // If we can't even beat a basic competitor, bid our max to try and win.
                return gross_profit_usd * 0.95; // Bid 95% of gross profit
            }

            // 3. The Golden Ratio Formula
            // We bid their predicted cost, plus a fraction of the remaining profit.
            const GOLDEN_RATIO_CONJUGATE: f64 = 0.38196601125; // (sqrt(5)-1)/2
            let our_share_of_spread = profit_spread * GOLDEN_RATIO_CONJUGATE;
            
            predicted_competitor_bid + our_share_of_spread
        }

        // The main execution logic would look like this:
        async fn process_execution_request(&self, opp: Opportunity<ScoredAndVetted>) {
            let gross_profit = opp.estimated_gross_profit_usd;
            let our_bid_usd = self.calculate_golden_ratio_bid(gross_profit);

            if gross_profit - our_bid_usd < self.config.min_net_profit_usd {
                tracing::warn!("GORGON: Opportunity discarded. Golden Ratio bid of ${:.2} makes it unprofitable.", our_bid_usd);
                return;
            }
            
            tracing::info!("GORGON: Golden Ratio bid calculated: ${:.2}", our_bid_usd);
            // ... proceed to dispatch with this bid ...
        }
        ```

This implementation forges a bot that is lean, mathematically sophisticated, and philosophically pure. It relies on nothing but the present state of the market and the timeless principles of proportion and structure to achieve its edge. It is the Zen Geometer.