async function main() {
  console.log("🚀 Deploying with correct wallet configuration...");
  
  // Force use the expected wallet address
  const expectedAddress = "******************************************";
  
  const [signer] = await ethers.getSigners();
  const actualAddress = await signer.getAddress();
  
  console.log(`Expected Address: ${expectedAddress}`);
  console.log(`Actual Address: ${actualAddress}`);
  console.log(`Network: ${hre.network.name} (${hre.network.config.chainId})`);
  
  if (actualAddress.toLowerCase() !== expectedAddress.toLowerCase()) {
    console.log("❌ Address mismatch! Please update your private key in .env");
    console.log("💡 The private key should derive to: ******************************************");
    return;
  }
  
  const balance = await ethers.provider.getBalance(actualAddress);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  
  if (balance === 0n) {
    console.log("❌ No testnet ETH! Get free ETH from:");
    console.log("🚰 https://www.coinbase.com/faucets/base-ethereum-goerli-faucet");
    console.log("🚰 https://faucet.quicknode.com/base/sepolia");
    console.log(`📍 Fund address: ${actualAddress}`);
    return;
  }
  
  console.log("✅ Wallet verified! Proceeding with deployment...");
  
  // Deploy with mock addresses for testnet
  const MOCK_AAVE_PROVIDER = "******************************************";
  const MOCK_STARGATE_ROUTER = "******************************************";
  
  console.log("📦 Deploying StargateCompassV1...");
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  const contract = await StargateCompassV1.deploy(MOCK_AAVE_PROVIDER, MOCK_STARGATE_ROUTER);
  
  await contract.waitForDeployment();
  const contractAddress = await contract.getAddress();
  const deployTx = contract.deploymentTransaction();
  
  console.log("\n🎉 TESTNET DEPLOYMENT SUCCESSFUL!");
  console.log(`📍 Contract Address: ${contractAddress}`);
  console.log(`🔗 Transaction Hash: ${deployTx.hash}`);
  console.log(`🔍 BaseScan: https://sepolia.basescan.org/address/${contractAddress}`);
  console.log(`👤 Deployed by: ${actualAddress}`);
  
  console.log("\n📋 UPDATE YOUR CONFIG:");
  console.log(`stargate_compass_v1 = "${contractAddress}"`);
  
  return contractAddress;
}

main().catch(console.error);
