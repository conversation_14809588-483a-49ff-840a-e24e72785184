# Stargate Compass Integration Tests - Configuration Guide

## Overview

This guide provides comprehensive information about configuring the Stargate Compass Integration Test Suite. Proper configuration is essential for successful test execution and accurate results.

## Configuration Files

### Primary Configuration Files

The integration test suite uses the following configuration files:

```
config/
├── local.toml          # Local development and testing
├── testnet.toml        # Testnet-specific settings
├── default.toml        # Default configuration template
└── test-config.toml    # Integration test specific settings
```

### Configuration Hierarchy

Configuration is loaded in the following order (later files override earlier ones):

1. `config/default.toml` - Base configuration
2. `config/local.toml` - Local overrides
3. Environment variables - Runtime overrides
4. Command line arguments - Highest priority

## Core Configuration Sections

### 1. Network Configuration

```toml
[network]
# Anvil testnet URL
rpc_url = "http://localhost:8545"

# Chain ID (Base mainnet fork)
chain_id = 8453

# Connection timeout in seconds
connection_timeout = 30

# Request retry attempts
retry_attempts = 3

# Block confirmation requirements
confirmation_blocks = 1
```

### 2. Contract Configuration

```toml
[contracts]
# StargateCompassV1 contract address (updated by tests)
stargate_compass_v1 = "******************************************"

# Token contract addresses
usdc = "******************************************"
weth = "******************************************"

# Contract interaction settings
gas_limit = 500000
gas_price_multiplier = 1.2
```

### 3. Testing Configuration

```toml
[testing]
# Anvil testnet URL for integration tests
anvil_url = "http://localhost:8545"

# Test execution timeout in seconds
test_timeout_seconds = 300

# Number of retry attempts for failed tests
retry_attempts = 3

# Enable parallel test execution
parallel_execution = false

# Test report output directory
report_output_dir = "test_reports"

# Enable comprehensive logging
verbose_logging = false
```

### 4. TUI Configuration

```toml
[tui]
# TUI binary path (relative to project root)
binary_path = "target/debug/tui_harness"

# Command execution timeout in seconds
command_timeout = 30

# Output capture buffer size
output_buffer_size = 8192

# Key input delay in milliseconds
key_input_delay = 100
```

### 5. Backend Configuration

```toml
[backend]
# ExecutionManager settings
execution_manager_timeout = 60

# ExecutionDispatcher settings
execution_dispatcher_timeout = 45

# Strategy simulation settings
opportunity_simulation_count = 5
simulation_timeout = 30
```

### 6. Validation Configuration

```toml
[validation]
# Balance validation tolerance (decimal places)
balance_tolerance = "0.01"

# Transaction status validation timeout
transaction_timeout = 120

# Data validation retry attempts
validation_retry_attempts = 3

# Enable strict validation mode
strict_validation = true
```

## Environment Variables

### Core Environment Variables

```bash
# Network settings
export ANVIL_URL="http://localhost:8545"
export CHAIN_ID="8453"

# Contract addresses
export STARGATE_COMPASS_ADDRESS="******************************************"
export USDC_ADDRESS="******************************************"

# Test settings
export TEST_TIMEOUT="300"
export RETRY_ATTEMPTS="3"
export PARALLEL_EXECUTION="false"

# Logging
export RUST_LOG="info"
export RUST_BACKTRACE="1"
```

### Advanced Environment Variables

```bash
# Performance tuning
export TEST_THREADS="1"
export TOKIO_WORKER_THREADS="4"

# Memory management
export RUST_MIN_STACK="8388608"  # 8MB stack size

# Network tuning
export HTTP_TIMEOUT="30"
export CONNECTION_POOL_SIZE="10"

# Debug settings
export RUST_LOG_STYLE="always"
export ENABLE_DEBUG_LOGS="true"
```

## Command Line Arguments

### Basic Arguments

```bash
# Anvil testnet URL
--anvil-url http://localhost:8545

# Contract address
--contract-address ******************************************

# Test timeout in seconds
--timeout 300

# Retry attempts
--retry-attempts 3
```

### Advanced Arguments

```bash
# Enable parallel execution
--parallel

# Enable verbose logging
--verbose

# Skip specific test phases
--skip-tui
--skip-backend
--skip-workflow

# Report settings
--no-report
--report-path ./custom_reports

# Performance settings
--test-threads 1
--timeout 600
```

## Configuration Templates

### Minimal Configuration

```toml
# config/minimal.toml
[network]
rpc_url = "http://localhost:8545"
chain_id = 8453

[contracts]
stargate_compass_v1 = "******************************************"

[testing]
anvil_url = "http://localhost:8545"
test_timeout_seconds = 300
```

### Development Configuration

```toml
# config/development.toml
[network]
rpc_url = "http://localhost:8545"
chain_id = 8453
connection_timeout = 30
retry_attempts = 3

[contracts]
stargate_compass_v1 = "******************************************"
usdc = "******************************************"
weth = "******************************************"

[testing]
anvil_url = "http://localhost:8545"
test_timeout_seconds = 600
retry_attempts = 5
parallel_execution = false
verbose_logging = true
report_output_dir = "test_reports"

[tui]
binary_path = "target/debug/tui_harness"
command_timeout = 60
output_buffer_size = 16384

[validation]
balance_tolerance = "0.001"
strict_validation = false
validation_retry_attempts = 3
```

### Production Testing Configuration

```toml
# config/production-test.toml
[network]
rpc_url = "http://localhost:8545"
chain_id = 8453
connection_timeout = 60
retry_attempts = 5
confirmation_blocks = 3

[contracts]
stargate_compass_v1 = "******************************************"
usdc = "******************************************"
weth = "******************************************"

[testing]
anvil_url = "http://localhost:8545"
test_timeout_seconds = 900
retry_attempts = 3
parallel_execution = true
verbose_logging = false
report_output_dir = "production_test_reports"

[tui]
binary_path = "target/release/tui_harness"
command_timeout = 45
output_buffer_size = 8192

[backend]
execution_manager_timeout = 120
execution_dispatcher_timeout = 90
opportunity_simulation_count = 10

[validation]
balance_tolerance = "0.01"
strict_validation = true
transaction_timeout = 180
validation_retry_attempts = 5
```

## Configuration Validation

### Automatic Validation

The integration test suite automatically validates configuration on startup:

```rust
// Configuration validation checks
✅ Network connectivity to Anvil
✅ Contract address format and existence
✅ File permissions and accessibility
✅ Required fields presence
✅ Value range validation
✅ Cross-reference consistency
```

### Manual Validation

```bash
# Validate configuration syntax
toml get config/local.toml

# Test network connectivity
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545

# Verify contract deployment
cast code --rpc-url http://localhost:8545 0xCONTRACT_ADDRESS

# Test TUI binary
./target/debug/tui_harness --help
```

## Dynamic Configuration Updates

### Contract Address Updates

The integration test suite can automatically update contract addresses:

```rust
// Configuration manager updates these fields automatically
[contracts]
stargate_compass_v1 = "0xOLD_ADDRESS"  # Updated to new address

// Backup created at
config/backups/local.toml.backup_TIMESTAMP
```

### Runtime Configuration Changes

```bash
# Override configuration at runtime
STARGATE_COMPASS_ADDRESS=0xNEW_ADDRESS \
TEST_TIMEOUT=600 \
cargo test --test stargate_compass_integration
```

## Configuration Security

### Sensitive Information

**Never commit sensitive information to configuration files:**

```toml
# ❌ DON'T DO THIS
[accounts]
private_key = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"

# ✅ DO THIS INSTEAD
[accounts]
# Private keys loaded from environment variables
# Set PRIVATE_KEY environment variable
```

### Environment-Specific Configurations

```bash
# Use different configurations for different environments
config/
├── local.toml          # Local development
├── ci.toml            # Continuous integration
├── staging.toml       # Staging environment
└── production.toml    # Production testing
```

## Troubleshooting Configuration Issues

### Common Configuration Problems

1. **Invalid TOML Syntax**

   ```bash
   # Validate syntax
   toml get config/local.toml
   ```

2. **Missing Required Fields**

   ```toml
   # Ensure all required fields are present
   [contracts]
   stargate_compass_v1 = "REQUIRED"

   [network]
   rpc_url = "REQUIRED"
   chain_id = "REQUIRED"
   ```

3. **Incorrect Address Format**

   ```toml
   # Addresses must be valid Ethereum addresses
   stargate_compass_v1 = "******************************************"  # 42 characters
   ```

4. **Network Connectivity Issues**
   ```bash
   # Test RPC connectivity
   curl -X POST -H "Content-Type: application/json" \
     --data '{"jsonrpc":"2.0","method":"net_version","params":[],"id":1}' \
     http://localhost:8545
   ```

### Configuration Debugging

```bash
# Enable configuration debug logging
RUST_LOG=config=debug cargo test --test stargate_compass_integration

# Print loaded configuration
RUST_LOG=stargate_compass_integration=debug cargo test --test stargate_compass_integration 2>&1 | grep "Configuration loaded"

# Validate specific configuration file
cargo run --bin config_validator -- config/local.toml
```

## Performance Tuning

### Test Execution Performance

```toml
[testing]
# Reduce timeout for faster feedback
test_timeout_seconds = 180

# Enable parallel execution
parallel_execution = true

# Reduce retry attempts
retry_attempts = 2

# Optimize buffer sizes
[tui]
output_buffer_size = 4096  # Smaller buffer for faster processing
```

### Network Performance

```toml
[network]
# Reduce connection timeout
connection_timeout = 15

# Increase retry attempts for reliability
retry_attempts = 5

# Optimize confirmation requirements
confirmation_blocks = 1  # Faster for testing
```

### Memory Optimization

```bash
# Environment variables for memory optimization
export RUST_MIN_STACK="4194304"  # 4MB stack
export TOKIO_WORKER_THREADS="2"  # Fewer threads
export TEST_THREADS="1"          # Sequential execution
```

## Configuration Best Practices

### 1. Environment Separation

- Use separate configuration files for different environments
- Never mix development and production settings
- Use environment variables for sensitive data

### 2. Version Control

```bash
# Track configuration files in git
git add config/default.toml
git add config/local.toml.example

# Don't track sensitive configurations
echo "config/local.toml" >> .gitignore
echo "config/production.toml" >> .gitignore
```

### 3. Documentation

- Document all configuration options
- Provide example configurations
- Explain the purpose of each setting

### 4. Validation

- Always validate configuration before use
- Provide clear error messages for invalid settings
- Test configuration changes thoroughly

### 5. Backup and Recovery

```bash
# Automatic backup before updates
cp config/local.toml config/backups/local.toml.backup.$(date +%s)

# Recovery procedure
cp config/backups/local.toml.backup.TIMESTAMP config/local.toml
```

## Integration with CI/CD

### GitHub Actions Configuration

```yaml
# .github/workflows/integration-tests.yml
env:
  ANVIL_URL: 'http://localhost:8545'
  STARGATE_COMPASS_ADDRESS: ${{ secrets.TEST_CONTRACT_ADDRESS }}
  TEST_TIMEOUT: '600'
  RUST_LOG: 'info'

steps:
  - name: Setup Test Configuration
    run: |
      cp config/ci.toml config/local.toml
      sed -i "s/CONTRACT_ADDRESS_PLACEHOLDER/${{ secrets.TEST_CONTRACT_ADDRESS }}/g" config/local.toml
```

### Docker Configuration

```dockerfile
# Dockerfile.test
FROM rust:1.70

# Copy configuration files
COPY config/docker.toml /app/config/local.toml

# Set environment variables
ENV ANVIL_URL=http://anvil:8545
ENV RUST_LOG=info

# Run tests
CMD ["cargo", "test", "--test", "stargate_compass_integration"]
```

## Configuration Migration

### Upgrading Configuration Format

When configuration format changes:

1. **Backup existing configuration**
2. **Update to new format**
3. **Validate new configuration**
4. **Test with new configuration**

```bash
# Migration script example
#!/bin/bash
cp config/local.toml config/local.toml.backup
./scripts/migrate_config.sh config/local.toml
cargo test --test stargate_compass_integration -- --dry-run
```

## Support and Maintenance

### Regular Configuration Review

- Review configuration monthly
- Update contract addresses when needed
- Optimize performance settings based on usage
- Update documentation for new options

### Configuration Monitoring

```bash
# Monitor configuration file changes
inotifywait -m config/ -e modify,create,delete

# Log configuration usage
RUST_LOG=config=info cargo test --test stargate_compass_integration
```

This configuration guide provides comprehensive information for setting up and maintaining the Stargate Compass Integration Test Suite configuration. Proper configuration is essential for reliable and accurate test execution.
