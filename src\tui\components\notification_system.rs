use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::logging::ErrorCode;

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum NotificationType {
    Success,
    Warning,
    Error,
    Critical,
    Info,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Notification {
    pub id: String,
    pub title: String,
    pub message: String,
    pub notification_type: NotificationType,
    pub timestamp: DateTime<Utc>,
    pub context: String,
    pub actions: Vec<String>,
    pub acknowledged: bool,
    pub auto_dismiss_after: Option<std::time::Duration>,
}

#[derive(Debug)]
pub struct NotificationSystem {
    notifications: Vec<Notification>,
}

impl NotificationSystem {
    pub fn new() -> Self {
        Self {
            notifications: Vec::new(),
        }
    }

    pub fn add_notification(&mut self, notification: Notification) {
        self.notifications.push(notification);
    }

    pub fn clear_notifications(&mut self) {
        self.notifications.clear();
    }
}
