# Basilisk Bot - High-Frequency DeFi Trading Bot
# Multi-stage Docker build for optimal size and security

# Build stage
FROM rust:1.87 as builder

# Set the working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./

# Create dummy source files to build dependencies
RUN mkdir -p src bin src/bin && \
    echo "fn main() {}" > src/main.rs && \
    echo "fn main() {}" > bin/data_ingestor.rs && \
    echo "fn main() {}" > bin/listener.rs && \
    echo "fn main() {}" > bin/tui_harness.rs && \
    echo "fn main() {}" > bin/feature_exporter.rs && \
    echo "fn main() {}" > src/bin/graph_analyzer.rs && \
    echo "fn main() {}" > src/bin/network_observer.rs && \
    echo "fn main() {}" > src/bin/seismic_analyzer.rs && \
    echo "fn main() {}" > src/bin/backtester.rs && \
    echo "fn main() {}" > src/bin/demo_data_generator.rs && \
    echo "fn main() {}" > src/bin/optimizer.rs && \
    echo "fn main() {}" > src/bin/mempool_backtester.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release && rm -rf src bin

# Copy the actual source code
COPY . .

# Build the release binaries
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN useradd -m -u 1000 basilisk_bot

# Set the working directory
WORKDIR /app

# Copy the built binaries from the builder stage
COPY --from=builder /app/target/release/basilisk_bot ./basilisk_bot
COPY --from=builder /app/target/release/data_ingestor ./data_ingestor
COPY --from=builder /app/target/release/listener ./listener
COPY --from=builder /app/target/release/tui_harness ./tui_harness
COPY --from=builder /app/target/release/feature_exporter ./feature_exporter
COPY --from=builder /app/target/release/graph_analyzer ./graph_analyzer
COPY --from=builder /app/target/release/network_observer ./network_observer
COPY --from=builder /app/target/release/seismic_analyzer ./seismic_analyzer
COPY --from=builder /app/target/release/backtester ./backtester
COPY --from=builder /app/target/release/demo_data_generator ./demo_data_generator
COPY --from=builder /app/target/release/optimizer ./optimizer
COPY --from=builder /app/target/release/mempool_backtester ./mempool_backtester

# Copy configuration files
COPY --from=builder /app/config ./config

# Copy scripts for operational use
COPY --from=builder /app/scripts ./scripts

# Create necessary directories
RUN mkdir -p logs sigint backups

# Make binaries executable
RUN chmod +x ./basilisk_bot ./data_ingestor ./listener ./tui_harness ./feature_exporter ./graph_analyzer ./network_observer ./seismic_analyzer ./backtester ./demo_data_generator ./optimizer ./mempool_backtester

# Make scripts executable
RUN chmod +x ./scripts/*.sh

# Change ownership to non-root user
RUN chown -R basilisk_bot:basilisk_bot /app

# Switch to non-root user
USER basilisk_bot

# Health check for the main application
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9090/metrics || exit 1

# Expose ports for monitoring and metrics
EXPOSE 9090

# Set environment variables for production
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1
ENV ENVIRONMENT=production

# Default command runs the main Basilisk Bot application in simulation mode
# Override with --mode live for production trading (use with extreme caution)
CMD ["./basilisk_bot", "run", "--mode", "simulate"]
