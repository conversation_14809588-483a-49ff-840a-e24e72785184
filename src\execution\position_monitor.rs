// MISSION: PositionMonitor - Real-Time Position and PnL Tracking
// WHY: Maintain accurate, real-time view of all open positions and their performance
// HOW: Subscribe to NATS trade lifecycle events and maintain position state

use anyhow::Result;
use async_nats::Client as NatsClient;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

use crate::shared_types::{NatsTopics, trade_lifecycle::{TradeLifecycleEvent, TradeStatus}};

/// Represents a single trading position
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub trade_id: String,
    pub opportunity_type: String,
    pub chain_id: u64,
    pub entry_time: DateTime<Utc>,
    pub entry_price_usd: Option<Decimal>,
    pub position_size_usd: Decimal,
    pub estimated_profit_usd: Decimal,
    pub actual_profit_usd: Option<Decimal>,
    pub gas_cost_usd: Option<Decimal>,
    pub status: PositionStatus,
    pub transaction_hash: Option<String>,
    pub last_updated: DateTime<Utc>,
}

/// Status of a trading position
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PositionStatus {
    Pending,     // Transaction submitted but not confirmed
    Active,      // Position is open and active
    Completed,   // Position closed successfully
    Failed,      // Position failed to execute
    Reverted,    // Transaction reverted
}

/// Summary statistics for position monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionSummary {
    pub total_positions: usize,
    pub active_positions: usize,
    pub completed_positions: usize,
    pub failed_positions: usize,
    pub total_pnl_usd: Decimal,
    pub total_gas_cost_usd: Decimal,
    pub success_rate: Decimal,
    pub average_profit_per_trade: Decimal,
    pub largest_win_usd: Decimal,
    pub largest_loss_usd: Decimal,
}

/// Real-time position monitoring service
pub struct PositionMonitor {
    nats_client: NatsClient,
    positions: Arc<Mutex<HashMap<String, Position>>>,
    position_history: Arc<Mutex<Vec<Position>>>,
}

impl PositionMonitor {
    /// Create a new PositionMonitor
    pub fn new(nats_client: NatsClient) -> Self {
        info!("PositionMonitor initialized");
        
        Self {
            nats_client,
            positions: Arc::new(Mutex::new(HashMap::new())),
            position_history: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    /// Start monitoring trade lifecycle events
    pub async fn run(&self) -> Result<()> {
        info!("PositionMonitor starting - subscribing to trade lifecycle events");
        
        let mut subscriber = self.nats_client
            .subscribe(NatsTopics::LIVING_CODEX_TRADE_LIFECYCLE)
            .await?;
        
        info!("PositionMonitor subscribed to {}", NatsTopics::LIVING_CODEX_TRADE_LIFECYCLE);
        
        while let Some(msg) = subscriber.next().await {
            if let Err(e) = self.process_trade_event(&msg.payload).await {
                error!("Failed to process trade event: {}", e);
            }
        }
        
        warn!("PositionMonitor subscription ended unexpectedly");
        Ok(())
    }
    
    /// Process a trade lifecycle event
    async fn process_trade_event(&self, payload: &[u8]) -> Result<()> {
        let event: TradeLifecycleEvent = serde_json::from_slice(payload)?;
        
        debug!(
            "Processing trade event - ID: {}, Status: {:?}",
            event.trade_id,
            event.status
        );
        
        let mut positions = self.positions.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire positions lock: {}", e))
        })?;
        
        match event.status {
            TradeStatus::Approved => {
                // Create new position when trade is approved
                let position = Position {
                    trade_id: event.trade_id.clone(),
                    opportunity_type: event.opportunity_type.clone(),
                    chain_id: event.chain_id,
                    entry_time: event.timestamp,
                    entry_price_usd: None, // Will be updated when executed
                    position_size_usd: event.estimated_profit_usd.unwrap_or(dec!(0.0)),
                    estimated_profit_usd: event.estimated_profit_usd.unwrap_or(dec!(0.0)),
                    actual_profit_usd: None,
                    gas_cost_usd: None,
                    status: PositionStatus::Pending,
                    transaction_hash: None,
                    last_updated: Utc::now(),
                };
                
                positions.insert(event.trade_id.clone(), position);
                info!("New position created: {}", event.trade_id);
            },
            
            TradeStatus::Executed => {
                // Update position when transaction is executed
                if let Some(position) = positions.get_mut(&event.trade_id) {
                    position.status = PositionStatus::Active;
                    position.transaction_hash = event.transaction_hash.clone();
                    position.gas_cost_usd = event.gas_cost_usd;
                    position.last_updated = Utc::now();
                    
                    info!("Position executed: {} - TX: {:?}", event.trade_id, event.transaction_hash);
                } else {
                    warn!("Received executed event for unknown position: {}", event.trade_id);
                }
            },
            
            TradeStatus::Success => {
                // Close position successfully
                if let Some(mut position) = positions.remove(&event.trade_id) {
                    position.status = PositionStatus::Completed;
                    position.actual_profit_usd = event.actual_profit_usd;
                    position.last_updated = Utc::now();
                    
                    // Move to history
                    let mut history = self.position_history.lock().map_err(|e| {
                        BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
                    })?;
                    history.push(position.clone());
                    
                    info!(
                        "Position completed successfully: {} - Profit: ${:.2}",
                        event.trade_id,
                        position.actual_profit_usd.unwrap_or(dec!(0.0))
                    );
                } else {
                    warn!("Received success event for unknown position: {}", event.trade_id);
                }
            },
            
            TradeStatus::Failed => {
                // Close position as failed
                if let Some(mut position) = positions.remove(&event.trade_id) {
                    position.status = PositionStatus::Failed;
                    position.actual_profit_usd = Some(dec!(0.0)); // No profit on failed trades
                    position.last_updated = Utc::now();
                    
                    // Move to history
                    let mut history = self.position_history.lock().map_err(|e| {
                        BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
                    })?;
                    history.push(position.clone());
                    
                    warn!("Position failed: {}", event.trade_id);
                } else {
                    warn!("Received failed event for unknown position: {}", event.trade_id);
                }
            },
            
            TradeStatus::Reverted => {
                // Handle reverted transactions
                if let Some(mut position) = positions.remove(&event.trade_id) {
                    position.status = PositionStatus::Reverted;
                    position.actual_profit_usd = event.gas_cost_usd.map(|cost| -cost); // Loss = gas cost
                    position.last_updated = Utc::now();
                    
                    // Move to history
                    let mut history = self.position_history.lock().map_err(|e| {
                        BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
                    })?;
                    history.push(position.clone());
                    
                    warn!(
                        "Position reverted: {} - Loss: ${:.2}",
                        event.trade_id,
                        position.actual_profit_usd.unwrap_or(dec!(0.0))
                    );
                } else {
                    warn!("Received reverted event for unknown position: {}", event.trade_id);
                }
            },
            
            _ => {
                // Handle other status updates
                if let Some(position) = positions.get_mut(&event.trade_id) {
                    position.last_updated = Utc::now();
                    debug!("Position updated: {} - Status: {:?}", event.trade_id, event.status);
                }
            }
        }
        
        Ok(())
    }
    
    /// Get all currently open positions
    pub fn get_open_positions(&self) -> Result<HashMap<String, Position>> {
        let positions = self.positions.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire positions lock: {}", e))
        })?;
        
        Ok(positions.clone())
    }
    
    /// Get position summary statistics
    pub fn get_position_summary(&self) -> Result<PositionSummary> {
        let positions = self.positions.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire positions lock: {}", e))
        })?;
        
        let history = self.position_history.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
        })?;
        
        let active_positions = positions.len();
        let total_positions = active_positions + history.len();
        
        let completed_positions = history.iter()
            .filter(|p| p.status == PositionStatus::Completed)
            .count();
        
        let failed_positions = history.iter()
            .filter(|p| matches!(p.status, PositionStatus::Failed | PositionStatus::Reverted))
            .count();
        
        let total_pnl_usd = history.iter()
            .map(|p| p.actual_profit_usd.unwrap_or(dec!(0.0)))
            .sum();
        
        let total_gas_cost_usd = history.iter()
            .map(|p| p.gas_cost_usd.unwrap_or(dec!(0.0)))
            .sum();
        
        let success_rate = if total_positions > 0 {
            Decimal::from(completed_positions) / Decimal::from(total_positions)
        } else {
            dec!(0.0)
        };
        
        let average_profit_per_trade = if !history.is_empty() {
            total_pnl_usd / Decimal::from(history.len())
        } else {
            dec!(0.0)
        };
        
        let largest_win_usd = history.iter()
            .map(|p| p.actual_profit_usd.unwrap_or(dec!(0.0)))
            .max()
            .unwrap_or(dec!(0.0));
        
        let largest_loss_usd = history.iter()
            .map(|p| p.actual_profit_usd.unwrap_or(dec!(0.0)))
            .min()
            .unwrap_or(dec!(0.0));
        
        Ok(PositionSummary {
            total_positions,
            active_positions,
            completed_positions,
            failed_positions,
            total_pnl_usd,
            total_gas_cost_usd,
            success_rate,
            average_profit_per_trade,
            largest_win_usd,
            largest_loss_usd,
        })
    }
    
    /// Get position by trade ID
    pub fn get_position(&self, trade_id: &str) -> Result<Option<Position>> {
        let positions = self.positions.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire positions lock: {}", e))
        })?;
        
        Ok(positions.get(trade_id).cloned())
    }
    
    /// Get recent position history
    pub fn get_recent_history(&self, limit: usize) -> Result<Vec<Position>> {
        let history = self.position_history.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
        })?;
        
        let mut recent: Vec<Position> = history.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect();
        
        recent.reverse(); // Return in chronological order
        Ok(recent)
    }
    
    /// Clear old history entries (for memory management)
    pub fn cleanup_old_history(&self, max_entries: usize) -> Result<usize> {
        let mut history = self.position_history.lock().map_err(|e| {
            BasiliskError::LockAcquisitionFailed(format!("Failed to acquire history lock: {}", e))
        })?;
        
        let initial_count = history.len();
        
        if history.len() > max_entries {
            let excess = history.len() - max_entries;
            history.drain(0..excess);
            info!("Cleaned up {} old position history entries", excess);
            Ok(excess)
        } else {
            Ok(0)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_position_creation() {
        let position = Position {
            trade_id: "test-123".to_string(),
            opportunity_type: "DexArbitrage".to_string(),
            chain_id: 8453,
            entry_time: Utc::now(),
            entry_price_usd: Some(dec!(100.0)),
            position_size_usd: dec!(1000.0),
            estimated_profit_usd: dec!(50.0),
            actual_profit_usd: None,
            gas_cost_usd: None,
            status: PositionStatus::Pending,
            transaction_hash: None,
            last_updated: Utc::now(),
        };
        
        assert_eq!(position.trade_id, "test-123");
        assert_eq!(position.status, PositionStatus::Pending);
        assert_eq!(position.estimated_profit_usd, dec!(50.0));
    }
    
    #[test]
    fn test_position_summary_calculation() {
        // This would require more setup in a real test environment
        // For now, just test that the structure is correct
        let summary = PositionSummary {
            total_positions: 10,
            active_positions: 2,
            completed_positions: 7,
            failed_positions: 1,
            total_pnl_usd: dec!(150.0),
            total_gas_cost_usd: dec!(25.0),
            success_rate: dec!(0.7),
            average_profit_per_trade: dec!(15.0),
            largest_win_usd: dec!(75.0),
            largest_loss_usd: dec!(-10.0),
        };
        
        assert_eq!(summary.total_positions, 10);
        assert_eq!(summary.success_rate, dec!(0.7));
    }
}