### **`/docs/strategies/PILOT_FISH_GUIDE.md` (Rust-Optimized Edition)**

# **Implementation Guide: The "Pilot Fish" Strategy**

**To:** Coder Agent
**From:** The Architect
**Subject:** Mission Directive: Implementing Flash Loan Arbitrage with Advanced Rust Features

**Mission:** You will now implement the **"Pilot Fish"** strategy. We will leverage Rust's powerful type system and concurrency patterns to build a system that is not only highly profitable with minimal capital but also fundamentally more robust and safer than any alternative. This is a masterclass in turning language features into a competitive edge.

### **Doctrine: The Predator's Symbiote**

The Pilot Fish does not hunt for its own food. It is a symbiote that attaches itself to the whales of the ecosystem. It profits from the **leverage** of their massive trades, using flash loans to capture arbitrage opportunities far larger than its own capital reserves would allow. Its genius is not in size, but in intelligence, speed, and the safety provided by its underlying Rust architecture.

### **The Operational Flow: From Type-Safe Scent to Flawless Strike**

#### **1. The SENSE (`MempoolScanner`): Creating a Type-Safe "Scent"**

The scanner's job is not just to find an opportunity, but to produce a **descriptive, type-safe proposal** that the rest of the system can trust implicitly.

- **Rust Feature (Expressive Enums):** We will enhance our `OpportunityType` enum in `shared_types.rs` to create a specific, rich variant for this strategy. This eliminates guesswork and optional fields, making the data's intent clear at the type level.

  ```rust
  // In shared_types.rs
  pub enum OpportunityType {
      DexArbitrage { path: Vec<Address>, pools: Vec<Address> },

      // The NEW Pilot Fish variant. It carries its own unique data.
      PilotFishBackrun {
          target_whale_tx: H256,
          backrun_path: Vec<Address>,
          backrun_pools: Vec<Address>,
          // The capital required, which signals a flash loan is needed
          capital_requirement_usd: f64,
      },
      // ... other opportunity types
  }
  ```

- **The Workflow:**
  1.  The `MempoolScanner`'s **`Seismologist`** classifier identifies a `Whale_Trade`.
  2.  Its **"Impact Analysis"** function calculates the back-run path and required capital.
  3.  It then constructs and sends an `Opportunity<Detected>` to the central MPSC channel with its type set to `OpportunityType::PilotFishBackrun { ... }`, populated with all the specific data.

#### **2. The BRAIN (`StrategyManager`): Pattern Matching on the Scent**

The brain receives the opportunity. Because of Rust's powerful type system, it doesn't need to guess what it's looking at. It can use **exhaustive pattern matching** to apply specific logic.

- **Rust Feature (Powerful Pattern Matching):** The `calculate_opportunity_score` function becomes more robust and readable.

  ```rust
  // In StrategyManager's calculate_opportunity_score()
  match &opp.opportunity_type {
      OpportunityType::PilotFishBackrun { capital_requirement_usd, .. } => {
          // The compiler guarantees this is a Pilot Fish opportunity.
          // We can even use its specific data in our scoring.
          let mut score = base_score * 2.5; // Massive base multiplier
          if *capital_requirement_usd > 1_000_000.0 {
              score *= 0.9; // Slightly penalize extremely large loan requirements
          }
          return score;
      },
      OpportunityType::DexArbitrage { .. } => {
          // Apply the standard regime/character multipliers for regular arbs.
          // ...
      },
      _ => { /* Handle other types */ }
  }
  ```

- **The Synergy with the `Aetheric Resonance Engine`:** The Pilot Fish opportunity is still evaluated against the macro context provided by the **`Chronos Sieve`**. The `base_score` used in the `match` arm is already the risk-adjusted **Certainty-Equivalent Profit**. If the `Chronos Sieve` has classified the market as `Bot_Gas_War`, the opportunity's score will be heavily penalized before it even reaches this `match` statement, ensuring the bot doesn't attempt a risky flash loan during a period of extreme network congestion.

#### **3. The STRIKE (`ExecutionManager`): A Flawless, Type-Safe Execution**

This is where the design pays the greatest dividends. The `ExecutionManager` can now operate with **compile-time guarantees of correctness**.

- **The Smart Contract:** The `Executor.sol` contract is upgraded with the `executeFlashLoanArbitrage` function and its `executeOperation` callback. This on-chain logic remains the definitive plan for the atomic strike.

- **The Off-Chain Execution Path (Rust Advantage):** The `ExecutionManager`'s main processing loop uses an exhaustive `match` statement. This is not just a stylistic choice; it is a **safety feature**.

  ```rust
  // In ExecutionManager's main processing function
  // The `opportunity` received here is of type Opportunity<ScoredAndVetted>
  // This is guaranteed by the type system.

  // ... Gorgon's Gaze bidding logic runs first ...

  match &opportunity.opportunity_type {
      OpportunityType::DexArbitrage { path, pools } => {
          // The compiler KNOWS this is a standard arbitrage.
          // It would be a COMPILE ERROR to try and call the flash loan dispatcher here.
          self.dispatcher.dispatch_simple_swap(path, pools, bid).await;
      },

      OpportunityType::PilotFishBackrun { backrun_path, backrun_pools, capital_requirement_usd, .. } => {
          // The compiler GUARANTEES we are in the Pilot Fish context
          // and that all its specific data fields are available and valid.
          self.dispatcher.dispatch_pilot_fish_trade(
              backrun_path,
              backrun_pools,
              *capital_requirement_usd,
              bid
          ).await;
      },

      // If you ever add a new OpportunityType, the Rust compiler will FAIL
      // to build until you add a handler for it here, preventing runtime errors.
      _ => {
          tracing::error!("Execution not implemented for this opportunity type!");
      }
  }
  ```

- **The Result:** This architecture creates a system that is incredibly robust. The "what" of an opportunity is encoded in its type. The "how" of its execution is determined by safe, exhaustive pattern matching. This eliminates an entire class of logical bugs that could lead to sending the wrong transaction for the wrong opportunity, which would be a catastrophic and costly failure. The bot's most powerful strike is now also its safest.
