#[cfg(test)]
mod config_manager_tests {
    use super::*;
    use std::fs;
    use std::path::PathBuf;
    use tempfile::TempDir;
    use ethers::types::Address;
    use std::str::FromStr;

    fn create_test_config_content() -> String {
        r#"
[network]
rpc_url = "http://localhost:8545"
chain_id = 8453

[contracts]
stargate_compass_v1 = "******************************************"

[execution]
max_gas_price = 50000000000
slippage_tolerance = 0.005
"#.to_string()
    }

    fn create_test_config_file(dir: &TempDir, filename: &str, content: &str) -> PathBuf {
        let config_path = dir.path().join(filename);
        fs::write(&config_path, content).unwrap();
        config_path
    }

    #[tokio::test]
    async fn test_update_single_config_file_success() {
        let temp_dir = TempDir::new().unwrap();
        let config_content = create_test_config_content();
        let config_path = create_test_config_file(&temp_dir, "test.toml", &config_content);
        
        let config_manager = ConfigurationManager::with_files(vec![config_path.clone()]);
        let new_address = Address::from_str("0x1234567890123456789012345678901234567890").unwrap();
        
        // Test the update
        let result = config_manager.update_single_config_file(&config_path, new_address).await;
        assert!(result.is_ok(), "Update should succeed: {:?}", result);
        
        // Verify the address was updated
        let updated_config = config_manager.parse_config_file(&config_path).unwrap();
        let actual_address = config_manager.extract_contract_address(&updated_config).unwrap();
        assert_eq!(actual_address, new_address);
    }

    #[tokio::test]
    async fn test_update_single_config_file_with_backup() {
        let temp_dir = TempDir::new().unwrap();
        let config_content = create_test_config_content();
        let config_path = create_test_config_file(&temp_dir, "test.toml", &config_content);
        
        let config_manager = ConfigurationManager::with_files(vec![config_path.clone()]);
        let new_address = Address::from_str("0x1234567890123456789012345678901234567890").unwrap();
        
        // Get original content
        let original_content = fs::read_to_string(&config_path).unwrap();
        
        // Test the update
        let result = config_manager.update_single_config_file(&config_path, new_address).await;
        assert!(result.is_ok(), "Update should succeed: {:?}", result);
        
        // Check that backup was created and then cleaned up
        let backup_files: Vec<_> = fs::read_dir(temp_dir.path())
            .unwrap()
            .filter_map(|entry| entry.ok())
            .filter(|entry| {
                entry.file_name().to_str()
                    .map(|name| name.contains("backup"))
                    .unwrap_or(false)
            })
            .collect();
        
        // Backup should be cleaned up after successful update
        assert_eq!(backup_files.len(), 0, "Backup files should be cleaned up after successful update");
    }

    #[tokio::test]
    async fn test_update_single_config_file_validation_failure() {
        let temp_dir = TempDir::new().unwrap();
        
        // Create invalid config (missing required sections)
        let invalid_config = r#"
[contracts]
stargate_compass_v1 = "******************************************"
"#;
        
        let config_path = create_test_config_file(&temp_dir, "invalid.toml", invalid_config);
        let config_manager = ConfigurationManager::with_files(vec![config_path.clone()]);
        let new_address = Address::from_str("0x1234567890123456789012345678901234567890").unwrap();
        
        // Test the update - should fail validation
        let result = config_manager.update_single_config_file(&config_path, new_address).await;
        assert!(result.is_err(), "Update should fail due to validation");
        
        // Original file should be unchanged
        let content = fs::read_to_string(&config_path).unwrap();
        assert!(content.contains("******************************************"));
    }

    #[tokio::test]
    async fn test_find_most_recent_backup() {
        let temp_dir = TempDir::new().unwrap();
        let config_path = temp_dir.path().join("test.toml");
        fs::write(&config_path, "test content").unwrap();
        
        let config_manager = ConfigurationManager::with_files(vec![config_path.clone()]);
        
        // Create some backup files with different timestamps
        let backup1 = temp_dir.path().join("test.toml.backup.1000");
        let backup2 = temp_dir.path().join("test.toml.backup.2000");
        let backup3 = temp_dir.path().join("test.toml.backup.1500");
        
        fs::write(&backup1, "backup1").unwrap();
        fs::write(&backup2, "backup2").unwrap();
        fs::write(&backup3, "backup3").unwrap();
        
        // Test finding most recent backup
        let result = config_manager.find_most_recent_backup(&config_path).unwrap();
        assert!(result.is_some());
        
        let most_recent = result.unwrap();
        assert_eq!(most_recent, backup2); // Should be the one with timestamp 2000
    }

    #[tokio::test]
    async fn test_validate_updates() {
        let temp_dir = TempDir::new().unwrap();
        let config_content = create_test_config_content();
        let config_path = create_test_config_file(&temp_dir, "test.toml", &config_content);
        
        let config_manager = ConfigurationManager::with_files(vec![config_path.clone()]);
        let new_address = Address::from_str("0x1234567890123456789012345678901234567890").unwrap();
        
        // Update the file first
        config_manager.update_single_config_file(&config_path, new_address).await.unwrap();
        
        // Test validation
        let result = config_manager.validate_updates(&[config_path], new_address).await.unwrap();
        assert!(result, "Validation should pass for correctly updated file");
        
        // Test validation with wrong address
        let wrong_address = Address::from_str("0x9999999999999999999999999999999999999999").unwrap();
        let result = config_manager.validate_updates(&[config_path], wrong_address).await.unwrap();
        assert!(!result, "Validation should fail for incorrect address");
    }
}