# MEV Module

This module is responsible for implementing strategies related to Maximal Extractable Value (MEV), including both protection against MEV attacks and the execution of MEV-seeking strategies.

## Table of Contents

- [Core Components](#core-components)
- [Further Documentation](#further-documentation)

## Core Components

-   **`mev_share_integrator.rs`**: Integrates with MEV-Share, a protocol for sharing MEV revenue with users.
-   **`sandwich_detector.rs`**: Detects and protects against sandwich attacks, a common form of MEV exploitation.

## Further Documentation

For more detailed information about the project's MEV strategies, please refer to the documents in the [`docs/`](../../docs/) directory.
