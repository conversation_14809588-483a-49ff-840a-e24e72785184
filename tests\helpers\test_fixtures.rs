// Test Fixtures and Utilities
// Common test data and helper functions

use basilisk_bot::shared_types::{Opportunity, OpportunityBase, DexArbitrageData, Pool};
use ethers::types::{Address, U256};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use uuid::Uuid;

/// Create a standard test opportunity for DEX arbitrage
pub fn create_test_dex_opportunity() -> Opportunity {
    Opportunity {
        id: Uuid::new_v4().to_string(),
        opportunity_type: basilisk_bot::shared_types::OpportunityType::DexArbitrage(
            DexArbitrageData {
                token_in: test_token_addresses().weth,
                token_out: test_token_addresses().usdc,
                amount_in: U256::from(1000000000000000000u64), // 1 ETH
                expected_amount_out: U256::from(3050000000u64), // 3050 USDC
                path: vec![test_token_addresses().weth, test_token_addresses().usdc],
                dex_protocol: "uniswap_v2".to_string(),
                pool_addresses: vec![Address::random()],
            }
        ),
        base: OpportunityBase {
            source_scanner: "TestScanner".to_string(),
            estimated_gross_profit_usd: dec!(50.0),
            associated_volatility: dec!(0.15),
            requires_flash_liquidity: false,
            chain_id: 8453,
            timestamp: chrono::Utc::now().timestamp() as u64,
            intersection_value_usd: dec!(3000.0),
        },
    }
}

/// Create a high-value opportunity requiring flash loan
pub fn create_flash_loan_opportunity() -> Opportunity {
    let mut opportunity = create_test_dex_opportunity();
    opportunity.base.estimated_gross_profit_usd = dec!(500.0);
    opportunity.base.requires_flash_liquidity = true;
    opportunity.base.intersection_value_usd = dec!(50000.0);
    
    if let basilisk_bot::shared_types::OpportunityType::DexArbitrage(ref mut data) = opportunity.opportunity_type {
        data.amount_in = U256::from(10000000000000000000u64); // 10 ETH
        data.expected_amount_out = U256::from(30500000000u64); // 30500 USDC
    }
    
    opportunity
}

/// Create test liquidity pools
pub fn create_test_pools() -> Vec<Pool> {
    let tokens = test_token_addresses();
    
    vec![
        Pool {
            address: Address::random(),
            token0: tokens.weth,
            token1: tokens.usdc,
            liquidity: dec!(1000000.0),
            protocol: "uniswap_v2".to_string(),
        },
        Pool {
            address: Address::random(),
            token0: tokens.weth,
            token1: tokens.usdc,
            liquidity: dec!(800000.0),
            protocol: "sushiswap".to_string(),
        },
        Pool {
            address: Address::random(),
            token0: tokens.usdc,
            token1: tokens.usdt,
            liquidity: dec!(2000000.0),
            protocol: "curve".to_string(),
        },
    ]
}

/// Standard test token addresses
pub struct TestTokenAddresses {
    pub weth: Address,
    pub usdc: Address,
    pub usdt: Address,
    pub dai: Address,
}

pub fn test_token_addresses() -> TestTokenAddresses {
    TestTokenAddresses {
        weth: Address::from([0x11; 20]),
        usdc: Address::from([0x22; 20]),
        usdt: Address::from([0x33; 20]),
        dai: Address::from([0x44; 20]),
    }
}

/// Create test market conditions
pub fn create_test_market_conditions() -> TestMarketConditions {
    TestMarketConditions {
        volatility_1h: dec!(0.05),
        volatility_24h: dec!(0.15),
        gas_price_gwei: dec!(20.0),
        eth_price_usd: dec!(3000.0),
        network_congestion: NetworkCongestion::Low,
    }
}

#[derive(Debug, Clone)]
pub struct TestMarketConditions {
    pub volatility_1h: Decimal,
    pub volatility_24h: Decimal,
    pub gas_price_gwei: Decimal,
    pub eth_price_usd: Decimal,
    pub network_congestion: NetworkCongestion,
}

#[derive(Debug, Clone)]
pub enum NetworkCongestion {
    Low,
    Medium,
    High,
    Critical,
}

/// Create test configuration for different scenarios
pub fn create_test_config(scenario: TestScenario) -> TestConfig {
    match scenario {
        TestScenario::Conservative => TestConfig {
            max_position_size_usd: dec!(1000.0),
            min_profit_threshold_usd: dec!(10.0),
            max_slippage_percent: dec!(0.5),
            kelly_fraction_limit: dec!(0.1),
        },
        TestScenario::Aggressive => TestConfig {
            max_position_size_usd: dec!(10000.0),
            min_profit_threshold_usd: dec!(50.0),
            max_slippage_percent: dec!(2.0),
            kelly_fraction_limit: dec!(0.25),
        },
        TestScenario::HighVolatility => TestConfig {
            max_position_size_usd: dec!(500.0),
            min_profit_threshold_usd: dec!(25.0),
            max_slippage_percent: dec!(1.0),
            kelly_fraction_limit: dec!(0.05),
        },
    }
}

#[derive(Debug, Clone)]
pub enum TestScenario {
    Conservative,
    Aggressive,
    HighVolatility,
}

#[derive(Debug, Clone)]
pub struct TestConfig {
    pub max_position_size_usd: Decimal,
    pub min_profit_threshold_usd: Decimal,
    pub max_slippage_percent: Decimal,
    pub kelly_fraction_limit: Decimal,
}

/// Utility functions for test assertions
pub mod assertions {
    use super::*;
    
    pub fn assert_profitable_opportunity(opportunity: &Opportunity) {
        assert!(opportunity.base.estimated_gross_profit_usd > dec!(0.0), 
               "Opportunity should be profitable");
    }
    
    pub fn assert_reasonable_gas_cost(gas_cost_usd: Decimal, profit_usd: Decimal) {
        assert!(gas_cost_usd < profit_usd * dec!(0.5), 
               "Gas cost should not exceed 50% of profit");
    }
    
    pub fn assert_valid_slippage(expected: U256, actual: U256, max_slippage_percent: Decimal) {
        let slippage = if actual < expected {
            (expected - actual) * U256::from(10000) / expected
        } else {
            U256::zero()
        };
        
        let max_slippage_bps = (max_slippage_percent * dec!(10000)).to_u64().unwrap_or(0);
        assert!(slippage <= U256::from(max_slippage_bps), 
               "Slippage should not exceed maximum allowed");
    }
}