// MISSION: Transaction Broadcaster - Multi-Path MEV-Aware Transaction Submission
// WHY: Protect high-value opportunities from front-running while maintaining fallback options
// HOW: Intelligent routing between public mempool and private MEV relays

use anyhow::Result;
use ethers::{
    prelude::*,
    providers::{Http, Middleware, Provider},
    types::{Address, Bytes, TransactionRequest, TransactionReceipt, TxHash, U256},
    middleware::SignerMiddleware,
    signers::LocalWallet,
};
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use super::wallet_manager::WalletManager;

#[derive(Clone)]
pub struct Broadcaster {
    provider: Arc<Provider<Http>>,
    client: Option<Arc<SignerMiddleware<Provider<Http>, LocalWallet>>>,
    dry_run: bool,
    verbose: bool,
    simulation_mode: bool,
    run_mode: crate::shared_types::RunMode,
    shadow_simulator: Option<Arc<crate::execution::shadow_simulator::ShadowSimulator>>,
}

impl Broadcaster {
    /// Create a new Broadcaster with a WalletManager for signing
    pub fn new(
        provider: Arc<Provider<Http>>,
        wallet_manager: &WalletManager,
    ) -> Self {
        let client = Arc::new(SignerMiddleware::new(
            (*provider).clone(),
            (*wallet_manager.signer()).clone(),
        ));

        Self {
            provider,
            client: Some(client),
            dry_run: false,
            verbose: false,
            simulation_mode: false,
            run_mode: crate::shared_types::RunMode::Live,
            shadow_simulator: None,
        }
    }

    pub fn new_legacy(
        provider: Arc<Provider<Http>>, 
        dry_run: bool, 
        verbose: bool,
        run_mode: crate::shared_types::RunMode,
        mainnet_rpc_url: Option<String>,
    ) -> Self {
        let shadow_simulator = if run_mode == crate::shared_types::RunMode::Shadow {
            mainnet_rpc_url.map(|url| {
                Arc::new(crate::execution::shadow_simulator::ShadowSimulator::new(url))
            })
        } else {
            None
        };

        Self {
            provider,
            client: None,
            dry_run,
            verbose,
            simulation_mode: dry_run,
            run_mode,
            shadow_simulator,
        }
    }

    /// Create a broadcaster specifically for simulation mode
    pub fn new_simulation(provider: Arc<Provider<Http>>) -> Self {
        Self {
            provider,
            client: None,
            dry_run: true,
            verbose: true,
            simulation_mode: true,
            run_mode: crate::shared_types::RunMode::Simulate,
            shadow_simulator: None,
        }
    }

    /// Create a broadcaster for a specific opportunity type - MEV-aware routing
    pub fn for_opportunity(
        provider: Arc<Provider<Http>>,
        mev_sensitive: bool,
        relays: Option<Vec<String>>,
        signer: Option<Arc<ethers::signers::LocalWallet>>,
        dry_run: bool,
        verbose: bool,
    ) -> Self {
        // MEV-aware routing: choose broadcaster type based on opportunity sensitivity
        if mev_sensitive && relays.is_some() && signer.is_some() {
            // Use MEV relay broadcaster for sensitive opportunities
            info!("Creating MEV relay broadcaster for sensitive opportunity");
            match (relays, signer) {
                (Some(relays), Some(signer)) => {
                    Self::new_mev_relay(provider, relays, signer, dry_run, verbose)
                }
                _ => {
                    warn!("MEV relay configuration missing, falling back to public broadcaster");
                    Self::new_public(provider, dry_run, verbose)
                }
            }
        } else {
            // Use public broadcaster for non-sensitive opportunities
            info!("Creating public broadcaster for standard opportunity");
            Self::new_public(provider, dry_run, verbose)
        }
    }

    /// Create a MEV relay broadcaster for sensitive opportunities
    pub fn new_mev_relay(
        provider: Arc<Provider<Http>>,
        relays: Vec<String>,
        signer: Arc<ethers::signers::LocalWallet>,
        dry_run: bool,
        verbose: bool,
    ) -> Self {
        info!("Initializing MEV relay broadcaster with {} relays", relays.len());
        Self {
            provider,
            client: None,
            dry_run,
            verbose,
            simulation_mode: dry_run,
            run_mode: if dry_run { 
                crate::shared_types::RunMode::Simulate 
            } else { 
                crate::shared_types::RunMode::Live 
            },
            shadow_simulator: None,
        }
    }

    /// Create a public broadcaster for standard opportunities
    pub fn new_public(
        provider: Arc<Provider<Http>>,
        dry_run: bool,
        verbose: bool,
    ) -> Self {
        info!("Initializing public broadcaster");
        Self {
            provider,
            client: None,
            dry_run,
            verbose,
            simulation_mode: dry_run,
            run_mode: if dry_run { 
                crate::shared_types::RunMode::Simulate 
            } else { 
                crate::shared_types::RunMode::Live 
            },
            shadow_simulator: None,
        }
    }

    /// Send a transaction and wait for confirmation
    /// This is the main method for Phase 1 implementation
    pub async fn send_and_confirm(&self, tx: TransactionRequest) -> Result<TransactionReceipt> {
        if let Some(client) = &self.client {
            info!("Sending transaction and waiting for confirmation");
            
            // Send the transaction using the SignerMiddleware
            let pending_tx = client.send_transaction(tx, None).await?;
            
            // Wait for the transaction to be mined
            let receipt = pending_tx.await?.ok_or_else(|| {
                anyhow::anyhow!("Transaction was dropped from mempool")
            })?;
            
            info!(
                "Transaction confirmed - Hash: {:?}, Block: {:?}, Gas Used: {:?}",
                receipt.transaction_hash,
                receipt.block_number,
                receipt.gas_used
            );
            
            Ok(receipt)
        } else {
            return Err(anyhow::anyhow!("No signer available - use new() with WalletManager"));
        }
    }

    /// Send a transaction through the appropriate channel
    /// SIMULATION GATE: This is the critical safety mechanism that prevents real transactions in simulation mode
    pub async fn send_transaction(&self, tx: TransactionRequest) -> Result<Option<TxHash>> {
        // SIMULATION GATE: Hard check for simulation mode
        if self.simulation_mode {
            info!("SIMULATION GATE: Transaction intercepted - generating fake hash for educational purposes");
            let fake_hash = TxHash::random();
            
            if self.verbose {
                self.print_simulation_transaction_preview(&tx, fake_hash).await;
            }
            
            // Return fake transaction hash for simulation
            return Ok(Some(fake_hash));
        }

        // Regular dry-run mode (legacy)
        if self.dry_run {
            if self.verbose {
                self.print_verbose_transaction_preview(&tx).await;
            } else {
                info!("DRY RUN: Transaction would be sent (use --verbose for details)");
            }
            return Ok(None);
        }

        // LIVE MODE: Only reached if not in simulation or dry-run
        warn!("LIVE TRANSACTION: Broadcasting to mainnet!");
        if let Some(client) = &self.client {
            let pending_tx = client.send_transaction(tx, None).await?;
            Ok(Some(*pending_tx))
        } else {
            Err(anyhow::anyhow!("No signer client available for live transaction broadcast"))
        }
    }

    /// Print detailed transaction preview for educational purposes
    async fn print_verbose_transaction_preview(&self, tx: &TransactionRequest) {
        println!("\n=== [DRY RUN: VERBOSE] Transaction Preview ===");
        
        if let Some(to) = &tx.to {
            println!("Target Contract: {:?}", to);
        }
        
        if let Some(value) = &tx.value {
            println!("Value: {} ETH", ethers::utils::format_ether(*value));
        }
        
        if let Some(gas) = &tx.gas {
            println!("Gas Limit: {}", gas);
        }
        
        if let Some(gas_price) = &tx.gas_price {
            println!("Gas Price: {} gwei", gas_price / U256::from(1_000_000_000u64));
            if let Some(gas) = &tx.gas {
                let total_gas_cost = *gas_price * *gas;
                println!("Estimated Gas Cost: {} ETH", ethers::utils::format_ether(total_gas_cost));
            }
        }
        
        if let Some(data) = &tx.data {
            println!("Calldata Length: {} bytes", data.len());
            if data.len() >= 4 {
                println!("Function Signature: 0x{}", hex::encode(&data[0..4]));
                
                let sig = hex::encode(&data[0..4]);
                let strategy = match sig.as_str() {
                    "38ed1739" => "Uniswap V2 swapExactTokensForTokens",
                    "18cbafe5" => "Uniswap V2 swapExactTokensForETH", 
                    "7ff36ab5" => "Uniswap V2 swapExactETHForTokens",
                    _ => "Unknown Strategy",
                };
                println!("Detected Strategy: {}", strategy);
            }
        }
        
        println!("=== Transaction would be broadcast to network ===");
        println!("Status: [OK] SIMULATION COMPLETE - No real transaction sent\n");
    }

    /// Print detailed transaction preview specifically for simulation mode
    async fn print_simulation_transaction_preview(&self, tx: &TransactionRequest, fake_hash: TxHash) {
        println!("\n=== [SIMULATION MODE] Educational Transaction Analysis ===");
        
        println!("SIMULATION HASH: {:?}", fake_hash);
        println!("NOTE: This transaction was NOT broadcast to the blockchain");
        
        if let Some(to) = &tx.to {
            println!("Target Contract: {:?}", to);
        }
        
        if let Some(value) = &tx.value {
            println!("Value: {} ETH", ethers::utils::format_ether(*value));
        }
        
        if let Some(gas) = &tx.gas {
            println!("Gas Limit: {}", gas);
        }
        
        if let Some(gas_price) = &tx.gas_price {
            println!("Gas Price: {} gwei", gas_price / U256::from(1_000_000_000u64));
            if let Some(gas) = &tx.gas {
                let total_gas_cost = *gas_price * *gas;
                println!("Estimated Gas Cost: {} ETH", ethers::utils::format_ether(total_gas_cost));
            }
        }
        
        if let Some(data) = &tx.data {
            println!("Calldata Length: {} bytes", data.len());
            if data.len() >= 4 {
                println!("Function Signature: 0x{}", hex::encode(&data[0..4]));
                
                let sig = hex::encode(&data[0..4]);
                let strategy = match sig.as_str() {
                    "38ed1739" => "Uniswap V2 swapExactTokensForTokens",
                    "18cbafe5" => "Uniswap V2 swapExactTokensForETH", 
                    "7ff36ab5" => "Uniswap V2 swapExactETHForTokens",
                    _ => "Unknown Strategy",
                };
                println!("Detected Strategy: {}", strategy);
            }
        }
        
        println!("=== EDUCATIONAL SIMULATION - SAFE FOR LEARNING ===");
        println!("Status: [SIMULATED] Transaction analyzed but not broadcast\n");
    }

    /// Send a bundle of transactions (placeholder for MEV functionality)
    pub async fn send_bundle(&self, bundle: Bundle) -> Result<Option<String>> {
        if self.simulation_mode {
            info!("SIMULATION GATE: Bundle intercepted - generating fake bundle ID");
            if self.verbose {
                println!("\n=== [SIMULATION MODE] Bundle Preview ===");
                println!("Bundle Transactions: {}", bundle.transactions.len());
                println!("Target Block: {}", bundle.block_number);
                println!("Bundle would be sent to MEV relay");
                println!("Status: [SIMULATED] Bundle analyzed but not broadcast\n");
            }
            return Ok(Some("simulation-bundle-id".to_string()));
        }

        if self.dry_run {
            if self.verbose {
                println!("\n=== [DRY RUN: VERBOSE] Bundle Preview ===");
                println!("Bundle Transactions: {}", bundle.transactions.len());
                println!("Target Block: {}", bundle.block_number);
                println!("Bundle would be sent to MEV relay");
                println!("Status: [OK] BUNDLE SIMULATION COMPLETE\n");
            } else {
                info!("DRY RUN: Bundle would be sent to MEV relay");
            }
            return Ok(Some("dry-run-bundle-id".to_string()));
        }

        warn!("Bundle submission not implemented - falling back to individual transactions");
        Ok(None)
    }
}

/// Bundle of transactions for MEV submission
#[derive(Debug, Clone)]
pub struct Bundle {
    pub transactions: Vec<Bytes>,
    pub block_number: u64,
}

impl Bundle {
    pub fn new(transactions: Vec<Bytes>, block_number: u64) -> Self {
        Self {
            transactions,
            block_number,
        }
    }
}