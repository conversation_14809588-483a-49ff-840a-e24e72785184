const { expect } = require('chai');
const { ethers } = require('hardhat');

describe('StargateCompassV1 - Medium Severity Vulnerability Tests', function () {
  let stargateCompass;
  let owner;
  let attacker;
  let mockAaveProvider;
  let mockStargateRouter;
  let mockPool;
  let mockUSDC;

  const USDC_ADDRESS = '******************************************';
  const LOAN_AMOUNT = ethers.parseUnits('1000', 6); // 1000 USDC
  const EXPECTED_PROFIT = ethers.parseUnits('10', 6); // 10 USDC profit

  beforeEach(async function () {
    [owner, attacker] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with ETH for fees
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('1'),
    });

    // Set up mock USDC balance for flash loan simulation
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('10000', 6));
  });

  describe('M-1: Proper Handling of LayerZero Fee Return Values', function () {
    it('should handle both nativeFee and zroFee return values correctly', async function () {
      const nativeFee = ethers.parseEther('0.01');
      const zroFee = 0; // Should be zero for supported operations

      await mockStargateRouter.setQuoteLayerZeroFee(nativeFee, zroFee);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should reject operations when zroFee is non-zero', async function () {
      const nativeFee = ethers.parseEther('0.01');
      const zroFee = ethers.parseUnits('100', 18); // Non-zero ZRO fee

      await mockStargateRouter.setQuoteLayerZeroFee(nativeFee, zroFee);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ZROFeesNotSupported')
        .withArgs(zroFee);
    });

    it('should handle edge cases with fee return values', async function () {
      // Test with zero native fee
      await mockStargateRouter.setQuoteLayerZeroFee(0, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;

      // Test with maximum allowed native fee
      const maxFee = ethers.parseEther('0.1');
      await mockStargateRouter.setQuoteLayerZeroFee(maxFee, 0);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should validate fee values before using them in swap call', async function () {
      // Test that fee validation occurs before the actual swap
      const excessiveFee = ethers.parseEther('0.2'); // Above limit
      await mockStargateRouter.setQuoteLayerZeroFee(excessiveFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'ExcessiveFee');
    });

    it('should handle fee quote failures gracefully', async function () {
      // This test would require a mock that can simulate quote failures
      // For now, we test that the contract handles the returned values correctly
      const normalFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFee(normalFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });

  describe('M-2: Zero Address Validation for All Parameters', function () {
    it('should validate remoteSwapRouter is not zero address', async function () {
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // The contract should validate the remoteSwapRouter parameter
      // Note: Current implementation doesn't explicitly check for zero address in executeRemoteDegenSwap
      // This test documents the expected behavior that should be implemented

      // For now, test that zero address would cause issues in the operation
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          ethers.ZeroAddress, // Zero address router
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted; // Current implementation allows this, but it should be validated
    });

    it('should validate constructor parameters are not zero addresses', async function () {
      const StargateCompassV1 = await ethers.getContractFactory(
        'StargateCompassV1'
      );

      // Test with zero Aave provider
      await expect(
        StargateCompassV1.deploy(ethers.ZeroAddress, mockStargateRouter.target)
      ).to.be.reverted; // Should fail in FlashLoanSimpleReceiverBase constructor

      // Test with zero Stargate router
      await expect(
        StargateCompassV1.deploy(mockAaveProvider.target, ethers.ZeroAddress)
      ).to.not.be.reverted; // Current implementation allows this, but should be validated
    });

    it('should validate token addresses in emergency functions', async function () {
      await expect(
        stargateCompass.emergencyWithdraw(ethers.ZeroAddress)
      ).to.be.revertedWith('Invalid token address');
    });

    it('should handle zero address in asset recovery functions', async function () {
      // Test that getRecoverableAssets handles zero address correctly for ETH
      const [tokens, balances] = await stargateCompass.getRecoverableAssets();

      // ETH should be represented as address(0)
      expect(tokens).to.include(ethers.ZeroAddress);
    });

    it('should validate addresses in all external function calls', async function () {
      // Test various functions that should validate address parameters
      const validAddress = owner.address;
      const zeroAddress = ethers.ZeroAddress;

      // Emergency withdraw with zero address should fail
      await expect(
        stargateCompass.emergencyWithdraw(zeroAddress)
      ).to.be.revertedWith('Invalid token address');

      // Valid address should work (if token has balance)
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('100', 6));
      await expect(stargateCompass.emergencyWithdraw(mockUSDC.target)).to.not.be
        .reverted;
    });
  });

  describe('M-3: ETH Balance Monitoring and Validation', function () {
    it('should monitor ETH balance before fee payments', async function () {
      // Drain ETH to minimal amount
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const withdrawAmount = currentBalance - ethers.parseEther('0.005'); // Leave very little
      await stargateCompass.withdrawETH(withdrawAmount);

      // Set fee that exceeds remaining balance
      const highFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFee(highFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });

    it('should provide accurate ETH balance reporting', async function () {
      const reportedBalance = await stargateCompass.getETHBalance();
      const actualBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      expect(reportedBalance).to.equal(actualBalance);
    });

    it('should validate ETH balance for buffered fees', async function () {
      const baseFee = ethers.parseEther('0.01');
      const buffer = (baseFee * BigInt(200)) / BigInt(10000); // 2% buffer
      const bufferedFee = baseFee + buffer;

      // Set balance to exactly the base fee (insufficient for buffered fee)
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const excessBalance = currentBalance - baseFee;
      if (excessBalance > 0) {
        await stargateCompass.withdrawETH(excessBalance);
      }

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });

    it('should maintain minimum ETH balance during withdrawals', async function () {
      const minBalance = await stargateCompass.MIN_ETH_BALANCE();
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const maxWithdraw = currentBalance - minBalance;

      // Withdrawal within limits should work
      if (maxWithdraw > 0) {
        await expect(stargateCompass.withdrawETH(maxWithdraw)).to.not.be
          .reverted;
      }

      // Withdrawal that violates minimum should fail
      await expect(
        stargateCompass.withdrawETH(ethers.parseEther('0.001'))
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientReserve');
    });

    it('should handle ETH deposits correctly', async function () {
      const depositAmount = ethers.parseEther('0.5');
      const initialBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );

      await expect(stargateCompass.depositETH({ value: depositAmount }))
        .to.emit(stargateCompass, 'ETHDeposited')
        .withArgs(depositAmount, owner.address);

      const finalBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      expect(finalBalance).to.equal(initialBalance + depositAmount);
    });

    it('should reject zero ETH deposits', async function () {
      await expect(stargateCompass.depositETH({ value: 0 })).to.be.revertedWith(
        'Deposit amount must be greater than zero'
      );
    });
  });

  describe('M-4: Fee Volatility Protection and Buffering', function () {
    it('should apply 2% fee buffer for volatility protection', async function () {
      const baseFee = ethers.parseEther('0.01');
      const expectedBuffer = (baseFee * BigInt(200)) / BigInt(10000); // 2%
      const bufferedFee = baseFee + expectedBuffer;

      // Ensure contract has enough ETH for buffered fee
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      if (currentBalance < bufferedFee + ethers.parseEther('0.1')) {
        await owner.sendTransaction({
          to: stargateCompass.target,
          value: bufferedFee + ethers.parseEther('0.1') - currentBalance,
        });
      }

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should handle zero fee buffer calculation', async function () {
      const zeroFee = 0;
      await mockStargateRouter.setQuoteLayerZeroFee(zeroFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should protect against fee volatility during execution', async function () {
      // Test scenario where fee increases between quote and execution
      const baseFee = ethers.parseEther('0.01');
      const volatileFee = ethers.parseEther('0.012'); // 20% increase

      // Buffer should protect against this volatility
      const buffer = (baseFee * BigInt(200)) / BigInt(10000); // 2%
      const bufferedFee = baseFee + buffer;

      // Ensure we have enough ETH for the buffered fee
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      if (currentBalance < bufferedFee + ethers.parseEther('0.1')) {
        await owner.sendTransaction({
          to: stargateCompass.target,
          value: bufferedFee + ethers.parseEther('0.1') - currentBalance,
        });
      }

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should validate buffered fee against ETH balance', async function () {
      const baseFee = ethers.parseEther('0.05');
      const buffer = (baseFee * BigInt(200)) / BigInt(10000); // 2%
      const bufferedFee = baseFee + buffer;

      // Set balance to less than buffered fee
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const excessBalance =
        currentBalance - (bufferedFee - ethers.parseEther('0.001'));
      if (excessBalance > 0) {
        await stargateCompass.withdrawETH(excessBalance);
      }

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });

    it('should handle maximum fee with buffer correctly', async function () {
      const maxBaseFee = ethers.parseEther('0.1'); // At the limit
      const buffer = (maxBaseFee * BigInt(200)) / BigInt(10000); // 2%
      const bufferedFee = maxBaseFee + buffer;

      // Ensure we have enough ETH
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      if (currentBalance < bufferedFee + ethers.parseEther('0.1')) {
        await owner.sendTransaction({
          to: stargateCompass.target,
          value: bufferedFee + ethers.parseEther('0.1') - currentBalance,
        });
      }

      await mockStargateRouter.setQuoteLayerZeroFee(maxBaseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });

  describe('M-5: Amount Validation for All Operations', function () {
    it('should validate loan amount is greater than zero', async function () {
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          0, // Zero loan amount
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit'); // Fails profitability check
    });

    it('should validate minAmountOut is greater than zero', async function () {
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          0, // Zero minimum amount
          EXPECTED_PROFIT
        )
      ).to.be.revertedWith('minAmountOut must be greater than zero');
    });

    it('should validate expectedProfit is sufficient', async function () {
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          0 // Zero expected profit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
    });

    it('should validate withdrawal amounts', async function () {
      await expect(stargateCompass.withdrawETH(0)).to.be.revertedWith(
        'Withdrawal amount must be greater than zero'
      );
    });

    it('should validate deposit amounts', async function () {
      await expect(stargateCompass.depositETH({ value: 0 })).to.be.revertedWith(
        'Deposit amount must be greater than zero'
      );
    });

    it('should handle large amounts correctly', async function () {
      const largeAmount = ethers.parseUnits('1000000', 6); // 1M USDC
      const largeProfit = ethers.parseUnits('10000', 6); // 10K USDC profit

      // Should fail due to insufficient profit (needs to cover flash loan costs + minimum margin)
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          largeAmount,
          '0x1234',
          owner.address,
          ethers.parseUnits('980000', 6),
          largeProfit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
    });

    it('should validate amounts in slippage calculations', async function () {
      // Test that slippage calculations handle edge cases
      const smallAmount = ethers.parseUnits('1', 6); // 1 USDC
      const smallProfit = ethers.parseUnits('1', 6); // 1 USDC profit (insufficient)

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          smallAmount,
          '0x1234',
          owner.address,
          ethers.parseUnits('0.98', 6), // 2% slippage
          smallProfit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
    });

    it("should validate minimum amount doesn't exceed loan amount", async function () {
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // This should be caught in the _calculateFinalMinAmount function
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          LOAN_AMOUNT + BigInt(1), // minAmountOut > loanAmount
          EXPECTED_PROFIT
        )
      ).to.be.reverted; // Should fail validation
    });
  });

  describe('Comprehensive Parameter Validation Tests', function () {
    it('should validate all parameters in executeRemoteDegenSwap', async function () {
      const validParams = {
        loanAmount: LOAN_AMOUNT,
        remoteCalldata: '0x1234',
        remoteSwapRouter: owner.address,
        minAmountOut: ethers.parseUnits('980', 6),
        expectedProfit: EXPECTED_PROFIT,
      };

      // Test each parameter individually
      const invalidParams = [
        { ...validParams, loanAmount: 0 },
        { ...validParams, minAmountOut: 0 },
        { ...validParams, expectedProfit: 0 },
        { ...validParams, remoteSwapRouter: ethers.ZeroAddress },
      ];

      for (const params of invalidParams) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            params.loanAmount,
            params.remoteCalldata,
            params.remoteSwapRouter,
            params.minAmountOut,
            params.expectedProfit
          )
        ).to.be.reverted;
      }
    });

    it('should handle edge cases in parameter validation', async function () {
      // Test with maximum values
      const maxUint256 = ethers.MaxUint256;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          maxUint256,
          '0x1234',
          owner.address,
          maxUint256,
          maxUint256
        )
      ).to.be.reverted; // Should fail due to overflow or other constraints
    });

    it('should validate calldata size limits', async function () {
      // Test with very large calldata
      const largeCalldata = '0x' + '00'.repeat(10000); // 10KB of calldata

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          largeCalldata,
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit'); // May fail on profitability first
    });
  });
});
