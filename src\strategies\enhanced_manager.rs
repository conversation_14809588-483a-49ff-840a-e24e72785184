// Enhanced Strategy Manager with structured logging and error handling

use async_nats::Client as NatsClient;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tokio_stream::StreamExt;

use crate::error::enhanced::*;
use crate::logging::{TradingContext, ErrorCode, HeartbeatLogger, SystemHealthMetrics};
use crate::{log_error, log_warning, log_info};
use crate::shared_types::{
    MarketRegime, NatsTopics, Opportunity, OpportunityType, StrategyType,
    are_analysis::{AREAnalysisReport, AREDecision},
};
use crate::strategies::scoring::ScoringEngine;
use crate::data::price_oracle::PriceOracle;
use crate::execution::gas_estimator::GasEstimator;
use rust_decimal::prelude::*;
use rust_decimal_macros::dec;

pub struct EnhancedStrategyManager {
    nats_client: NatsClient,
    opportunity_rx: mpsc::Receiver<Opportunity>,
    latest_market_regime: Arc<Mutex<MarketRegime>>,
    
    // Enhanced logging and monitoring
    heartbeat_logger: Arc<HeartbeatLogger>,
    opportunities_scanned: Arc<Mutex<u64>>,
    trades_executed: Arc<Mutex<u64>>,
    
    // Core components
    scoring_engine: ScoringEngine,
    min_execution_score: Decimal,
    base_gas_estimator: Arc<GasEstimator>,
    degen_gas_estimator: Arc<GasEstimator>,
    price_oracle: Arc<PriceOracle>,
    min_net_profit_usd: Decimal,
    
    // Configuration
    autonomous_mode: bool,
    execution_request_tx: Option<mpsc::Sender<Opportunity>>,
}

impl EnhancedStrategyManager {
    pub fn new(
        nats_client: NatsClient,
        opportunity_rx: mpsc::Receiver<Opportunity>,
        strategy_config: crate::config::StrategiesConfig,
        scoring_config: crate::config::ScoringConfig,
        base_gas_estimator: Arc<GasEstimator>,
        degen_gas_estimator: Arc<GasEstimator>,
        price_oracle: Arc<PriceOracle>,
        execution_request_tx: Option<mpsc::Sender<Opportunity>>,
    ) -> StrategyResult<Self> {
        let context = TradingContext::new("EnhancedStrategyManager", "new");
        
        let scoring_engine = ScoringEngine::new(scoring_config.clone())
            .map_err(|e| ContextualError::new(
                EnhancedStrategyError::InsufficientProfit {
                    actual_usd: dec!(0.0),
                    minimum_usd: dec!(0.0),
                    strategy_type: "initialization".to_string(),
                },
                context.clone()
            ))?;
        
        let heartbeat_logger = Arc::new(HeartbeatLogger::new());
        
        log_info!(context, "Enhanced Strategy Manager initialized with structured logging");
        
        Ok(Self {
            nats_client,
            opportunity_rx,
            latest_market_regime: Arc::new(Mutex::new(MarketRegime::CalmOrderly)),
            heartbeat_logger,
            opportunities_scanned: Arc::new(Mutex::new(0)),
            trades_executed: Arc::new(Mutex::new(0)),
            scoring_engine,
            min_execution_score: strategy_config.min_execution_score,
            base_gas_estimator,
            degen_gas_estimator,
            price_oracle,
            min_net_profit_usd: dec!(50.0),
            autonomous_mode: true,
            execution_request_tx,
        })
    }
    
    pub async fn run(&mut self) -> StrategyResult<()> {
        let context = TradingContext::new("EnhancedStrategyManager", "run");
        
        log_info!(context, "Starting enhanced strategy manager with heartbeat monitoring");
        
        // Start heartbeat task
        let heartbeat_logger = self.heartbeat_logger.clone();
        let opportunities_scanned = self.opportunities_scanned.clone();
        let trades_executed = self.trades_executed.clone();
        let latest_regime = self.latest_market_regime.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
            loop {
                interval.tick().await;
                
                if heartbeat_logger.should_log_heartbeat(60).await {
                    let metrics = SystemHealthMetrics {
                        opportunities_scanned_last_minute: *opportunities_scanned.lock().await,
                        trades_executed_last_hour: *trades_executed.lock().await,
                        active_strategy: "zen_geometer".to_string(),
                        current_market_regime: format!("{:?}", *latest_regime.lock().await),
                        data_stream_status: HashMap::new(),
                        rpc_latency_ms: HashMap::new(),
                        memory_usage_mb: 0, // Would be populated with actual metrics
                        cpu_usage_percent: 0.0,
                        uptime_seconds: 0,
                    };
                    
                    heartbeat_logger.update_metrics(metrics).await;
                    heartbeat_logger.log_heartbeat().await;
                }
            }
        });
        
        // Main opportunity processing loop
        while let Some(opportunity) = self.opportunity_rx.recv().await {
            let mut context = TradingContext::new("EnhancedStrategyManager", "process_opportunity")
                .with_opportunity(&opportunity.base().id);
            
            // Update scan counter
            {
                let mut scanned = self.opportunities_scanned.lock().await;
                *scanned += 1;
            }
            
            // Add opportunity context
            context = context
                .with_chain(opportunity.base().chain_id)
                .with_profit(opportunity.base().estimated_gross_profit_usd)
                .with_strategy(&format!("{:?}", opportunity.strategy_type()));
            
            match self.evaluate_opportunity(&opportunity, &context).await {
                Ok(approved) => {
                    if approved {
                        log_info!(context, "Opportunity approved for execution");
                        
                        if let Some(ref tx) = self.execution_request_tx {
                            if let Err(e) = tx.send(opportunity).await {
                                log_error!(context, ErrorCode::ESystemOverloaded, 
                                    "Failed to send opportunity to execution: {}", e);
                            } else {
                                let mut executed = self.trades_executed.lock().await;
                                *executed += 1;
                            }
                        }
                    } else {
                        log_info!(context, "Opportunity rejected by scoring engine");
                    }
                }
                Err(e) => {
                    log_error!(context, e.error.error_code(), 
                        "Failed to evaluate opportunity: {}", e.error);
                }
            }
        }
        
        log_info!(context, "Strategy manager shutting down");
        Ok(())
    }
    
    async fn evaluate_opportunity(
        &self,
        opportunity: &Opportunity,
        context: &TradingContext,
    ) -> StrategyResult<bool> {
        // Check if opportunity is expired
        let age_seconds = chrono::Utc::now().timestamp() as u64 - opportunity.base().timestamp;
        if age_seconds > 300 { // 5 minutes max age
            return Err(ContextualError::new(
                EnhancedStrategyError::OpportunityExpired {
                    opportunity_id: opportunity.base().id.clone(),
                    age_seconds,
                    max_age_seconds: 300,
                },
                context.clone()
            ));
        }
        
        // Calculate cross-chain profitability
        let net_profit = match self.calculate_cross_chain_profit(opportunity, context).await {
            Ok(profit) => profit,
            Err(e) => {
                log_warning!(context, ErrorCode::EInsufficientProfit,
                    "Failed to calculate cross-chain profit: {}", e.error);
                return Ok(false);
            }
        };
        
        // Check minimum profit threshold
        if net_profit < self.min_net_profit_usd {
            return Err(ContextualError::new(
                EnhancedStrategyError::InsufficientProfit {
                    actual_usd: net_profit,
                    minimum_usd: self.min_net_profit_usd,
                    strategy_type: format!("{:?}", opportunity.strategy_type()),
                },
                context.clone()
            ));
        }
        
        // Score the opportunity
        let score = self.scoring_engine.score_opportunity(opportunity)
            .map_err(|e| ContextualError::new(
                EnhancedStrategyError::InsufficientProfit {
                    actual_usd: net_profit,
                    minimum_usd: self.min_execution_score,
                    strategy_type: "scoring".to_string(),
                },
                context.clone()
            ))?;
        
        let approved = score >= self.min_execution_score;
        
        if approved {
            log_info!(context, 
                "Opportunity approved - Score: {:.2}, Net profit: ${:.2}", 
                score, net_profit);
        } else {
            log_info!(context,
                "Opportunity rejected - Score: {:.2} < {:.2}, Net profit: ${:.2}",
                score, self.min_execution_score, net_profit);
        }
        
        Ok(approved)
    }
    
    async fn calculate_cross_chain_profit(
        &self,
        opportunity: &Opportunity,
        context: &TradingContext,
    ) -> ExecutionResult<Decimal> {
        let gross_profit = opportunity.base().estimated_gross_profit_usd;
        
        // Estimate gas costs on both chains
        let base_gas_cost = self.estimate_base_gas_cost(opportunity, context).await?;
        let degen_gas_cost = self.estimate_degen_gas_cost(opportunity, context).await?;
        
        // Calculate Stargate bridge fees (approximately $1-3)
        let bridge_fee = dec!(2.0);
        
        // Calculate flash loan fee (0.09% of loan amount)
        let flash_loan_fee = if opportunity.base().requires_flash_liquidity {
            gross_profit * dec!(0.0009) // 0.09%
        } else {
            dec!(0.0)
        };
        
        let total_costs = base_gas_cost + degen_gas_cost + bridge_fee + flash_loan_fee;
        let net_profit = gross_profit - total_costs;
        
        log_info!(context,
            "Profit calculation - Gross: ${:.2}, Costs: ${:.2}, Net: ${:.2}",
            gross_profit, total_costs, net_profit);
        
        Ok(net_profit)
    }
    
    async fn estimate_base_gas_cost(
        &self,
        opportunity: &Opportunity,
        context: &TradingContext,
    ) -> ExecutionResult<Decimal> {
        match self.base_gas_estimator.estimate_gas_cost(8453, "flash_loan_execution").await {
            Ok(cost) => Ok(cost),
            Err(e) => Err(ContextualError::new(
                EnhancedExecutionError::GasEstimationFailed {
                    function_name: "flash_loan_execution".to_string(),
                    reason: e.to_string(),
                },
                context.clone()
            ))
        }
    }
    
    async fn estimate_degen_gas_cost(
        &self,
        opportunity: &Opportunity,
        context: &TradingContext,
    ) -> ExecutionResult<Decimal> {
        match self.degen_gas_estimator.estimate_gas_cost(666666666, "dex_swap").await {
            Ok(cost) => Ok(cost),
            Err(e) => Err(ContextualError::new(
                EnhancedExecutionError::GasEstimationFailed {
                    function_name: "dex_swap".to_string(),
                    reason: e.to_string(),
                },
                context.clone()
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;
    
    #[tokio::test]
    async fn test_enhanced_strategy_manager_creation() {
        let (nats_client, _) = async_nats::connect("nats://localhost:4222").await
            .unwrap_or_else(|_| panic!("NATS not available for testing"));
        
        let (_, opportunity_rx) = mpsc::channel(100);
        let (execution_tx, _) = mpsc::channel(100);
        
        // Mock components would be created here in a real test
        // For now, this test just verifies the structure compiles
    }
}