# Patient Hunter Configuration

## Overview

The Patient Hunter configuration transforms the Zen Geometer from a generalist into a specialist hunter. It ignores the schools of tiny fish and patiently waits for larger, more valuable prey that provides a worthwhile reward for its sophisticated efforts.

## Core Philosophy

- **Quality Over Quantity**: Focus exclusively on high-quality, medium-frequency opportunities
- **Significant Profits**: Target net profit of over $15 per trade
- **Risk Aversion**: Conservative position sizing and risk management
- **Network Stability**: Execute only during periods of exceptional network calm

## Configuration Parameters

### Strategy Configuration

| Parameter | Value | Description |
|-----------|-------|-------------|
| `min_potential_profit_usd` | $50.0 | Minimum potential profit before costs |
| `min_net_profit_usd` | $15.0 | Minimum net profit after all costs |

### Aetheric Resonance Engine

| Parameter | Value | Description |
|-----------|-------|-------------|
| `min_resonance_score` | 7.5 | Minimum score to proceed to execution (scale 0-10) |
| `temporal_harmonics_weight` | 0.8 | Weight for temporal patterns (lower for Patient Hunter) |
| `geometric_score_weight` | 1.2 | Weight for geometric structure (higher for Patient Hunter) |
| `network_resonance_weight` | 1.5 | Weight for network stability (highest for Patient Hunter) |

### Risk Management

| Parameter | Value | Description |
|-----------|-------|-------------|
| `max_slippage` | 0.2% | Maximum allowed slippage |
| `kelly_fraction` | 0.15 | Conservative Kelly fraction for position sizing |

### Execution Parameters

| Parameter | Value | Description |
|-----------|-------|-------------|
| `tx_confirmation_timeout_seconds` | 120 | Longer timeout for transaction confirmation |

## Scanner Configurations

### GazeScanner

- **Minimum Price Deviation**: 0.3%
- **Minimum Profit Threshold**: $15.0

### LiquidationScanner

- **Minimum Liquidation Size**: $20,000
- **Minimum Profit Threshold**: $50.0

### PilotFishScanner

- **Minimum Whale Trade Size**: $250,000
- **Minimum Price Impact**: $1,000

## HarmonicTimingOracle

The Patient Hunter uses a specialized HarmonicTimingOracle with strict rules:

1. **Network Coherence**: Must be > 85% (highly synchronized network)
2. **Network Jitter**: Must be in the bottom 20% of recent values (exceptional calm)
3. **Gas Price**: Must not be more than 50% above the 1-hour moving average

## Expected Behavior

With the Patient Hunter configuration, the Zen Geometer will:

- Execute fewer trades overall
- Achieve higher average profit per trade
- Reduce exposure to market noise and MEV attacks
- Operate with greater certainty and lower risk
- Prioritize well-structured opportunities in stable market conditions

## When to Use

The Patient Hunter configuration is ideal for:

- Mature markets with established liquidity
- Periods of high gas costs
- Conservative trading strategies
- Long-term sustainable operations
- Environments where certainty is valued over speed

## Performance Monitoring

When using the Patient Hunter configuration, monitor these key metrics:

- Average profit per trade (should increase)
- Trade frequency (will decrease)
- Success rate (should increase)
- Gas costs as percentage of profit (should decrease)
- Time to execution (will increase)