// MISSION: Enhanced Nonce Manager with Escalation Policy
// WHY: Prevent nonce deadlocks and implement intelligent recovery mechanisms
// HOW: Escalating gas bumps, stuck transaction detection, emergency recovery

use ethers::{
    prelude::*,
    providers::{Http, Middleware, Provider},
    types::{Address, TransactionReceipt, transaction::eip2718::TypedTransaction},
    signers::{LocalWallet, Signer},
    middleware::SignerMiddleware,
};
use std::collections::HashMap;
use std::sync::Arc;
use thiserror::Error;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{Duration, Instant};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;
use crate::execution::broadcaster::Broadcaster;
use crate::execution::circuit_breaker::CircuitBreaker;

#[derive(Error, Debug)]
pub enum EnhancedNonceManagerError {
    #[error("Provider error: {0}")]
    ProviderError(#[from] ProviderError),
    
    #[error("Transaction stuck for too long: nonce {nonce}, hash {hash:?}, attempts {attempts}")]
    TransactionStuck { 
        nonce: U256, 
        hash: Option<TxHash>,
        attempts: u32,
    },
    
    #[error("Nonce gap detected: expected {expected}, got {actual}")]
    NonceGap { expected: U256, actual: U256 },
    
    #[error("Maximum pending transactions reached: {max}")]
    MaxPendingReached { max: usize },
    
    #[error("Signing error: {0}")]
    SigningError(String),
    
    #[error("Broadcaster error: {0}")]
    BroadcasterError(String),
    
    #[error("Nonce critically stuck: nonce {nonce}, attempts {attempts}")]
    NonceCriticallyStuck { nonce: U256, attempts: u32 },
    
    #[error("Emergency recovery required: {reason}")]
    EmergencyRecoveryRequired { reason: String },
}

#[derive(Debug, Clone)]
struct EnhancedPendingTransaction {
    hash: TxHash,
    nonce: U256,
    submitted_at: Instant,
    gas_price: U256,
    max_fee_per_gas: Option<U256>,
    max_priority_fee_per_gas: Option<U256>,
    retry_count: u32,
    escalation_level: u32,
    original_tx_request: TransactionRequest,
    last_replacement_at: Option<Instant>,
    is_critical: bool, // High-priority transactions that need aggressive recovery
}

impl EnhancedPendingTransaction {
    fn calculate_next_gas_bump(&self) -> f64 {
        match self.escalation_level {
            0 => 1.15, // 15% bump for first retry
            1 => 1.30, // 30% bump for second retry  
            2 => 1.50, // 50% bump for third retry
            3 => 2.00, // 100% bump for fourth retry
            _ => 2.50, // 150% bump for subsequent retries (emergency mode)
        }
    }
    
    fn is_stuck(&self, stuck_threshold: Duration) -> bool {
        self.submitted_at.elapsed() > stuck_threshold
    }
    
    fn is_critically_stuck(&self, critical_threshold: Duration) -> bool {
        self.submitted_at.elapsed() > critical_threshold || self.escalation_level >= 4
    }
    
    fn should_escalate(&self, escalation_interval: Duration) -> bool {
        match self.last_replacement_at {
            Some(last) => last.elapsed() > escalation_interval,
            None => self.submitted_at.elapsed() > escalation_interval,
        }
    }
}

pub struct EnhancedNonceManager {
    provider: Arc<Provider<Http>>,
    wallet: LocalWallet,
    broadcaster: Arc<Broadcaster>,
    circuit_breaker: Arc<CircuitBreaker>,
    
    // Nonce tracking
    current_nonce: Arc<Mutex<U256>>,
    pending_transactions: Arc<RwLock<HashMap<U256, EnhancedPendingTransaction>>>,
    
    // Configuration
    max_pending_transactions: usize,
    stuck_threshold: Duration,
    critical_threshold: Duration,
    escalation_interval: Duration,
    max_retry_attempts: u32,
    
    // Emergency handling
    emergency_mode: Arc<Mutex<bool>>,
    last_emergency_reset: Arc<Mutex<Option<Instant>>>,
}

impl EnhancedNonceManager {
    pub fn new(
        provider: Arc<Provider<Http>>,
        wallet: LocalWallet,
        broadcaster: Arc<Broadcaster>,
        circuit_breaker: Arc<CircuitBreaker>,
    ) -> Self {
        Self {
            provider,
            wallet,
            broadcaster,
            circuit_breaker,
            current_nonce: Arc::new(Mutex::new(U256::zero())),
            pending_transactions: Arc::new(RwLock::new(HashMap::new())),
            max_pending_transactions: 10,
            stuck_threshold: Duration::from_secs(300), // 5 minutes
            critical_threshold: Duration::from_secs(900), // 15 minutes
            escalation_interval: Duration::from_secs(120), // 2 minutes between escalations
            max_retry_attempts: 5,
            emergency_mode: Arc::new(Mutex::new(false)),
            last_emergency_reset: Arc::new(Mutex::new(None)),
        }
    }

    pub async fn initialize(&self) -> Result<(), EnhancedNonceManagerError> {
        let nonce = self.circuit_breaker.execute("nonce_fetch", || async {
            self.provider.get_transaction_count(self.wallet.address(), None).await
                .map_err(BasiliskError::from)
        }).await.map_err(|e| match e {
            BasiliskError::Rpc(msg) => EnhancedNonceManagerError::ProviderError(ProviderError::CustomError(msg.to_string())),
            _ => EnhancedNonceManagerError::ProviderError(ProviderError::CustomError("Circuit breaker error".to_string())),
        })?;
        
        *self.current_nonce.lock().await = nonce;
        info!("Enhanced nonce manager initialized with nonce: {}", nonce);
        Ok(())
    }

    pub async fn get_next_nonce(&self) -> Result<U256, EnhancedNonceManagerError> {
        // Check if we're in emergency mode
        if *self.emergency_mode.lock().await {
            return self.handle_emergency_nonce_recovery().await;
        }

        let pending = self.pending_transactions.read().await;
        if pending.len() >= self.max_pending_transactions {
            return Err(EnhancedNonceManagerError::MaxPendingReached { 
                max: self.max_pending_transactions 
            });
        }

        let mut current_nonce = self.current_nonce.lock().await;
        let nonce = *current_nonce;
        *current_nonce += U256::one();
        
        Ok(nonce)
    }

    pub async fn submit_transaction(
        &self,
        mut tx_request: TransactionRequest,
        is_critical: bool,
    ) -> Result<TxHash, EnhancedNonceManagerError> {
        let nonce = self.get_next_nonce().await?;
        tx_request.nonce = Some(nonce);

        // Submit transaction directly (simplified approach)
        let client = SignerMiddleware::new(self.provider.clone(), self.wallet.clone());
        let pending_tx = client.send_transaction(tx_request.clone(), None).await
            .map_err(|e| EnhancedNonceManagerError::BroadcasterError(e.to_string()))?;
        let tx_hash = *pending_tx;

        // Track the pending transaction
        let pending_tx = EnhancedPendingTransaction {
            hash: tx_hash,
            nonce,
            submitted_at: Instant::now(),
            gas_price: tx_request.gas_price.unwrap_or_default(),
            max_fee_per_gas: Some(tx_request.gas_price.unwrap_or_default()),
            max_priority_fee_per_gas: None,
            retry_count: 0,
            escalation_level: 0,
            original_tx_request: tx_request,
            last_replacement_at: None,
            is_critical,
        };

        self.pending_transactions.write().await.insert(nonce, pending_tx);
        
        info!("Transaction submitted with nonce {}: {:?}", nonce, tx_hash);
        Ok(tx_hash)
    }

    pub async fn monitor_and_recover(&self) -> Result<(), EnhancedNonceManagerError> {
        let mut pending = self.pending_transactions.write().await;
        let mut stuck_transactions = Vec::new();
        let mut critically_stuck = Vec::new();

        // Identify stuck transactions
        for (nonce, tx) in pending.iter() {
            if tx.is_critically_stuck(self.critical_threshold) {
                critically_stuck.push(*nonce);
            } else if tx.is_stuck(self.stuck_threshold) && tx.should_escalate(self.escalation_interval) {
                stuck_transactions.push(*nonce);
            }
        }

        // Handle critically stuck transactions first
        for nonce in critically_stuck {
            if let Some(tx) = pending.get(&nonce) {
                error!("Transaction critically stuck: nonce {}, attempts {}", nonce, tx.retry_count);
                
                if tx.retry_count >= self.max_retry_attempts {
                    // Trigger emergency mode
                    self.trigger_emergency_mode(format!(
                        "Transaction with nonce {} stuck after {} attempts", 
                        nonce, tx.retry_count
                    )).await;
                    
                    return Err(EnhancedNonceManagerError::NonceCriticallyStuck { 
                        nonce, 
                        attempts: tx.retry_count 
                    });
                }
            }
        }

        // Handle normally stuck transactions with escalation
        for nonce in stuck_transactions {
            if let Some(mut tx) = pending.remove(&nonce) {
                match self.escalate_transaction(&mut tx).await {
                    Ok(new_hash) => {
                        info!("Escalated transaction nonce {} to hash {:?}", nonce, new_hash);
                        tx.hash = new_hash;
                        tx.retry_count += 1;
                        tx.escalation_level += 1;
                        tx.last_replacement_at = Some(Instant::now());
                        pending.insert(nonce, tx);
                    }
                    Err(e) => {
                        error!("Failed to escalate transaction nonce {}: {}", nonce, e);
                        // Put it back for next attempt
                        pending.insert(nonce, tx);
                    }
                }
            }
        }

        Ok(())
    }

    async fn escalate_transaction(
        &self,
        tx: &mut EnhancedPendingTransaction,
    ) -> Result<TxHash, EnhancedNonceManagerError> {
        let gas_multiplier = tx.calculate_next_gas_bump();
        let mut new_tx = tx.original_tx_request.clone();
        
        // Apply escalated gas pricing
        if let Some(gas_price) = new_tx.gas_price {
            new_tx.gas_price = Some(U256::from((gas_price.as_u128() as f64 * gas_multiplier) as u128));
        }
        
        if let Some(gas_price) = new_tx.gas_price {
            new_tx.gas_price = Some(U256::from((gas_price.as_u128() as f64 * gas_multiplier) as u128));
        }

        // Set nonce
        new_tx.nonce = Some(tx.nonce);

        // Submit replacement transaction directly
        let client = SignerMiddleware::new(self.provider.clone(), self.wallet.clone());
        let pending_tx = client.send_transaction(new_tx.clone(), None).await
            .map_err(|e| EnhancedNonceManagerError::BroadcasterError(e.to_string()))?;
        let new_hash = *pending_tx;

        warn!(
            "Escalated transaction nonce {} (level {}) with {}x gas bump: {:?} -> {:?}",
            tx.nonce, tx.escalation_level + 1, gas_multiplier, tx.hash, new_hash
        );

        Ok(new_hash)
    }

    async fn trigger_emergency_mode(&self, reason: String) {
        *self.emergency_mode.lock().await = true;
        *self.last_emergency_reset.lock().await = Some(Instant::now());
        
        error!("EMERGENCY MODE ACTIVATED: {}", reason);
        
        // Notify emergency handler (would integrate with alerting system)
        // self.emergency_handler.notify(reason).await;
    }

    async fn handle_emergency_nonce_recovery(&self) -> Result<U256, EnhancedNonceManagerError> {
        warn!("Handling emergency nonce recovery");
        
        // Fetch fresh nonce from chain
        let chain_nonce = self.provider.get_transaction_count(self.wallet.address(), None).await
            .map_err(EnhancedNonceManagerError::ProviderError)?;
        
        // Clear all pending transactions
        self.pending_transactions.write().await.clear();
        
        // Reset current nonce
        *self.current_nonce.lock().await = chain_nonce;
        
        // Exit emergency mode
        *self.emergency_mode.lock().await = false;
        
        info!("Emergency nonce recovery completed. Reset to nonce: {}", chain_nonce);
        Ok(chain_nonce)
    }

    pub async fn confirm_transaction(&self, nonce: U256, receipt: TransactionReceipt) {
        let mut pending = self.pending_transactions.write().await;
        if let Some(tx) = pending.remove(&nonce) {
            info!(
                "Transaction confirmed: nonce {}, hash {:?}, gas used: {:?}",
                nonce, receipt.transaction_hash, receipt.gas_used
            );
        }
    }

    pub async fn get_pending_count(&self) -> usize {
        self.pending_transactions.read().await.len()
    }

    pub async fn is_emergency_mode(&self) -> bool {
        *self.emergency_mode.lock().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gas_bump_calculation() {
        let mut tx = EnhancedPendingTransaction {
            hash: TxHash::zero(),
            nonce: U256::zero(),
            submitted_at: Instant::now(),
            gas_price: U256::zero(),
            max_fee_per_gas: None,
            max_priority_fee_per_gas: None,
            retry_count: 0,
            escalation_level: 0,
            original_tx_request: TransactionRequest::default(),
            last_replacement_at: None,
            is_critical: false,
        };

        assert_eq!(tx.calculate_next_gas_bump(), 1.15);
        
        tx.escalation_level = 1;
        assert_eq!(tx.calculate_next_gas_bump(), 1.30);
        
        tx.escalation_level = 4;
        assert_eq!(tx.calculate_next_gas_bump(), 2.50);
    }
}