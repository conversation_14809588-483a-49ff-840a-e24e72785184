# Stargate Compass Integration Test Suite

## Overview

The Stargate Compass Integration Test Suite provides comprehensive testing of the Basilisk Bot's interaction with the newly audited StargateCompassV1 smart contract. This test suite validates that all backend services and TUI components can successfully interact with the contract on a live testnet environment, ensuring production readiness.

## Quick Start

### Prerequisites

1. **Anvil Testnet**: Running Anvil with Base fork
2. **Contract Deployment**: StargateCompassV1 contract deployed on Anvil
3. **Environment Setup**: Proper configuration files and environment variables
4. **Dependencies**: All Rust dependencies installed

### Running the Tests

```bash
# Run all integration tests
cargo test --test stargate_compass_integration

# Run with verbose output
cargo test --test stargate_compass_integration -- --verbose

# Run specific test phases
cargo test --test stargate_compass_integration -- --skip-tui
cargo test --test stargate_compass_integration -- --skip-backend
cargo test --test stargate_compass_integration -- --skip-workflow

# Run with custom configuration
cargo test --test stargate_compass_integration -- \
  --anvil-url http://localhost:8545 \
  --contract-address ****************************************** \
  --timeout 600 \
  --retry-attempts 5
```

## Test Architecture

### Test Phases

The integration test suite executes in the following phases:

1. **Configuration Management** - Updates bot configuration with new contract address
2. **Backend Integration Testing** - Validates ExecutionManager and ExecutionDispatcher
3. **TUI Functionality Testing** - Verifies all TUI commands work correctly
4. **End-to-End Workflow Testing** - Validates complete data pipeline
5. **Test Reporting** - Generates comprehensive test reports

### Key Components

- **IntegrationTestController** - Main orchestrator for all test phases
- **ConfigurationManager** - Handles dynamic configuration updates
- **BackendIntegrationTester** - Tests backend contract interactions
- **TuiFunctionalityTester** - Validates TUI command functionality
- **TestReporter** - Generates detailed test reports and diagnostics

## Environment Setup

### 1. Start Anvil Testnet

```bash
# Start Anvil with Base fork
anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545

# Alternative: Use specific block number for consistency
anvil --fork-url https://mainnet.base.org --fork-block-number 12345678 --chain-id 8453 --port 8545
```

### 2. Deploy StargateCompassV1 Contract

```bash
# Navigate to contract directory
cd geometer-contracts

# Deploy to Anvil testnet
npm run deploy:anvil

# Note the deployed contract address for test configuration
```

### 3. Configure Test Environment

Create or update configuration files:

```toml
# config/local.toml
[contracts]
stargate_compass_v1 = "******************************************"  # Your deployed address

[network]
rpc_url = "http://localhost:8545"
chain_id = 8453

[testing]
anvil_url = "http://localhost:8545"
test_timeout_seconds = 300
retry_attempts = 3
```

### 4. Fund Test Accounts

```bash
# Fund accounts with ETH for gas
cast send --rpc-url http://localhost:8545 --private-key 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80 \
  0xYourTestAddress --value 10ether

# Fund with USDC for trading tests
cast send --rpc-url http://localhost:8545 --private-key 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80 \
  0xUSDCAddress "transfer(address,uint256)" 0xYourTestAddress **********  # 1000 USDC
```

## Test Configuration Options

### Command Line Arguments

| Argument             | Description                        | Default                 |
| -------------------- | ---------------------------------- | ----------------------- |
| `--anvil-url`        | Anvil testnet URL                  | `http://localhost:8545` |
| `--contract-address` | StargateCompassV1 contract address | Required                |
| `--timeout`          | Test timeout in seconds            | `300`                   |
| `--retry-attempts`   | Number of retry attempts           | `3`                     |
| `--parallel`         | Enable parallel test execution     | `false`                 |
| `--verbose`          | Enable verbose logging             | `false`                 |
| `--no-report`        | Skip saving test report            | `false`                 |
| `--report-path`      | Directory for test reports         | `test_reports`          |
| `--skip-tui`         | Skip TUI functionality tests       | `false`                 |
| `--skip-backend`     | Skip backend integration tests     | `false`                 |
| `--skip-workflow`    | Skip end-to-end workflow tests     | `false`                 |

### Environment Variables

```bash
# Optional environment variables
export ANVIL_URL="http://localhost:8545"
export STARGATE_COMPASS_ADDRESS="******************************************"
export TEST_TIMEOUT="600"
export RUST_LOG="debug"  # For detailed logging
```

## Test Components

### 1. Configuration Management Tests

**Purpose**: Verify configuration updates work correctly

**Tests**:

- Configuration file discovery and parsing
- Contract address updates with validation
- Configuration backup and rollback functionality
- Configuration syntax validation

**Expected Results**:

- ✅ Configuration files located and parsed successfully
- ✅ Contract addresses updated in all relevant files
- ✅ Configuration validation passes
- ✅ Backup files created for rollback

### 2. Backend Integration Tests

**Purpose**: Validate ExecutionManager and ExecutionDispatcher contract interactions

**Tests**:

- ExecutionManager opportunity processing
- ExecutionDispatcher transaction creation
- Contract method calls and response validation
- Gas estimation and transaction broadcasting
- Error handling for failed transactions

**Expected Results**:

- ✅ Trading opportunities processed successfully
- ✅ Transactions created and broadcast correctly
- ✅ Contract calls return expected values
- ✅ Error scenarios handled gracefully

### 3. TUI Functionality Tests

**Purpose**: Verify TUI commands interact correctly with the contract

**Tests**:

- Emergency stop command execution
- Pause/restart bot commands
- Balance query commands
- Contract status queries
- Transaction initiation commands
- Data validation against on-chain state

**Expected Results**:

- ✅ All TUI commands execute without errors
- ✅ Displayed data matches on-chain state
- ✅ Transaction commands succeed on testnet
- ✅ Error messages display correctly

### 4. End-to-End Workflow Tests

**Purpose**: Validate complete data pipeline from decision to execution

**Tests**:

- Complete arbitrage opportunity simulation
- Data flow from StrategyManager to ExecutionManager to TUI
- Profit/loss calculation verification
- System coherence across all components

**Expected Results**:

- ✅ Complete workflow executes successfully
- ✅ Data flows correctly between components
- ✅ TUI displays accurate execution results
- ✅ System maintains coherence throughout

## Test Reports

### Report Generation

Test reports are automatically generated and saved to the `test_reports` directory:

```
test_reports/
├── stargate_compass_integration_report_20240117_143022.json
├── stargate_compass_integration_report_20240117_143022.html
└── latest_report.json -> stargate_compass_integration_report_20240117_143022.json
```

### Report Structure

```json
{
  "session_id": "stargate_compass_1705493422",
  "start_time": "2024-01-17T14:30:22Z",
  "end_time": "2024-01-17T14:35:45Z",
  "total_duration": "5m23s",
  "overall_success": true,
  "total_tests": 47,
  "total_passed": 45,
  "success_rate": 0.957,
  "configuration_passed": true,
  "backend_passed": true,
  "tui_passed": true,
  "workflow_passed": true,
  "errors": [],
  "warnings": ["Test execution took longer than expected"],
  "recommendations": [
    "✅ All integration tests passed - system is ready for production deployment"
  ]
}
```

### Report Interpretation

- **Overall Success**: `true` indicates all critical tests passed
- **Success Rate**: Percentage of individual tests that passed
- **Component Results**: Individual results for each test phase
- **Errors**: Critical issues that must be addressed
- **Warnings**: Non-critical issues that should be reviewed
- **Recommendations**: Actionable next steps based on results

## Troubleshooting

### Common Issues

#### 1. Anvil Connection Failures

**Symptoms**:

```
Error: Failed to connect to Anvil at http://localhost:8545
```

**Solutions**:

- Verify Anvil is running: `curl http://localhost:8545`
- Check port availability: `netstat -an | grep 8545`
- Restart Anvil with correct parameters
- Verify firewall settings

#### 2. Contract Address Not Found

**Symptoms**:

```
Error: Contract not found at address 0x1234...
```

**Solutions**:

- Verify contract deployment: `cast code --rpc-url http://localhost:8545 0x1234...`
- Check contract address in configuration files
- Redeploy contract if necessary
- Verify network and chain ID match

#### 3. Configuration Update Failures

**Symptoms**:

```
Error: Failed to update configuration file config/local.toml
```

**Solutions**:

- Check file permissions: `ls -la config/local.toml`
- Verify file format: `toml check config/local.toml`
- Ensure backup directory exists and is writable
- Check for file locks or concurrent access

#### 4. TUI Command Execution Timeouts

**Symptoms**:

```
Error: TUI command 'query_balances' timed out after 30s
```

**Solutions**:

- Increase timeout: `--timeout 600`
- Check TUI binary compilation: `cargo build --bin tui_harness`
- Verify TUI can connect to Anvil independently
- Check system resource usage

#### 5. Backend Integration Test Failures

**Symptoms**:

```
Error: ExecutionManager failed to process opportunity
```

**Solutions**:

- Check contract ABI compatibility
- Verify gas estimation settings
- Ensure sufficient test account balances
- Review contract state and permissions

#### 6. Data Validation Mismatches

**Symptoms**:

```
Error: Balance mismatch - TUI: 1000.00 USDC, On-chain: 999.99 USDC
```

**Solutions**:

- Adjust balance tolerance in validation config
- Check for pending transactions
- Verify token contract addresses
- Review decimal precision settings

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
# Set environment variable
export RUST_LOG=debug

# Run tests with verbose output
cargo test --test stargate_compass_integration -- --verbose

# Check specific component logs
export RUST_LOG=stargate_compass_integration=debug,tui_functionality_tester=trace
```

### Log Analysis

Key log patterns to look for:

```
✅ Configuration management tests passed
❌ Backend integration tests failed: ExecutionManager error
⚠️  TUI command took longer than expected: 45s
🔍 Validating balance display: expected 1000.00, got 999.99
📊 Test completed: 45/47 tests passed (95.7% success rate)
```

## Performance Considerations

### Test Execution Time

- **Typical Duration**: 3-8 minutes for full suite
- **Configuration Phase**: 10-30 seconds
- **Backend Tests**: 1-3 minutes
- **TUI Tests**: 2-4 minutes
- **Workflow Tests**: 1-2 minutes

### Resource Usage

- **Memory**: ~100-200 MB during execution
- **CPU**: Moderate usage during TUI command execution
- **Network**: Frequent RPC calls to Anvil testnet
- **Disk**: Minimal, mainly for report generation

### Optimization Tips

1. **Parallel Execution**: Use `--parallel` for independent tests
2. **Selective Testing**: Skip phases with `--skip-*` flags
3. **Timeout Tuning**: Adjust `--timeout` based on system performance
4. **Resource Monitoring**: Monitor system resources during execution

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: Stargate Compass Integration Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  integration-tests:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Start Anvil
        run: |
          anvil --fork-url ${{ secrets.BASE_RPC_URL }} --chain-id 8453 --port 8545 &
          sleep 5

      - name: Deploy Test Contract
        run: |
          cd geometer-contracts
          npm install
          npm run deploy:anvil

      - name: Run Integration Tests
        run: |
          cargo test --test stargate_compass_integration -- \
            --contract-address ${{ env.CONTRACT_ADDRESS }} \
            --timeout 600 \
            --no-report

      - name: Upload Test Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-reports
          path: test_reports/
```

## Best Practices

### 1. Test Environment Isolation

- Use dedicated Anvil instance for testing
- Reset blockchain state between test runs
- Use deterministic test data and addresses

### 2. Configuration Management

- Keep test configurations separate from production
- Use environment-specific configuration files
- Validate configuration before test execution

### 3. Error Handling

- Implement comprehensive error logging
- Use retry logic for transient failures
- Provide clear error messages and remediation steps

### 4. Test Data Management

- Use realistic but deterministic test scenarios
- Maintain consistent test account balances
- Document test data requirements

### 5. Continuous Integration

- Run integration tests on every significant change
- Set up automated test result notifications
- Maintain test environment consistency

## Support and Maintenance

### Regular Maintenance Tasks

1. **Update Test Data**: Refresh test scenarios monthly
2. **Review Test Coverage**: Ensure new features are tested
3. **Performance Monitoring**: Track test execution times
4. **Environment Updates**: Keep Anvil and dependencies current

### Getting Help

- **Documentation**: Check this README and component-specific docs
- **Logs**: Review detailed logs with `RUST_LOG=debug`
- **Issues**: Create GitHub issues for persistent problems
- **Team**: Contact the Basilisk Bot development team

### Contributing

When adding new integration tests:

1. Follow existing test patterns and naming conventions
2. Add comprehensive error handling and logging
3. Update this documentation with new test descriptions
4. Ensure tests are deterministic and isolated
5. Add appropriate timeout and retry logic

## Conclusion

The Stargate Compass Integration Test Suite provides comprehensive validation of the Basilisk Bot's interaction with the StargateCompassV1 contract. By following this guide, you can successfully run integration tests, interpret results, and troubleshoot common issues.

For production deployment, ensure all integration tests pass consistently before proceeding with mainnet operations.
