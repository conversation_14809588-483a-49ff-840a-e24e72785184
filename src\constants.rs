// Basilisk Bot - Central Constants Module
// This module defines all NATS topic strings and other constants to prevent typos
// and ensure consistency across the application.

/// NATS Topic Constants for the Basilisk Bot
///
/// These topics follow a hierarchical naming convention:
/// - `data.*` - Raw data ingestion topics
/// - `state.*` - Processed state information topics  
/// - `opportunities.*` - Trading opportunity topics
/// - `execution.*` - Trade execution topics
/// - `control.*` - System control and monitoring topics
pub struct NatsTopics;

impl NatsTopics {
    // === DATA INGESTION TOPICS ===

    /// Raw blockchain data topics
    pub const DATA_CHAIN_BLOCKS: &'static str = "data.chain.blocks";
    pub const DATA_CHAIN_LOGS: &'static str = "data.chain.logs";
    pub const DATA_CHAIN_MEMPOOL: &'static str = "data.chain.mempool";

    /// Chain-specific block data: data.chain.blocks.{chain_id}
    pub fn chain_blocks_for(chain_id: u64) -> String {
        format!("data.chain.blocks.{}", chain_id)
    }

    /// Chain-specific event logs: data.chain.events.{chain_id}
    pub fn chain_events_for(chain_id: u64) -> String {
        format!("data.chain.events.{}", chain_id)
    }

    /// Chain-specific mempool data: data.chain.mempool.{chain_id}
    pub fn chain_mempool_for(chain_id: u64) -> String {
        format!("data.chain.mempool.{}", chain_id)
    }

    /// CEX data topics
    pub const DATA_CEX_TRADES: &'static str = "data.cex.trades";
    pub const DATA_CEX_ORDERBOOK: &'static str = "data.cex.orderbook";

    /// Processed swap events from DEXes
    pub const DATA_CHAIN_LOGS_PROCESSED_SWAPS: &'static str = "data.chain.logs.processed.swaps";
    pub const DATA_CHAIN_BLOCKS_PROCESSED: &'static str = "data.chain.blocks.processed";
    
    /// Whale trade events for Pilot Fish scanner
    pub const DATA_CHAIN_WHALE_TRADES: &'static str = "data.chain.whale_trades";

    /// NFT marketplace data
    pub const DATA_NFT_LISTINGS: &'static str = "data.nft.listings";
    pub const DATA_NFT_FLOOR_PRICES: &'static str = "data.nft.floor_prices";

    /// Classified mempool transactions
    pub const DATA_MEMPOOL_TXS_CLASSIFIED: &'static str = "data.mempool.txs.classified";

    // === STATE TOPICS ===

    /// Market state and regime classification
    pub const STATE_MARKET_REGIME: &'static str = "state.market.regime";
    pub const STATE_MARKET_CHARACTER: &'static str = "state.market.character";
    pub const STATE_MARKET_VOLATILITY: &'static str = "state.market.volatility";

    /// Graph analysis and centrality scores
    pub const STATE_GRAPH_CENTRALITY: &'static str = "state.graph.centrality";
    pub const STATE_GRAPH_LIQUIDITY: &'static str = "state.graph.liquidity";

    /// Lending protocol health data
    pub const STATE_LENDING_LOAN_HEALTH: &'static str = "state.lending.loan_health";

    /// Fractal analysis results from Chronos Sieve
    pub const STATE_FRACTAL_ANALYSIS: &'static str = "state.fractal.analysis";
    
    /// Temporal harmonics from Chronos Sieve
    pub const STATE_MARKET_HARMONICS: &'static str = "state.market.harmonics";
    
    /// Network resonance state from Network Seismology
    pub const STATE_NETWORK_RESONANCE: &'static str = "state.network.resonance";

    // === OPPORTUNITY TOPICS ===

    /// Raw opportunities from scanners
    pub const OPPORTUNITIES_RAW: &'static str = "opportunities.raw";
    pub const OPPORTUNITIES_SCORED: &'static str = "opportunities.scored";
    pub const OPPORTUNITIES_FILTERED: &'static str = "opportunities.filtered";

    /// Specific opportunity types
    pub const OPPORTUNITIES_DEX_ARBITRAGE: &'static str = "opportunities.dex.arbitrage";
    pub const OPPORTUNITIES_NFT_ARBITRAGE: &'static str = "opportunities.nft.arbitrage";
    pub const OPPORTUNITIES_LIQUIDATION: &'static str = "opportunities.liquidation";
    pub const OPPORTUNITIES_PILOT_FISH: &'static str = "opportunities.pilot_fish";

    /// Scanner-specific topics
    pub const OPPORTUNITIES_GAZE_SCANNER: &'static str = "opportunities.gaze_scanner";
    pub const OPPORTUNITIES_SWAP_SCANNER: &'static str = "opportunities.swap_scanner";
    pub const OPPORTUNITIES_MEMPOOL_SCANNER: &'static str = "opportunities.mempool_scanner";
    pub const OPPORTUNITIES_NFT_SCANNER: &'static str = "opportunities.nft_scanner";
    pub const OPPORTUNITIES_LIQUIDATION_SCANNER: &'static str = "opportunities.liquidation_scanner";

    // === EXECUTION TOPICS ===

    /// Execution requests and results
    pub const EXECUTION_REQUEST: &'static str = "execution.request";
    pub const EXECUTION_RESULT: &'static str = "execution.result";
    pub const EXECUTION_STATUS: &'static str = "execution.status";

    /// Transaction lifecycle
    pub const EXECUTION_TX_SUBMITTED: &'static str = "execution.tx.submitted";
    pub const EXECUTION_TX_CONFIRMED: &'static str = "execution.tx.confirmed";
    pub const EXECUTION_TX_FAILED: &'static str = "execution.tx.failed";

    /// MEV-specific execution
    pub const EXECUTION_MEV_BUNDLE: &'static str = "execution.mev.bundle";
    pub const EXECUTION_MEV_RESULT: &'static str = "execution.mev.result";

    // === CONTROL TOPICS ===

    /// System control and monitoring
    pub const CONTROL_PAUSE_TRADING: &'static str = "control.pause_trading";
    pub const CONTROL_RESUME_TRADING: &'static str = "control.resume_trading";
    pub const CONTROL_SHUTDOWN: &'static str = "control.shutdown";
    pub const CONTROL_HEALTH_CHECK: &'static str = "control.health_check";

    /// Service status updates
    pub const CONTROL_SERVICE_STATUS: &'static str = "control.service.status";
    pub const CONTROL_SERVICE_METRICS: &'static str = "control.service.metrics";

    /// Risk management alerts
    pub const CONTROL_RISK_ALERT: &'static str = "control.risk.alert";
    pub const CONTROL_RISK_BREACH: &'static str = "control.risk.breach";

    /// SIGINT workflow topics
    pub const CONTROL_SIGINT_DIRECTIVE: &'static str = "control.sigint.directive";
    pub const CONTROL_SIGINT_REPORT: &'static str = "control.sigint.report";
    
    /// Narrative event logs for educational purposes
    pub const LOG_EVENTS_NARRATIVE: &'static str = "log.events.narrative";
    
    // === LIVING CODEX EDUCATIONAL SYSTEM TOPICS ===
    
    /// ARE Analysis for the Resonance Chamber
    pub const LIVING_CODEX_ARE_ANALYSIS: &'static str = "living_codex.are.analysis";
    
    /// Trade lifecycle events with educational context
    pub const LIVING_CODEX_TRADE_LIFECYCLE: &'static str = "living_codex.trade.lifecycle";
    
    /// Knowledge base queries and responses
    pub const LIVING_CODEX_KNOWLEDGE_QUERY: &'static str = "living_codex.knowledge.query";
    pub const LIVING_CODEX_KNOWLEDGE_RESPONSE: &'static str = "living_codex.knowledge.response";

    // === MIGRATION TOPICS (NOMADIC HUNTER) ===

    /// Chain migration and nomadic hunting
    pub const MIGRATION_CHAIN_SWITCH: &'static str = "migration.chain.switch";
    pub const MIGRATION_LIQUIDITY_SCAN: &'static str = "migration.liquidity.scan";
    pub const MIGRATION_BRIDGE_STATUS: &'static str = "migration.bridge.status";

    // === MISSING CONSTANTS FROM AUDIT ===
    
    /// Critical alert topics
    pub const ALERTS_CRITICAL_CIRCUIT_BREAKER: &'static str = "alerts.CRITICAL.circuit_breaker";
    pub const ALERTS_EMERGENCY_OPERATOR: &'static str = "alerts.EMERGENCY.operator";
    pub const ALERTS_NETWORK_REORG: &'static str = "alerts.network.reorg";
    
    /// System state topics
    pub const STATE_BALANCES: &'static str = "state.balances";
    pub const STATE_TREASURY: &'static str = "state.treasury";
    pub const STATE_MIGRATION_STATUS: &'static str = "state.migration.status";
    pub const STATE_SYSTEM_STATUS: &'static str = "state.system.status";
    
    /// Control topics
    pub const CONTROL_SYSTEM_SET_MODE: &'static str = "control.system.set_mode";
    pub const ZEN_GEOMETER_CONTROL_SET_ACTIVE_CHAIN: &'static str = "zen_geometer.control.set_active_chain";
    
    /// Logging topics
    pub const LOG_OPPORTUNITIES_DEGEN: &'static str = "log.opportunities.degen";
    pub const LOG_ANALYSIS_CROSS_CHAIN: &'static str = "log.analysis.cross_chain";
    pub const LOG_EVENTS_OROBOROS: &'static str = "log.events.oroboros";
    pub const INTELLIGENCE_ECOSYSTEM_HEALTH: &'static str = "intelligence.ecosystem.health";
    
    /// Data topics
    pub const DATA_OPPORTUNITIES_DETECTED: &'static str = "data.opportunities.detected";
    pub const EXECUTION_TRADE_COMPLETED: &'static str = "execution.trade.completed";
    pub const NETWORK_SEISMOLOGY_UPDATE: &'static str = "network.seismology.update";
    pub const LOGS_SYSTEM: &'static str = "logs.system";
}

/// Mathematical Constants for the Aetheric Resonance Engine
pub struct MathConstants;

impl MathConstants {
    /// Golden Ratio (φ) - Used in bidding strategies and geometric analysis
    pub const GOLDEN_RATIO: f64 = 1.618033988749895;

    /// Golden Ratio Conjugate (1/φ) - Used for position sizing
    pub const GOLDEN_RATIO_CONJUGATE: f64 = 0.618033988749895;

    /// Sacred Geometry: Vesica Piscis ratio for Mandorla Gauge
    pub const VESICA_PISCIS_RATIO: f64 = 1.732050807568877; // √3

    /// Fibonacci sequence ratios for fractal analysis
    pub const FIBONACCI_RATIOS: [f64; 5] = [
        0.236, // 23.6%
        0.382, // 38.2% (Golden ratio conjugate)
        0.500, // 50%
        0.618, // 61.8% (Golden ratio conjugate)
        0.786, // 78.6%
    ];

    /// Default Kelly Criterion fraction for position sizing
    pub const DEFAULT_KELLY_FRACTION: f64 = 0.25; // Conservative 25%

    /// Maximum Kelly Criterion fraction (risk management)
    pub const MAX_KELLY_FRACTION: f64 = 0.5; // Never risk more than 50%
}

/// Gas and Transaction Constants
pub struct GasConstants;

impl GasConstants {
    /// Standard gas limits for different transaction types
    pub const SIMPLE_TRANSFER: u64 = 21_000;
    pub const ERC20_TRANSFER: u64 = 65_000;
    pub const UNISWAP_V2_SWAP: u64 = 150_000;
    pub const UNISWAP_V3_SWAP: u64 = 180_000;
    pub const FLASH_LOAN_ARBITRAGE: u64 = 500_000;
    pub const NFT_PURCHASE: u64 = 200_000;
    pub const LIQUIDATION: u64 = 300_000;

    /// Gas price thresholds (in gwei)
    pub const LOW_GAS_THRESHOLD: u64 = 10;
    pub const MEDIUM_GAS_THRESHOLD: u64 = 30;
    pub const HIGH_GAS_THRESHOLD: u64 = 100;
    pub const EXTREME_GAS_THRESHOLD: u64 = 500;

    /// Priority fee multipliers for different urgency levels
    pub const PRIORITY_FEE_MULTIPLIER_LOW: f64 = 1.1;
    pub const PRIORITY_FEE_MULTIPLIER_MEDIUM: f64 = 1.25;
    pub const PRIORITY_FEE_MULTIPLIER_HIGH: f64 = 1.5;
    pub const PRIORITY_FEE_MULTIPLIER_URGENT: f64 = 2.0;
}

/// Time Constants for various operations
pub struct TimeConstants;

impl TimeConstants {
    /// Block time estimates for different chains (in seconds)
    pub const BASE_BLOCK_TIME: u64 = 2;
    pub const ARBITRUM_BLOCK_TIME: u64 = 1;
    pub const POLYGON_BLOCK_TIME: u64 = 2;
    pub const ETHEREUM_BLOCK_TIME: u64 = 12;

    /// Timeout constants (in seconds)
    pub const RPC_TIMEOUT: u64 = 10;
    pub const WEBSOCKET_TIMEOUT: u64 = 30;
    pub const EXECUTION_TIMEOUT: u64 = 60;
    pub const HEALTH_CHECK_INTERVAL: u64 = 30;

    /// Fractal analysis windows (in blocks)
    pub const FRACTAL_SHORT_WINDOW: usize = 100; // ~3.3 minutes on Base
    pub const FRACTAL_MEDIUM_WINDOW: usize = 1800; // ~1 hour on Base
    pub const FRACTAL_LONG_WINDOW: usize = 43200; // ~24 hours on Base

    /// Graph analysis update intervals (in seconds)
    pub const GRAPH_UPDATE_INTERVAL: u64 = 300; // 5 minutes
    pub const CENTRALITY_RECALC_INTERVAL: u64 = 1800; // 30 minutes
}

/// Precision Constants for decimal calculations
pub struct PrecisionConstants;

impl PrecisionConstants {
    /// Decimal places for different value types
    pub const PRICE_PRECISION: u32 = 18;
    pub const PERCENTAGE_PRECISION: u32 = 4;
    pub const USD_PRECISION: u32 = 2;
    pub const RATIO_PRECISION: u32 = 6;

    /// Minimum values for various calculations
    pub const MIN_PROFIT_USD: f64 = 0.01;
    pub const MIN_SLIPPAGE: f64 = 0.0001; // 0.01%
    pub const MIN_POSITION_SIZE: f64 = 0.001; // 0.001 ETH

    /// Maximum values for safety
    pub const MAX_SLIPPAGE: f64 = 0.1; // 10%
    pub const MAX_POSITION_SIZE_ETH: f64 = 100.0;
    pub const MAX_GAS_PRICE_GWEI: f64 = 1000.0;
}
