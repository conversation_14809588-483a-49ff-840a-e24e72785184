#![no_main]

use libfuzzer_sys::fuzz_target;
use basilisk_bot::shared_types::*;

fuzz_target!(|data: &[u8]| {
    // Test JSON parsing of various opportunity types
    if let Ok(s) = std::str::from_utf8(data) {
        // Try to parse as different opportunity types
        let _: Result<OpportunityMetric, _> = serde_json::from_str(s);
        let _: Result<MarketState, _> = serde_json::from_str(s);
        let _: Result<SigintDirective, _> = serde_json::from_str(s);
        let _: Result<ExecutionRequest, _> = serde_json::from_str(s);
        
        // Test configuration parsing
        if s.len() < 10000 { // Limit size to prevent excessive memory usage
            let _: Result<toml::Value, _> = toml::from_str(s);
        }
    }
    
    // Test binary data parsing for potential buffer overflows
    if data.len() >= 4 {
        let _value = u32::from_le_bytes([data[0], data[1], data[2], data[3]]);
    }
    
    // Test decimal parsing which is critical for financial calculations
    if let Ok(s) = std::str::from_utf8(data) {
        if s.len() < 100 { // Reasonable limit for decimal strings
            let _: Result<rust_decimal::Decimal, _> = s.parse();
        }
    }
});
