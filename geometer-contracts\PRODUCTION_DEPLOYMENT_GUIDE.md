# StargateCompassV1 Production Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the security-hardened StargateCompassV1 contract to production. The contract has been thoroughly tested and all security vulnerabilities have been resolved.

## Pre-Deployment Requirements

### Environment Setup

1. **Node.js and Dependencies**

   ```bash
   node --version  # Ensure v18+
   npm install
   ```

2. **Environment Variables**

   ```bash
   cp .env.example .env
   # Configure the following variables:
   PRIVATE_KEY=your_deployment_private_key
   INFURA_API_KEY=your_infura_api_key
   ETHERSCAN_API_KEY=your_etherscan_api_key
   ```

3. **Network Configuration**
   - Base Mainnet RPC URL
   - Sufficient ETH for deployment gas costs
   - Verified contract addresses for dependencies

### Required Contract Addresses

#### Base Mainnet Addresses

```javascript
const PRODUCTION_ADDRESSES = {
  // Aave V3 on Base
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',

  // Stargate on Base
  STARGATE_ROUTER: '******************************************',

  // USDC on Base
  USDC_ADDRESS: '******************************************',
};
```

## Pre-Deployment Validation

### 1. Contract Compilation

```bash
npx hardhat compile
```

**Expected Output**: Clean compilation with no errors or warnings.

### 2. Test Suite Execution

```bash
# Run complete test suite
npx hardhat test

# Run specific security tests
npx hardhat test test/StargateCompassV1.SecurityValidation.test.js
npx hardhat test test/StargateCompassV1.ProductionReadiness.test.js
```

**Expected Results**:

- All tests passing (90+ test cases)
- No security vulnerabilities detected
- Gas usage within acceptable limits

### 3. Security Checklist Verification

- ✅ All 13 audit findings resolved
- ✅ Comprehensive test coverage implemented
- ✅ Gas optimization completed
- ✅ Access controls validated
- ✅ Emergency procedures tested
- ✅ Event monitoring verified

## Deployment Process

### Step 1: Deploy Contract

```bash
# Deploy to Base mainnet
npx hardhat run scripts/deploy-production.js --network base

# Verify deployment
npx hardhat verify --network base <CONTRACT_ADDRESS> <AAVE_PROVIDER> <STARGATE_ROUTER>
```

### Step 2: Initial Configuration

```javascript
// deployment-config.js
const initialConfig = {
  maxSlippageBps: 200, // 2% default slippage
  initialETHDeposit: '1.0', // 1 ETH for operations
  emergencyPaused: false, // Start unpaused
};
```

### Step 3: Fund Contract

```bash
# Send initial ETH for LayerZero fees
# Recommended: 1-2 ETH for initial operations
```

### Step 4: Verify Configuration

```javascript
// Verify deployment configuration
const contract = await ethers.getContractAt(
  'StargateCompassV1',
  contractAddress
);

console.log('Max Slippage BPS:', await contract.maxSlippageBps());
console.log('Emergency Paused:', await contract.emergencyPaused());
console.log('ETH Balance:', await contract.getETHBalance());
console.log('Owner:', await contract.owner());
```

## Post-Deployment Verification

### 1. Contract State Validation

```javascript
// Verify all critical parameters
const validationChecks = {
  maxSlippageBps: 200,
  MAX_SLIPPAGE_BPS: 1000,
  MIN_PROFIT_BPS: 50,
  MAX_NATIVE_FEE: ethers.parseEther("0.1"),
  MIN_ETH_BALANCE: ethers.parseEther("0.05"),
  emergencyPaused: false
};

// Run validation script
npx hardhat run scripts/validate-deployment.js --network base
```

### 2. Integration Testing

```javascript
// Test basic functionality with small amounts
const testParams = {
  loanAmount: ethers.parseUnits('100', 6), // 100 USDC
  expectedProfit: ethers.parseUnits('2', 6), // 2 USDC profit
  minAmountOut: ethers.parseUnits('98', 6), // 2% slippage
  remoteCalldata: '0x1234',
  remoteSwapRouter: '0x...', // Valid router address
};
```

### 3. Security Validation

```bash
# Run production security tests
npx hardhat test test/StargateCompassV1.SecurityValidation.test.js --network base-fork
```

## Monitoring Setup

### 1. Event Monitoring

Set up monitoring for critical events:

```javascript
const criticalEvents = [
  'ProfitabilityValidated',
  'SlippageConfigured',
  'ETHDeposited',
  'ETHWithdrawn',
  'EmergencyPaused',
  'EmergencyUnpaused',
  'EmergencyWithdraw',
];

// Example monitoring setup
contract.on(
  'ProfitabilityValidated',
  (loanAmount, expectedProfit, requiredProfit) => {
    console.log(
      `Profitability validated: ${loanAmount}, ${expectedProfit}, ${requiredProfit}`
    );
    // Send to monitoring system
  }
);
```

### 2. Balance Monitoring

```javascript
// Monitor ETH balance
setInterval(async () => {
  const balance = await contract.getETHBalance();
  const minBalance = await contract.MIN_ETH_BALANCE();

  if (balance < minBalance * 2n) {
    // Alert when below 2x minimum
    console.warn(`Low ETH balance: ${ethers.formatEther(balance)} ETH`);
    // Send alert
  }
}, 60000); // Check every minute
```

### 3. Configuration Monitoring

```javascript
// Monitor configuration changes
contract.on('SlippageConfigured', (oldSlippage, newSlippage) => {
  console.log(`Slippage updated: ${oldSlippage} -> ${newSlippage}`);
  // Log configuration change
});
```

## Operational Procedures

### 1. Regular Operations

#### ETH Management

```javascript
// Check ETH balance
const balance = await contract.getETHBalance();

// Deposit ETH when needed
await contract.depositETH({ value: ethers.parseEther('1.0') });

// Withdraw excess ETH (maintaining reserves)
await contract.withdrawETH(ethers.parseEther('0.5'));
```

#### Configuration Updates

```javascript
// Update slippage tolerance (if needed)
await contract.setMaxSlippage(300); // 3%

// Verify change
const newSlippage = await contract.maxSlippageBps();
console.log(`New slippage: ${newSlippage} BPS`);
```

### 2. Emergency Procedures

#### Emergency Pause

```javascript
// In case of security incident or operational issue
await contract.emergencyPause();
console.log('Contract paused - all operations halted');

// Verify pause status
const isPaused = await contract.emergencyPaused();
console.log(`Emergency paused: ${isPaused}`);
```

#### Asset Recovery

```javascript
// Recover specific token
await contract.emergencyWithdraw(tokenAddress);

// Recover all assets
await contract.emergencyRecoverAll();

// Check recoverable assets
const [tokens, balances] = await contract.getRecoverableAssets();
console.log('Recoverable assets:', tokens, balances);
```

#### Resume Operations

```javascript
// After resolving emergency
await contract.emergencyUnpause();
console.log('Contract unpaused - operations resumed');
```

## Security Best Practices

### 1. Access Control

- **Owner Key Security**: Use hardware wallet or multi-sig for owner key
- **Key Rotation**: Regularly rotate access keys
- **Principle of Least Privilege**: Limit access to essential personnel only

### 2. Operational Security

- **Regular Monitoring**: Monitor all events and balances continuously
- **Incident Response**: Have documented procedures for emergency scenarios
- **Configuration Changes**: Log and verify all configuration updates

### 3. Financial Security

- **ETH Reserves**: Maintain adequate ETH for operations (2-3x minimum)
- **Profit Validation**: Monitor profitability validation events
- **Fee Management**: Track LayerZero fee trends and adjust limits if needed

## Troubleshooting Guide

### Common Issues

#### 1. Insufficient ETH Balance

```
Error: InsufficientETHBalance(required, available)
```

**Solution**: Deposit more ETH using `depositETH()`

#### 2. Excessive Fee Error

```
Error: ExcessiveFee(provided, maximum)
```

**Solution**: LayerZero fees have increased. Consider adjusting `MAX_NATIVE_FEE` if appropriate.

#### 3. Insufficient Profit Error

```
Error: InsufficientProfit(expectedProfit, requiredProfit, flashLoanCosts)
```

**Solution**: Increase expected profit or check market conditions.

#### 4. Contract Paused Error

```
Error: ContractPaused()
```

**Solution**: Check if emergency pause is active. Unpause if safe to do so.

### Diagnostic Commands

```javascript
// Check contract status
const status = {
  owner: await contract.owner(),
  paused: await contract.emergencyPaused(),
  ethBalance: await contract.getETHBalance(),
  slippage: await contract.maxSlippageBps(),
  recoverableAssets: await contract.getRecoverableAssets(),
};
console.log('Contract Status:', status);
```

## Maintenance Schedule

### Daily

- Monitor ETH balance
- Check for emergency events
- Verify operational status

### Weekly

- Review profitability validation events
- Check LayerZero fee trends
- Validate configuration settings

### Monthly

- Review security logs
- Update monitoring systems
- Test emergency procedures

## Upgrade Procedures

### Contract Upgrades

**Note**: StargateCompassV1 is not upgradeable. Any upgrades require new deployment.

1. Deploy new contract version
2. Pause old contract
3. Recover all assets from old contract
4. Configure new contract
5. Update monitoring systems
6. Resume operations

### Configuration Updates

```javascript
// Safe configuration update procedure
1. Pause contract (if critical change)
2. Update configuration
3. Verify new settings
4. Resume operations
5. Monitor for issues
```

## Support and Contacts

### Emergency Contacts

- **Technical Lead**: [Contact Information]
- **Security Team**: [Contact Information]
- **Operations Team**: [Contact Information]

### Documentation

- **Security Audit Report**: `SECURITY_AUDIT_VERIFICATION.md`
- **Test Documentation**: `test/` directory
- **Contract Documentation**: Inline NatSpec comments

### Monitoring Dashboards

- **Contract Events**: [Dashboard URL]
- **Balance Monitoring**: [Dashboard URL]
- **Performance Metrics**: [Dashboard URL]

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Reviewed By**: Operations Team  
**Approved By**: Technical Lead

## Appendix

### A. Contract ABI

[Include full contract ABI for integration]

### B. Event Schemas

[Include detailed event schemas for monitoring]

### C. Error Codes Reference

[Include comprehensive error code documentation]
