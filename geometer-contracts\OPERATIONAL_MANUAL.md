# StargateCompassV1 Operational Manual

## Table of Contents

1. [Overview](#overview)
2. [Daily Operations](#daily-operations)
3. [Monitoring and Alerting](#monitoring-and-alerting)
4. [Emergency Procedures](#emergency-procedures)
5. [Configuration Management](#configuration-management)
6. [Troubleshooting](#troubleshooting)
7. [Performance Optimization](#performance-optimization)
8. [Security Procedures](#security-procedures)

## Overview

The StargateCompassV1 contract is a production-ready, security-hardened smart contract for cross-chain arbitrage operations. This manual provides comprehensive operational guidance for managing the contract in production environments.

### Key Features

- Flash loan-powered arbitrage execution
- Comprehensive slippage protection
- Profitability validation
- Emergency control systems
- Asset recovery mechanisms
- Real-time monitoring capabilities

### Security Posture

- All 13 audit findings resolved
- Defense-in-depth architecture
- Multiple validation layers
- Emergency response capabilities

## Daily Operations

### Morning Checklist

#### 1. System Health Check

```bash
# Check contract status
npx hardhat run scripts/health-check.js --network base
```

**Verify**:

- ✅ Contract is not paused
- ✅ ETH balance > 2x minimum reserve
- ✅ No emergency events in last 24h
- ✅ Configuration unchanged

#### 2. Balance Monitoring

```javascript
// Check ETH balance
const ethBalance = await contract.getETHBalance();
const minBalance = await contract.MIN_ETH_BALANCE();

console.log(`ETH Balance: ${ethers.formatEther(ethBalance)} ETH`);
console.log(`Minimum Required: ${ethers.formatEther(minBalance)} ETH`);

// Alert if below 2x minimum
if (ethBalance < minBalance * 2n) {
  console.warn('⚠️  ETH balance below recommended level');
}
```

#### 3. Recent Activity Review

```javascript
// Check recent events (last 24h)
const filter = contract.filters.ProfitabilityValidated();
const events = await contract.queryFilter(filter, -5760); // ~24h of blocks

console.log(`Operations in last 24h: ${events.length}`);
events.forEach((event) => {
  console.log(
    `Loan: ${event.args.loanAmount}, Profit: ${event.args.expectedProfit}`
  );
});
```

### Ongoing Monitoring Tasks

#### ETH Balance Management

```javascript
// Automated balance management
async function manageETHBalance() {
  const balance = await contract.getETHBalance();
  const minBalance = await contract.MIN_ETH_BALANCE();
  const targetBalance = ethers.parseEther('2.0'); // 2 ETH target

  if (balance < minBalance * 2n) {
    // Deposit ETH to reach target
    const depositAmount = targetBalance - balance;
    await contract.depositETH({ value: depositAmount });
    console.log(`Deposited ${ethers.formatEther(depositAmount)} ETH`);
  }

  if (balance > ethers.parseEther('5.0')) {
    // Withdraw excess ETH (keeping 2 ETH)
    const withdrawAmount = balance - targetBalance;
    await contract.withdrawETH(withdrawAmount);
    console.log(`Withdrew ${ethers.formatEther(withdrawAmount)} ETH`);
  }
}
```

#### Configuration Monitoring

```javascript
// Monitor configuration changes
contract.on('SlippageConfigured', (oldSlippage, newSlippage, event) => {
  console.log(`🔧 Slippage updated: ${oldSlippage} -> ${newSlippage} BPS`);
  // Log to monitoring system
  logConfigurationChange(
    'slippage',
    oldSlippage,
    newSlippage,
    event.blockNumber
  );
});
```

## Monitoring and Alerting

### Critical Alerts

#### 1. Emergency Events

```javascript
// Emergency pause alert
contract.on('EmergencyPaused', (pauser, event) => {
  sendCriticalAlert({
    type: 'EMERGENCY_PAUSE',
    message: `Contract paused by ${pauser}`,
    blockNumber: event.blockNumber,
    severity: 'CRITICAL',
  });
});

// Emergency unpause alert
contract.on('EmergencyUnpaused', (unpauser, event) => {
  sendCriticalAlert({
    type: 'EMERGENCY_UNPAUSE',
    message: `Contract unpaused by ${unpauser}`,
    blockNumber: event.blockNumber,
    severity: 'HIGH',
  });
});
```

#### 2. Balance Alerts

```javascript
// Low ETH balance alert
async function checkBalanceAlerts() {
  const balance = await contract.getETHBalance();
  const minBalance = await contract.MIN_ETH_BALANCE();

  if (balance < minBalance) {
    sendCriticalAlert({
      type: 'INSUFFICIENT_ETH',
      message: `ETH balance below minimum: ${ethers.formatEther(balance)} ETH`,
      severity: 'CRITICAL',
    });
  } else if (balance < minBalance * 2n) {
    sendAlert({
      type: 'LOW_ETH_BALANCE',
      message: `ETH balance low: ${ethers.formatEther(balance)} ETH`,
      severity: 'WARNING',
    });
  }
}
```

#### 3. Operational Alerts

```javascript
// Failed operation alert
contract.on(
  'ProfitabilityValidated',
  (loanAmount, expectedProfit, requiredProfit, event) => {
    if (expectedProfit < requiredProfit) {
      sendAlert({
        type: 'PROFITABILITY_WARNING',
        message: `Low profit margin: Expected ${expectedProfit}, Required ${requiredProfit}`,
        severity: 'WARNING',
      });
    }
  }
);
```

### Performance Monitoring

#### 1. Operation Metrics

```javascript
// Track operation performance
const operationMetrics = {
  totalOperations: 0,
  successfulOperations: 0,
  failedOperations: 0,
  totalVolume: 0n,
  totalProfit: 0n,
  averageGasUsed: 0n,
};

// Update metrics on each operation
contract.on(
  'ProfitabilityValidated',
  (loanAmount, expectedProfit, requiredProfit) => {
    operationMetrics.totalOperations++;
    operationMetrics.totalVolume += loanAmount;
    operationMetrics.totalProfit += expectedProfit;
  }
);
```

#### 2. Gas Usage Tracking

```javascript
// Monitor gas usage trends
async function trackGasUsage(txHash) {
  const receipt = await ethers.provider.getTransactionReceipt(txHash);
  const gasUsed = receipt.gasUsed;

  // Log gas usage
  console.log(`Gas used: ${gasUsed.toString()}`);

  // Alert if gas usage is unusually high
  if (gasUsed > 500000n) {
    // 500k gas threshold
    sendAlert({
      type: 'HIGH_GAS_USAGE',
      message: `High gas usage: ${gasUsed.toString()}`,
      severity: 'WARNING',
    });
  }
}
```

## Emergency Procedures

### Emergency Response Flowchart

```
Security Incident Detected
         ↓
    Assess Severity
         ↓
   ┌─────────────────┐
   │   CRITICAL      │
   │ Immediate Pause │
   └─────────────────┘
         ↓
   Investigate Issue
         ↓
   Recover Assets (if needed)
         ↓
   Fix Issue
         ↓
   Test Solution
         ↓
   Resume Operations
```

### Emergency Pause Procedure

#### When to Pause

- Suspected security breach
- Unusual transaction patterns
- Smart contract exploit detected
- Operational anomalies

#### How to Pause

```javascript
// Emergency pause
async function emergencyPause() {
  try {
    console.log('🚨 Initiating emergency pause...');
    const tx = await contract.emergencyPause();
    await tx.wait();

    console.log('✅ Contract successfully paused');

    // Verify pause status
    const isPaused = await contract.emergencyPaused();
    console.log(`Pause status: ${isPaused}`);

    // Send notifications
    sendCriticalAlert({
      type: 'EMERGENCY_PAUSE_EXECUTED',
      message: 'Contract has been emergency paused',
      severity: 'CRITICAL',
    });
  } catch (error) {
    console.error('❌ Failed to pause contract:', error);
    sendCriticalAlert({
      type: 'EMERGENCY_PAUSE_FAILED',
      message: `Failed to pause contract: ${error.message}`,
      severity: 'CRITICAL',
    });
  }
}
```

### Asset Recovery Procedures

#### Emergency Asset Recovery

```javascript
// Recover all assets in emergency
async function emergencyRecoverAll() {
  try {
    console.log('🔄 Starting emergency asset recovery...');

    // Check recoverable assets first
    const [tokens, balances] = await contract.getRecoverableAssets();
    console.log('Recoverable assets:', tokens, balances);

    // Execute recovery
    const tx = await contract.emergencyRecoverAll();
    await tx.wait();

    console.log('✅ Emergency asset recovery completed');

    // Verify recovery
    const [tokensAfter, balancesAfter] = await contract.getRecoverableAssets();
    console.log('Remaining assets:', tokensAfter, balancesAfter);
  } catch (error) {
    console.error('❌ Emergency recovery failed:', error);
  }
}
```

#### Specific Token Recovery

```javascript
// Recover specific token
async function recoverToken(tokenAddress) {
  try {
    console.log(`🔄 Recovering token: ${tokenAddress}`);

    const tx = await contract.emergencyWithdraw(tokenAddress);
    await tx.wait();

    console.log('✅ Token recovery completed');
  } catch (error) {
    console.error('❌ Token recovery failed:', error);
  }
}
```

### Resume Operations Procedure

#### Pre-Resume Checklist

- ✅ Issue identified and resolved
- ✅ Contract state verified as safe
- ✅ All assets accounted for
- ✅ Configuration validated
- ✅ Test transaction successful

#### Resume Operations

```javascript
async function resumeOperations() {
  try {
    console.log('🔄 Resuming operations...');

    // Unpause contract
    const tx = await contract.emergencyUnpause();
    await tx.wait();

    // Verify unpause
    const isPaused = await contract.emergencyPaused();
    console.log(`Contract paused: ${isPaused}`);

    if (!isPaused) {
      console.log('✅ Operations resumed successfully');

      // Send notification
      sendAlert({
        type: 'OPERATIONS_RESUMED',
        message: 'Contract operations have been resumed',
        severity: 'INFO',
      });
    }
  } catch (error) {
    console.error('❌ Failed to resume operations:', error);
  }
}
```

## Configuration Management

### Slippage Configuration

#### Current Settings

```javascript
// Check current slippage settings
async function checkSlippageConfig() {
  const currentSlippage = await contract.maxSlippageBps();
  const maxSlippage = await contract.MAX_SLIPPAGE_BPS();

  console.log(
    `Current slippage: ${currentSlippage} BPS (${currentSlippage / 100}%)`
  );
  console.log(`Maximum allowed: ${maxSlippage} BPS (${maxSlippage / 100}%)`);
}
```

#### Update Slippage

```javascript
// Update slippage tolerance
async function updateSlippage(newSlippageBps) {
  try {
    // Validate new slippage
    const maxSlippage = await contract.MAX_SLIPPAGE_BPS();
    if (newSlippageBps > maxSlippage) {
      throw new Error(
        `Slippage ${newSlippageBps} exceeds maximum ${maxSlippage}`
      );
    }

    console.log(`Updating slippage to ${newSlippageBps} BPS`);

    const tx = await contract.setMaxSlippage(newSlippageBps);
    await tx.wait();

    console.log('✅ Slippage updated successfully');

    // Verify update
    const updatedSlippage = await contract.maxSlippageBps();
    console.log(`New slippage: ${updatedSlippage} BPS`);
  } catch (error) {
    console.error('❌ Failed to update slippage:', error);
  }
}
```

### Recommended Slippage Settings

| Market Condition   | Recommended Slippage | Rationale                                 |
| ------------------ | -------------------- | ----------------------------------------- |
| Normal             | 200 BPS (2%)         | Standard market conditions                |
| Volatile           | 300-400 BPS (3-4%)   | Higher volatility requires more tolerance |
| Extreme Volatility | 500 BPS (5%)         | Maximum for extreme conditions            |
| Low Liquidity      | 400-500 BPS (4-5%)   | Account for wider spreads                 |

## Troubleshooting

### Common Error Scenarios

#### 1. InsufficientProfit Error

```
Error: InsufficientProfit(expectedProfit, requiredProfit, flashLoanCosts)
```

**Diagnosis**:

```javascript
// Calculate required profit
const loanAmount = ethers.parseUnits('1000', 6);
const flashLoanCosts = (loanAmount * 5n) / 10000n; // 0.05%
const minimumProfit = (loanAmount * 50n) / 10000n; // 0.5%
const totalRequired = flashLoanCosts + minimumProfit;

console.log(`Flash loan costs: ${flashLoanCosts}`);
console.log(`Minimum profit: ${minimumProfit}`);
console.log(`Total required: ${totalRequired}`);
```

**Solutions**:

- Increase expected profit parameter
- Check market conditions for better opportunities
- Verify arbitrage calculations

#### 2. ExcessiveFee Error

```
Error: ExcessiveFee(provided, maximum)
```

**Diagnosis**:

```javascript
// Check current fee limits
const maxFee = await contract.MAX_NATIVE_FEE();
console.log(`Maximum allowed fee: ${ethers.formatEther(maxFee)} ETH`);

// Check current LayerZero fees
// (This would require calling Stargate router directly)
```

**Solutions**:

- Wait for LayerZero fees to decrease
- Consider if fee limit adjustment is warranted
- Check for network congestion

#### 3. InsufficientETHBalance Error

```
Error: InsufficientETHBalance(required, available)
```

**Diagnosis**:

```javascript
// Check ETH balance
const balance = await contract.getETHBalance();
const minBalance = await contract.MIN_ETH_BALANCE();

console.log(`Current balance: ${ethers.formatEther(balance)} ETH`);
console.log(`Minimum required: ${ethers.formatEther(minBalance)} ETH`);
```

**Solutions**:

```javascript
// Deposit more ETH
await contract.depositETH({ value: ethers.parseEther('1.0') });
```

### Diagnostic Tools

#### Contract Health Check

```javascript
async function healthCheck() {
  const health = {
    owner: await contract.owner(),
    paused: await contract.emergencyPaused(),
    ethBalance: await contract.getETHBalance(),
    slippage: await contract.maxSlippageBps(),
    constants: {
      maxSlippage: await contract.MAX_SLIPPAGE_BPS(),
      minProfit: await contract.MIN_PROFIT_BPS(),
      maxFee: await contract.MAX_NATIVE_FEE(),
      minEthBalance: await contract.MIN_ETH_BALANCE(),
    },
  };

  console.log('Contract Health Check:', health);
  return health;
}
```

#### Event Log Analysis

```javascript
async function analyzeRecentEvents(hours = 24) {
  const blocksPerHour = 300; // Approximate for Base
  const fromBlock = -1 * hours * blocksPerHour;

  const events = await Promise.all([
    contract.queryFilter(contract.filters.ProfitabilityValidated(), fromBlock),
    contract.queryFilter(contract.filters.SlippageConfigured(), fromBlock),
    contract.queryFilter(contract.filters.EmergencyPaused(), fromBlock),
    contract.queryFilter(contract.filters.EmergencyUnpaused(), fromBlock),
  ]);

  console.log(`Events in last ${hours} hours:`);
  console.log(`- Profitability validations: ${events[0].length}`);
  console.log(`- Slippage configurations: ${events[1].length}`);
  console.log(`- Emergency pauses: ${events[2].length}`);
  console.log(`- Emergency unpauses: ${events[3].length}`);
}
```

## Performance Optimization

### Gas Optimization

#### Monitor Gas Usage

```javascript
// Track gas usage patterns
const gasTracker = {
  samples: [],
  average: 0n,
  max: 0n,
  min: 0n,
};

function trackGas(gasUsed) {
  gasTracker.samples.push(gasUsed);

  // Keep last 100 samples
  if (gasTracker.samples.length > 100) {
    gasTracker.samples.shift();
  }

  // Calculate statistics
  gasTracker.average =
    gasTracker.samples.reduce((a, b) => a + b, 0n) /
    BigInt(gasTracker.samples.length);
  gasTracker.max = gasTracker.samples.reduce((a, b) => (a > b ? a : b), 0n);
  gasTracker.min = gasTracker.samples.reduce(
    (a, b) => (a < b ? a : b),
    gasTracker.samples[0]
  );
}
```

#### Optimize Operation Timing

```javascript
// Monitor network conditions for optimal timing
async function getNetworkConditions() {
  const gasPrice = await ethers.provider.getGasPrice();
  const block = await ethers.provider.getBlock('latest');

  return {
    gasPrice: ethers.formatUnits(gasPrice, 'gwei'),
    blockUtilization: (block.gasUsed * 100n) / block.gasLimit,
    timestamp: block.timestamp,
  };
}
```

### Operational Efficiency

#### Batch Operations

```javascript
// For multiple configuration changes
async function batchConfigUpdate(updates) {
  console.log('Starting batch configuration update...');

  for (const update of updates) {
    switch (update.type) {
      case 'slippage':
        await contract.setMaxSlippage(update.value);
        break;
      case 'ethDeposit':
        await contract.depositETH({ value: update.value });
        break;
      // Add other configuration types
    }
  }

  console.log('Batch update completed');
}
```

## Security Procedures

### Access Control Management

#### Owner Key Security

- Use hardware wallet for owner key
- Implement multi-signature if possible
- Regular key rotation schedule
- Secure backup procedures

#### Operational Security

```javascript
// Verify owner before critical operations
async function verifyOwner() {
  const currentOwner = await contract.owner();
  const signerAddress = await signer.getAddress();

  if (currentOwner.toLowerCase() !== signerAddress.toLowerCase()) {
    throw new Error('Signer is not contract owner');
  }

  console.log('✅ Owner verification successful');
}
```

### Security Monitoring

#### Anomaly Detection

```javascript
// Monitor for unusual patterns
function detectAnomalies(operationData) {
  const anomalies = [];

  // Check for unusual loan amounts
  if (operationData.loanAmount > ethers.parseUnits('1000000', 6)) {
    anomalies.push('Unusually large loan amount');
  }

  // Check for low profit margins
  const profitMargin =
    (operationData.expectedProfit * 10000n) / operationData.loanAmount;
  if (profitMargin < 100n) {
    // Less than 1%
    anomalies.push('Low profit margin');
  }

  return anomalies;
}
```

### Incident Response

#### Security Incident Checklist

1. ✅ Identify and assess threat
2. ✅ Pause contract if necessary
3. ✅ Secure assets
4. ✅ Investigate root cause
5. ✅ Implement fix
6. ✅ Test solution
7. ✅ Resume operations
8. ✅ Post-incident review

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Maintained By**: Operations Team
