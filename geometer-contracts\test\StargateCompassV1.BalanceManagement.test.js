const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 Balance Management Tests', function () {
  // Test fixture for contract deployment
  async function deployStargateCompassFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockAaveProvider.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory('MockPool');
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockStargateRouter.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock provider to return mock pool
    await mockAaveProvider.setPool(mockPool.target);

    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
    };
  }

  describe('Balance Monitoring Constants', function () {
    it('Should have correct minimum ETH balance constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.MIN_ETH_BALANCE()).to.equal(
        ethers.parseEther('0.05')
      );
    });

    it('Should have correct low balance threshold constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.LOW_BALANCE_THRESHOLD()).to.equal(
        ethers.parseEther('0.1')
      );
    });
  });

  describe('Balance Management Utilities', function () {
    describe('getRequiredETHBalance', function () {
      it('Should calculate correct required balance for single operation', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const operationCount = 1;
        const requiredBalance = await stargateCompass.getRequiredETHBalance(
          operationCount
        );

        // Expected: MAX_NATIVE_FEE + FEE_BUFFER + MIN_ETH_BALANCE
        const maxFee = ethers.parseEther('0.1'); // MAX_NATIVE_FEE
        const feeBuffer = (maxFee * 200n) / 10000n; // 2% buffer
        const bufferedFee = maxFee + feeBuffer;
        const minBalance = ethers.parseEther('0.05'); // MIN_ETH_BALANCE
        const expectedBalance = bufferedFee + minBalance;

        expect(requiredBalance).to.equal(expectedBalance);
      });

      it('Should return minimum balance for zero operations', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const operationCount = 0;
        const requiredBalance = await stargateCompass.getRequiredETHBalance(
          operationCount
        );

        // Expected: MIN_ETH_BALANCE only
        const expectedBalance = ethers.parseEther('0.05');
        expect(requiredBalance).to.equal(expectedBalance);
      });
    });

    describe('estimateOperationCost', function () {
      it('Should return estimated cost when fee quote succeeds', async function () {
        const { stargateCompass, mockStargateRouter } = await loadFixture(
          deployStargateCompassFixture
        );

        // Set up mock fee quote
        const mockFee = ethers.parseEther('0.05');
        await mockStargateRouter.setQuoteLayerZeroFeeResponse(mockFee, 0);

        const destinationChainId = 204; // Degen Chain
        const remoteCalldata = '0x1234';
        const remoteSwapRouter = ethers.Wallet.createRandom().address;

        const estimatedCost = await stargateCompass.estimateOperationCost(
          destinationChainId,
          remoteCalldata,
          remoteSwapRouter
        );

        // Expected: mockFee + 2% buffer
        const expectedCost = mockFee + (mockFee * 200n) / 10000n;
        expect(estimatedCost).to.equal(expectedCost);
      });
    });
  });
});
