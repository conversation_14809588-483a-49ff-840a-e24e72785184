# Zen Geometer CLI Reference

Complete reference for all commands, operational modes, and binary tools in the Zen Geometer autonomous trading system.

## Quick Start Commands

```bash
# New user setup (recommended)
cargo run -- config wizard

# Validate current configuration
cargo run -- validate

# Show current configuration
cargo run -- config show

# Start in simulation mode (safe learning)
cargo run -- run --mode simulate

# Launch TUI interface
cargo run -- tui
```

## Operational Modes

The Zen Geometer implements a **5-Tier Deployment Ladder** for progressive risk management:

### Simulate Mode (Educational)
```bash
# Educational simulation with live data analysis
cargo run -- run --mode simulate

# With verbose logging
cargo run -- run --mode simulate --verbose

# Educational simulation with detailed lifecycle reports
cargo run -- simulate --detailed

# Filter by specific scanner
cargo run -- simulate --scanner zen_geometer

# Set minimum profit threshold for reports
cargo run -- simulate --min-profit 5.0
```

**Features:**
- Connects to live market data but never broadcasts transactions
- Perfect for learning how the bot analyzes real opportunities
- Shows detailed opportunity lifecycle reports
- Safe for beginners - no real money at risk

### Shadow Mode (Live Simulation)
```bash
# Live simulation with on-chain verification
cargo run -- run --mode shadow --verbose
```

**Features:**
- Tests transactions on forked blockchain state
- Validates profitability without broadcasting to mainnet
- Uses real market data and network conditions
- Fork validation ensures transaction success

### Sentinel Mode (Monitoring)
```bash
# Live on-chain event monitoring
cargo run -- run --mode sentinel --verbose
```

**Features:**
- Monitors deployed contracts and network health
- Tests contract functionality with minimal capital
- Real contract state verification
- Network coherence assessment

### Low-Capital Mode (Conservative Trading)
```bash
# Live trading with conservative limits
cargo run -- run --mode low-capital --verbose
```

**Features:**
- Real money trading with hardcoded safe limits
- Maximum position: $500, Maximum daily loss: $200
- Kelly fraction: 5% for position sizing
- Enhanced opportunity scanning with realistic simulation

### Live Mode (Production Trading)
```bash
# Full production trading (REAL MONEY AT RISK)
cargo run -- run --mode live --verbose
```

**Features:**
- Uses configured risk parameters
- Full production trading capabilities
- All safety limits removed
- Only use after thorough testing in other modes

## TUI Interface

### Launch TUI
```bash
# Launch Terminal User Interface
cargo run -- tui

# Alternative TUI harness for testing
cargo run --bin tui_harness
```

### TUI Navigation
- **Tab switching**: `1` (Dashboard), `2` (Operations), `3` (Systems), `4` (Config)
- **Universal controls**: `↑/↓` (navigate), `Enter` (select), `Esc` (cancel), `Q` (quit)
- **Strategy Inspector**: `I` (when opportunity selected)

### TUI Tabs Overview

**Dashboard Tab - "The Bridge"**
- Aetheric Resonance Engine visualization
- Network Seismology with S-P latency
- Chronos Sieve market rhythm analysis
- Mandorla Gauge opportunity scoring
- Execution & PnL tracking

**Operations Tab - "The Cockpit"**
- Master control panel (`S` start/stop, `P` pause, `R` restart, `E` emergency stop)
- Live activity log with filtering (`F` filter, `C` clear)
- Trade history with nformation
ealth

"**
- Component status matrix (all ARE compon
- Key performance indicators
- Network seismology data
- Node connectivity manager
- Enhanced log viewer

**Config Tab - "The Control Panel"**

- Parameter editor with hot-reload
- Real-time validation
- Profile management





```bash
# Show current configuratalth check
cargo run -- config show

ysis
cargo run -- config validate

# Strict validation with networkty tests
cargo run -- config validate --strrk

# Output validation 


AML
cargo run -- config val
`

### Configuration Wizard

sh
# Run interactive configuration wizard
cargo run -- config wizard
```

The wizard guides you through:
.)
- Network configuration (Base, ArbiDegen)
- Strategy parameters with recommendations
ings
- Infrastructure setup
- Validation and saving

n

ash
# Set B
cargo run -- config set-gaze \
  --pool-a 0x2F2f249534a5d
  - \
\
  --test-amount 1.0
`

### Interactive Editor

```bash
# Interactive configur soon)
cargo run -- config edi

# Edit specific section
ion
```

### Template Generation

```bash
# Generate built-in 
cargo run -- configates
```

Creates templates:
figuration
- `deveg
- `production-conservative` - Conservative live ing
- `production-aggressiveing
s

## Profile Management

ons

sh
# List s
cargo run -- config profile list

# Ln
ed

n
cargo run -- config profile show my-profil

# Create new profile
cargo run -- config profile create my-profile

isting one
cargo run -- config pve

# Copy profile to new e


# Delete profile
cargo run -- config profile dele-profile

# Force delete without confirmation
cargo run -- config profile delete old-profe --force


### Profile Search and Statistics

```bash
# Search profiles by name, description, tags,
"
cargo run -- config profile search "co
cargo run -- config profile search "test"

# Show profile statistics 
cargo run -- config profile stats
```

### Profile Validation

```bash
# Validate specific profile
carle

# Strict validation for profile
ct
```

## Import/Export Operations

### Export Profiles

```bash
# Export profile as TOML (default
carml

# Export profile as JSON


# Export profile as YAML
cargo run -- config export my-profile --format ml

# Export full profile with metata
cargo run -- config export my-profile --format profile -e.toml


cargo run -- config export 
```

files

```bash
# Import TOML configuration


# Import JSON configuration
nfig

# Import YAML configuration
onfig

# Auto-detect format
fig

# Import full profile with metadata
carle


g

### System Validation

sh
# Comprehensive system valition
cargo run -- validate

# Validate with custom confle
cargo run -- validate --config my-profile.toml
``

### Utility Commands

```bash
# Check wallet balances
carces

# Test connectivity to all services
g-nodes

ation
cargo r

# Test Basilisk Gaze 
cles 10
```

## 

The Zen Geometer inc

### Datn

#### `data_ingestor`

# Start real-time market data ingestion
cargo run --bin data_ingestor

# With specific configuration
cargo run --bin data_ingestor ml

**Purpose**: Real-time marketon

###
sh
# Monitor all NATopics
listener

 URL
NATS_URL=nats://localhost:4222 cargo run --bin listener

**Purpoitoring

#### `feature_exporter`
h
# Generate SIGINT intelligence report
cargo run --bin feature_exporter

io
MARKET_STRESS=high cargo r
```
**Purpose**: SIGINT workflow feature extractionlysis

### Analysis & Optimization

#### `graph_analyzer`
```bash
# Ss)
analyzer
```
PageRank

#### `seismic_analyzer`
```bash
ATS)
cargo run --bin seismic_analyzr
```
ing

#### `optimizer`
`bash
# Optimize strateg
cargo run --bin optimiz

ration
cargo run --bin optimizer -- -
```
**Purpo

#### `backtester`

# Run simple backtest simulation
cargo run --bin backtester
```
n

`
```bash
a
cargo r8001000

# With custom fork URL

```
**Purpose**: Historical mempool analysis wulation

### MonitorinI

###
`bash
# Start TUI harnesulation
rness
```
**Purpose**: Standalone TUI testing envgeneration


```bash
# Start network observer with automatic endpointn
er
```
**Purpose**: Enhanced network seismology analysition

#### `mempool_observer`
```bash
# S
server
```
ing

tion

#### `demo_data_generator`
```bash
on
cargo run --bin demo_data_gerator
```


## Complete Command Reference

### Main Commands
| Command | Description | E
|---|

| `tui` | Launch Termina` |

| `simuled` |
| `config` | Configuration mana show` |
| `utils` | Utility commands | `cargo run -- utils balances` |

### Run Mode Options
| Mode | Risk Level | Description |
|
| `simulate` | None | data |
| `shadow` | None | Fork validation testing |
ring |
| `low-capital` | Low |
| `live` | Full | Production trading |

s
| Subcommand | Descriptiople |
------|
| `showhow` |
| `validate` | Validate configurationict` |
| `wizard` | Interactive setup wizard | `cargo run -- config wizard` |

| `edit` | Interactive configu
| `templates` | Generate configuratio
 |
| `import` | Import configura |
| `export` | Export configuration | `cargo run -- 

### Profile Management Subcommands
| Subcommand | Description | Example |
--------|
| `list` | List all profiled` |
| `show` | Show  |
| `
orce` |
| `copy` | Copy profile | `cargo runt` |

| `statts` |
| `validate` | Valida

### Utility Subcommands
| Subcommand | Description | Example |
|-----|

| `ping-nodes` | T
| `show-config` | Show Basilifig` |
| `test-gaze` | Test Basilisk 

### ns
| O|
---|
| `--config` | Specify configuration 
e` |
| `--help` | Show help inhelp` |


## TUI Keyboard Reference

Controls
| Key | Action | Coxt |
|-----|--------|---------|

| `1-4` | Switch to tab | Any
| `Tab` | Cycle through tabs/panels | Any tab |
b |
| `Enter` | Select/Confirm | b |
| `Esc` | Cancel/Back | Any tab |
| `vailable |

### Dashboard Tab Controls

|-----|
| `Tab` | Cycle through ARE widgets |
| `Enter` | Drill down to detailed analysis |

### Operations Tab Contrs
| Key | Action |

| `S` | Start/Stop bot |
| `P` | Pause bot |

| `E` | Emergency stop |
| `G` | Graceful stop |
| `g |
r |


| Key | Action |

| `Tab`anels |
| `R` | Restart component/reco
| `S` | Start service |
ce |
| `F` | Filter logs |
| `C` | Clear log filter |
| `
gs |
| `X` | Clear all logs |

### Cons
| Key | Action |
|-----|--------|
d |
| `R` | Reset to defauls |
| `L` | Load profile |
| `
eter |


| Key |n |
|-----|--------|
| `I` | Open inspector |
 |
| `E` | Execute ounity |
| `R` | Reject opportunity |

sage

s

les:

bash
# Required for live ding
export _here

# Optional RPC API keys for bce
se_api_key
export ARBITRUM_RPC_API_KEY=yey

# O
export COINBASE_API_KEY=your_coinbase_key
_secret

# Infras
export DATABASE_URL=pob
export REDIS_URL=redis://host:port

```

### Configuration File Locations

```bash
# Default confuration
config/default.toml

# Local overridered)
config/local.toml

# Profile storage
config/profiles/*.toml

# Templastorage
config/templates/*.toml
`

### Custes

```bash
# Use custom confi file
cargo run -- un

# Validate custom configuration
cargo run -- --config path/todate
```

## Common Workflows

### New User Setup

```bash
# 1. Run configurationard
cargo run -- conf

# 2. Validate configura
cargo run -- validate --c

# 3. Test in simulation mode
ulate

# 4. Monitor with TUI
cargo run -- tu
```

ow

```bash
# 1. Create development profile
cargo run -- config profile create dev --fro

# 2. Customize for your n
cargo run -- config profidev

# 3. Validate changes
cargo run -- config profict

# 4. Export for sharing
ig.json
```

### Production Deployment

```bash
# 1. Start with conservative template
cargo run -- config profile

# 2. Customize risk parameters
cargo run -- config profile show prod

# 3. Comprehensive validation
cargo run -- config profile valitrict

# 4. Test with shadow moirst
carverbose

# 5. Deploy with monitoring
cargo run -- run --mode live
```

This CLI reference provides comprehensive coverage of all commands, operational modes, binary tom.systerading r tometeGee Zen thls in ontro cnd interfaceols, a