// src/strategies/centrality_manager.rs

use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, info, warn};
use anyhow::Result;

/// CentralityScoreManager manages token centrality scores for the Aetheric Resonance Engine
/// 
/// This component implements the AXIS MUNDI concept where tokens are scored based on their
/// centrality in the DeFi ecosystem. Higher centrality scores indicate tokens that are more
/// connected and liquid, making arbitrage paths through them more stable and profitable.
pub struct CentralityScoreManager {
    centrality_scores: Arc<HashMap<String, Decimal>>,
    default_fallback_score: Decimal,
}

impl CentralityScoreManager {
    /// Creates a new CentralityScoreManager with populated major token centrality scores
    pub fn new() -> Self {
        let mut scores = HashMap::new();
        
        // Major token centrality values as specified in the task
        // These scores reflect the relative importance and connectivity of tokens in DeFi
        scores.insert("WETH".to_string(), dec!(0.95));  // Ethereum - highest centrality
        scores.insert("USDC".to_string(), dec!(0.90));  // USD Coin - primary stablecoin
        scores.insert("USDT".to_string(), dec!(0.85));  // Tether - widely used stablecoin
        scores.insert("DAI".to_string(), dec!(0.80));   // DAI - decentralized stablecoin
        scores.insert("WBTC".to_string(), dec!(0.75));  // Wrapped Bitcoin - major asset
        
        // Additional major DeFi tokens with appropriate centrality scores
        scores.insert("UNI".to_string(), dec!(0.70));   // Uniswap governance token
        scores.insert("LINK".to_string(), dec!(0.65));  // Chainlink - oracle network
        scores.insert("AAVE".to_string(), dec!(0.60));  // Aave - lending protocol
        scores.insert("COMP".to_string(), dec!(0.55));  // Compound governance token
        scores.insert("MKR".to_string(), dec!(0.50));   // Maker governance token
        scores.insert("SNX".to_string(), dec!(0.45));   // Synthetix network token
        scores.insert("CRV".to_string(), dec!(0.40));   // Curve governance token
        scores.insert("SUSHI".to_string(), dec!(0.35)); // SushiSwap governance token
        
        // Default fallback score for unknown tokens
        let default_fallback_score = dec!(0.05);
        scores.insert("UNKNOWN".to_string(), default_fallback_score);
        
        info!("CentralityScoreManager initialized with {} token centrality scores", scores.len());
        debug!("Major token centrality scores: WETH={}, USDC={}, USDT={}, DAI={}, WBTC={}", 
               scores.get("WETH").unwrap_or(&dec!(0.0)),
               scores.get("USDC").unwrap_or(&dec!(0.0)),
               scores.get("USDT").unwrap_or(&dec!(0.0)),
               scores.get("DAI").unwrap_or(&dec!(0.0)),
               scores.get("WBTC").unwrap_or(&dec!(0.0)));
        
        Self {
            centrality_scores: Arc::new(scores),
            default_fallback_score,
        }
    }
    
    /// Gets the centrality score for a specific token symbol
    /// Returns the default fallback score for unknown tokens
    pub fn get_centrality_score(&self, token_symbol: &str) -> Decimal {
        self.centrality_scores
            .get(token_symbol)
            .copied()
            .unwrap_or_else(|| {
                debug!("Unknown token '{}', using default centrality score: {}", 
                       token_symbol, self.default_fallback_score);
                self.default_fallback_score
            })
    }
    
    /// Gets all centrality scores as a shared reference
    pub fn get_all_scores(&self) -> Arc<HashMap<String, Decimal>> {
        self.centrality_scores.clone()
    }
    
    /// Updates the centrality score for a specific token
    /// This allows for dynamic updates based on market conditions or new analysis
    pub fn update_score(&mut self, token_symbol: String, score: Decimal) -> Result<()> {
        if score < dec!(0.0) || score > dec!(1.0) {
            return Err(anyhow::anyhow!(
                "Centrality score must be between 0.0 and 1.0, got: {}", score
            ));
        }
        
        // Create a new HashMap with the updated score
        let mut new_scores = (*self.centrality_scores).clone();
        let old_score = new_scores.insert(token_symbol.clone(), score);
        
        self.centrality_scores = Arc::new(new_scores);
        
        match old_score {
            Some(old) => info!("Updated centrality score for {}: {} -> {}", token_symbol, old, score),
            None => info!("Added new centrality score for {}: {}", token_symbol, score),
        }
        
        Ok(())
    }
    
    /// Adds multiple centrality scores at once
    pub fn update_multiple_scores(&mut self, scores: HashMap<String, Decimal>) -> Result<()> {
        // Validate all scores first
        for (token, score) in &scores {
            if *score < dec!(0.0) || *score > dec!(1.0) {
                return Err(anyhow::anyhow!(
                    "Centrality score for {} must be between 0.0 and 1.0, got: {}", token, score
                ));
            }
        }
        
        // Create a new HashMap with all updates
        let mut new_scores = (*self.centrality_scores).clone();
        for (token, score) in scores {
            new_scores.insert(token.clone(), score);
            debug!("Updated centrality score: {} = {}", token, score);
        }
        
        let scores_count = new_scores.len();
        self.centrality_scores = Arc::new(new_scores);
        info!("Updated {} centrality scores", scores_count);
        
        Ok(())
    }
    
    /// Gets the default fallback score used for unknown tokens
    pub fn get_default_fallback_score(&self) -> Decimal {
        self.default_fallback_score
    }
    
    /// Sets a new default fallback score for unknown tokens
    pub fn set_default_fallback_score(&mut self, score: Decimal) -> Result<()> {
        if score < dec!(0.0) || score > dec!(1.0) {
            return Err(anyhow::anyhow!(
                "Default fallback score must be between 0.0 and 1.0, got: {}", score
            ));
        }
        
        self.default_fallback_score = score;
        
        // Update the UNKNOWN entry in the scores map
        let mut new_scores = (*self.centrality_scores).clone();
        new_scores.insert("UNKNOWN".to_string(), score);
        self.centrality_scores = Arc::new(new_scores);
        
        info!("Updated default fallback centrality score to: {}", score);
        Ok(())
    }
    
    /// Returns statistics about the centrality scores
    pub fn get_statistics(&self) -> CentralityStatistics {
        let scores: Vec<Decimal> = self.centrality_scores.values().copied().collect();
        
        if scores.is_empty() {
            return CentralityStatistics {
                total_tokens: 0,
                min_score: dec!(0.0),
                max_score: dec!(0.0),
                average_score: dec!(0.0),
            };
        }
        
        let min_score = scores.iter().min().copied().unwrap_or(dec!(0.0));
        let max_score = scores.iter().max().copied().unwrap_or(dec!(0.0));
        let sum: Decimal = scores.iter().sum();
        let average_score = sum / Decimal::from(scores.len());
        
        CentralityStatistics {
            total_tokens: scores.len(),
            min_score,
            max_score,
            average_score,
        }
    }
}

impl Default for CentralityScoreManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Statistics about centrality scores
#[derive(Debug, Clone)]
pub struct CentralityStatistics {
    pub total_tokens: usize,
    pub min_score: Decimal,
    pub max_score: Decimal,
    pub average_score: Decimal,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_centrality_manager_initialization() {
        let manager = CentralityScoreManager::new();
        
        // Test major token scores
        assert_eq!(manager.get_centrality_score("WETH"), dec!(0.95));
        assert_eq!(manager.get_centrality_score("USDC"), dec!(0.90));
        assert_eq!(manager.get_centrality_score("USDT"), dec!(0.85));
        assert_eq!(manager.get_centrality_score("DAI"), dec!(0.80));
        assert_eq!(manager.get_centrality_score("WBTC"), dec!(0.75));
        
        // Test unknown token fallback
        assert_eq!(manager.get_centrality_score("UNKNOWN_TOKEN"), dec!(0.05));
    }
    
    #[test]
    fn test_score_updates() {
        let mut manager = CentralityScoreManager::new();
        
        // Test single score update
        manager.update_score("TEST_TOKEN".to_string(), dec!(0.42)).unwrap();
        assert_eq!(manager.get_centrality_score("TEST_TOKEN"), dec!(0.42));
        
        // Test invalid score rejection
        assert!(manager.update_score("INVALID".to_string(), dec!(1.5)).is_err());
        assert!(manager.update_score("INVALID".to_string(), dec!(-0.1)).is_err());
    }
    
    #[test]
    fn test_multiple_score_updates() {
        let mut manager = CentralityScoreManager::new();
        
        let mut new_scores = HashMap::new();
        new_scores.insert("TOKEN1".to_string(), dec!(0.30));
        new_scores.insert("TOKEN2".to_string(), dec!(0.60));
        
        manager.update_multiple_scores(new_scores).unwrap();
        
        assert_eq!(manager.get_centrality_score("TOKEN1"), dec!(0.30));
        assert_eq!(manager.get_centrality_score("TOKEN2"), dec!(0.60));
    }
    
    #[test]
    fn test_default_fallback_score_update() {
        let mut manager = CentralityScoreManager::new();
        
        manager.set_default_fallback_score(dec!(0.10)).unwrap();
        assert_eq!(manager.get_default_fallback_score(), dec!(0.10));
        assert_eq!(manager.get_centrality_score("UNKNOWN_TOKEN"), dec!(0.10));
        
        // Test invalid fallback score rejection
        assert!(manager.set_default_fallback_score(dec!(1.5)).is_err());
    }
    
    #[test]
    fn test_statistics() {
        let manager = CentralityScoreManager::new();
        let stats = manager.get_statistics();
        
        assert!(stats.total_tokens > 0);
        assert!(stats.min_score >= dec!(0.0));
        assert!(stats.max_score <= dec!(1.0));
        assert!(stats.average_score >= dec!(0.0) && stats.average_score <= dec!(1.0));
    }
}