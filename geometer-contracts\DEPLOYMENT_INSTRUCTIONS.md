# 🚀 Contract Deployment Instructions

## ❌ Current Issue: Insufficient Funds

**Deployment Wallet**: `******************************************`  
**Current Balance**: `0.0 ETH`  
**Required**: `~0.005-0.01 ETH` for gas fees

## 💰 Step 1: Fund Your Deployment Wallet

### Option A: Send ETH from Another Wallet
1. Send **0.01 ETH** to: `******************************************`
2. Use Base network (not Ethereum mainnet!)
3. Wait for confirmation

### Option B: Bridge ETH to Base
1. Go to: https://bridge.base.org/
2. Bridge ETH from Ethereum mainnet to Base
3. Send to: `******************************************`

### Option C: Buy ETH on Base
1. Use a CEX that supports Base network
2. Buy ETH and withdraw to Base network
3. Address: `******************************************`

## 🔧 Step 2: Deploy the Contract

Once your wallet is funded, run:

```bash
cd geometer-contracts

# Check balance (should show > 0 ETH)
npx hardhat run --network base check-balance.js

# Deploy the contract
npx hardhat run --network base deploy-manual.js
```

## ✅ Step 3: Update Bot Configuration

After successful deployment, update the contract address in:

```bash
# File: config/production.toml
[chains.8453.contracts]
stargate_compass_v1 = "0xYourNewContractAddress"  # Replace with actual address
```

## 🔍 Step 4: Verify Contract (Optional)

```bash
# Verify on BaseScan
npx hardhat verify --network base <CONTRACT_ADDRESS> \
  "******************************************" \
  "******************************************"
```

## 📋 Your API Key Status

✅ **BASESCAN_API_KEY**: `78WD6QFJ1W7RD5WWQJBRVSNS9ZDEFAQHP6` (Valid)

**Note**: Etherscan V2 API is not yet available for BaseScan. Your current API key works with the V1 endpoint.

## 🎯 Expected Deployment Cost

- **Gas Limit**: ~1,200,000 gas
- **Gas Price**: ~1 gwei
- **Total Cost**: ~0.0012 ETH (~$3-5 USD)

## 🔗 Useful Links

- **Fund Wallet**: Send ETH to `******************************************`
- **Base Bridge**: https://bridge.base.org/
- **BaseScan**: https://basescan.org/
- **Base Faucet** (testnet only): https://www.coinbase.com/faucets/base-ethereum-goerli-faucet

---

**Next Step**: Fund the deployment wallet with 0.01 ETH on Base network, then run the deployment script.