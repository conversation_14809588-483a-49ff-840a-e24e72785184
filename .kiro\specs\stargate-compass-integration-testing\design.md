# Design Document

## Overview

This design document outlines the architecture and implementation approach for a comprehensive integration testing system that verifies seamless interaction between the Basilisk Bot's backend services, TUI interface, and the newly audited StargateCompassV1 smart contract on a live Anvil testnet environment.

The integration testing system implements a four-phase verification workflow: configuration management, backend integration testing, TUI functionality validation, and end-to-end workflow verification. The system is designed to provide comprehensive coverage of all interaction points between off-chain logic and on-chain components, ensuring production readiness of the integrated system.

Key design principles:

- **Automated Configuration Management**: Dynamic contract address updates with validation
- **Isolated Component Testing**: Independent verification of backend and TUI components
- **End-to-End Validation**: Complete workflow testing from decision to execution to display
- **Comprehensive Reporting**: Detailed test results with actionable failure diagnostics
- **Safety-First Approach**: Testnet-only operations with built-in safeguards

## Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "Integration Test Controller"
        ITC[Integration Test Controller]
        CR[Configuration Manager]
        TR[Test Reporter]
        VF[Validation Framework]
    end

    subgraph "Backend Testing Layer"
        BIT[Backend Integration Tester]
        EM[ExecutionManager Analyzer]
        ED[ExecutionDispatcher Analyzer]
        SM[Strategy Manager Simulator]
    end

    subgraph "TUI Testing Layer"
        TFT[TUI Functionality Tester]
        CMD[Command Executor]
        UI[UI State Validator]
        DV[Data Validator]
    end

    subgraph "Target System Under Test"
        subgraph "Basilisk Bot Backend"
            EMT[ExecutionManager]
            EDT[ExecutionDispatcher]
            SMT[StrategyManager]
        end

        subgraph "TUI Interface"
            TUI[TUI Harness]
            APP[TUI App]
        end

        subgraph "Anvil Testnet"
            SC[StargateCompassV1 Contract]
            BC[Blockchain State]
        end
    end

    ITC --> CR
    ITC --> BIT
    ITC --> TFT
    ITC --> TR

    CR --> EMT
    CR --> EDT
    CR --> TUI

    BIT --> EM
    BIT --> SM
    EM --> EMT
    SM --> SMT

    TFT --> CMD
    TFT --> UI
    CMD --> TUI
    UI --> APP

    EMT --> SC
    EDT --> SC
    TUI --> SC

    BIT --> VF
    TFT --> VF
    VF --> TR
```

### Test Execution Flow

```mermaid
sequenceDiagram
    participant ITC as Integration Test Controller
    participant CM as Configuration Manager
    participant BIT as Backend Integration Tester
    participant TFT as TUI Functionality Tester
    participant TR as Test Reporter
    participant SUT as System Under Test

    ITC->>CM: Initialize configuration update
    CM->>SUT: Update contract addresses
    CM->>ITC: Configuration updated

    ITC->>BIT: Execute backend integration tests
    BIT->>SUT: Simulate trading opportunities
    SUT->>BIT: Return execution results
    BIT->>ITC: Backend test results

    ITC->>TFT: Execute TUI functionality tests
    TFT->>SUT: Execute TUI commands
    SUT->>TFT: Return UI state and data
    TFT->>ITC: TUI test results

    ITC->>TR: Generate comprehensive report
    TR->>ITC: Integration test report
```

## Components and Interfaces

### 1. Integration Test Controller

**Purpose**: Central orchestrator for the entire integration testing workflow

**Key Responsibilities**:

- Coordinate test execution phases
- Manage test environment setup and teardown
- Aggregate results from all testing components
- Generate final integration report

**Interface**:

```rust
pub struct IntegrationTestController {
    config_manager: ConfigurationManager,
    backend_tester: BackendIntegrationTester,
    tui_tester: TuiFunctionalityTester,
    test_reporter: TestReporter,
    anvil_client: AnvilClient,
}

impl IntegrationTestController {
    pub async fn run_full_integration_test(&self) -> IntegrationTestResult;
    pub async fn setup_test_environment(&self) -> Result<()>;
    pub async fn teardown_test_environment(&self) -> Result<()>;
}
```

### 2. Configuration Manager

**Purpose**: Dynamically update bot configuration to point to the newly deployed audited contract

**Key Responsibilities**:

- Locate active configuration files (local.toml, testnet.toml)
- Identify StargateCompassV1 contract address configuration keys
- Update configuration with new contract address
- Validate configuration changes
- Backup original configuration for rollback

**Interface**:

```rust
pub struct ConfigurationManager {
    config_paths: Vec<PathBuf>,
    backup_dir: PathBuf,
}

impl ConfigurationManager {
    pub async fn update_contract_address(&self, new_address: Address) -> Result<()>;
    pub async fn validate_configuration(&self) -> Result<ConfigValidationResult>;
    pub async fn backup_configuration(&self) -> Result<()>;
    pub async fn restore_configuration(&self) -> Result<()>;
}
```

### 3. Backend Integration Tester

**Purpose**: Verify ExecutionManager and ExecutionDispatcher can successfully interact with the audited smart contract

**Key Responsibilities**:

- Analyze ExecutionManager and ExecutionDispatcher code to identify contract interaction functions
- Create realistic trading opportunity simulations
- Execute backend integration tests with transaction validation
- Verify transaction success and response processing

**Interface**:

```rust
pub struct BackendIntegrationTester {
    execution_manager: Arc<ExecutionManager>,
    execution_dispatcher: Arc<ExecutionDispatcher>,
    opportunity_simulator: OpportunitySimulator,
    transaction_validator: TransactionValidator,
}

impl BackendIntegrationTester {
    pub async fn test_execution_manager_integration(&self) -> BackendTestResult;
    pub async fn test_execution_dispatcher_integration(&self) -> BackendTestResult;
    pub async fn simulate_trading_opportunity(&self) -> Opportunity;
    pub async fn validate_transaction_execution(&self, tx_hash: H256) -> TransactionValidationResult;
}
```

### 4. TUI Functionality Tester

**Purpose**: Verify all TUI commands correctly interact with the audited smart contract and display accurate data

**Key Responsibilities**:

- Identify TUI commands that interact with StargateCompassV1 contract
- Execute TUI commands programmatically
- Validate displayed data against on-chain state
- Verify transaction initiation commands succeed

**Interface**:

```rust
pub struct TuiFunctionalityTester {
    tui_harness: TuiHarness,
    command_executor: CommandExecutor,
    ui_state_validator: UiStateValidator,
    data_validator: DataValidator,
}

impl TuiFunctionalityTester {
    pub async fn test_contract_status_commands(&self) -> TuiTestResult;
    pub async fn test_balance_query_commands(&self) -> TuiTestResult;
    pub async fn test_transaction_commands(&self) -> TuiTestResult;
    pub async fn validate_displayed_data(&self, command: &str) -> DataValidationResult;
}
```

### 5. Test Reporter

**Purpose**: Generate comprehensive integration test reports with actionable failure diagnostics

**Key Responsibilities**:

- Aggregate test results from all components
- Generate detailed failure analysis
- Provide specific remediation recommendations
- Create standardized test reports

**Interface**:

```rust
pub struct TestReporter {
    results: Vec<TestResult>,
    failure_analyzer: FailureAnalyzer,
}

impl TestReporter {
    pub fn add_test_result(&mut self, result: TestResult);
    pub fn generate_report(&self) -> IntegrationTestReport;
    pub fn analyze_failures(&self) -> Vec<FailureAnalysis>;
}
```

## Data Models

### Test Result Data Structures

```rust
#[derive(Debug, Clone)]
pub struct IntegrationTestResult {
    pub overall_success: bool,
    pub configuration_result: ConfigurationTestResult,
    pub backend_result: BackendTestResult,
    pub tui_result: TuiTestResult,
    pub end_to_end_result: EndToEndTestResult,
    pub execution_time: Duration,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct ConfigurationTestResult {
    pub success: bool,
    pub config_files_updated: Vec<String>,
    pub validation_errors: Vec<String>,
    pub backup_created: bool,
}

#[derive(Debug, Clone)]
pub struct BackendTestResult {
    pub success: bool,
    pub execution_manager_tests: Vec<ComponentTestResult>,
    pub execution_dispatcher_tests: Vec<ComponentTestResult>,
    pub transaction_validations: Vec<TransactionValidationResult>,
    pub contract_interactions: Vec<ContractInteractionResult>,
}

#[derive(Debug, Clone)]
pub struct TuiTestResult {
    pub success: bool,
    pub command_tests: Vec<CommandTestResult>,
    pub data_validations: Vec<DataValidationResult>,
    pub ui_state_validations: Vec<UiStateValidationResult>,
}

#[derive(Debug, Clone)]
pub struct EndToEndTestResult {
    pub success: bool,
    pub workflow_validations: Vec<WorkflowValidationResult>,
    pub data_pipeline_tests: Vec<DataPipelineTestResult>,
}
```

### Contract Interaction Models

```rust
#[derive(Debug, Clone)]
pub struct ContractInteractionResult {
    pub function_name: String,
    pub success: bool,
    pub transaction_hash: Option<H256>,
    pub gas_used: Option<U256>,
    pub error_message: Option<String>,
    pub execution_time: Duration,
}

#[derive(Debug, Clone)]
pub struct TransactionValidationResult {
    pub transaction_hash: H256,
    pub success: bool,
    pub reverted: bool,
    pub gas_used: U256,
    pub return_values: Vec<String>,
    pub events_emitted: Vec<String>,
    pub validation_errors: Vec<String>,
}
```

### TUI Testing Models

```rust
#[derive(Debug, Clone)]
pub struct CommandTestResult {
    pub command: String,
    pub success: bool,
    pub execution_time: Duration,
    pub output_captured: String,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone)]
pub struct DataValidationResult {
    pub data_type: String,
    pub expected_value: String,
    pub actual_value: String,
    pub matches: bool,
    pub validation_method: String,
}
```

## Error Handling

### Error Classification System

```rust
#[derive(Debug, Clone)]
pub enum IntegrationTestError {
    ConfigurationError {
        config_file: String,
        error_type: ConfigErrorType,
        message: String,
    },
    BackendIntegrationError {
        component: String,
        function: String,
        error_type: BackendErrorType,
        message: String,
    },
    TuiInteractionError {
        command: String,
        error_type: TuiErrorType,
        message: String,
    },
    ContractInteractionError {
        contract_address: Address,
        function: String,
        error_type: ContractErrorType,
        message: String,
    },
    EnvironmentError {
        error_type: EnvironmentErrorType,
        message: String,
    },
}

#[derive(Debug, Clone)]
pub enum ConfigErrorType {
    FileNotFound,
    InvalidFormat,
    ValidationFailed,
    BackupFailed,
    RestoreFailed,
}

#[derive(Debug, Clone)]
pub enum BackendErrorType {
    ComponentNotFound,
    FunctionNotFound,
    ExecutionFailed,
    ValidationFailed,
    TransactionReverted,
}

#[derive(Debug, Clone)]
pub enum TuiErrorType {
    CommandNotFound,
    ExecutionTimeout,
    InvalidOutput,
    DataMismatch,
    UiStateInvalid,
}
```

### Error Recovery Strategies

1. **Configuration Errors**: Automatic rollback to backed-up configuration
2. **Backend Errors**: Retry with different parameters, fallback to simulation mode
3. **TUI Errors**: Command retry with timeout extension, alternative command paths
4. **Contract Errors**: Transaction replay with adjusted gas, error state analysis
5. **Environment Errors**: Environment reset, dependency verification

## Testing Strategy

### Phase 1: Configuration Update Testing

**Objective**: Verify configuration management system correctly updates contract addresses

**Test Cases**:

1. **Configuration File Discovery**
   - Locate config/local.toml and config/testnet.toml
   - Verify file accessibility and format validation
   - Test backup creation functionality

2. **Contract Address Update**
   - Update StargateCompassV1 address in configuration
   - Validate configuration syntax after update
   - Verify configuration reload triggers system updates

3. **Configuration Validation**
   - Test invalid address format handling
   - Verify required configuration keys presence
   - Test configuration rollback functionality

### Phase 2: Backend Integration Testing

**Objective**: Verify ExecutionManager and ExecutionDispatcher can interact with the audited contract

**Test Cases**:

1. **ExecutionManager Integration**
   - Test `process_opportunity` method with ZenGeometer opportunities
   - Verify transaction building for StargateCompass calls
   - Test gas estimation and nonce management
   - Validate circuit breaker integration

2. **ExecutionDispatcher Integration**
   - Test `create_stargate_compass_tx` method
   - Verify cross-chain transaction encoding
   - Test transaction broadcasting and confirmation
   - Validate error handling for failed transactions

3. **Strategy Manager Integration**
   - Simulate realistic trading opportunities
   - Test opportunity scoring and validation
   - Verify strategy execution pipeline
   - Test risk management integration

### Phase 3: TUI Functionality Testing

**Objective**: Verify TUI commands correctly interact with the audited contract

**Test Cases**:

1. **Contract Status Commands**
   - Test contract balance queries
   - Verify contract state display
   - Test transaction history retrieval
   - Validate real-time data updates

2. **Transaction Commands**
   - Test transaction initiation from TUI
   - Verify transaction status monitoring
   - Test emergency stop functionality
   - Validate transaction confirmation display

3. **Data Validation**
   - Compare TUI displayed data with on-chain state
   - Test data refresh mechanisms
   - Verify error message display
   - Test connection status indicators

### Phase 4: End-to-End Workflow Testing

**Objective**: Validate complete data pipeline from decision to execution to display

**Test Cases**:

1. **Complete Arbitrage Workflow**
   - Simulate opportunity detection
   - Test backend execution pipeline
   - Verify TUI displays execution results
   - Validate profit/loss calculations

2. **Error Scenario Testing**
   - Test failed transaction handling
   - Verify error propagation to TUI
   - Test recovery mechanisms
   - Validate user notification systems

3. **Performance Testing**
   - Test system performance under load
   - Verify response times for critical operations
   - Test concurrent operation handling
   - Validate resource usage patterns

### Test Environment Setup

**Anvil Configuration**:

```bash
# Start Anvil with Base fork
anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545
```

**Contract Deployment Verification**:

```rust
pub async fn verify_contract_deployment(address: Address) -> Result<bool> {
    // Verify contract bytecode matches expected
    // Test basic contract functionality
    // Validate contract state initialization
}
```

**Test Data Preparation**:

- Pre-funded test accounts with USDC
- Mock trading opportunities with known outcomes
- Prepared TUI command sequences
- Expected on-chain state snapshots

### Automated Test Execution

**Test Runner Configuration**:

```rust
pub struct IntegrationTestConfig {
    pub anvil_url: String,
    pub contract_address: Address,
    pub test_timeout: Duration,
    pub retry_attempts: u32,
    pub parallel_execution: bool,
}
```

**Continuous Integration Integration**:

- Automated test execution on contract updates
- Test result reporting to development team
- Performance regression detection
- Test coverage analysis

### Test Reporting Format

**Executive Summary**:

- Overall integration status (PASS/FAIL)
- Critical issues requiring immediate attention
- Performance metrics and benchmarks
- Production readiness assessment

**Detailed Results**:

- Component-by-component test results
- Transaction-level validation details
- TUI command execution logs
- Error analysis with remediation steps

**Appendices**:

- Complete test execution logs
- Configuration file diffs
- Transaction receipts and traces
- Performance profiling data
