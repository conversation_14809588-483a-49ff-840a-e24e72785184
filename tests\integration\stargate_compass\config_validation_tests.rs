// Comprehensive integration tests for configuration validation and verification system
// Tests the complete configuration update workflow and rollback scenarios

use super::*;
use std::fs;
use std::path::Path;
use tempfile::TempDir;
use ethers::types::Address;
use std::str::FromStr;
use tokio::time::{sleep, Duration};

/// Create a comprehensive test environment with various configuration scenarios
fn create_comprehensive_test_config_dir() -> (TempDir, TestConfigFiles) {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let config_dir = temp_dir.path().join("config");
    fs::create_dir(&config_dir).expect("Failed to create config directory");
    
    let mut test_files = TestConfigFiles::new();
    
    // Create local.toml with valid Stargate Compass configuration
    let local_toml = r#"
[contracts]
stargate_compass_v1 = "******************************************"
other_contract = "******************************************"

[network]
rpc_url = "https://mainnet.base.org"
chain_id = 8453
block_confirmations = 3

[execution]
max_gas_price = 50000000000
slippage_tolerance = 0.005
max_retries = 3

[logging]
level = "info"
file_path = "logs/basilisk.log"
"#;
    
    let local_path = config_dir.join("local.toml");
    fs::write(&local_path, local_toml).expect("Failed to write local.toml");
    test_files.local_path = Some(local_path);
    test_files.original_local_address = Some(Address::from_str("******************************************").unwrap());
    
    // Create testnet.toml with different key name
    let testnet_toml = r#"
[contracts]
stargate_compass = "0x2222222222222222222222222222222222222222"
backup_contract = "0x3333333333333333333333333333333333333333"

[network]
rpc_url = "https://sepolia.base.org"
chain_id = 84532
block_confirmations = 1

[execution]
max_gas_price = 20000000000
slippage_tolerance = 0.01
max_retries = 5

[testing]
enabled = true
mock_transactions = false
"#;
    
    let testnet_path = config_dir.join("testnet.toml");
    fs::write(&testnet_path, testnet_toml).expect("Failed to write testnet.toml");
    test_files.testnet_path = Some(testnet_path);
    test_files.original_testnet_address = Some(Address::from_str("0x2222222222222222222222222222222222222222").unwrap());
    
    // Create anvil.toml with compass_v1 key
    let anvil_toml = r#"
[contracts]
compass_v1 = "0x4444444444444444444444444444444444444444"

[network]
rpc_url = "http://localhost:8545"
chain_id = 8453
fork_block = 12345678

[execution]
max_gas_price = 1000000000
slippage_tolerance = 0.001
"#;
    
    let anvil_path = config_dir.join("anvil.toml");
    fs::write(&anvil_path, anvil_toml).expect("Failed to write anvil.toml");
    test_files.anvil_path = Some(anvil_path);
    test_files.original_anvil_address = Some(Address::from_str("0x4444444444444444444444444444444444444444").unwrap());
    
    // Create a configuration file with missing required sections
    let incomplete_toml = r#"
[contracts]
stargate_compass_v1 = "0x5555555555555555555555555555555555555555"

# Missing network and execution sections
[logging]
level = "debug"
"#;
    
    let incomplete_path = config_dir.join("incomplete.toml");
    fs::write(&incomplete_path, incomplete_toml).expect("Failed to write incomplete.toml");
    test_files.incomplete_path = Some(incomplete_path);
    
    // Create a configuration file with invalid address
    let invalid_address_toml = r#"
[contracts]
stargate_compass_v1 = "invalid_address_format"

[network]
rpc_url = "https://mainnet.base.org"
chain_id = 8453

[execution]
max_gas_price = 50000000000
"#;
    
    let invalid_address_path = config_dir.join("invalid_address.toml");
    fs::write(&invalid_address_path, invalid_address_toml).expect("Failed to write invalid_address.toml");
    test_files.invalid_address_path = Some(invalid_address_path);
    
    // Create a file with malformed TOML
    let malformed_toml = r#"
[contracts
stargate_compass_v1 = "0x6666666666666666666666666666666666666666"
missing_closing_bracket_and_quotes
"#;
    
    let malformed_path = config_dir.join("malformed.toml");
    fs::write(&malformed_path, malformed_toml).expect("Failed to write malformed.toml");
    test_files.malformed_path = Some(malformed_path);
    
    (temp_dir, test_files)
}

/// Test configuration file paths and addresses
#[derive(Debug)]
struct TestConfigFiles {
    local_path: Option<std::path::PathBuf>,
    testnet_path: Option<std::path::PathBuf>,
    anvil_path: Option<std::path::PathBuf>,
    incomplete_path: Option<std::path::PathBuf>,
    invalid_address_path: Option<std::path::PathBuf>,
    malformed_path: Option<std::path::PathBuf>,
    original_local_address: Option<Address>,
    original_testnet_address: Option<Address>,
    original_anvil_address: Option<Address>,
}

impl TestConfigFiles {
    fn new() -> Self {
        Self {
            local_path: None,
            testnet_path: None,
            anvil_path: None,
            incomplete_path: None,
            invalid_address_path: None,
            malformed_path: None,
            original_local_address: None,
            original_testnet_address: None,
            original_anvil_address: None,
        }
    }
    
    fn get_valid_files(&self) -> Vec<std::path::PathBuf> {
        let mut files = Vec::new();
        if let Some(ref path) = self.local_path {
            files.push(path.clone());
        }
        if let Some(ref path) = self.testnet_path {
            files.push(path.clone());
        }
        if let Some(ref path) = self.anvil_path {
            files.push(path.clone());
        }
        files
    }
}

#[tokio::test]
async fn test_comprehensive_configuration_update_workflow() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    let new_address = Address::from_str("0x9999999999999999999999999999999999999999").unwrap();
    
    // Step 1: Validate initial configuration state
    let initial_validation = config_manager.validate_configuration().await.unwrap();
    assert!(initial_validation.valid, "Initial configuration should be valid");
    assert!(initial_validation.contract_address_valid, "Should have valid contract addresses");
    assert!(initial_validation.config_files_found.len() >= 3, "Should find multiple config files");
    
    // Step 2: Perform configuration update
    let update_result = config_manager.update_contract_address(new_address).await.unwrap();
    assert!(update_result.success, "Configuration update should succeed");
    assert!(update_result.backup_created, "Backups should be created");
    assert!(update_result.validation_passed, "Validation should pass after update");
    assert!(!update_result.files_modified.is_empty(), "Should modify files");
    
    // Step 3: Verify all files were updated correctly
    let valid_files = test_files.get_valid_files();
    for file_path in &valid_files {
        let config = config_manager.parse_config_file(file_path).unwrap();
        let actual_address = config_manager.extract_contract_address(&config).unwrap();
        assert_eq!(actual_address, new_address, 
            "Address in {} should be updated", file_path.display());
    }
    
    // Step 4: Validate configuration structure is still intact
    let post_update_validation = config_manager.validate_configuration().await.unwrap();
    assert!(post_update_validation.valid, "Configuration should remain valid after update");
    assert!(post_update_validation.contract_address_valid, "Contract addresses should be valid");
    
    // Step 5: Test the enhanced validate_updates method
    let validation_success = config_manager.validate_updates(&valid_files, new_address).await.unwrap();
    assert!(validation_success, "Enhanced validation should pass");
    
    // Step 6: Verify backups exist and are valid
    for file_path in &valid_files {
        let backup_path = config_manager.find_most_recent_backup(file_path).unwrap();
        assert!(backup_path.is_some(), "Backup should exist for {}", file_path.display());
        
        let backup_path = backup_path.unwrap();
        assert!(backup_path.exists(), "Backup file should exist");
        
        // Verify backup contains original address
        let backup_config = config_manager.parse_config_file(&backup_path).unwrap();
        let backup_address = config_manager.extract_contract_address(&backup_config).unwrap();
        assert_ne!(backup_address, new_address, "Backup should contain original address");
    }
    
    println!("✅ Comprehensive configuration update workflow test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_configuration_rollback_scenarios() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    let new_address = Address::from_str("0x8888888888888888888888888888888888888888").unwrap();
    
    // Step 1: Store original addresses for verification
    let original_addresses: Vec<Address> = test_files.get_valid_files()
        .iter()
        .map(|path| {
            let config = config_manager.parse_config_file(path).unwrap();
            config_manager.extract_contract_address(&config).unwrap()
        })
        .collect();
    
    // Step 2: Perform update to create backups
    let update_result = config_manager.update_contract_address(new_address).await.unwrap();
    assert!(update_result.success, "Initial update should succeed");
    
    // Step 3: Verify addresses were changed
    for file_path in &test_files.get_valid_files() {
        let config = config_manager.parse_config_file(file_path).unwrap();
        let current_address = config_manager.extract_contract_address(&config).unwrap();
        assert_eq!(current_address, new_address, "Address should be updated");
    }
    
    // Step 4: Test configuration restoration
    let restore_result = config_manager.restore_configuration().await;
    assert!(restore_result.is_ok(), "Configuration restoration should succeed");
    
    // Step 5: Verify addresses were restored to original values
    let valid_files = test_files.get_valid_files();
    for (i, file_path) in valid_files.iter().enumerate() {
        let config = config_manager.parse_config_file(file_path).unwrap();
        let restored_address = config_manager.extract_contract_address(&config).unwrap();
        assert_eq!(restored_address, original_addresses[i], 
            "Address in {} should be restored to original", file_path.display());
    }
    
    // Step 6: Verify configuration is still valid after restoration
    let post_restore_validation = config_manager.validate_configuration().await.unwrap();
    assert!(post_restore_validation.valid, "Configuration should be valid after restoration");
    
    println!("✅ Configuration rollback scenarios test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_enhanced_validation_with_error_scenarios() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    let new_address = Address::from_str("0x7777777777777777777777777777777777777777").unwrap();
    
    // Test 1: Validation with non-existent files
    let non_existent_files = vec![
        std::path::PathBuf::from("config/non_existent1.toml"),
        std::path::PathBuf::from("config/non_existent2.toml"),
    ];
    
    let validation_result = config_manager.validate_updates(&non_existent_files, new_address).await.unwrap();
    assert!(!validation_result, "Validation should fail for non-existent files");
    
    // Test 2: Validation with mismatched addresses
    let valid_files = test_files.get_valid_files();
    
    // Update files first
    let _update_result = config_manager.update_contract_address(new_address).await.unwrap();
    
    // Then validate with a different address
    let wrong_address = Address::from_str("******************************************").unwrap();
    let mismatch_validation = config_manager.validate_updates(&valid_files, wrong_address).await.unwrap();
    assert!(!mismatch_validation, "Validation should fail when addresses don't match");
    
    // Test 3: Validation with correct address should pass
    let correct_validation = config_manager.validate_updates(&valid_files, new_address).await.unwrap();
    assert!(correct_validation, "Validation should pass with correct address");
    
    // Test 4: Test validation with corrupted file
    let corrupted_path = temp_dir.path().join("config").join("corrupted.toml");
    fs::write(&corrupted_path, "corrupted content that is not valid TOML").unwrap();
    
    let corrupted_files = vec![corrupted_path];
    let corrupted_validation = config_manager.validate_updates(&corrupted_files, new_address).await.unwrap();
    assert!(!corrupted_validation, "Validation should fail for corrupted files");
    
    println!("✅ Enhanced validation with error scenarios test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_atomic_update_failure_recovery() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    let new_address = Address::from_str("0x6666666666666666666666666666666666666666").unwrap();
    
    // Step 1: Store original state
    let valid_files = test_files.get_valid_files();
    let original_addresses: Vec<Address> = valid_files
        .iter()
        .map(|path| {
            let config = config_manager.parse_config_file(path).unwrap();
            config_manager.extract_contract_address(&config).unwrap()
        })
        .collect();
    
    // Step 2: Make one of the files read-only to simulate failure
    let test_file = &valid_files[0];
    let mut permissions = fs::metadata(test_file).unwrap().permissions();
    permissions.set_readonly(true);
    fs::set_permissions(test_file, permissions).unwrap();
    
    // Step 3: Attempt update (should fail due to read-only file)
    let update_result = config_manager.update_contract_address(new_address).await.unwrap();
    
    // The update might succeed or fail depending on the implementation
    // If it fails, verify that no files were partially updated
    if !update_result.success {
        // Verify that addresses remain unchanged
        for (i, file_path) in valid_files.iter().enumerate() {
            // Skip the read-only file for this check
            if file_path == test_file {
                continue;
            }
            
            let config = config_manager.parse_config_file(file_path).unwrap();
            let current_address = config_manager.extract_contract_address(&config).unwrap();
            assert_eq!(current_address, original_addresses[i], 
                "Address should remain unchanged after failed update");
        }
    }
    
    // Step 4: Restore file permissions
    let mut permissions = fs::metadata(test_file).unwrap().permissions();
    permissions.set_readonly(false);
    fs::set_permissions(test_file, permissions).unwrap();
    
    // Step 5: Retry update (should succeed now)
    let retry_result = config_manager.update_contract_address(new_address).await.unwrap();
    assert!(retry_result.success, "Retry update should succeed after fixing permissions");
    
    // Step 6: Verify all files are now updated
    for file_path in &valid_files {
        let config = config_manager.parse_config_file(file_path).unwrap();
        let current_address = config_manager.extract_contract_address(&config).unwrap();
        assert_eq!(current_address, new_address, 
            "Address should be updated after successful retry");
    }
    
    println!("✅ Atomic update failure recovery test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_concurrent_configuration_operations() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = std::sync::Arc::new(ConfigurationManager::new().unwrap());
    let address1 = Address::from_str("0xAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA").unwrap();
    let address2 = Address::from_str("0xBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB").unwrap();
    
    // Test concurrent updates (this tests the robustness of the system)
    let manager1 = config_manager.clone();
    let manager2 = config_manager.clone();
    
    let handle1 = tokio::spawn(async move {
        sleep(Duration::from_millis(10)).await; // Small delay to create race condition
        manager1.update_contract_address(address1).await
    });
    
    let handle2 = tokio::spawn(async move {
        manager2.update_contract_address(address2).await
    });
    
    let (result1, result2) = tokio::join!(handle1, handle2);
    
    // At least one update should succeed
    let result1 = result1.unwrap().unwrap();
    let result2 = result2.unwrap().unwrap();
    
    assert!(result1.success || result2.success, "At least one concurrent update should succeed");
    
    // Verify final state is consistent
    let final_validation = config_manager.validate_configuration().await.unwrap();
    assert!(final_validation.valid, "Final configuration should be valid");
    
    // All files should have the same address (whichever update succeeded last)
    let valid_files = test_files.get_valid_files();
    if valid_files.len() > 1 {
        let first_config = config_manager.parse_config_file(&valid_files[0]).unwrap();
        let first_address = config_manager.extract_contract_address(&first_config).unwrap();
        
        for file_path in &valid_files[1..] {
            let config = config_manager.parse_config_file(file_path).unwrap();
            let address = config_manager.extract_contract_address(&config).unwrap();
            assert_eq!(address, first_address, "All files should have consistent addresses");
        }
    }
    
    println!("✅ Concurrent configuration operations test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_configuration_validation_edge_cases() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Test 1: Validation with incomplete configuration
    if let Some(incomplete_path) = &test_files.incomplete_path {
        let incomplete_config = config_manager.parse_config_file(incomplete_path).unwrap();
        let validation_result = config_manager.validate_config_structure(&incomplete_config, incomplete_path);
        
        assert!(!validation_result.valid, "Incomplete configuration should be invalid");
        assert!(!validation_result.missing_keys.is_empty(), "Should report missing keys");
    }
    
    // Test 2: Validation with invalid address format
    if let Some(invalid_path) = &test_files.invalid_address_path {
        let invalid_config = config_manager.parse_config_file(invalid_path);
        // This should fail at parsing stage due to invalid address
        match invalid_config {
            Ok(config) => {
                let validation_result = config_manager.validate_config_structure(&config, invalid_path);
                assert!(!validation_result.valid, "Configuration with invalid address should be invalid");
            }
            Err(_) => {
                // Expected - invalid address should cause parsing to fail
            }
        }
    }
    
    // Test 3: Test malformed TOML handling
    if let Some(malformed_path) = &test_files.malformed_path {
        let malformed_result = config_manager.parse_config_file(malformed_path);
        assert!(malformed_result.is_err(), "Malformed TOML should fail to parse");
    }
    
    // Test 4: Test validation with empty configuration directory
    let empty_temp_dir = TempDir::new().expect("Failed to create temp directory");
    let empty_config_dir = empty_temp_dir.path().join("config");
    fs::create_dir(&empty_config_dir).expect("Failed to create empty config directory");
    
    let original_dir_2 = std::env::current_dir().unwrap();
    std::env::set_current_dir(empty_temp_dir.path()).unwrap();
    
    let empty_manager_result = ConfigurationManager::new();
    assert!(empty_manager_result.is_err(), "Should fail to create manager with no config files");
    
    std::env::set_current_dir(original_dir_2).unwrap();
    
    // Test 5: Test comprehensive validation with mixed valid/invalid files
    let comprehensive_validation = config_manager.validate_configuration().await.unwrap();
    
    // Should find valid files but report issues with invalid ones
    assert!(!comprehensive_validation.config_files_found.is_empty(), "Should find some config files");
    assert!(comprehensive_validation.contract_address_valid, "Should find valid contract addresses");
    
    // May have warnings about invalid files
    if !comprehensive_validation.warnings.is_empty() {
        println!("Validation warnings (expected): {:?}", comprehensive_validation.warnings);
    }
    
    println!("✅ Configuration validation edge cases test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_backup_and_restore_comprehensive() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    let new_address = Address::from_str("0xCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC").unwrap();
    
    // Step 1: Create initial backups
    let backup_result = config_manager.backup_configuration().await;
    assert!(backup_result.is_ok(), "Initial backup should succeed");
    
    // Step 2: Verify backups were created
    let valid_files = test_files.get_valid_files();
    for file_path in &valid_files {
        let backup_path = config_manager.find_most_recent_backup(file_path).unwrap();
        assert!(backup_path.is_some(), "Backup should exist for {}", file_path.display());
    }
    
    // Step 3: Perform multiple updates to create multiple backup generations
    let addresses = [
        Address::from_str("0xDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD").unwrap(),
        Address::from_str("0xEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE").unwrap(),
        Address::from_str("0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF").unwrap(),
    ];
    
    for (i, &address) in addresses.iter().enumerate() {
        sleep(Duration::from_millis(100)).await; // Ensure different timestamps
        
        let update_result = config_manager.update_contract_address(address).await.unwrap();
        assert!(update_result.success, "Update {} should succeed", i + 1);
        
        // Verify the address was updated
        for file_path in &valid_files {
            let config = config_manager.parse_config_file(file_path).unwrap();
            let current_address = config_manager.extract_contract_address(&config).unwrap();
            assert_eq!(current_address, address, "Address should be updated to {}", address);
        }
    }
    
    // Step 4: Verify multiple backup generations exist
    for file_path in &valid_files {
        let config_dir = file_path.parent().unwrap();
        let file_name = file_path.file_name().unwrap().to_str().unwrap();
        let backup_pattern = format!("{}.backup.", file_name);
        
        let backup_count = fs::read_dir(config_dir)
            .unwrap()
            .filter_map(|entry| entry.ok())
            .filter(|entry| {
                entry.file_name().to_str()
                    .map(|name| name.starts_with(&backup_pattern))
                    .unwrap_or(false)
            })
            .count();
        
        assert!(backup_count >= 3, "Should have multiple backup generations for {}", file_path.display());
    }
    
    // Step 5: Test restoration to most recent backup
    let restore_result = config_manager.restore_configuration().await;
    assert!(restore_result.is_ok(), "Restoration should succeed");
    
    // Step 6: Verify restoration worked correctly
    // After restoration, addresses should be from the previous generation
    let expected_address = addresses[addresses.len() - 2]; // Second to last address
    
    for file_path in &valid_files {
        let config = config_manager.parse_config_file(file_path).unwrap();
        let restored_address = config_manager.extract_contract_address(&config).unwrap();
        // Note: The exact restored address depends on backup timing and implementation
        // We just verify that it's a valid address and different from the last one
        assert_ne!(restored_address, addresses[addresses.len() - 1], 
            "Restored address should be different from the last update");
    }
    
    // Step 7: Verify configuration is still valid after restoration
    let post_restore_validation = config_manager.validate_configuration().await.unwrap();
    assert!(post_restore_validation.valid, "Configuration should be valid after restoration");
    
    println!("✅ Comprehensive backup and restore test completed successfully");
    
    std::env::set_current_dir(original_dir).unwrap();
}

#[tokio::test]
async fn test_integration_with_real_workflow() {
    let (temp_dir, test_files) = create_comprehensive_test_config_dir();
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    let config_manager = ConfigurationManager::new().unwrap();
    
    // Simulate a real integration test workflow
    println!("🚀 Starting integration test workflow simulation");
    
    // Phase 1: Initial validation
    println!("📋 Phase 1: Initial configuration validation");
    let initial_validation = config_manager.validate_configuration().await.unwrap();
    assert!(initial_validation.valid, "Initial configuration must be valid for integration testing");
    
    // Phase 2: Update to new audited contract address
    println!("🔄 Phase 2: Updating to new audited contract address");
    let new_audited_address = Address::from_str("0x1234567890ABCDEF1234567890ABCDEF12345678").unwrap();
    let update_result = config_manager.update_contract_address(new_audited_address).await.unwrap();
    
    assert!(update_result.success, "Contract address update must succeed");
    assert!(update_result.backup_created, "Backups must be created before update");
    assert!(update_result.validation_passed, "Post-update validation must pass");
    
    // Phase 3: Comprehensive validation of the update
    println!("✅ Phase 3: Comprehensive update validation");
    let valid_files = test_files.get_valid_files();
    let validation_success = config_manager.validate_updates(&valid_files, new_audited_address).await.unwrap();
    assert!(validation_success, "Comprehensive validation must pass");
    
    // Phase 4: Simulate integration test execution
    println!("🧪 Phase 4: Simulating integration test execution");
    
    // Verify each configuration file individually
    for file_path in &valid_files {
        let config = config_manager.parse_config_file(file_path).unwrap();
        
        // Verify structure is intact
        let structure_validation = config_manager.validate_config_structure(&config, file_path);
        assert!(structure_validation.valid, "Configuration structure must remain valid in {}", file_path.display());
        
        // Verify address is correct
        let address = config_manager.extract_contract_address(&config).unwrap();
        assert_eq!(address, new_audited_address, "Contract address must be updated in {}", file_path.display());
        
        // Verify other sections are preserved
        assert!(config.get("network").is_some(), "Network section must be preserved in {}", file_path.display());
        assert!(config.get("execution").is_some(), "Execution section must be preserved in {}", file_path.display());
    }
    
    // Phase 5: Test rollback capability (important for production safety)
    println!("🔄 Phase 5: Testing rollback capability");
    let restore_result = config_manager.restore_configuration().await;
    assert!(restore_result.is_ok(), "Rollback capability must work");
    
    // Verify rollback worked
    let post_rollback_validation = config_manager.validate_configuration().await.unwrap();
    assert!(post_rollback_validation.valid, "Configuration must be valid after rollback");
    
    // Phase 6: Re-apply update for final state
    println!("🔄 Phase 6: Re-applying update for final state");
    let final_update = config_manager.update_contract_address(new_audited_address).await.unwrap();
    assert!(final_update.success, "Final update must succeed");
    
    // Phase 7: Final comprehensive validation
    println!("✅ Phase 7: Final comprehensive validation");
    let final_validation = config_manager.validate_configuration().await.unwrap();
    assert!(final_validation.valid, "Final configuration must be valid");
    assert!(final_validation.contract_address_valid, "Final contract addresses must be valid");
    
    // Verify the system is ready for integration testing
    let current_address = config_manager.get_current_contract_address().await.unwrap();
    assert_eq!(current_address, new_audited_address, "System must be configured with new audited contract");
    
    println!("🎉 Integration test workflow simulation completed successfully!");
    println!("   ✓ Configuration validation system working");
    println!("   ✓ Contract address update system working");
    println!("   ✓ Backup and rollback system working");
    println!("   ✓ Comprehensive validation system working");
    println!("   ✓ System ready for integration testing");
    
    std::env::set_current_dir(original_dir).unwrap();
}