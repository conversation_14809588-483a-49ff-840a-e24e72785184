import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";

const StargateCompassV1Module = buildModule("StargateCompassV1Module", (m) => {
  // Deploy Mock Dependencies first
  const mockPool = m.contract("MockAavePool");
  const mockAaveProvider = m.contract("MockAaveProvider", [mockPool]);
  const mockStargateRouter = m.contract("MockStargateRouter");

  // Deploy the main contract with the addresses of the mocks
  const stargateCompassV1 = m.contract("StargateCompassV1", [
    mockAaveProvider,
    mockStargateRouter,
  ]);

  return { stargateCompassV1 };
});

export default StargateCompassV1Module;
