# Zen Geometer TUI User Guide
## Mission Control Interface

*"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."*

---

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Global Navigation](#global-navigation)
4. [Dashboard Tab - "The Bridge"](#dashboard-tab---the-bridge)
5. [Operations Tab - "The Cockpit"](#operations-tab---the-cockpit)
6. [Systems Tab - "The Engine Room"](#systems-tab---the-engine-room)
7. [Config Tab - "The Control Panel"](#config-tab---the-control-panel)
8. [Strategy Inspector](#strategy-inspector)
9. [Live Data Integration](#live-data-integration)
10. [Keyboard Reference](#keyboard-reference)
11. [Status Indicators](#status-indicators)
12. [Troubleshooting](#troubleshooting)

---

## Overview

The Zen Geometer TUI (Terminal User Interface) is a comprehensive Mission Control center that provides real-time visibility and control over the autonomous trading bot. The interface is designed around four core tabs that represent different aspects of bot operations:

- **📊 Dashboard** - "The Bridge": Aetheric Resonance Engine visualization with advanced analytics
- **🎛️ Operations** - "The Cockpit": Daily operational control and monitoring
- **🔧 Systems** - "The Engine Room": Component health and performance monitoring  
- **⚙️ Config** - "The Control Panel": Safe configuration management with hot-reload

### Design Philosophy

The TUI follows a "Mission Control" approach, providing:
- **Progressive Disclosure**: Information organized from high-level overview to detailed diagnostics
- **Narrative-Driven Interface**: Each tab tells a story about different aspects of the bot
- **Safety-First Design**: Critical operations require confirmation and provide clear feedback
- **Real-time Intelligence**: Live data feeds with color-coded status indicators

---

## Getting Started

### Launch the TUI

```bash
# Start the TUI harness for testing
cargo run --bin tui_harness

# Or launch with the main bot in TUI mode
cargo run -- run --mode simulate --tui
```

### First Launch Checklist

1. **Verify Connections**: Check that all status indicators show green/connected
2. **Review Bot Status**: Ensure the bot is in the expected operational mode
3. **Check System Health**: Verify all components are running normally
4. **Familiarize with Navigation**: Practice switching between tabs and panels

---

## Global Navigation

### Tab Switching
- **`1`** - Dashboard Tab
- **`2`** - Operations Tab  
- **`3`** - Systems Tab
- **`4`** - Config Tab
- **`Tab`** - Cycle through tabs sequentially
- **`Q`** - Quit the TUI

### Universal Controls
- **`↑/↓`** - Navigate within lists and tables
- **`Enter`** - Select item or confirm action
- **`Esc`** - Cancel operation or go back
- **`I`** - Open Strategy Inspector (when opportunity selected)
- **`?`** - Show help (context-sensitive)

---

## Dashboard Tab - "The Bridge"

*Advanced Aetheric Resonance Engine visualization and control center*

### Layout Overview
```
┌─────────────────────────────┬─────────────────────────────────┐
│ 🌐 NETWORK SEISMOLOGY       │ ⌛ CHRONOS SIEVE                │
│ S-P Latency: 152ms ● LIVE   │ Rhythm: HARMONIC ● LIVE         │
│ State: COHERENT             │ Cycle: 12.5 min                │
│ Block Propagation (ms):     │ Power Spectrum:                 │
│   50| ▇                     │  1m |█████                      │
│  100| ████                  │  5m |████████████               │
│  150| █████████             │ 15m |██████                     │
│  200| █████                 │ 60m |██                         │
│  250| █                     │                                │
├─────────────────────────────┼─────────────────────────────────┤
│ 💠 MANDORLA GAUGE           │ 📈 EXECUTION & PNL             │
│ Opp. Flow: 60/min           │ Session PNL: +$1,234.56        │
│ Avg. Geo Score: 0.82        │ Success Rate: 94% (15/16)      │
│ Score Distribution:         │ PNL Trend:         ┌-----┐     │
│ Low |██                     │               -----┘     └-    │
│ Med |█████                  │ Wallet: 2.5 ETH | 10,000 USDC │
│High |████████               │                                │
│Exc  |███                    │                                │
├─────────────────────────────┴─────────────────────────────────┤
│ ⚡ MASTER CONTROL & STATUS                                    │
│ BOT: RUNNING | Uptime: 72h 15m | Active Strategy: Zen Geometer │
│ [ (S)top ] [ (P)ause ] [ (R)estart ] [ (E)mergency Stop ]     │
└───────────────────────────────────────────────────────────────┘
```

### Aetheric Resonance Engine Widgets

#### **🌐 Network Seismology Widget**
- **S-P Latency Display**: Real-time blockchain network timing with data source indicators
  - **🟢 ● LIVE** - Real data from Network Seismology component
  - **🟡 ● CACHED** - Real data but stale (>30 seconds)
  - **🔴 ● MOCK** - Fallback simulation data
- **Network State**: Dynamic classification (COHERENT/STRESSED/SHOCKWAVE)
- **Block Propagation Histogram**: Visual distribution of node report times
- **Interactive Focus**: Tab navigation with blue border highlighting

#### **⌛ Chronos Sieve Widget**
- **Market Rhythm Analysis**: FFT-based market classification (HARMONIC/STABLE/CHAOTIC)
- **Dominant Cycle Display**: Primary market frequency identification
- **Power Spectrum Chart**: Bar chart showing frequency band strengths (1m, 5m, 15m, 60m)
- **Real-time Integration**: Live data from Fractal Analyzer when available

#### **💠 Mandorla Gauge Widget**
- **Opportunity Flow**: Real-time opportunities per minute counter
- **Geometric Score**: Vesica Piscis-based opportunity quality (0.0-1.0 scale)
- **Score Distribution**: Quality breakdown (Low/Med/High/Excellent)
- **Market Richness**: Visual indicator of arbitrage opportunity abundance

#### **📈 Execution & PNL Widget**
- **Session P&L**: Profit/loss in USD and ETH with color coding
- **Success Rate**: Trade execution efficiency with counts
- **PNL Sparkline**: Historical profit/loss trend visualization
- **Wallet Balances**: Current holdings of key assets

#### **⚡ Master Control & Status Footer**
- **Bot Status**: Current operational state with color coding
- **Uptime Display**: Session duration tracking
- **Active Strategy**: Currently running strategy identification
- **Quick Controls**: Master control operations (S/P/R/E)

### Dashboard Navigation
- **`Tab`** - Cycle through widgets (Network → Chronos → Mandorla → Execution)
- **`Enter`** - Drill down to detailed analysis:
  - Network → Systems Tab (Node Connectivity)
  - Execution → Operations Tab (Trade History)
  - Mandorla → Operations Tab (Live Opportunities)
  - Chronos → Info log (FFT analysis details)

### Data Source Transparency
The dashboard provides complete transparency about data sources:
- **🟢 ● LIVE** - Real data from ARE components (fresh, <30 seconds)
- **🟡 ● CACHED** - Real data but stale (>30 seconds old)
- **🔴 ● MOCK** - Fallback simulation data when real data unavailable

---

## Operations Tab - "The Cockpit"

*The command center for daily trading operations*

### Layout Overview
```
┌─────────────────────────────────────────────────────────────────┐
│ 🎛️ Master Control | 🔧 Controls        | 📊 Stats            │
├─────────────────────┬───────────────────────────────────────────┤
│ 📡 Live Activity    │ 🧭 Market State                          │
│ Log                 │                                           │
│                     │ 💰 Wallet Health                         │
│                     │                                           │
├─────────────────────┤ 📊 Performance                           │
│ 📈 Trade History    │ Gauge                                     │
│                     │                                           │
└─────────────────────┴───────────────────────────────────────────┘
```

### Master Control Panel

**Bot Status Display**
- **🟢 RUNNING** - Bot is actively trading
- **🔴 STOPPED** - Bot is halted
- **🟡 PAUSED** - Bot is paused (can resume)
- **🔴 ERROR** - Bot encountered an error

**Control Operations**
- **`S`** - Start/Stop bot (toggles between running and stopped)
- **`P`** - Pause bot (temporary halt, can resume)
- **`R`** - Restart bot (full restart sequence)
- **`E`** - Emergency Stop (immediate halt, requires confirmation)
- **`G`** - Graceful Stop (complete current operations then stop)

### Live Activity Log

Real-time stream of bot activities with color-coded severity:

- **🟢 SUCCESS** - Successful operations (trades, connections)
- **🟡 INFO** - General information (heartbeats, status updates)
- **🟡 WARN** - Warnings (high gas, slippage)
- **🔴 ERROR** - Errors (failed transactions, connection issues)
- **🔵 OPPORTUNITY** - Detected trading opportunities

**Log Controls**
- **`F`** - Toggle filter (cycles through: All → EXECUTOR → GAZE → All)
- **`C`** - Clear current filter
- **`↑/↓`** - Scroll through log history
- **`Page Up/Down`** - Fast scroll

### Trade History Panel

Interactive table showing executed trades:

| Column | Description |
|--------|-------------|
| Time | Execution timestamp |
| Strategy | Strategy that executed the trade |
| Assets | Trading pair (e.g., WETH/USDC) |
| P&L | Profit/Loss in USD |
| Status | Trade outcome with color coding |

**Trade History Controls**
- **`Tab`** - Focus on trade history panel
- **`↑/↓`** - Navigate through trades
- **`Enter`** - View detailed trade information
- **Status Colors**:
  - 🟢 **SUCCESS** - Trade completed successfully
  - 🔴 **FAILED: SLIPPAGE** - Failed due to slippage
  - 🔴 **FAILED: NETWORK** - Failed due to network issues
  - 🟡 **FAILED: GAS** - Failed due to gas issues
  - 🔵 **PENDING** - Trade in progress

### Current State Dashboard

**Market State**
- **Market Regime**: Current market classification (TRENDING/RANGING)
- **Active Strategy**: Currently running strategy (e.g., Zen Geometer)

**Wallet Health**
- **ETH Balance**: Current ETH holdings
- **USDC Balance**: Current USDC holdings
- **Other Assets**: Additional token balances

**Performance Gauge**
- Visual representation of 24h performance
- Green indicates positive performance
- Red indicates negative performance

---

## Systems Tab - "The Engine Room"

*Comprehensive health and performance monitoring*

### Layout Overview
```
┌─────────────────────────────┬─────────────────────────────────┐
│ 🔧 Component Status Matrix  │ 📊 Key Performance Indicators  │
│                             │                                 │
│                             ├─────────────────────────────────┤
│                             │ 🌊 Network Seismology          │
│                             │                                 │
├─────────────────────────────┼─────────────────────────────────┤
│ 📋 Live Log Viewer          │ 🌐 Node Connectivity           │
│                             │                                 │
└─────────────────────────────┴─────────────────────────────────┘
```

### Panel Navigation
- **`Tab`** - Cycle through panels: Logs → Components → Services → Nodes → Logs
- **Current panel is highlighted with a blue background**

### Component Status Matrix

Monitors all analytical pillars from the Aetheric Resonance Engine:

| Component | Function |
|-----------|----------|
| Data Ingestor | Real-time market data ingestion |
| Chronos Sieve | Fractal market state classification |
| Mandorla Gauge | Opportunity depth qualification using Vesica Piscis |
| Network Seismology | Network timing and propagation analysis |
| Strategy Manager | Strategy evaluation and selection |
| Execution Manager | Trade execution and management |
| Risk Manager | Risk assessment and position sizing |
| MEV Protector | MEV detection and protection |

**Status Indicators**
- 🟢 **RUNNING** - Component operating normally
- 🔴 **ERROR** - Component has encountered an error
- 🟡 **STALLED** - Component is not responding

**Component Controls**
- **`↑/↓`** - Navigate through components
- **`Enter`** - Show detailed component information
- **`R`** - Restart selected component

### Key Performance Indicators (KPIs)

Critical metrics from the analytical pillars:

**Network Seismology**
- **S-P Time**: Network propagation delay (lower is better)
- **Resonance State**: Network harmonic state (HARMONIC/DISSONANT)

**Market Analysis**
- **Temporal Cycle**: Current market phase (BULL_TREND/BEAR_TREND/RANGING)
- **Data Ingestion**: Messages per second from data feeds

**System Health**
- **NATS Connection**: Message bus health status
- **RPC Latency**: Blockchain node response time

### Node Connectivity Manager

Monitors all external network connections:

| Node Type | Purpose |
|-----------|---------|
| Base RPC | Base network blockchain access |
| Arbitrum RPC | Arbitrum network access |
| Degen RPC | Degen Chain access |
| WebSocket Feed | Real-time price feeds |
| MEV Relay | MEV protection service |
| Backup RPC | Fallback blockchain access |

**Connection Status**
- 🟢 **CONNECTED** - Node is accessible and responsive
- 🔴 **DISCONNECTED** - Node is not reachable
- 🟡 **RECONNECTING** - Attempting to reconnect

**Node Controls**
- **`↑/↓`** - Navigate through nodes
- **`Enter`** - Ping selected node (latency test)
- **`R`** - Reconnect to selected node

### Enhanced Log Viewer

**Log Controls**
- **`F`** - Toggle log filter
- **`C`** - Clear current filter
- **`/`** - Start search mode
- **`↑/↓`** - Scroll through logs
- **`Page Up/Down`** - Fast scroll
- **`E`** - Export logs to file
- **`X`** - Clear all logs (requires confirmation)

---

## Config Tab - "The Control Panel"

*Safe configuration management with hot-reload capabilities*

### Layout Overview
```
┌─────────────────────────────┬─────────────────────────────────┐
│ 🗂️ Configuration Sections   │ 📝 Parameter Editor             │
│                             │                                 │
│ 📊 [strategy]               │ Section: strategy               │
│ ⚡ [execution]              │                                 │
│ 🌐 [data_sources]           │ min_resonance_threshold: 0.75   │
│ ⚖️ [risk]                   │ max_position_size_usd: 1000.0   │
│ 🔗 [chains]                 │ enabled_strategies: zen_geo...   │
│ 🔧 [system]                 │                                 │
│                             ├─────────────────────────────────┤
│                             │ 🔄 Hot-Reload | ✅ Validation  │
│                             │ Control        | & Status       │
└─────────────────────────────┴─────────────────────────────────┘
```

### Panel Navigation
- **`Tab`** - Cycle through panels: Sections → Parameters → Details → Sections

### Configuration Sections

**Available Sections**
- **📊 [strategy]** - Trading strategy configuration
- **⚡ [execution]** - Transaction execution parameters
- **🌐 [data_sources]** - Data feed and RPC settings
- **⚖️ [risk]** - Risk management parameters
- **🔗 [chains]** - Blockchain network configuration
- **🔧 [system]** - System-level settings

**Section Navigation**
- **`↑/↓`** - Navigate through sections
- **`Enter`** - Select section and move to parameters

### Parameter Editor

**Parameter Display**
Each parameter shows:
- **Parameter Name**: Configuration key
- **Current Value**: Current setting (highlighted)
- **Description**: Explanation of what the parameter controls

**Example Parameters by Section**

**Strategy Section**
- `min_resonance_threshold`: Minimum score to execute trade (0.0-1.0)
- `max_position_size_usd`: Maximum position size in USD
- `enabled_strategies`: Comma-separated strategy list

**Execution Section**
- `gas_price_multiplier`: Gas price multiplier for transactions
- `slippage_tolerance`: Maximum allowed slippage (0.5%)
- `max_retry_attempts`: Maximum transaction retry attempts

**Data Sources Section**
- `base_rpc_url`: Base network RPC endpoint
- `websocket_enabled`: Enable WebSocket data feeds
- `refresh_interval_ms`: Data refresh interval in milliseconds

**Parameter Controls**
- **`↑/↓`** - Navigate through parameters
- **`Enter`** - Start editing selected parameter
- **`Esc`** - Cancel editing

### Hot-Reload Control & Validation

**Available Actions**
- **`S`** - Save & Hot-Reload (applies changes without restart)
- **`R`** - Reset to Defaults (reverts all changes)
- **`L`** - Load Profile (switch configuration profile)
- **`V`** - Validate Config (check for errors)

**Status Indicators**
- **Configuration Status**:
  - 🟢 **CLEAN** - No unsaved changes
  - 🟡 **MODIFIED** - Unsaved changes present
- **Validation Status**:
  - 🟢 **0 Errors** - Configuration is valid
  - 🔴 **X Errors** - Configuration has validation errors
- **Active Profile**: Currently loaded configuration file

**Safety Features**
- **Real-time Validation**: Parameters are validated as you edit
- **Confirmation Dialogs**: Critical operations require confirmation
- **Rollback Capability**: Can revert to last known good configuration
- **Atomic Updates**: Hot-reload is atomic (all or nothing)

---

## Dashboard Tab - "The Bridge"

*High-level situational awareness and system overview*

### Layout Overview
```
┌─────────────────────────────┬─────────────────────────────────┐
│ 📊 Key Metrics              │ 🔧 System Health               │
├─────────────────────────────┴─────────────────────────────────┤
│ ⚖️ Risk Metrics & Portfolio Overview                          │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

### Key Metrics Panel
- **24h P&L**: Total profit/loss for the last 24 hours
- **Trades**: Number of trades executed in 24 hours
- **Active Strategies**: Currently running strategies
- **Gas Cost 24h**: Total gas costs for the last 24 hours

### System Health Panel
- **RPC Node**: Blockchain connection status
- **NATS Bus**: Message queue health
- **Database**: Database connection status
- **Redis**: Cache system status

### Risk Metrics Panel
- **Portfolio Value**: Total portfolio value in USD
- **Max Drawdown**: Maximum portfolio decline percentage
- **Kelly Fraction**: Current position sizing multiplier
- **Position Size**: Current position size in USD
- **Risk Level**: Overall risk assessment (LOW/MEDIUM/HIGH)

---

## Strategy Inspector

*Deep analysis tool for understanding Aetheric Resonance Engine decisions*

### Overview
The Strategy Inspector provides complete transparency into how the Zen Geometer evaluates trading opportunities. It shows the detailed breakdown of each analytical pillar's contribution to the final Aetheric Resonance Score.

### Accessing the Inspector
1. **Navigate** to any tab with opportunities (Dashboard, Operations)
2. **Select** an opportunity (if applicable)
3. **Press `I`** to open the Strategy Inspector modal
4. **Review** the detailed analysis and financial projections

### Inspector Interface Layout
```
┌─────────────────────────────────────────────────────────────────────────┐
│ 🔍 Strategy Inspector - Aetheric Resonance Analysis                     │
├─────────────────────────────────────────────────────────────────────────┤
│ 📋 Opportunity Details                                                  │
│ ID: 550e8400-e29b-41d4-a716-446655440000                              │
│ Summary: ARB: 1.5 ETH -> WETH -> USDC -> ETH (Base -> Arbitrum)        │
│ Status: STAGED | Risk Level: LOW                                       │
├─────────────────────────────────────────────────────────────────────────┤
│ ⚖️ Score Breakdown Analysis                                             │
│ Pillar           │Metric        │Value   │Score │Weight│Explanation     │
│ 💠 Mandorla Gauge│Geometric Score│0.8245  │0.3298│40.0% │Vesica Piscis...│
│ ⌛ Chronos Sieve │Rhythm Stability│0.7834  │0.2350│30.0% │Market phase... │
│ 🌊 Network Seismo│Coherence Score│0.9123  │0.2737│30.0% │Network state...│
├─────────────────────────────┬───────────────────────────────────────────┤
│ 💰 Financial Analysis       │ 🎛️ Controls & Analysis                   │
│ Final Score: 0.8385         │ Controls: [ESC] Close | [E] Execute      │
│ Confidence: 92.3%           │          [R] Reject                      │
│ Profit Est: $125.50         │                                          │
│ Gas Est: $28.75             │ ✅ Strengths:                            │
│ Net Profit: $96.75          │ • High Coherence Score: 0.91            │
│                             │ • High Geometric Score: 0.82            │
│                             │                                          │
│                             │ ⚠️ Risk Factors:                         │
│                             │ • Low Rhythm Stability: 0.78            │
└─────────────────────────────┴───────────────────────────────────────────┘
```

### Inspector Components

#### **📋 Opportunity Details Header**
- **Unique ID**: UUID for tracking and reference
- **Summary**: Human-readable opportunity description
- **Status**: Current execution state (STAGED/EXECUTING/COMPLETED/FAILED/REJECTED)
- **Risk Level**: Overall assessment (LOW/MEDIUM/HIGH)

#### **⚖️ Score Breakdown Analysis**
Detailed table showing each Aetheric Resonance Engine pillar:

| Pillar | Weight | Function |
|--------|--------|----------|
| **💠 Mandorla Gauge** | 40% | Vesica Piscis geometric scoring with path efficiency |
| **⌛ Chronos Sieve** | 30% | Market rhythm stability and temporal harmony |
| **🌊 Network Seismology** | 30% | S-P wave timing and network coherence |

Each row shows:
- **Metric Name**: Specific measurement (e.g., "Geometric Score")
- **Value**: Raw metric value with proper formatting
- **Score**: Weighted contribution to final score
- **Weight**: Percentage weight in final calculation
- **Explanation**: Human-readable description of the analysis

#### **💰 Financial Analysis**
- **Final Score**: Combined Aetheric Resonance Score (0.0-1.0)
- **Confidence**: Statistical confidence in the analysis
- **Profit Estimate**: Expected profit in USD
- **Gas Estimate**: Expected gas costs in USD
- **Net Profit**: Profit minus gas costs

#### **🎛️ Controls & Analysis**
- **Interactive Controls**: Execute, reject, or close the inspector
- **Risk Factors**: Automatically identified weaknesses
- **Strengths**: Automatically identified advantages

### Inspector Controls
- **`ESC`** - Close the inspector and return to main interface
- **`E`** - Execute the opportunity (logs action and closes inspector)
- **`R`** - Reject the opportunity (logs rejection and closes inspector)

### Educational Value
The Strategy Inspector serves as an educational tool, helping operators understand:
- **How each pillar contributes** to the final decision
- **What makes opportunities risky** or attractive
- **The mathematical basis** for autonomous decisions
- **When manual override** might be appropriate

---

## Live Data Integration

*Real-time market data consumption and processing*

### Overview
The Zen Geometer integrates live market data from multiple sources through a sophisticated NATS-based pipeline, providing real-time updates to all dashboard components and analysis engines.

### Data Sources
- **🌊 Network Seismology**: Live S-P wave analysis and blockchain timing
- **⌛ Chronos Sieve**: Real-time FFT market rhythm analysis
- **💠 Mandorla Gauge**: Live geometric opportunity scoring
- **📈 Market Data**: Real-time price feeds and trading data
- **🔗 Network Monitoring**: Live block data and gas price tracking

### Data Pipeline Architecture
```
NATS Message Bus → Data Ingestor → TUI Updates
     ↓                  ↓              ↓
Market Feeds    →  ARE Components → Dashboard Widgets
Network Data    →  Analysis Engine → Status Indicators
Gas Prices      →  Risk Manager   → Activity Logs
```

### NATS Subjects
The system subscribes to multiple data streams:
- **`market.>`** - All market data (trades, prices, volumes)
- **`network.blocks`** - New block notifications
- **`gas.prices`** - Gas price updates
- **`are.temporal_harmonics`** - Chronos Sieve FFT analysis
- **`are.network_seismology`** - Network timing analysis
- **`are.geometric_score`** - Mandorla Gauge opportunity scoring
- **`execution.trades`** - Trade execution results
- **`risk.alerts`** - Risk management alerts

### Data Quality Indicators
All widgets show real-time data source status:
- **🟢 ● LIVE** - Fresh data from ARE components (<30 seconds)
- **🟡 ● CACHED** - Real data but stale (>30 seconds old)
- **🔴 ● MOCK** - Simulation data when real feeds unavailable

### Testing Live Data
```bash
# Start infrastructure
docker-compose -f docker-compose.infrastructure.yml up -d nats

# Test data pipeline
./scripts/test_live_data_integration.sh

# Start components
cargo run --bin data_ingestor  # Terminal 1
cargo run --bin tui_harness    # Terminal 2

# Publish test data
nats pub are.temporal_harmonics '{"market_rhythm_stability": 0.85}'
nats pub market.trades '{"pair": "ETH/USDC", "price": 2456.78}'
```

### Expected Results
- **Dashboard**: Data indicators change from 🔴 MOCK to 🟢 LIVE
- **Operations**: Activity log shows live data entries
- **Systems**: Data Ingestor component shows RUNNING status
- **Real-time Updates**: Sparklines and charts update with live data

---

## Keyboard Reference

### Global Controls
| Key | Action |
|-----|--------|
| `Q` | Quit TUI |
| `1-4` | Switch to tab (1=Dashboard, 2=Operations, 3=Systems, 4=Config) |
| `Tab` | Cycle through tabs or panels |
| `↑/↓` | Navigate lists/tables |
| `Enter` | Select/Confirm |
| `Esc` | Cancel/Back |
| `I` | Open Strategy Inspector (when opportunity available) |

### Dashboard Tab
| Key | Action |
|-----|--------|
| `Tab` | Cycle through ARE widgets (Network → Chronos → Mandorla → Execution) |
| `Enter` | Drill down to detailed analysis |
| `I` | Open Strategy Inspector for selected opportunity |

### Operations Tab
| Key | Action |
|-----|--------|
| `S` | Start/Stop bot |
| `P` | Pause bot |
| `R` | Restart bot |
| `E` | Emergency stop |
| `G` | Graceful stop |
| `F` | Filter activity log |
| `C` | Clear log filter |

### Systems Tab
| Key | Action |
|-----|--------|
| `Tab` | Switch between panels |
| `R` | Restart component/service/reconnect node |
| `S` | Start service |
| `K` | Stop service |
| `F` | Filter logs |
| `C` | Clear log filter |
| `/` | Search logs |
| `E` | Export logs |
| `X` | Clear all logs |

### Config Tab
| Key | Action |
|-----|--------|
| `S` | Save & Hot-Reload |
| `R` | Reset to defaults |
| `L` | Load profile |
| `V` | Validate configuration |
| `Enter` | Edit parameter |

### Strategy Inspector
| Key | Action |
|-----|--------|
| `I` | Open inspector (from any tab with opportunities) |
| `ESC` | Close inspector |
| `E` | Execute selected opportunity |
| `R` | Reject selected opportunity |

### Live Data Integration
| Key | Action |
|-----|--------|
| All existing keys work with live data |
| Data source indicators show real-time status |
| 🟢 ● LIVE | Real data from ARE components |
| 🟡 ● CACHED | Real data but stale |
| 🔴 ● MOCK | Simulation data |

---

## Status Indicators

### Color Coding System

**Green (🟢)** - Healthy/Success
- Components running normally
- Successful trades
- Connected nodes
- Valid configuration
- Live data feeds (🟢 ● LIVE)

**Yellow (🟡)** - Warning/Caution
- Paused operations
- High gas costs
- Reconnecting nodes
- Modified configuration
- Stale data feeds (🟡 ● CACHED)

**Red (🔴)** - Error/Critical
- Failed operations
- Component errors
- Disconnected nodes
- Invalid configuration
- Mock/simulation data (🔴 ● MOCK)

**Blue (🔵)** - Information/Active
- Selected items
- Pending operations
- Detected opportunities
- Active focus
- Dashboard widget focus

**Cyan (🔵)** - Strategy Inspector
- Inspector modal borders
- Opportunity summaries
- Final scores
- ARE component names

**Gray** - Inactive/Disabled
- Stopped components
- Disabled features
- Historical data
- Rejected opportunities

### Status Symbols

| Symbol | Meaning |
|--------|---------|
| ● | Status indicator (colored) |
| ▶ | Running/Active |
| ⏸ | Paused |
| ⏹ | Stopped |
| ⚠ | Warning |
| ✗ | Error |
| ✓ | Success |
| ⟳ | Reconnecting/Restarting |

---

## Troubleshooting

### Common Issues

**TUI Won't Start**
```bash
# Check if required services are running
cargo run -- preflight

# Verify configuration
cargo run -- config validate

# Check for port conflicts
netstat -tulpn | grep :4222  # NATS port
```

**Bot Status Shows Error**
1. Check the Operations tab Live Activity Log for error details
2. Review Systems tab Component Status Matrix for failed components
3. Verify network connectivity in Node Connectivity Manager
4. Check configuration validity in Config tab

**No Data Appearing**
1. Verify NATS connection in Systems tab
2. Check Data Ingestor status in Component Status Matrix
3. Confirm RPC node connectivity
4. Review log filters (press `C` to clear filters)

**Configuration Changes Not Applied**
1. Ensure configuration is valid (press `V` in Config tab)
2. Use `S` to Save & Hot-Reload
3. Check validation status for errors
4. Verify you have write permissions to config files

**High CPU/Memory Usage**
1. Monitor component resource usage in Systems tab
2. Check for stuck components (STALLED status)
3. Restart problematic components with `R`
4. Review log files for error patterns

### Emergency Procedures

**Emergency Stop**
1. Press `2` to go to Operations tab
2. Press `E` for Emergency Stop
3. Confirm the action when prompted
4. Verify bot status shows "ERROR: EMERGENCY STOP"

**System Recovery**
1. Use `R` in Operations tab to restart the bot
2. Check Systems tab for component health
3. Verify all nodes are connected
4. Review configuration for any issues

**Data Recovery**
1. Export logs with `E` in Systems tab before clearing
2. Use `X` to clear corrupted log data
3. Restart data ingestion components
4. Monitor for normal operation resumption

### Performance Optimization

**Reducing Latency**
- Monitor S-P Time in Systems tab KPIs
- Check RPC latency in Node Connectivity
- Consider switching to faster RPC endpoints
- Verify network connection quality

**Memory Management**
- Monitor component memory usage
- Clear logs periodically with `X`
- Restart high-memory components
- Check for memory leaks in error logs

**CPU Optimization**
- Monitor component CPU usage
- Adjust data refresh intervals in config
- Disable unnecessary features
- Optimize strategy parameters

---

## Advanced Features

### Simulation Mode
When running in simulation mode:
- All trades are simulated (no real money at risk)
- Full system functionality for learning
- Trade history shows simulated results
- Perfect for training and testing

### Hot Configuration Reload
The Config tab supports hot-reloading:
- Changes applied without restarting the bot
- Atomic updates (all changes applied together)
- Automatic validation before applying
- Rollback capability if issues occur

### Multi-Chain Monitoring
Systems tab shows status for all supported chains:
- Base (L2) - Primary settlement layer
- Arbitrum - Alternative L2 for opportunities
- Degen Chain (L3) - Emerging market inefficiencies
- Real-time latency monitoring for all chains

### Aetheric Resonance Engine Integration
The TUI provides direct visibility into the ARE components:
- Chronos Sieve fractal analysis results
- Mandorla Gauge opportunity scoring
- Network Seismology timing data
- Composite resonance scores

---

## Support and Resources

### Documentation
- `README.md` - Project overview and setup
- `docs/USER_GUIDE.md` - Comprehensive user documentation
- `docs/strategies/` - Strategy-specific guides
- `PRODUCTION_DEPLOYMENT_GUIDE.md` - Production setup

### Command Line Tools
```bash
# Configuration management
cargo run -- config validate
cargo run -- config show

# System diagnostics
cargo run -- preflight
cargo run -- health-check

# Strategy testing
cargo run -- run --mode simulate
cargo run -- run --mode shadow
```

### Getting Help
1. Use the TUI's built-in help (`?` key)
2. Check the logs in Systems tab for error details
3. Review the configuration in Config tab
4. Consult the documentation in `docs/`
5. Run preflight checks for system validation

---

*"In the dance of markets, the Zen Geometer finds harmony through perfect information and precise action."*

**End of TUI User Guide**