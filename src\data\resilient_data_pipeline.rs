// MISSION: Resilient Data Pipeline - Bulletproof Data Ingestion and Processing
// WHY: Ensure continuous data flow even when sources fail or become unreliable
// HOW: Timeout protection, circuit breakers, stale data detection, graceful degradation

use anyhow::{Context, Result};
use async_nats::Client as NatsClient;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::{Mutex, RwLock};
use tokio::time::timeout;
use tracing::{debug, error, info, warn};

use crate::error::BasiliskError;
use crate::execution::circuit_breaker::CircuitBreaker;
use crate::shared_types::degradation::{DataFreshness, DegradationLevel, DegradationState};

/// Configuration for data pipeline resilience
#[derive(Debug, Clone)]
pub struct DataPipelineConfig {
    pub data_timeout: Duration,
    pub staleness_threshold: Duration,
    pub max_retry_attempts: u32,
    pub circuit_breaker_threshold: u32,
    pub health_check_interval: Duration,
}

impl Default for DataPipelineConfig {
    fn default() -> Self {
        Self {
            data_timeout: Duration::from_secs(30),
            staleness_threshold: Duration::from_secs(60),
            max_retry_attempts: 3,
            circuit_breaker_threshold: 5,
            health_check_interval: Duration::from_secs(10),
        }
    }
}

/// Data source health tracking
#[derive(Debug, Clone)]
pub struct DataSourceHealth {
    pub source_name: String,
    pub last_success: Option<Instant>,
    pub last_failure: Option<Instant>,
    pub consecutive_failures: u32,
    pub success_rate: f64,
    pub average_latency: Duration,
    pub is_healthy: bool,
}

impl DataSourceHealth {
    pub fn new(source_name: String) -> Self {
        Self {
            source_name,
            last_success: None,
            last_failure: None,
            consecutive_failures: 0,
            success_rate: 1.0,
            average_latency: Duration::from_millis(0),
            is_healthy: true,
        }
    }

    pub fn record_success(&mut self, latency: Duration) {
        self.last_success = Some(Instant::now());
        self.consecutive_failures = 0;
        self.is_healthy = true;
        
        // Update average latency with exponential moving average
        if self.average_latency.is_zero() {
            self.average_latency = latency;
        } else {
            let current_ms = self.average_latency.as_millis() as f64;
            let new_ms = latency.as_millis() as f64;
            let updated_ms = 0.8 * current_ms + 0.2 * new_ms;
            self.average_latency = Duration::from_millis(updated_ms as u64);
        }
    }

    pub fn record_failure(&mut self) {
        self.last_failure = Some(Instant::now());
        self.consecutive_failures += 1;
        
        // Mark as unhealthy after 3 consecutive failures
        if self.consecutive_failures >= 3 {
            self.is_healthy = false;
        }
    }
}

/// Resilient data pipeline manager
pub struct ResilientDataPipeline {
    config: DataPipelineConfig,
    circuit_breaker: Arc<CircuitBreaker>,
    nats_client: NatsClient,
    
    // Data source health tracking
    source_health: Arc<RwLock<HashMap<String, DataSourceHealth>>>,
    
    // Data freshness tracking
    data_freshness: Arc<RwLock<HashMap<String, DataFreshness>>>,
    
    // System degradation state
    degradation_state: Arc<Mutex<DegradationState>>,
}

impl ResilientDataPipeline {
    pub fn new(
        config: DataPipelineConfig,
        circuit_breaker: Arc<CircuitBreaker>,
        nats_client: NatsClient,
    ) -> Self {
        Self {
            config,
            circuit_breaker,
            nats_client,
            source_health: Arc::new(RwLock::new(HashMap::new())),
            data_freshness: Arc::new(RwLock::new(HashMap::new())),
            degradation_state: Arc::new(Mutex::new(DegradationState::new(
                DegradationLevel::Operational,
                "Data pipeline starting up".to_string(),
            ))),
        }
    }

    /// Execute a data operation with full resilience protection
    pub async fn execute_with_protection<T, F, Fut>(
        &self,
        source_name: &str,
        operation: F,
    ) -> Result<T, BasiliskError>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, BasiliskError>>,
    {
        let start_time = Instant::now();
        
        // Check if source is healthy
        {
            let health_map = self.source_health.read().await;
            if let Some(health) = health_map.get(source_name) {
                if !health.is_healthy {
                    return Err(BasiliskError::ServiceDegraded {
                        service_name: source_name.to_string(),
                        reason: format!("Data source unhealthy after {} consecutive failures", health.consecutive_failures),
                    });
                }
            }
        }

        // Execute with circuit breaker and timeout protection
        let result = self.circuit_breaker.execute(source_name, || async {
            timeout(self.config.data_timeout, operation()).await
                .map_err(|_| BasiliskError::Timeout {
                    operation: format!("Data operation: {}", source_name),
                })?
        }).await;

        // Record the result
        let latency = start_time.elapsed();
        match &result {
            Ok(_) => {
                self.record_source_success(source_name, latency).await;
                self.update_data_freshness(source_name).await;
            }
            Err(e) => {
                self.record_source_failure(source_name).await;
                warn!("Data operation failed for {}: {}", source_name, e);
            }
        }

        result
    }

    /// Check if data is stale and needs refresh
    pub async fn is_data_stale(&self, data_type: &str) -> bool {
        let freshness_map = self.data_freshness.read().await;
        if let Some(freshness) = freshness_map.get(data_type) {
            freshness.is_stale()
        } else {
            true // No data available, consider it stale
        }
    }

    /// Get the current degradation state of the data pipeline
    pub async fn get_degradation_state(&self) -> DegradationState {
        self.degradation_state.lock().await.clone()
    }

    /// Force degradation to a specific level
    pub async fn force_degradation(&self, level: DegradationLevel, reason: String) {
        let mut state = self.degradation_state.lock().await;
        *state = DegradationState::new(level, reason)
            .with_affected_services(vec!["DataPipeline".to_string()])
            .with_fallback_actions(vec!["Using cached/default data".to_string()]);
        
        warn!("Data pipeline degradation forced: {:?}", state);
    }

    /// Start health monitoring for all data sources
    pub async fn start_health_monitoring(&self) {
        let source_health = Arc::clone(&self.source_health);
        let degradation_state = Arc::clone(&self.degradation_state);
        let check_interval = self.config.health_check_interval;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(check_interval);
            
            loop {
                interval.tick().await;
                
                let health_map = source_health.read().await;
                let mut unhealthy_sources = Vec::new();
                let mut total_sources = 0;
                let mut healthy_sources = 0;

                for (name, health) in health_map.iter() {
                    total_sources += 1;
                    if health.is_healthy {
                        healthy_sources += 1;
                    } else {
                        unhealthy_sources.push(name.clone());
                    }
                }

                // Determine overall system health
                let health_ratio = if total_sources > 0 {
                    healthy_sources as f64 / total_sources as f64
                } else {
                    1.0
                };

                let new_degradation_level = match health_ratio {
                    r if r >= 0.8 => DegradationLevel::Operational,
                    r if r >= 0.6 => DegradationLevel::MinorDegradation,
                    r if r >= 0.4 => DegradationLevel::SafeMode,
                    r if r >= 0.2 => DegradationLevel::Emergency,
                    _ => DegradationLevel::Shutdown,
                };

                let mut state = degradation_state.lock().await;
                if state.level != new_degradation_level {
                    let reason = if unhealthy_sources.is_empty() {
                        "All data sources healthy".to_string()
                    } else {
                        format!("Unhealthy data sources: {}", unhealthy_sources.join(", "))
                    };

                    *state = DegradationState::new(new_degradation_level, reason)
                        .with_affected_services(unhealthy_sources.clone())
                        .with_auto_recovery(true);

                    info!("Data pipeline health status updated: {:?}", state);
                }
            }
        });
    }

    /// Publish data with staleness checking
    pub async fn publish_with_staleness_check<T: serde::Serialize>(
        &self,
        topic: &str,
        data: &T,
        data_type: &str,
    ) -> Result<(), BasiliskError> {
        // Add timestamp to data if possible
        let timestamped_data = serde_json::json!({
            "data": data,
            "timestamp": SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            "data_type": data_type,
        });

        let payload = serde_json::to_vec(&timestamped_data)
            .context("Failed to serialize data with timestamp")?;

        self.nats_client.publish(topic.to_string(), payload.into()).await
            .context("Failed to publish data to NATS")?;

        // Update data freshness
        self.update_data_freshness(data_type).await;

        Ok(())
    }

    /// Get health status for all data sources
    pub async fn get_all_source_health(&self) -> HashMap<String, DataSourceHealth> {
        self.source_health.read().await.clone()
    }

    /// Register a new data source for monitoring
    pub async fn register_data_source(&self, source_name: String) {
        let mut health_map = self.source_health.write().await;
        health_map.insert(source_name.clone(), DataSourceHealth::new(source_name.clone()));
        
        let mut freshness_map = self.data_freshness.write().await;
        freshness_map.insert(source_name.clone(), DataFreshness::new(self.config.staleness_threshold));
        
        debug!("Registered data source for monitoring: {}", source_name);
    }

    /// Record successful data operation
    async fn record_source_success(&self, source_name: &str, latency: Duration) {
        let mut health_map = self.source_health.write().await;
        if let Some(health) = health_map.get_mut(source_name) {
            health.record_success(latency);
        } else {
            // Auto-register new source
            let mut new_health = DataSourceHealth::new(source_name.to_string());
            new_health.record_success(latency);
            health_map.insert(source_name.to_string(), new_health);
        }
    }

    /// Record failed data operation
    async fn record_source_failure(&self, source_name: &str) {
        let mut health_map = self.source_health.write().await;
        if let Some(health) = health_map.get_mut(source_name) {
            health.record_failure();
        } else {
            // Auto-register new source
            let mut new_health = DataSourceHealth::new(source_name.to_string());
            new_health.record_failure();
            health_map.insert(source_name.to_string(), new_health);
        }
    }

    /// Update data freshness timestamp
    async fn update_data_freshness(&self, data_type: &str) {
        let mut freshness_map = self.data_freshness.write().await;
        if let Some(freshness) = freshness_map.get_mut(data_type) {
            freshness.update();
        } else {
            // Auto-register new data type
            freshness_map.insert(data_type.to_string(), DataFreshness::new(self.config.staleness_threshold));
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_data_source_health() {
        let mut health = DataSourceHealth::new("test_source".to_string());
        
        // Initially healthy
        assert!(health.is_healthy);
        assert_eq!(health.consecutive_failures, 0);
        
        // Record failures
        health.record_failure();
        health.record_failure();
        assert!(health.is_healthy); // Still healthy after 2 failures
        
        health.record_failure();
        assert!(!health.is_healthy); // Unhealthy after 3 failures
        
        // Recovery
        health.record_success(Duration::from_millis(100));
        assert!(health.is_healthy);
        assert_eq!(health.consecutive_failures, 0);
    }

    #[test]
    fn test_data_freshness() {
        let mut freshness = DataFreshness::new(Duration::from_millis(50)); // Shorter threshold
        
        // Initially fresh
        assert!(!freshness.is_stale());
        
        // Wait for staleness - use longer sleep to ensure timing
        std::thread::sleep(Duration::from_millis(100)); // Double the threshold
        assert!(freshness.is_stale());
        
        // Update freshness
        freshness.update();
        assert!(!freshness.is_stale());
    }
}