use chrono::{DateTime, Utc, Duration};
use serde::{Deserialize, Serialize};
use std::collections::{BinaryHeap, HashMap, HashSet};
use std::sync::Mutex;
use ethers::types::{Signature, Address};
use ethers::utils::hash_message;
use tracing::{info, warn, error};
use tokio::sync::mpsc;
use tokio_stream::StreamExt;

use crate::shared_types::{SignedDirective, DirectiveCommand};

pub struct DirectiveManager {
    nats_client: async_nats::Client,
    command_tx: mpsc::Sender<DirectiveCommand>,
    authorized_operators: HashSet<Address>,
    used_nonces: tokio::sync::Mutex<HashMap<Address, HashSet<u64>>>,
}

impl DirectiveManager {
    pub fn new(nats_client: async_nats::Client, command_tx: mpsc::Sender<DirectiveCommand>, authorized_operators: HashSet<Address>) -> Self {
        Self {
            nats_client,
            command_tx,
            authorized_operators,
            used_nonces: Default::default(),
        }
    }

    pub async fn start(&mut self) -> crate::error::Result<()> {
        info!("Starting Directive Manager");

        let mut subscriber = self.nats_client.subscribe("control.directives").await
            .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to subscribe to NATS topic: {}", e) })?;

        loop {
            tokio::select! {
                Some(msg) = subscriber.next() => {
                    match serde_json::from_slice::<SignedDirective>(&msg.payload) {
                        Ok(signed_directive) => {
                            let is_dry_run = signed_directive.is_dry_run;
                            if let Err(e) = self.process_signed_directive(signed_directive, is_dry_run).await {
                                error!("Failed to process signed directive: {}", e);
                            }
                        },
                        Err(e) => {
                            error!("Failed to parse signed directive: {}", e);
                        }
                    }
                }
                _ = tokio::time::sleep(tokio::time::Duration::from_secs(1)) => {
                    // Process directives periodically
                    // This part is now handled by process_signed_directive which directly executes
                    // the command, so this periodic check might be removed or repurposed.
                }
            }
        }
    }

    async fn process_signed_directive(&self, signed_directive: SignedDirective, is_dry_run: bool) -> crate::error::Result<()> {
        // Extract required fields, handling Option types
        let signer_str = signed_directive.signer.as_ref()
            .ok_or_else(|| crate::error::BasiliskError::Security { message: "Missing signer".to_string() })?;
        let nonce = signed_directive.nonce
            .ok_or_else(|| crate::error::BasiliskError::Security { message: "Missing nonce".to_string() })?;
        let directive_json = signed_directive.directive_json.as_ref()
            .ok_or_else(|| crate::error::BasiliskError::Security { message: "Missing directive JSON".to_string() })?;
        let signature_str = signed_directive.signature.as_ref()
            .ok_or_else(|| crate::error::BasiliskError::Security { message: "Missing signature".to_string() })?;

        // Parse signer address
        let signer_address: Address = signer_str.parse()
            .map_err(|e| crate::error::BasiliskError::Security { message: format!("Invalid signer address: {}", e) })?;

        // 1. Check if the signer is an authorized operator
        if !self.authorized_operators.contains(&signer_address) {
            warn!("Received directive from unauthorized signer: {}", signer_address);
            return Err(crate::error::BasiliskError::Security { message: "Unauthorized signer".to_string() });
        }

        // 2. Verify nonce to prevent replay attacks
        let mut used_nonces = self.used_nonces.lock().await;
        if used_nonces.entry(signer_address).or_default().contains(&nonce) {
            warn!("Replay attack detected for signer {} with nonce {}", signer_address, nonce);
            return Err(crate::error::BasiliskError::Security { message: "Replay attack detected".to_string() });
        }

        // 3. Re-create the signed message from the command payload
        let message_hash = hash_message(directive_json.as_bytes());

        // 4. Verify the signature
        let signature = signature_str.parse::<Signature>()
            .map_err(|e| crate::error::BasiliskError::Security { message: format!("Invalid signature format: {}", e) })?;
        if signature.verify(message_hash, signer_address).is_err() {
            warn!("Invalid signature for directive from signer {}", signer_address);
            return Err(crate::error::BasiliskError::Security { message: "Invalid signature".to_string() });
        }

        // If all checks pass, add nonce to used nonces
        used_nonces.get_mut(&signer_address).unwrap().insert(nonce);

        // Deserialize and execute the command
        let directive_command: DirectiveCommand = serde_json::from_str(directive_json)?;

        if is_dry_run {
            tracing::info!("DRY RUN: Received directive for dry run: {:?}", directive_command);
            tracing::info!("DRY RUN: This directive would have been processed if not in dry run mode.");
            // In a real dry run, you might simulate the effect or log more details
        } else {
            info!("Processing cryptographically verified directive: {:?}", directive_command);
            self.command_tx.send(directive_command).await
                .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to send command: {}", e) })?;
        }

        Ok(())
    }
}
