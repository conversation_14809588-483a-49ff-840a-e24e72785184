async function main() {
  console.log("🔍 Checking actual wallet configuration...");
  
  // Get the signer from the configured private key
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  
  console.log(`Network: ${hre.network.name}`);
  console.log(`Chain ID: ${hre.network.config.chainId}`);
  console.log(`Wallet Address: ${address}`);
  console.log(`Private Key (first 10 chars): ${process.env.BASILISK_EXECUTION_PRIVATE_KEY?.substring(0, 10)}...`);
  
  // Check balance on Base Sepolia
  const balance = await ethers.provider.getBalance(address);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  
  if (balance === 0n) {
    console.log("\n❌ No testnet ETH! Fund this address:");
    console.log(`📍 Address: ${address}`);
    console.log("🚰 Faucets:");
    console.log("• https://www.coinbase.com/faucets/base-ethereum-goerli-faucet");
    console.log("• https://faucet.quicknode.com/base/sepolia");
  } else {
    console.log("✅ Ready for deployment!");
  }
}

main().catch(console.error);
