// MISSION: Dynamic Log Level Configuration
// WHY: Allow runtime adjustment of log levels without restart for production debugging
// HOW: File-based configuration watching with atomic updates

use std::sync::Arc;
use std::path::Path;
use tokio::sync::RwLock;
use tokio::fs;
use tracing::{info, warn, error, Level};
use tracing_subscriber::{EnvFilter, reload};
use serde::{Deserialize, Serialize};
use std::str::FromStr;

/// Dynamic logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DynamicLogConfig {
    pub global_level: String,
    pub module_levels: std::collections::HashMap<String, String>,
    pub json_format: bool,
    pub include_timestamps: bool,
    pub include_thread_info: bool,
}

impl Default for DynamicLogConfig {
    fn default() -> Self {
        let mut module_levels = std::collections::HashMap::new();
        module_levels.insert("basilisk_bot::strategies".to_string(), "info".to_string());
        module_levels.insert("basilisk_bot::execution".to_string(), "debug".to_string());
        module_levels.insert("basilisk_bot::data".to_string(), "info".to_string());
        module_levels.insert("basilisk_bot::risk".to_string(), "warn".to_string());
        
        Self {
            global_level: "info".to_string(),
            module_levels,
            json_format: true,
            include_timestamps: true,
            include_thread_info: true,
        }
    }
}

/// Dynamic log level manager with file watching
pub struct DynamicLogManager {
    config: Arc<RwLock<DynamicLogConfig>>,
    config_path: std::path::PathBuf,
    reload_handle: Option<reload::Handle<EnvFilter, tracing_subscriber::Registry>>,
}

impl DynamicLogManager {
    pub fn new(config_path: impl AsRef<Path>) -> Self {
        Self {
            config: Arc::new(RwLock::new(DynamicLogConfig::default())),
            config_path: config_path.as_ref().to_path_buf(),
            reload_handle: None,
        }
    }
    
    /// Initialize the dynamic log manager with file watching
    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Load initial configuration
        self.load_config().await?;
        
        // Start file watcher
        self.start_config_watcher().await;
        
        info!("Dynamic log manager initialized, watching: {:?}", self.config_path);
        Ok(())
    }
    
    /// Load configuration from file
    async fn load_config(&self) -> Result<(), Box<dyn std::error::Error>> {
        if !self.config_path.exists() {
            // Create default config file
            self.create_default_config().await?;
        }
        
        let content = fs::read_to_string(&self.config_path).await?;
        let new_config: DynamicLogConfig = toml::from_str(&content)?;
        
        // Update the filter
        self.update_filter(&new_config).await?;
        
        // Store the new config
        *self.config.write().await = new_config;
        
        info!("Log configuration loaded from {:?}", self.config_path);
        Ok(())
    }
    
    /// Create default configuration file
    async fn create_default_config(&self) -> Result<(), Box<dyn std::error::Error>> {
        let default_config = DynamicLogConfig::default();
        let content = toml::to_string_pretty(&default_config)?;
        
        if let Some(parent) = self.config_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::write(&self.config_path, content).await?;
        info!("Created default log configuration at {:?}", self.config_path);
        Ok(())
    }
    
    /// Update the tracing filter based on configuration
    async fn update_filter(&self, config: &DynamicLogConfig) -> Result<(), Box<dyn std::error::Error>> {
        let mut filter_string = config.global_level.clone();
        
        // Add module-specific levels
        for (module, level) in &config.module_levels {
            filter_string.push_str(&format!(",{}={}", module, level));
        }
        
        let new_filter = EnvFilter::from_str(&filter_string)
            .map_err(|e| format!("Invalid filter configuration: {}", e))?;
        
        if let Some(handle) = &self.reload_handle {
            handle.reload(new_filter)
                .map_err(|e| format!("Failed to reload filter: {}", e))?;
        }
        
        info!("Log filter updated: {}", filter_string);
        Ok(())
    }
    
    /// Start watching the configuration file for changes
    async fn start_config_watcher(&self) {
        let config_path = self.config_path.clone();
        let config = self.config.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(5));
            let mut last_modified = std::time::SystemTime::UNIX_EPOCH;
            
            loop {
                interval.tick().await;
                
                if let Ok(metadata) = fs::metadata(&config_path).await {
                    if let Ok(modified) = metadata.modified() {
                        if modified > last_modified {
                            last_modified = modified;
                            
                            // Reload configuration
                            match fs::read_to_string(&config_path).await {
                                Ok(content) => {
                                    match toml::from_str::<DynamicLogConfig>(&content) {
                                        Ok(new_config) => {
                                            info!("Detected log configuration change, reloading...");
                                            *config.write().await = new_config;
                                        }
                                        Err(e) => {
                                            warn!("Failed to parse log configuration: {}", e);
                                        }
                                    }
                                }
                                Err(e) => {
                                    warn!("Failed to read log configuration file: {}", e);
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    
    /// Get current configuration
    pub async fn get_config(&self) -> DynamicLogConfig {
        self.config.read().await.clone()
    }
    
    /// Update configuration programmatically
    pub async fn update_config(&self, new_config: DynamicLogConfig) -> Result<(), Box<dyn std::error::Error>> {
        // Update filter
        self.update_filter(&new_config).await?;
        
        // Save to file
        let content = toml::to_string_pretty(&new_config)?;
        fs::write(&self.config_path, content).await?;
        
        // Update in-memory config
        *self.config.write().await = new_config;
        
        info!("Log configuration updated and saved");
        Ok(())
    }
    
    /// Set log level for a specific module
    pub async fn set_module_level(&self, module: &str, level: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut config = self.get_config().await;
        config.module_levels.insert(module.to_string(), level.to_string());
        self.update_config(config).await
    }
    
    /// Set global log level
    pub async fn set_global_level(&self, level: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut config = self.get_config().await;
        config.global_level = level.to_string();
        self.update_config(config).await
    }
}

/// Initialize structured logging with dynamic configuration support
pub fn init_dynamic_logging(config_path: impl AsRef<Path>) -> Result<DynamicLogManager, Box<dyn std::error::Error>> {
    let mut manager = DynamicLogManager::new(config_path);
    
    // Set up the initial logging configuration
    let (filter, reload_handle) = reload::Layer::new(EnvFilter::from_default_env());
    manager.reload_handle = Some(reload_handle);
    
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    use crate::logging::JsonFormatter;
    
    let json_layer = tracing_subscriber::fmt::layer()
        .event_format(JsonFormatter);
    
    tracing_subscriber::registry()
        .with(filter)
        .with(json_layer)
        .init();
    
    Ok(manager)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[tokio::test]
    async fn test_dynamic_config_creation() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("log_config.toml");
        
        let manager = DynamicLogManager::new(&config_path);
        manager.create_default_config().await.unwrap();
        
        assert!(config_path.exists());
        
        let content = fs::read_to_string(&config_path).await.unwrap();
        let config: DynamicLogConfig = toml::from_str(&content).unwrap();
        
        assert_eq!(config.global_level, "info");
        assert!(config.json_format);
    }
    
    #[tokio::test]
    async fn test_config_update() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("log_config.toml");
        
        let manager = DynamicLogManager::new(&config_path);
        manager.create_default_config().await.unwrap();
        
        // Update global level
        manager.set_global_level("debug").await.unwrap();
        
        let config = manager.get_config().await;
        assert_eq!(config.global_level, "debug");
    }
}