# Geometer Contracts

This directory contains the Solidity smart contracts for the Basilisk Bot. These contracts are essential for the bot's cross-chain operations and other on-chain interactions.

## Table of Contents

- [Introduction](#introduction)
- [Contracts](#contracts)
  - [StargateCompassV1](#stargatecompassv1)
- [Development](#development)
  - [Setup](#setup)
  - [Compiling](#compiling)
  - [Testing](#testing)
  - [Deployment](#deployment)

## Introduction

The smart contracts in this directory are managed using Hardhat, a development environment for Ethereum software. They are designed to be gas-efficient and secure, and they are rigorously tested to ensure their reliability.

## Contracts

### StargateCompassV1

The `StargateCompassV1` contract is a key component of the bot's cross-chain strategy. It interacts with the Stargate protocol to facilitate seamless asset transfers between different blockchains.

-   **Source Code**: `contracts/StargateCompassV1.sol`
-   **Tests**: `test/StargateCompassV1.ts`

## Development

### Setup

1.  **Navigate to the `geometer-contracts` directory:**
    ```bash
    cd geometer-contracts
    ```

2.  **Install the dependencies:**
    ```bash
    npm install
    ```

### Compiling

To compile the contracts, run the following command:

```bash
npx hardhat compile
```

### Testing

The project includes a suite of tests for the smart contracts. To run the tests, use the following command:

```bash
npx hardhat test
```

### Deployment

The contracts can be deployed to a local or public network using Hardhat Ignition. The following command deploys the `StargateCompassV1` contract:

```bash
npx hardhat ignition deploy ./ignition/modules/StargateCompassV1.ts
```