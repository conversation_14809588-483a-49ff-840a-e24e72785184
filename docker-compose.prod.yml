version: '3.8'

# Production environment overrides for Basilisk Bot
# Enhanced security, monitoring, and performance optimizations

services:
  # Production instance of the bot
  basilisk_bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: basilisk_bot_prod
    environment:
      RUST_LOG: info
      RUST_BACKTRACE: 1
      ENVIRONMENT: production
      NATS_URL: nats://nats:4222
      DATABASE_URL: postgresql://basilisk_bot:${POSTGRES_PASSWORD:-basilisk_bot_secure_password}@timescaledb:5432/basilisk_bot
      REDIS_URL: redis://redis:6379
      METRICS_ENABLED: true
      METRICS_PORT: 9090
      # Core Configuration
      FRACTAL_ANALYSIS_ENABLED: true
      KELLY_CRITERION_ENABLED: true
      DYNAMIC_SLIPPAGE_ENABLED: true
      MEV_PROTECTION_ENABLED: true
      SIGINT_ENABLED: true
      # Multi-Chain Configuration
      ACTIVE_CHAIN_ID: 8453  # Base Network
      NOMADIC_HUNTER_ENABLED: true
      # Safety Configuration
      MAX_POSITION_SIZE_USD: 10000
      KELLY_FRACTION: 0.25  # Quarter-Kelly for safety
      MAX_DAILY_LOSS_USD: -5000
      CIRCUIT_BREAKER_ENABLED: true
    volumes:
      - ./config/production.toml:/app/config/local.toml:ro
      - ./sigint:/app/sigint
      - ./logs:/app/logs
    ports:
      - "9090:9090"  # Metrics endpoint
    networks:
      - basilisk_bot_network
    depends_on:
      - nats
      - timescaledb
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9090/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "com.basilisk_bot.service=trading_bot"
      - "com.basilisk_bot.environment=production"

  # Enhanced NATS with clustering
  nats:
    command: [
      "--jetstream",
      "--store_dir=/data",
      "--max_memory=2GB",
      "--max_file=50GB",
      "--cluster_name=basilisk_bot_cluster",
      "--cluster=nats://0.0.0.0:6222"
    ]
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Enhanced TimescaleDB with performance tuning
  timescaledb:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_SHARED_PRELOAD_LIBRARIES: timescaledb
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_SHARED_BUFFERS: 1GB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 4GB
      POSTGRES_WORK_MEM: 16MB
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Enhanced Redis with persistence
  redis:
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10 --save 60 10000
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # Nginx reverse proxy for security
  nginx:
    image: nginx:alpine
    container_name: basilisk_bot_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - basilisk_bot_network
    depends_on:
      - basilisk_bot
      - grafana
    restart: unless-stopped
    labels:
      - "com.basilisk_bot.service=reverse_proxy"
      - "com.basilisk_bot.environment=production"

  # Log shipping to external service
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: basilisk_bot_fluentd
    volumes:
      - ./config/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/basilisk_bot:ro
    environment:
      FLUENTD_CONF: fluent.conf
      LOG_ENDPOINT: ${LOG_ENDPOINT}
      LOG_API_KEY: ${LOG_API_KEY}
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    labels:
      - "com.basilisk_bot.service=log_shipping"
      - "com.basilisk_bot.environment=production"

  # Backup service
  backup:
    image: postgres:15-alpine
    container_name: basilisk_bot_backup
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: ["sh", "-c", "while true; do sleep 86400; /backup.sh; done"]
    networks:
      - basilisk_bot_network
    depends_on:
      - timescaledb
    restart: unless-stopped
    labels:
      - "com.basilisk_bot.service=backup"
      - "com.basilisk_bot.environment=production"

  # Security monitoring
  fail2ban:
    image: crazymax/fail2ban:latest
    container_name: basilisk_bot_fail2ban
    network_mode: "host"
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - ./config/fail2ban:/data
      - ./logs:/var/log/basilisk_bot:ro
    environment:
      TZ: UTC
      F2B_LOG_LEVEL: INFO
    restart: unless-stopped
    labels:
      - "com.basilisk_bot.service=security"
      - "com.basilisk_bot.environment=production"

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.47.1
    container_name: basilisk_bot_prometheus
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - --config.file=/etc/prometheus/prometheus.yml
      - --storage.tsdb.path=/prometheus
      - --web.enable-lifecycle
    ports:
      - "9090:9090"
    networks:
      - basilisk_bot_network
    depends_on:
      - basilisk_bot
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.1.5
    container_name: basilisk_bot_grafana
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    ports:
      - "3000:3000"
    networks:
      - basilisk_bot_network
    depends_on:
      - prometheus
      - loki
    restart: unless-stopped

  loki:
    image: grafana/loki:2.8.0
    container_name: basilisk_bot_loki
    volumes:
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    networks:
      - basilisk_bot_network
    restart: unless-stopped

  promtail:
    image: grafana/promtail:2.8.0
    container_name: basilisk_bot_promtail
    volumes:
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./config/promtail-config.yaml:/etc/promtail/config.yaml:ro
    command: -config.file=/etc/promtail/config.yaml
    networks:
      - basilisk_bot_network
    depends_on:
      - loki
    restart: unless-stopped

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: basilisk_bot_alertmanager
    volumes:
      - ./config/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command: --config.file=/etc/alertmanager/alertmanager.yml --web.external-url=http://localhost:9093
    ports:
      - "9093:9093"
    networks:
      - basilisk_bot_network
    restart: unless-stopped

  jaeger:
    image: jaegertracing/all-in-one:1.48
    container_name: basilisk_bot_jaeger
    environment:
      COLLECTOR_ZIPKIN_HOST_PORT: 9411
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    networks:
      - basilisk_bot_network
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
  loki_data:
  alertmanager_data:

networks:
  basilisk_bot_network:
    driver: bridge
