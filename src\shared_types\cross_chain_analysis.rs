// DEGEN CHAIN: Cross-Chain Analysis Report Types
// WHY: Track the decision-making process for cross-chain trades
// HOW: Step-by-step analysis with educational context

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::time::Instant;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CrossChainAnalysisReport {
    pub trade_id: String,
    pub step: u8, // 1-4 for the 4 analysis steps
    pub step_name: String,
    pub description: String,
    pub status: AnalysisStatus,
    pub gross_profit: Option<Decimal>,
    pub net_profit: Option<Decimal>,
    pub costs: Option<CrossChainCosts>,
    pub timestamp: u64, // Unix timestamp for serialization
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CrossChainCosts {
    pub aave_fee_base: Decimal,
    pub stargate_fee: Decimal,
    pub degen_gas_cost: Decimal,
    pub total_cost: Decimal,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AnalysisStatus {
    InProgress,
    Completed,
    Approved,
    Rejected,
}

impl CrossChainAnalysisReport {
    pub fn new_opportunity_detected(trade_id: String, gross_profit: Decimal) -> Result<Self, crate::error::BasiliskError> {
        Ok(Self {
            trade_id,
            step: 1,
            step_name: "Opportunity Detected".to_string(),
            description: format!("Gross Profit: ${:.2} USD", gross_profit),
            status: AnalysisStatus::Completed,
            gross_profit: Some(gross_profit),
            net_profit: None,
            costs: None,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map_err(|e| crate::error::BasiliskError::SystemTimeError(e.to_string()))?
                .as_secs(),
        })
    }

    pub fn new_cost_analysis(trade_id: String, costs: CrossChainCosts) -> Result<Self, crate::error::BasiliskError> {
        Ok(Self {
            trade_id,
            step: 2,
            step_name: "Cost Analysis".to_string(),
            description: format!(
                "Aave Fee (Base): -${:.2} | Stargate Fee: -${:.2}",
                costs.aave_fee_base, costs.stargate_fee
            ),
            status: AnalysisStatus::Completed,
            gross_profit: None,
            net_profit: None,
            costs: Some(costs),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map_err(|e| crate::error::BasiliskError::SystemTimeError(e.to_string()))?
                .as_secs(),
        })
    }

    pub fn new_gas_estimation(trade_id: String, gas_cost: Decimal) -> Result<Self, crate::error::BasiliskError> {
        Ok(Self {
            trade_id,
            step: 3,
            step_name: "Gas Estimation".to_string(),
            description: format!("Degen Gas: ${:.2}", gas_cost),
            status: AnalysisStatus::Completed,
            gross_profit: None,
            net_profit: None,
            costs: None,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map_err(|e| crate::error::BasiliskError::SystemTimeError(e.to_string()))?
                .as_secs(),
        })
    }

    pub fn new_final_decision(trade_id: String, net_profit: Decimal, approved: bool) -> Result<Self, crate::error::BasiliskError> {
        let status = if approved {
            AnalysisStatus::Approved
        } else {
            AnalysisStatus::Rejected
        };

        let status_text = if approved { "✅ APPROVED" } else { "❌ REJECTED" };

        Ok(Self {
            trade_id,
            step: 4,
            step_name: "Final Decision".to_string(),
            description: format!("Net Profit: ${:.2} | Status: {}", net_profit, status_text),
            status,
            gross_profit: None,
            net_profit: Some(net_profit),
            costs: None,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map_err(|e| crate::error::BasiliskError::SystemTimeError(e.to_string()))?
                .as_secs(),
        })
    }
}

impl AnalysisStatus {
    pub fn indicator(&self) -> &'static str {
        match self {
            AnalysisStatus::InProgress => "[~]",
            AnalysisStatus::Completed => "[*]",
            AnalysisStatus::Approved => "[OK]",
            AnalysisStatus::Rejected => "[X]",
        }
    }

    pub fn color(&self) -> ratatui::style::Color {
        match self {
            AnalysisStatus::InProgress => ratatui::style::Color::Yellow,
            AnalysisStatus::Completed => ratatui::style::Color::Blue,
            AnalysisStatus::Approved => ratatui::style::Color::Green,
            AnalysisStatus::Rejected => ratatui::style::Color::Red,
        }
    }
}