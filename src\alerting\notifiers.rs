// MISSION: Notification Channels - The "Mouthpiece" of the Zen Geometer
// WHY: Ensure critical alerts reach operators through multiple channels
// HOW: Plugin architecture with Discord, Telegram, PagerDuty, and more

use async_trait::async_trait;
use crate::shared_types::Alert;
use anyhow::Result;

#[async_trait]
pub trait Notifier: Send + Sync {
    async fn send(&self, alert: &Alert) -> Result<()>;
    fn name(&self) -> &'static str;
    fn is_enabled(&self) -> bool;
}

pub mod console;