use async_nats::Client;
use serde_json::Value;
use std::error::Error;
use tokio_stream::StreamExt;
use tracing::{info, Level};
use tracing_subscriber::FmtSubscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Initialize logging
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;

    // Connect to NATS server
    let nats_url =
        std::env::var("NATS_URL").unwrap_or_else(|_| "nats://localhost:4222".to_string());
    info!("Connecting to NATS server at {}", nats_url);
    let client = async_nats::connect(&nats_url).await?;
    info!("Connected to NATS server");

    // Subscribe to all data topics
    let mut subscription = client.subscribe("data.>").await?;
    info!("Subscribed to all data topics");

    // Process incoming messages
    info!("Listening for messages...");
    while let Some(msg) = subscription.next().await {
        let subject = msg.subject.to_string();
        let payload = String::from_utf8_lossy(&msg.payload);

        // Try to parse as JSON for pretty printing
        match serde_json::from_str::<Value>(&payload) {
            Ok(json) => {
                info!(
                    "Received message on {}: {}",
                    subject,
                    serde_json::to_string_pretty(&json)?
                );
            }
            Err(_) => {
                info!("Received message on {}: {}", subject, payload);
            }
        }
    }

    Ok(())
}
