// MISSION: NonceManager - Production-Ready Nonce Management with Recovery
// WHY: Prevent stuck transactions and handle concurrent nonce allocation safely
// HOW: Track pending transactions, detect stuck states, implement recovery mechanisms

use ethers::{
    prelude::*,
    providers::{Http, Middleware, Provider},
    types::{Address, TransactionReceipt, transaction::eip2718::TypedTransaction},
    signers::{LocalWallet, Signer},
};
use std::collections::HashMap;
use std::sync::Arc;
use thiserror::Error;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{Duration, Instant};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

use crate::execution::broadcaster::Broadcaster;

#[derive(Error, Debug)]
pub enum NonceManagerError {
    #[error("Provider error: {0}")]
    ProviderError(#[from] ProviderError),
    #[error("Transaction stuck for too long: nonce {nonce}, hash {hash:?}")]
    TransactionStuck { nonce: U256, hash: Option<TxHash> },
    #[error("Nonce gap detected: expected {expected}, got {actual}")]
    NonceGap { expected: U256, actual: U256 },
    #[error("Maximum pending transactions reached: {max}")]
    MaxPendingReached { max: usize },
    #[error("Signing error: {0}")]
    SigningError(String),
    #[error("Broadcaster error: {0}")]
    BroadcasterError(String),
}

#[derive(Debug, Clone)]
struct PendingTransaction {
    hash: TxHash,
    nonce: U256,
    submitted_at: Instant,
    gas_price: U256,
    max_fee_per_gas: Option<U256>,
    max_priority_fee_per_gas: Option<U256>,
    retry_count: u32,
    // Store the original transaction request for replacement
    original_tx_request: TransactionRequest,
}

pub struct NonceManager {
    provider: Arc<Provider<Http>>,
    address: Address,
    signer: Arc<LocalWallet>,
    broadcaster: Broadcaster,
    current_nonce: Arc<Mutex<U256>>,
    pending_transactions: Arc<RwLock<HashMap<U256, PendingTransaction>>>,
    max_pending_transactions: usize,
    stuck_transaction_timeout: Duration,
    nonce_gap_tolerance: u64,
    recovery_enabled: bool,
}

impl NonceManager {
    /// Create a new NonceManager with production-ready configuration
    pub async fn new(
        provider: Arc<Provider<Http>>,
        address: Address,
        signer: Arc<LocalWallet>,
        broadcaster: Broadcaster,
    ) -> Result<Self, NonceManagerError> {
        Self::new_with_config(
            provider,
            address,
            signer,
            broadcaster,
            50,                       // max_pending_transactions
            Duration::from_secs(300), // stuck_transaction_timeout (5 minutes)
            5,                        // nonce_gap_tolerance
            true,                     // recovery_enabled
        )
        .await
    }

    /// Create a new NonceManager with custom configuration
    pub async fn new_with_config(
        provider: Arc<Provider<Http>>,
        address: Address,
        signer: Arc<LocalWallet>,
        broadcaster: Broadcaster,
        max_pending_transactions: usize,
        stuck_transaction_timeout: Duration,
        nonce_gap_tolerance: u64,
        recovery_enabled: bool,
    ) -> Result<Self, NonceManagerError> {
        let current_nonce = provider.get_transaction_count(address, None).await?;

        info!(
            "NonceManager initialized for address {:?} with nonce {}",
            address, current_nonce
        );

        let manager = Self {
            provider,
            address,
            signer,
            broadcaster,
            current_nonce: Arc::new(Mutex::new(current_nonce)),
            pending_transactions: Arc::new(RwLock::new(HashMap::new())),
            max_pending_transactions,
            stuck_transaction_timeout,
            nonce_gap_tolerance,
            recovery_enabled,
        };

        // Start background monitoring task
        if recovery_enabled {
            manager.start_monitoring_task().await;
        }

        Ok(manager)
    }

    /// Get the next available nonce for transaction submission
    pub async fn get_next_nonce(&self) -> Result<U256, NonceManagerError> {
        // First, check the condition without holding the primary lock.
        let pending_count = self.pending_transactions.read().await.len();
        if pending_count >= self.max_pending_transactions {
            return Err(NonceManagerError::MaxPendingReached {
                max: self.max_pending_transactions,
            });
        }

        // Now, acquire the lock only for the critical section.
        let mut nonce_guard = self.current_nonce.lock().await;
        let current_nonce = *nonce_guard;
        *nonce_guard += U256::one();
        drop(nonce_guard); // Explicitly drop the guard to release the lock.

        debug!(
            "Allocated nonce {} (pending: {})",
            current_nonce, pending_count
        );
        Ok(current_nonce)
    }

    /// Register a transaction as pending with its details
    pub async fn register_pending_transaction(
        &self,
        nonce: U256,
        hash: TxHash,
        gas_price: U256,
        max_fee_per_gas: Option<U256>,
        max_priority_fee_per_gas: Option<U256>,
        original_tx_request: TransactionRequest,
    ) -> Result<(), NonceManagerError> {
        let pending_tx = PendingTransaction {
            hash,
            nonce,
            submitted_at: Instant::now(),
            gas_price,
            max_fee_per_gas,
            max_priority_fee_per_gas,
            retry_count: 0,
            original_tx_request,
        };

        let mut pending = self.pending_transactions.write().await;
        pending.insert(nonce, pending_tx);

        debug!(
            "Registered pending transaction: nonce {}, hash {:?}",
            nonce, hash
        );
        Ok(())
    }

    /// Mark a transaction as confirmed and remove from pending
    pub async fn confirm_transaction(&self, nonce: U256) -> Result<(), NonceManagerError> {
        let mut pending = self.pending_transactions.write().await;
        if let Some(tx) = pending.remove(&nonce) {
            let duration = tx.submitted_at.elapsed();
            info!(
                "Transaction confirmed: nonce {}, hash {:?}, duration: {:?}",
                nonce, tx.hash, duration
            );
        }
        Ok(())
    }

    /// Force reset nonce to on-chain value (emergency recovery)
    pub async fn force_reset_nonce(&self) -> Result<U256, NonceManagerError> {
        warn!("Force resetting nonce for address {:?}", self.address);

        let on_chain_nonce = self
            .provider
            .get_transaction_count(self.address, None)
            .await?;

        {
            let mut nonce = self.current_nonce.lock().await;
            *nonce = on_chain_nonce;
        }

        // Clear all pending transactions as they may be invalid
        {
            let mut pending = self.pending_transactions.write().await;
            let cleared_count = pending.len();
            pending.clear();
            warn!(
                "Cleared {} pending transactions during nonce reset",
                cleared_count
            );
        }

        info!("Nonce reset to on-chain value: {}", on_chain_nonce);
        Ok(on_chain_nonce)
    }

    /// Get current nonce without incrementing
    pub async fn get_current_nonce(&self) -> U256 {
        *self.current_nonce.lock().await
    }

    /// Get the number of pending transactions
    pub async fn get_pending_count(&self) -> usize {
        self.pending_transactions.read().await.len()
    }

    /// Get details of all pending transactions
    pub async fn get_pending_transactions(&self) -> Vec<(U256, TxHash, Duration)> {
        let pending = self.pending_transactions.read().await;
        pending
            .values()
            .map(|tx| (tx.nonce, tx.hash, tx.submitted_at.elapsed()))
            .collect()
    }

    /// Check for and handle stuck transactions
    pub async fn check_stuck_transactions(&self) -> Result<Vec<U256>, NonceManagerError> {
        let mut stuck_nonces = Vec::new();
        let pending = self.pending_transactions.read().await;

        for (nonce, tx) in pending.iter() {
            if tx.submitted_at.elapsed() > self.stuck_transaction_timeout {
                warn!(
                    "Detected stuck transaction: nonce {}, hash {:?}, age: {:?}",
                    nonce,
                    tx.hash,
                    tx.submitted_at.elapsed()
                );
                stuck_nonces.push(*nonce);
            }
        }

        Ok(stuck_nonces)
    }

    /// Attempt to recover from stuck transactions by replacement
    pub async fn recover_stuck_transaction(&self, nonce: U256) -> Result<bool, NonceManagerError> {
        if !self.recovery_enabled {
            return Ok(false);
        }

        let pending = self.pending_transactions.read().await;
        let stuck_tx = match pending.get(&nonce) {
            Some(tx) => tx.clone(),
            None => return Ok(false), // Transaction no longer pending
        };
        drop(pending);

        // Check if transaction is actually stuck by querying the network
        match self.provider.get_transaction(stuck_tx.hash).await? {
            Some(tx) => {
                // Transaction exists, check if it's been mined
                if let Some(receipt) = self.provider.get_transaction_receipt(stuck_tx.hash).await? {
                    // Transaction was mined, mark as confirmed
                    self.confirm_transaction(nonce).await?;
                    info!(
                        "Previously stuck transaction was actually mined: nonce {}",
                        nonce
                    );
                    return Ok(true);
                }

                // Transaction exists but not mined - attempt replacement
                warn!("Attempting to replace stuck transaction: nonce {}", nonce);
                self.attempt_transaction_replacement(stuck_tx).await
            }
            None => {
                // Transaction doesn't exist in mempool, likely dropped
                warn!(
                    "Stuck transaction not found in mempool, marking as failed: nonce {}",
                    nonce
                );
                let mut pending = self.pending_transactions.write().await;
                pending.remove(&nonce);
                Ok(true)
            }
        }
    }

    /// Attempt to replace a stuck transaction with higher gas
    async fn attempt_transaction_replacement(
        &self,
        stuck_tx: PendingTransaction,
    ) -> Result<bool, NonceManagerError> {
        // Calculate replacement gas price (increase by 10%)
        let replacement_gas_price = stuck_tx.gas_price * U256::from(110) / U256::from(100);

        info!(
            "Attempting to replace transaction nonce {} with gas price {} -> {} (10% increase)",
            stuck_tx.nonce, stuck_tx.gas_price, replacement_gas_price
        );

        // Reconstruct the original transaction with the new gas price and nonce
        let mut new_tx_request = stuck_tx.original_tx_request.clone();
        new_tx_request.nonce = Some(stuck_tx.nonce);
        new_tx_request.gas_price = Some(replacement_gas_price);

        // Sign the new transaction
        let signed_tx = self.signer.sign_transaction(&TypedTransaction::Legacy(new_tx_request.clone())).await
            .map_err(|e| NonceManagerError::SigningError(e.to_string()))?;

        // Submit the new, higher-gas transaction to the network via the Broadcaster
        match self.broadcaster.send_transaction(new_tx_request.clone()).await {
            Ok(Some(new_hash)) => {
                info!("Replacement transaction submitted: nonce {}, old hash {:?}, new hash {:?}", stuck_tx.nonce, stuck_tx.hash, new_hash);
                // Update the pending transaction record with the new transaction hash and gas details
                let mut pending = self.pending_transactions.write().await;
                if let Some(tx) = pending.get_mut(&stuck_tx.nonce) {
                    tx.hash = new_hash;
                    tx.gas_price = replacement_gas_price;
                    tx.submitted_at = Instant::now(); // Reset submission time
                    tx.retry_count += 1;
                }
                Ok(true)
            },
            Ok(None) => {
                // Broadcaster might return None if it's an MEV bundle and doesn't get an immediate hash
                warn!("Replacement transaction submitted but no hash returned (MEV bundle?): nonce {}", stuck_tx.nonce);
                // Still update internal state as it was submitted
                let mut pending = self.pending_transactions.write().await;
                if let Some(tx) = pending.get_mut(&stuck_tx.nonce) {
                    tx.gas_price = replacement_gas_price;
                    tx.submitted_at = Instant::now();
                    tx.retry_count += 1;
                }
                Ok(true)
            },
            Err(e) => {
                error!("Failed to submit replacement transaction: {}", e);
                Err(NonceManagerError::BroadcasterError(e.to_string()))
            }
        }
    }

    /// Start background monitoring task for automatic recovery
    async fn start_monitoring_task(&self) {
        let provider = self.provider.clone();
        let address = self.address;
        let pending_transactions = self.pending_transactions.clone();
        let current_nonce = self.current_nonce.clone();
        let stuck_timeout = self.stuck_transaction_timeout;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));

            loop {
                interval.tick().await;

                // Check for confirmed transactions
                if let Err(e) =
                    Self::monitor_confirmed_transactions(&provider, address, &pending_transactions)
                        .await
                {
                    error!("Error monitoring confirmed transactions: {}", e);
                }

                // Check for nonce gaps
                if let Err(e) = Self::monitor_nonce_gaps(
                    &provider,
                    address,
                    &current_nonce,
                    &pending_transactions,
                )
                .await
                {
                    error!("Error monitoring nonce gaps: {}", e);
                }
            }
        });

        info!("NonceManager monitoring task started");
    }

    /// Monitor for confirmed transactions and update pending list
    async fn monitor_confirmed_transactions(
        provider: &Arc<Provider<Http>>,
        address: Address,
        pending_transactions: &RwLock<HashMap<U256, PendingTransaction>>,
    ) -> Result<(), NonceManagerError> {
        let on_chain_nonce = provider.get_transaction_count(address, None).await?;
        let mut pending = pending_transactions.write().await;

        // Remove confirmed transactions (nonce < on_chain_nonce)
        let mut confirmed_nonces = Vec::new();
        for (&nonce, tx) in pending.iter() {
            if nonce < on_chain_nonce {
                confirmed_nonces.push(nonce);
                debug!(
                    "Auto-confirmed transaction: nonce {}, hash {:?}",
                    nonce, tx.hash
                );
            }
        }

        for nonce in confirmed_nonces {
            pending.remove(&nonce);
        }

        Ok(())
    }

    /// Monitor for nonce gaps and handle them
    async fn monitor_nonce_gaps(
        provider: &Arc<Provider<Http>>,
        address: Address,
        current_nonce: &Mutex<U256>,
        pending_transactions: &RwLock<HashMap<U256, PendingTransaction>>,
    ) -> Result<(), NonceManagerError> {
        let on_chain_nonce = provider.get_transaction_count(address, None).await?;
        let local_nonce = *current_nonce.lock().await;

        // Check for significant nonce gaps
        if local_nonce > on_chain_nonce {
            let gap = (local_nonce - on_chain_nonce).as_u64();
            if gap > 10 {
                // Configurable threshold
                warn!(
                    "Large nonce gap detected: local {}, on-chain {}, gap: {}",
                    local_nonce, on_chain_nonce, gap
                );

                // In production, this might trigger automatic recovery
                // For now, just log the issue
            }
        }

        Ok(())
    }

    /// Get comprehensive status for monitoring
    pub async fn get_status(&self) -> NonceManagerStatus {
        let current_nonce = self.get_current_nonce().await;
        let pending_count = self.get_pending_count().await;
        let stuck_nonces = self.check_stuck_transactions().await.unwrap_or_default();

        // Get on-chain nonce for comparison
        let on_chain_nonce = self
            .provider
            .get_transaction_count(self.address, None)
            .await
            .unwrap_or(U256::zero());

        NonceManagerStatus {
            address: self.address,
            current_nonce,
            on_chain_nonce,
            pending_count,
            stuck_count: stuck_nonces.len(),
            max_pending: self.max_pending_transactions,
            recovery_enabled: self.recovery_enabled,
        }
    }
}

#[derive(Debug, Clone)]
pub struct NonceManagerStatus {
    pub address: Address,
    pub current_nonce: U256,
    pub on_chain_nonce: U256,
    pub pending_count: usize,
    pub stuck_count: usize,
    pub max_pending: usize,
    pub recovery_enabled: bool,
}
