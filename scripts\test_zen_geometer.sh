#!/bin/bash

# MISSION: Test the Zen Geometer Strategy
# WHY: Verify that the Zen Geometer strategy is working correctly
# HOW: Start services, run the bot in dry-run mode, and check the logs for activity

set -e

echo "Testing Zen Geometer Strategy..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[BASILISK BOT]${NC} $1"
}

print_success() {
    echo -e "${GREEN}SUCCESS${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}WARNING${NC} $1"
}

print_error() {
    echo -e "${RED}ERROR${NC} $1"
}

# Check if Docker services are running
print_status "Checking infrastructure services..."
if ! docker compose ps | grep -q "Up"; then
    print_warning "Starting Docker services..."
    docker compose up -d
    sleep 10
else
    print_success "Docker services are running"
fi

# Build the project
print_status "Building Basilisk Bot..."
if cargo build; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Run the bot in dry-run mode
print_status "Running Zen Geometer in dry-run mode..."
cargo run -- run --dry-run &
BOT_PID=$!

# Wait for the bot to start up
sleep 10

# Check the logs for activity
print_status "Checking logs for Zen Geometer activity..."
sleep 5
if ps aux | grep -q "basilisk_bot.*run.*dry-run"; then
    print_success "Zen Geometer is running in dry-run mode"
else
    print_warning "Zen Geometer process not found"
fi

# Test the TUI harness
print_status "Testing TUI harness..."
timeout 10 cargo run --bin tui_harness &>/dev/null &
TUI_PID=$!
sleep 3
if ps -p $TUI_PID > /dev/null; then
    print_success "TUI harness is running"
    kill $TUI_PID 2>/dev/null || true
else
    print_warning "TUI harness failed to start"
fi

# Kill the bot
kill $BOT_PID

print_status "Zen Geometer test completed!"