# Basilisk Bot Environment Configuration
# Copy this file to .env and fill in your actual values

# CRITICAL: Private key for transaction execution (without 0x prefix)
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here

# RPC API Keys (optional but recommended for production)
BASE_RPC_API_KEY=your_base_rpc_api_key
ARBITRUM_RPC_API_KEY=your_arbitrum_rpc_api_key
POLYGON_RPC_API_KEY=your_polygon_rpc_api_key

# CEX API Credentials (optional)
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret

# MEV Relay Configuration (optional)
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
TITAN_RELAY_URL=https://rpc.titanbuilder.xyz

# Database Configuration (optional override)
DATABASE_URL=postgres://basilisk:basilisk_password@localhost:5432/basilisk_db

# Redis Configuration (optional override)
REDIS_URL=redis://localhost:6379

# NATS Configuration (optional override)
NATS_URL=nats://localhost:4222

# Security Settings
HONEYPOT_API_KEY=your_goplus_api_key

# Monitoring & Alerting (optional)
DISCORD_WEBHOOK_URL=your_discord_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id