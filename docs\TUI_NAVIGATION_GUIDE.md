# Zen Geometer TUI Navigation Guide

## Mission Brief: Complete Terminal User Interface Navigation Guide

This comprehensive tactical manual covers Terminal User Interface navigation, keybindings, and interface features for the **Zen Geometer** Mission Control system. Master these controls to achieve optimal operational efficiency through the four-tab Mission Control interface.

**Mission Objective**: Complete mastery of the Mission Control TUI for autonomous trading operations  
**Tactical Approach**: Progressive skill development through tab-specific navigation and controls  
**Production Status**: ✅ **INTERFACE READY** - All TUI features operational and battle-tested

## 📋 Table of Contents

1. [Interface Overview](#interface-overview)
2. [Global Navigation](#global-navigation)
3. [Tab-Specific Controls](#tab-specific-controls)
4. [Strategy Inspector](#strategy-inspector)
5. [Status Indicators](#status-indicators)
6. [Keyboard Reference](#keyboard-reference)
7. [Interface Features](#interface-features)

## Interface Overview

The Zen Geometer TUI is designed as a Mission Control center with four core operational tabs:

- **📊 Dashboard** - "The Bridge": Aetheric Resonance Engine visualization
- **🎛️ Operations** - "The Cockpit": Daily operational control and monitoring
- **🔧 Systems** - "The Engine Room": Component health and performance monitoring
- **⚙️ Config** - "The Control Panel": Configuration management with hot-reload

### Design Philosophy

- **Progressive Disclosure**: Information flows from high-level overview to detailed diagnostics
- **Mission Control Approach**: Each tab represents a different operational aspect
- **Safety-First Design**: Critical operations require confirmation
- **Real-time Intelligence**: Live data feeds with clear status indicators

## Global Navigation

### Tab Switching

| Key   | Tab        | Description                             |
| ----- | ---------- | --------------------------------------- |
| `1`   | Dashboard  | Aetheric Resonance Engine visualization |
| `2`   | Operations | Trading operations and control          |
| `3`   | Systems    | Component health monitoring             |
| `4`   | Config     | Configuration management                |
| `Tab` | Next Tab   | Cycle through tabs sequentially         |

### Universal Controls

| Key     | Action             | Context                                     |
| ------- | ------------------ | ------------------------------------------- |
| `Q`     | Quit TUI           | Any tab - exits application                 |
| `↑/↓`   | Navigate           | Lists, tables, and menu items               |
| `Enter` | Select/Confirm     | Activate selected item or confirm action    |
| `Esc`   | Cancel/Back        | Cancel operation or return to previous view |
| `I`     | Strategy Inspector | Open when opportunity is selected           |
| `?`     | Context Help       | Show help for current tab (when available)  |

## Tab-Specific Controls

### Dashboard Tab - "The Bridge"

**Purpose**: High-level situational awareness with Aetheric Resonance Engine data

#### Widget Navigation

| Key     | Action        | Description                                      |
| ------- | ------------- | ------------------------------------------------ |
| `Tab`   | Cycle Widgets | Network → Chronos → Mandorla → Execution         |
| `Enter` | Drill Down    | Navigate to detailed analysis                    |
| `I`     | Inspector     | Open Strategy Inspector for selected opportunity |

#### Widget Focus Areas

**🌐 Network Seismology Widget**

- Shows S-P latency and network coherence
- Real-time block propagation histogram
- Network state classification (COHERENT/STRESSED/SHOCKWAVE)

**⌛ Chronos Sieve Widget**

- Market rhythm analysis and classification
- Dominant cycle identification
- Power spectrum visualization

**💠 Mandorla Gauge Widget**

- Opportunity flow rate (opportunities per minute)
- Geometric score distribution
- Quality breakdown visualization

**📈 Execution & PnL Widget**

- Session profit/loss tracking
- Success rate with trade counts
- PnL trend sparkline
- Current wallet balances

#### Data Source Indicators

- 🟢 **● LIVE**: Fresh data from ARE components (<30 seconds)
- 🟡 **● CACHED**: Real data but stale (>30 seconds)
- 🔴 **● MOCK**: Simulation data when real feeds unavailable

### Operations Tab - "The Cockpit"

**Purpose**: Real-time trading operations and master control

#### Master Control Panel

| Key | Action         | Description                            |
| --- | -------------- | -------------------------------------- |
| `S` | Start/Stop     | Toggle bot between running and stopped |
| `P` | Pause          | Temporary halt (can resume)            |
| `R` | Restart        | Full restart sequence                  |
| `E` | Emergency Stop | Immediate halt with confirmation       |
| `G` | Graceful Stop  | Complete current operations then stop  |

#### Activity Log Controls

| Key            | Action       | Description                                         |
| -------------- | ------------ | --------------------------------------------------- |
| `F`            | Filter       | Cycle through filters (All → EXECUTOR → GAZE → All) |
| `C`            | Clear Filter | Remove current filter                               |
| `↑/↓`          | Scroll       | Navigate through log history                        |
| `Page Up/Down` | Fast Scroll  | Quick navigation through logs                       |

#### Trade History Panel

| Key     | Action          | Description                     |
| ------- | --------------- | ------------------------------- |
| `Tab`   | Focus Panel     | Switch focus to trade history   |
| `↑/↓`   | Navigate Trades | Browse through executed trades  |
| `Enter` | Trade Details   | View detailed trade information |

#### Panel Navigation

| Key   | Action       | Description                                   |
| ----- | ------------ | --------------------------------------------- |
| `Tab` | Switch Panel | Activity Log → Trade History → Master Control |

### Systems Tab - "The Engine Room"

**Purpose**: Comprehensive system health and performance monitoring

#### Panel Navigation

| Key   | Action       | Description                                 |
| ----- | ------------ | ------------------------------------------- |
| `Tab` | Switch Panel | Logs → Components → Services → Nodes → Logs |

#### Component Status Matrix

| Key     | Action              | Description                         |
| ------- | ------------------- | ----------------------------------- |
| `↑/↓`   | Navigate Components | Browse through system components    |
| `Enter` | Component Details   | Show detailed component information |
| `R`     | Restart Component   | Restart selected component          |

#### Network Node Management

| Key     | Action         | Description                      |
| ------- | -------------- | -------------------------------- |
| `↑/↓`   | Navigate Nodes | Browse through network endpoints |
| `Enter` | Ping Node      | Test latency to selected node    |
| `R`     | Reconnect      | Reconnect to selected node       |

#### Enhanced Log Viewer

| Key            | Action         | Description                            |
| -------------- | -------------- | -------------------------------------- |
| `F`            | Filter Logs    | Toggle log filtering                   |
| `C`            | Clear Filter   | Remove current filter                  |
| `/`            | Search Mode    | Start searching through logs           |
| `↑/↓`          | Scroll Logs    | Navigate through log entries           |
| `Page Up/Down` | Fast Scroll    | Quick log navigation                   |
| `E`            | Export Logs    | Export logs to file                    |
| `X`            | Clear All Logs | Clear all logs (requires confirmation) |

#### Service Management

| Key | Action          | Description              |
| --- | --------------- | ------------------------ |
| `S` | Start Service   | Start selected service   |
| `K` | Stop Service    | Stop selected service    |
| `R` | Restart Service | Restart selected service |

### Config Tab - "The Control Panel"

**Purpose**: Safe configuration management with hot-reload capabilities

#### Panel Navigation

| Key   | Action       | Description                                |
| ----- | ------------ | ------------------------------------------ |
| `Tab` | Switch Panel | Sections → Parameters → Details → Sections |

#### Configuration Sections

| Key     | Action            | Description                             |
| ------- | ----------------- | --------------------------------------- |
| `↑/↓`   | Navigate Sections | Browse configuration sections           |
| `Enter` | Select Section    | Move to parameters for selected section |

#### Parameter Editor

| Key     | Action              | Description                      |
| ------- | ------------------- | -------------------------------- |
| `↑/↓`   | Navigate Parameters | Browse through parameters        |
| `Enter` | Edit Parameter      | Start editing selected parameter |
| `Esc`   | Cancel Edit         | Cancel parameter editing         |

#### Configuration Control

| Key | Action            | Description                    |
| --- | ----------------- | ------------------------------ |
| `S` | Save & Hot-Reload | Apply changes without restart  |
| `R` | Reset to Defaults | Revert all changes             |
| `L` | Load Profile      | Switch configuration profile   |
| `V` | Validate Config   | Check configuration for errors |

#### Safety Features

- Real-time validation during editing
- Confirmation dialogs for critical operations
- Rollback capability for failed changes
- Atomic updates (all changes applied together)

## Strategy Inspector

**Purpose**: Deep analysis tool for understanding Aetheric Resonance Engine decisions

### Accessing the Inspector

1. Navigate to any tab with opportunities (Dashboard, Operations)
2. Select an opportunity using `↑/↓` keys
3. Press `I` to open the Strategy Inspector modal
4. Review detailed analysis and projections

### Inspector Interface

#### Opportunity Details Header

- Unique UUID for tracking
- Human-readable opportunity summary
- Current execution status
- Overall risk level assessment

#### Score Breakdown Analysis

Detailed breakdown of each Aetheric Resonance Engine pillar:

**💠 Mandorla Gauge (40% weight)**

- Vesica Piscis geometric scoring
- Path efficiency analysis
- Liquidity centroid assessment

**⌛ Chronos Sieve (30% weight)**

- Market rhythm stability
- Temporal harmony analysis
- Cycle alignment scoring

**🌊 Network Seismology (30% weight)**

- S-P wave timing analysis
- Network coherence scoring
- Optimal timing assessment

#### Financial Analysis

- Final combined Aetheric Resonance Score (0.0-1.0)
- Statistical confidence in analysis
- Estimated profit in USD
- Expected gas costs
- Net profit calculation

#### Controls & Analysis

| Key   | Action              | Description                       |
| ----- | ------------------- | --------------------------------- |
| `ESC` | Close Inspector     | Return to main interface          |
| `E`   | Execute Opportunity | Log execution and close inspector |
| `R`   | Reject Opportunity  | Log rejection and close inspector |

#### Educational Value

The inspector helps operators understand:

- How each pillar contributes to decisions
- What makes opportunities attractive or risky
- Mathematical basis for autonomous decisions
- When manual override might be appropriate

## Status Indicators

### Color Coding System

#### Green (🟢) - Healthy/Success

- Components running normally
- Successful trades and operations
- Connected network nodes
- Valid configuration
- Live data feeds

#### Yellow (🟡) - Warning/Caution

- Paused operations
- High gas costs or network congestion
- Reconnecting nodes
- Modified but unsaved configuration
- Stale data feeds

#### Red (🔴) - Error/Critical

- Failed operations or trades
- Component errors or crashes
- Disconnected network nodes
- Invalid configuration
- Mock/simulation data fallback

#### Blue (🔵) - Information/Active

- Selected items or active focus
- Pending operations
- Detected opportunities
- Current panel focus

#### Cyan - Strategy Inspector

- Inspector modal elements
- Opportunity summaries
- Final scores and analysis
- ARE component highlights

#### Gray - Inactive/Disabled

- Stopped components
- Disabled features
- Historical data
- Rejected opportunities

### Status Symbols

| Symbol | Meaning                        |
| ------ | ------------------------------ |
| ●      | Status indicator (colored dot) |
| ▶     | Running/Active state           |
| ⏸     | Paused state                   |
| ⏹     | Stopped state                  |
| ⚠     | Warning condition              |
| ✗      | Error condition                |
| ✓      | Success condition              |
| ⟳      | Reconnecting/Restarting        |

## Keyboard Reference

### Complete Key Mapping

#### Global Keys (Available in All Tabs)

| Key     | Action             | Notes                      |
| ------- | ------------------ | -------------------------- |
| `Q`     | Quit Application   | Immediate exit             |
| `1`     | Dashboard Tab      | Direct navigation          |
| `2`     | Operations Tab     | Direct navigation          |
| `3`     | Systems Tab        | Direct navigation          |
| `4`     | Config Tab         | Direct navigation          |
| `Tab`   | Next Tab/Panel     | Context-sensitive          |
| `↑`     | Navigate Up        | Lists and menus            |
| `↓`     | Navigate Down      | Lists and menus            |
| `Enter` | Select/Confirm     | Context-sensitive          |
| `Esc`   | Cancel/Back        | Context-sensitive          |
| `I`     | Strategy Inspector | When opportunity available |

#### Dashboard Tab Keys

| Key     | Action            | Context                                  |
| ------- | ----------------- | ---------------------------------------- |
| `Tab`   | Cycle ARE Widgets | Network → Chronos → Mandorla → Execution |
| `Enter` | Drill Down        | Navigate to detailed analysis            |

#### Operations Tab Keys

| Key         | Action              | Context                            |
| ----------- | ------------------- | ---------------------------------- |
| `S`         | Start/Stop Bot      | Master control                     |
| `P`         | Pause Bot           | Master control                     |
| `R`         | Restart Bot         | Master control                     |
| `E`         | Emergency Stop      | Master control (with confirmation) |
| `G`         | Graceful Stop       | Master control                     |
| `F`         | Filter Activity Log | Log viewer                         |
| `C`         | Clear Log Filter    | Log viewer                         |
| `Page Up`   | Fast Scroll Up      | Log viewer                         |
| `Page Down` | Fast Scroll Down    | Log viewer                         |

#### Systems Tab Keys

| Key   | Action            | Context                              |
| ----- | ----------------- | ------------------------------------ |
| `Tab` | Switch Panel      | Logs → Components → Services → Nodes |
| `R`   | Restart/Reconnect | Component/Service/Node management    |
| `S`   | Start Service     | Service management                   |
| `K`   | Stop Service      | Service management                   |
| `F`   | Filter Logs       | Log viewer                           |
| `C`   | Clear Filter      | Log viewer                           |
| `/`   | Search Logs       | Log viewer                           |
| `E`   | Export Logs       | Log viewer                           |
| `X`   | Clear All Logs    | Log viewer (with confirmation)       |

#### Config Tab Keys

| Key     | Action            | Context                         |
| ------- | ----------------- | ------------------------------- |
| `Tab`   | Switch Panel      | Sections → Parameters → Details |
| `S`     | Save & Hot-Reload | Configuration management        |
| `R`     | Reset to Defaults | Configuration management        |
| `L`     | Load Profile      | Configuration management        |
| `V`     | Validate Config   | Configuration management        |
| `Enter` | Edit Parameter    | Parameter editor                |

#### Strategy Inspector Keys

| Key   | Action              | Context                    |
| ----- | ------------------- | -------------------------- |
| `I`   | Open Inspector      | From opportunity selection |
| `ESC` | Close Inspector     | Inspector modal            |
| `E`   | Execute Opportunity | Inspector modal            |
| `R`   | Reject Opportunity  | Inspector modal            |

## Interface Features

### Real-Time Data Integration

#### NATS Message Bus Integration

- Live market data consumption
- Real-time component status updates
- Network monitoring data
- Trading execution results

#### Data Pipeline Visualization

```
NATS Message Bus → Data Processing → TUI Updates
     ↓                  ↓              ↓
Market Feeds    →  ARE Components → Dashboard Widgets
Network Data    →  Analysis Engine → Status Indicators
Trading Data    →  Risk Manager   → Activity Logs
```

#### Data Quality Transparency

All widgets show data source status:

- Real-time freshness indicators
- Automatic fallback to simulation data
- Clear distinction between live and cached data

### Progressive Disclosure Design

#### Information Hierarchy

1. **Tab Level**: High-level operational areas
2. **Panel Level**: Specific functional areas within tabs
3. **Widget Level**: Individual data visualizations
4. **Detail Level**: Drill-down analysis and inspector views

#### Navigation Flow

- Start with Dashboard for situational awareness
- Move to Operations for active control
- Use Systems for troubleshooting
- Access Config for parameter adjustments

### Safety and Confirmation Systems

#### Critical Operation Confirmations

- Emergency stop procedures
- Configuration resets
- Log clearing operations
- Service restarts

#### Error Prevention

- Real-time validation feedback
- Clear status indicators
- Confirmation dialogs for destructive actions
- Rollback capabilities

### Accessibility Features

#### Visual Indicators

- Color coding with symbol backup
- Clear typography and spacing
- High contrast status indicators
- Consistent layout patterns

#### Keyboard Navigation

- Full keyboard accessibility
- Logical tab order
- Consistent key mappings
- Context-sensitive help

This comprehensive navigation guide ensures operators can effectively use all TUI features for safe and efficient trading operations across all deployment modes.
