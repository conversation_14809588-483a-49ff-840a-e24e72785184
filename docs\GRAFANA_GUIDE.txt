✦ To implement a Grafana dashboard for Prometheus metrics to
  monitor your bot's performance, you'll generally follow these
  steps:


   1. Ensure Prometheus is collecting metrics from your bot. Your
      bot application needs to expose metrics in a
      Prometheus-compatible format, and your Prometheus server
      needs to be configured to scrape these metrics.
   2. Add Prometheus as a data source in Grafana.
   3. Create or import a Grafana dashboard.


  Since I cannot directly interact with your Grafana UI, I will
  provide you with the instructions and a sample dashboard JSON
  that you can import.

  Step 1: Add Prometheus as a Data Source in Grafana


  Assuming your Grafana instance is running (e.g., at
  http://localhost:3000) and your Prometheus server is
  accessible (e.g., at http://localhost:9090), follow these
  steps in your web browser:


   1. Open your Grafana instance in a web browser.
   2. In the left-hand menu, hover over the "Connections" icon
      (plug icon) and click on "Data sources".
   3. Click the "Add data source" button.
   4. Select "Prometheus" from the list of available data sources.
   5. In the "HTTP" section, set the "URL" field to your Prometheus
      server's address (e.g., http://localhost:9090).
   6. (Optional) Configure other settings like authentication if
      necessary.
   7. Click the "Save & test" button. You should see a "Data source
      is working" message.

  Step 2: Import a Sample Grafana Dashboard


  You can import a dashboard by providing its JSON model. Below
  is a sample JSON for a basic bot performance dashboard. This
  dashboard includes panels for:


   * Total Bot Requests: A counter for the total number of
     requests your bot has processed.
   * Bot Error Rate: The percentage of requests that resulted in
     an error.
   * Bot Request Duration (P99): The 99th percentile of your bot's
     request duration.


  Note: You will need to replace the placeholder metric names
  (e.g., bot_requests_total, bot_errors_total,
  bot_request_duration_seconds) with the actual metric names
  exposed by your bot.

  To import this dashboard:


   1. In Grafana, hover over the "Dashboards" icon (four squares)
      in the left-hand menu and click on "New dashboard".
   2. Click the "Import dashboard" button.
   3. In the "Import via panel json" text area, paste the JSON
      content provided below.
   4. Click "Load".
   5. On the next screen, select your Prometheus data source from
      the dropdown (the one you configured in Step 1).
   6. Click "Import".


  Here is the sample dashboard JSON:


 ╭─────────────────────────────────────────────────────────╮
 │ ✔  WriteFile Writing to bot_performance_dashboard.json  │
 │                                                         │
 │    ... first 208 lines hidden ...                       │
 │        sum(rate(bot_requests_total[5m])) * 100",        │
 │    208           "legendFormat": "Error Rate",          │
 │    209           "range": true,                         │
 │    210           "refId": "A"                           │
 │    211         }                                        │
 │    212       ],                                         │
 │    213       "title": "Bot Error Rate",                 │
 │    214       "type": "timeseries"                       │
 │    215     },                                           │
 │    216     {                                            │
 │    217       "datasource": {                            │
 │    218         "type": "prometheus",                    │
 │    219         "uid": "prometheus"                      │
 │    220       },                                         │
 │    221       "fieldConfig": {                           │
 │    222         "defaults": {                            │
 │    223           "color": {                             │
 │    224             "mode": "palette-classic"            │
 │    225           },                                     │
 │    226           "custom": {                            │
 │    227             "axisCenteredZero": false,           │
 │    228             "axisColorMode": "text",             │
 │    229             "axisLabel": "",                     │
 │    230             "axisPlacement": "auto",             │
 │    231             "barAlignment": 0,                   │
 │    232             "drawStyle": "line",                 │
 │    233             "fillOpacity": 0,                    │
 │    234             "gradientMode": "none",              │
 │    235             "hideFrom": {                        │
 │    236               "legend": false,                   │
 │    237               "tooltip": false,                  │
 │    238               "viz": false                       │
 │    239             },                                   │
 │    240             "lineInterpolation": "linear",       │
 │    241             "lineStyle": {                       │
 │    242               "fill": "solid"                    │
 │    243             },                                   │
 │    244             "lineWidth": 1,                      │
 │    245             "pointSize": 5,                      │
 │    246             "scaleDistribution": {               │
 │    247               "type": "linear"                   │
 │    248             },                                   │
 │    249             "showPoints": "auto",                │
 │    250             "spanNulls": false,                  │
 │    251             "stacking": {                        │
 │    252               "group": "A",                      │
 │    253               "mode": "none"                     │
 │    254             },                                   │
 │    255             "thresholdsStyle": {                 │
 │    256               "mode": "off"                      │
 │    257             }                                    │
 │    258           },                                     │
 │    259           "mappings": [],                        │
 │    260           "thresholds": {                        │
 │    261             "mode": "absolute",                  │
 │    262             "steps": [                           │
 │    263               {                                  │
 │    264                 "color": "green",                │
 │    265                 "value": null                    │
 │    266               },                                 │
 │    267               {                                  │
 │    268                 "color": "red",                  │
 │    269                 "value": 80                      │
 │    270               }                                  │
 │    271             ]                                    │
 │    272           },                                     │
 │    273           "unit": "s"                            │
 │    274         },                                       │
 │    275         "overrides": []                          │
 │    276       },                                         │
 │    277       "gridPos": {                               │
 │    278         "h": 8,                                  │
 │    279         "w": 12,                                 │
 │    280         "x": 0,                                  │
 │    281         "y": 8                                   │
 │    282       },                                         │
 │    283       "id": 3,                                   │
 │    284       "options": {                               │
 │    285         "legend": {                              │
 │    286           "calcs": [],                           │
 │    287           "displayMode": "list",                 │
 │    288           "placement": "bottom",                 │
 │    289           "showLegend": true                     │
 │    290         },                                       │
 │    291         "tooltip": {                             │
 │    292           "mode": "single",                      │
 │    293           "sort": "none"                         │
 │    294         }                                        │
 │    295       },                                         │
 │    296       "targets": [                               │
 │    297         {                                        │
 │    298           "datasource": {                        │
 │    299             "type": "prometheus",                │
 │    300             "uid": "prometheus"                  │
 │    301           },                                     │
 │    302           "editorMode": "builder",               │
 │    303           "expr": "histogram_quantile(0.99,      │
 │        sum by (le)                                      │
 │        (rate(bot_request_duration_seconds_bucket[5m])   │
 │        ))",                                             │
 │    304           "legendFormat": "P99 Request           │
 │        Duration",                                       │
 │    305           "range": true,                         │
 │    306           "refId": "A"                           │
 │    307         }                                        │
 │    308       ],                                         │
 │    309       "title": "Bot Request Duration (P99)",     │
 │    310       "type": "timeseries"                       │
 │    311     }                                            │
 │    312   ],                                             │
 │    313   "schemaVersion": 38,                           │
 │    314   "style": "dark",                               │
 │    315   "tags": [],                                    │
 │    316   "templating": {                                │
 │    317     "list": []                                   │
 │    318   },                                             │
 │    319   "time": {                                      │
 │    320     "from": "now-1h",                            │
 │    321     "to": "now"                                  │
 │    322   },                                             │
 │    323   "timepicker": {},                              │
 │    324   "timezone": "",                                │
 │    325   "title": "Bot Performance Dashboard",          │
 │    326   "uid": "bot_performance",                      │
 │    327   "version": 1                                   │
 │    328 }                                                │
 ╰─────────────────────────────────────────────────────────╯
✦ I have created a file named bot_performance_dashboard.json in
  your current directory (C:\Users\<USER>\) containing a sample
  Grafana dashboard for bot performance monitoring.

  You can import this dashboard into Grafana by following these
  steps:


   1. Ensure Prometheus is collecting metrics from your bot. Your
      bot application needs to expose metrics in a
      Prometheus-compatible format, and your Prometheus server
      needs to be configured to scrape these metrics.
   2. Add Prometheus as a data source in Grafana.
       * Open your Grafana instance in a web browser.
       * In the left-hand menu, hover over the "Connections" icon
         (plug icon) and click on "Data sources".
       * Click the "Add data source" button.
       * Select "Prometheus" from the list.
       * Set the "URL" field to your Prometheus server's address
         (e.g., http://localhost:9090).
       * Click "Save & test".
   3. Import the dashboard:
       * In Grafana, hover over the "Dashboards" icon (four
         squares) in the left-hand menu and click on "New
         dashboard".
       * Click the "Import dashboard" button.
       * Copy the content of the bot_performance_dashboard.json
         file and paste it into the "Import via panel json" text
         area.
       * Click "Load".
       * Select your Prometheus data source from the dropdown.
       * Click "Import".


  Remember to replace the placeholder metric names in the
  dashboard (e.g., bot_requests_total, bot_errors_total,
  bot_request_duration_seconds) with the actual metric names
  exposed by your bot.