### **`/docs/onchain_data/BASE_REFERENCE.md` (OROBOROS Edition v2)**

# **Base Blockchain: On-Chain Data Reference**

**To:** Coder Agent
**From:** The Architect
**Subject:** Operational Data & Targeting Parameters (OROBOROS Protocol Update)

This document provides the essential, verified on-chain data for operations on the Base network. It has been significantly updated with a **deep list of redundant RPC endpoints** to fortify the `Protocol: OROBOROS` self-healing system.

### **How to Use This Document for OROBOROS**

- **RPC Endpoint Redundancy:** The `config.toml` file must be structured to support a list of RPC endpoints, not a single primary and backup. This allows OROBOROS to cycle through multiple providers to find a stable connection during a failure.
  ```toml
  # config.toml
  [rpc.base_mainnet]
  # OROBOROS will try these in order upon failure.
  endpoints = [
      "https://mainnet.base.org", # Official
      "https://base.publicnode.com", # Backup 1
      "https://base.drpc.org", # Backup 2
      # ... and so on from the list below
  ]
  ```
- **DEX Address Targeting:** The addresses provided are the specific targets for the `Basilisk's Gaze` and other core strategies. They must be loaded into the bot's configuration.

---

### **Network RPC Endpoints (Expanded for Redundancy)**

A deep list of reliable public RPC endpoints for interacting with the Base network. **For production, replace this list with dedicated, private endpoints from multiple providers.**

#### **Base Mainnet**

| Provider/Notes        | Endpoint URL                                        |
| :-------------------- | :-------------------------------------------------- |
| **Official Endpoint** | `https://mainnet.base.org`                          |
| PublicNode            | `https://base.publicnode.com`                       |
| DRPC                  | `https://base.drpc.org`                             |
| 1RPC                  | `https://1rpc.io/base`                              |
| Ankr                  | `https://rpc.ankr.com/base`                         |
| BlastAPI              | `https://base-mainnet.public.blastapi.io`           |
| BlockPI               | `https://base.public.blockpi.network/v1/rpc/public` |
| Lava Network          | `https://base.lava.build`                           |

#### **Base Sepolia Testnet**

| Provider/Notes        | Endpoint URL                                         |
| :-------------------- | :--------------------------------------------------- |
| **Official Endpoint** | `https://sepolia.base.org`                           |
| PublicNode            | `https://base-sepolia-rpc.publicnode.com`            |
| Tenderly              | `https://base-sepolia.gateway.tenderly.co`           |
| StackUp               | `https://public.stackup.sh/api/v1/node/base-sepolia` |
| NotADegen             | `https://rpc.notadegen.com/base/sepolia`             |

---

### **Official Block Explorer**

The primary tool for manual verification of transactions, contracts, and on-chain state.

- **Basescan:** `https://basescan.org`

---

### **DEX Smart Contract Addresses (Base Mainnet)**

Core contract addresses for prominent Decentralized Exchanges (DEXs) on Base Mainnet.

> **CRITICAL VERIFICATION NOTICE:** Smart contract addresses can be upgraded. An outdated address is a fatal error that will lead to financial loss. **Always cross-reference these addresses with official documentation and Basescan before production deployment.**

| DEX Protocol          | Contract Name      | Official Address                                 |
| :-------------------- | :----------------- | :----------------------------------------------- |
| **Uniswap V3**        | Factory            | `******************************************`     |
|                       | SwapRouter02       | `******************************************`     |
|                       | Universal Router   | `******************************************`     |
| **Aerodrome Finance** | PoolFactory        | `******************************************`     |
|                       | Router             | `******************************************`     |
|                       | **WETH/USDC Pool** | **`******************************************`** |
| **SushiSwap**         | Factory            | `******************************************`     |
|                       | Router (V2)        | `******************************************`     |
| **Balancer**          | Vault              | `******************************************`     |
