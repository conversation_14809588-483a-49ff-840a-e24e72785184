// MISSION: Trade Lifecycle Component - Educational Transaction Tracking
// WHY: Show users the complete journey of each trade with educational context
// HOW: Real-time display of trade status updates with rich narratives

use async_nats::Client as NatsClient;
use ratatui::{
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span, Text},
    widgets::{Block, Borders, Clear, List, ListItem, ListState, Paragraph, Wrap},
    Frame,
};
use std::collections::{HashMap, VecDeque};
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::{debug, error, info};

use crate::shared_types::trade_lifecycle::{TradeLifecycleEvent, TradeStatus};

/// Maximum number of trade events to keep in memory
const MAX_TRADE_HISTORY: usize = 200;

/// The Trade Lifecycle component - displays trade execution with educational context
pub struct TradeLifecycle {
    /// All trade lifecycle events organized by trade ID
    trade_history: HashMap<String, VecDeque<TradeLifecycleEvent>>,
    
    /// Chronological list of all events for the main view
    all_events: VecDeque<TradeLifecycleEvent>,
    
    /// Currently selected trade for detailed view
    selected_trade_id: Option<String>,
    
    /// List state for navigation
    list_state: ListState,
    
    /// Event receiver for new trade updates
    event_receiver: Option<mpsc::UnboundedReceiver<TradeLifecycleEvent>>,
    
    /// Show detailed view for selected trade
    show_details: bool,
}

impl TradeLifecycle {
    /// Create a new Trade Lifecycle component
    pub fn new() -> Self {
        Self {
            trade_history: HashMap::new(),
            all_events: VecDeque::new(),
            selected_trade_id: None,
            list_state: ListState::default(),
            event_receiver: None,
            show_details: false,
        }
    }
    
    /// Initialize the component with NATS client
    pub async fn init(&mut self, nats_client: NatsClient) -> Result<(), Box<dyn std::error::Error>> {
        let (tx, rx) = mpsc::unbounded_channel();
        self.event_receiver = Some(rx);
        
        // Subscribe to trade lifecycle events
        let mut subscriber = nats_client
            .subscribe("trade.lifecycle.events")
            .await?;
        
        // Spawn task to listen for events
        tokio::spawn(async move {
            while let Some(message) = subscriber.next().await {
                if let Ok(event) = serde_json::from_slice::<TradeLifecycleEvent>(&message.payload) {
                    if tx.send(event).is_err() {
                        break; // Channel closed
                    }
                }
            }
        });
        
        info!("Trade Lifecycle component initialized");
        Ok(())
    }
    
    /// Update the component state (call this regularly from the main loop)
    pub async fn update(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Process any new trade lifecycle events
        let mut events_to_process = Vec::new();
        if let Some(receiver) = &mut self.event_receiver {
            while let Ok(event) = receiver.try_recv() {
                events_to_process.push(event);
            }
        }
        
        // Process all collected events
        for event in events_to_process {
            self.add_trade_event(event);
        }
        
        Ok(())
    }
    
    /// Add a new trade lifecycle event
    fn add_trade_event(&mut self, event: TradeLifecycleEvent) {
        let trade_id = event.trade_id.clone();
        
        // Add to trade-specific history
        let trade_events = self.trade_history.entry(trade_id.clone()).or_insert_with(VecDeque::new);
        trade_events.push_back(event.clone());
        
        // Add to chronological list
        self.all_events.push_back(event);
        
        // Trim history if too long
        if self.all_events.len() > MAX_TRADE_HISTORY {
            self.all_events.pop_front();
        }
        
        debug!("Added trade lifecycle event for trade {}", trade_id);
    }
    
    /// Handle key input
    pub fn handle_key(&mut self, key: crossterm::event::KeyCode) {
        use crossterm::event::KeyCode;
        
        match key {
            KeyCode::Up => {
                if let Some(selected) = self.list_state.selected() {
                    if selected > 0 {
                        self.list_state.select(Some(selected - 1));
                    }
                } else if !self.all_events.is_empty() {
                    self.list_state.select(Some(self.all_events.len() - 1));
                }
            }
            KeyCode::Down => {
                if let Some(selected) = self.list_state.selected() {
                    if selected < self.all_events.len().saturating_sub(1) {
                        self.list_state.select(Some(selected + 1));
                    }
                } else if !self.all_events.is_empty() {
                    self.list_state.select(Some(0));
                }
            }
            KeyCode::Enter => {
                if let Some(selected) = self.list_state.selected() {
                    if let Some(event) = self.all_events.get(selected) {
                        self.selected_trade_id = Some(event.trade_id.clone());
                        self.show_details = true;
                    }
                }
            }
            KeyCode::Esc => {
                self.show_details = false;
                self.selected_trade_id = None;
            }
            _ => {}
        }
    }
    
    /// Render the component
    pub fn render(&mut self, f: &mut Frame, area: Rect) {
        if self.show_details && self.selected_trade_id.is_some() {
            self.render_detailed_view(f, area);
        } else {
            self.render_overview(f, area);
        }
    }
    
    /// Render the overview of all trades
    fn render_overview(&mut self, f: &mut Frame, area: Rect) {
        let block = Block::default()
            .title("Trade Lifecycle - Educational View")
            .borders(Borders::ALL)
            .border_style(Style::default().fg(Color::Cyan));
        
        if self.all_events.is_empty() {
            let paragraph = Paragraph::new("No trade events yet. Trades will appear here as they are executed.")
                .block(block)
                .wrap(Wrap { trim: true });
            f.render_widget(paragraph, area);
            return;
        }
        
        // Create list items from events
        let items: Vec<ListItem> = self.all_events
            .iter()
            .rev() // Show newest first
            .map(|event| {
                let (icon, status_text) = event.status.status_indicator();
                let time = event.formatted_timestamp();
                let trade_id_short = &event.trade_id[..8.min(event.trade_id.len())];
                
                let content = format!(
                    "{} [{}] {} - {} ({})",
                    icon,
                    time,
                    trade_id_short,
                    status_text,
                    event.opportunity_type
                );
                
                let style = match &event.status {
                    TradeStatus::Success { .. } => Style::default().fg(Color::Green),
                    TradeStatus::Failed { .. } | TradeStatus::Reverted { .. } => Style::default().fg(Color::Red),
                    TradeStatus::PendingInMempool { .. } => Style::default().fg(Color::Yellow),
                    _ => Style::default().fg(Color::White),
                };
                
                ListItem::new(content).style(style)
            })
            .collect();
        
        let list = List::new(items)
            .block(block)
            .highlight_style(Style::default().add_modifier(Modifier::REVERSED))
            .highlight_symbol(">> ");
        
        f.render_stateful_widget(list, area, &mut self.list_state);
        
        // Show help text at bottom
        let help_area = Rect {
            x: area.x + 1,
            y: area.y + area.height - 2,
            width: area.width - 2,
            height: 1,
        };
        
        let help_text = Paragraph::new("↑/↓: Navigate | Enter: View Details | ESC: Back")
            .style(Style::default().fg(Color::Gray));
        f.render_widget(help_text, help_area);
    }
    
    /// Render detailed view for a specific trade
    fn render_detailed_view(&mut self, f: &mut Frame, area: Rect) {
        if let Some(trade_id) = &self.selected_trade_id {
            if let Some(events) = self.trade_history.get(trade_id) {
                let block = Block::default()
                    .title(format!("Trade Details - {}", &trade_id[..16.min(trade_id.len())]))
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Cyan));
                
                let inner = block.inner(area);
                f.render_widget(block, area);
                
                // Split into timeline and details
                let chunks = Layout::default()
                    .direction(Direction::Vertical)
                    .constraints([Constraint::Min(10), Constraint::Length(5)])
                    .split(inner);
                
                // Render timeline
                self.render_trade_timeline(f, chunks[0], events);
                
                // Render educational context for latest event
                if let Some(latest_event) = events.back() {
                    self.render_educational_context(f, chunks[1], latest_event);
                }
            }
        }
    }
    
    /// Render the timeline for a specific trade
    fn render_trade_timeline(&self, f: &mut Frame, area: Rect, events: &VecDeque<TradeLifecycleEvent>) {
        let items: Vec<ListItem> = events
            .iter()
            .map(|event| {
                let (icon, status_text) = event.status.status_indicator();
                let time = event.formatted_timestamp();
                
                let content = format!("{} [{}] {}", icon, time, status_text);
                
                let style = match &event.status {
                    TradeStatus::Success { .. } => Style::default().fg(Color::Green),
                    TradeStatus::Failed { .. } | TradeStatus::Reverted { .. } => Style::default().fg(Color::Red),
                    TradeStatus::PendingInMempool { .. } => Style::default().fg(Color::Yellow),
                    _ => Style::default().fg(Color::White),
                };
                
                ListItem::new(content).style(style)
            })
            .collect();
        
        let list = List::new(items)
            .block(Block::default().title("Timeline").borders(Borders::ALL));
        
        f.render_widget(list, area);
    }
    
    /// Render educational context for an event
    fn render_educational_context(&self, f: &mut Frame, area: Rect, event: &TradeLifecycleEvent) {
        let paragraph = Paragraph::new(event.educational_context.clone())
            .block(Block::default().title("Educational Context").borders(Borders::ALL))
            .wrap(Wrap { trim: true })
            .style(Style::default().fg(Color::White));
        
        f.render_widget(paragraph, area);
    }
}

impl Default for TradeLifecycle {
    fn default() -> Self {
        Self::new()
    }
}