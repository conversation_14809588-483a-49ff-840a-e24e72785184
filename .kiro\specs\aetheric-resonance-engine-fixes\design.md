# Design Document

## Overview

This document outlines the design for implementing comprehensive fixes to the Aetheric Resonance Engine (ARE) based on critical audit findings from Tasks 5-15. The design addresses 27 critical findings across mathematical errors, integration gaps, and implementation flaws in all three pillars while maintaining the system's architectural integrity and ensuring components work together cohesively for profitable trading.

### Critical Issues Summary from Audit

**Task 15 - Scoring Engine Integration**: Configured weights completely ignored, incomplete geometric score usage, misleading educational reports
**Task 13 - Aetheric Resonance Score**: Zero-out problem with missing pillar data, unused pillar weights, incomplete pillar integration  
**Task 12 - Network Integration**: ExecutionManager doesn't use network state, HarmonicTimingOracle not integrated, gas strategy doesn't receive network conditions
**Task 11 - Network State Analysis**: Missing censorship detection, inaccurate network coherence formula, oversimplified sequencer monitoring
**Task 9 - Asset Centrality**: Empty centrality scores initialization, broken price oracle integration, architectural disconnection
**Task 8 - Vesica Piscis**: Negative price deviation bug (50% of arbitrage opportunities ignored), not integrated into geometric scoring
**Task 7 - Geometric Score**: Liquidity centroid bias not implemented, price oracle address resolution broken
**Task 6 - Hurst Exponent**: Mathematical errors in R/S analysis, insufficient data requirements, biased variance calculation
**Task 5 - Temporal Harmonics**: Market rhythm stability calculation flawed, incomplete StrategyManager integration, oversimplified wavelet implementation

## Architecture

The fix implementation follows a component-by-component approach that systematically addresses critical issues while maintaining backward compatibility and system stability. The architecture preserves the three-pillar design (Chronos Sieve, Mandorla Gauge, Network Seismology) while fixing fundamental flaws in each component.

### Core Design Principles

1. **Mathematical Correctness**: All calculations must be mathematically sound and numerically stable
2. **Component Integration**: Components must share data effectively and operate cohesively
3. **Graceful Degradation**: System must handle missing or invalid data without catastrophic failure
4. **Configuration Flexibility**: All parameters must be configurable with proper validation
5. **Performance Optimization**: Fixes must not significantly impact system performance
6. **Zero-Out Prevention**: Use neutral scores (0.5) instead of zero when data is missing
7. **Complete Data Usage**: All calculated components must be used in final scoring
8. **Proper Weight Application**: Configured weights must be actually applied in calculations

### Fix Priority Matrix

| Component              | Priority | Impact   | Risk Level | Estimated Effort |
| ---------------------- | -------- | -------- | ---------- | ---------------- |
| Scoring Engine Weights | P0       | Critical | High       | 4-6 hours        |
| Zero-Out Problem       | P0       | Critical | High       | 2-3 hours        |
| Vesica Piscis Bug      | P0       | Critical | High       | 1-2 hours        |
| Centrality Scores      | P0       | Critical | High       | 2-3 hours        |
| Network Integration    | P1       | High     | Medium     | 6-8 hours        |
| Temporal Analysis      | P1       | High     | Medium     | 8-10 hours       |
| Geometric Fixes        | P1       | High     | Medium     | 4-6 hours        |
| Hurst Exponent         | P2       | Medium   | Medium     | 6-8 hours        |

## Components and Interfaces

### 1. Scoring Engine Fixes

**Purpose**: Fix the core scoring calculation to properly use configured weights and complete data
**Priority**: CRITICAL - Affects entire system functionality

#### Current Issues

- Configured weights completely ignored in calculations
- Only partial geometric score components used
- Missing pillar data causes zero-out problem
- Misleading educational reports

#### Design Solution

```rust
pub struct FixedScoringEngine {
    config: ScoringConfig,
    geometric_scorer: Arc<GeometricScorer>,
    price_oracle: Arc<dyn PriceOracle>,
}

impl FixedScoringEngine {
    pub async fn calculate_opportunity_score_fixed(
        &self,
        opportunity: &Opportunity,
        market_regime: &MarketRegime,
        temporal_harmonics: &Option<TemporalHarmonics>,
        network_resonance: &Option<NetworkResonanceState>,
        centrality_scores: &Arc<HashMap<String, Decimal>>,
    ) -> Result<Decimal> {
        // Step 1: Calculate risk-adjusted profit with bounds checking
        let certainty_equivalent = self.calculate_certainty_equivalent(opportunity)?;

        // Step 2: Apply regime multiplier
        let regime_multiplier = self.get_regime_multiplier(market_regime);

        // Step 3: Calculate comprehensive pillar scores with fallbacks
        let temporal_score = self.calculate_temporal_score(temporal_harmonics)?;
        let geometric_score = self.calculate_geometric_score(opportunity, centrality_scores).await?;
        let network_score = self.calculate_network_score(network_resonance)?;

        // Step 4: Apply proper weighting (FIXED)
        let weighted_pillar_score = (temporal_score * self.config.temporal_harmonics_weight) +
                                   (geometric_score * self.config.geometric_score_weight) +
                                   (network_score * self.config.network_resonance_weight);

        // Step 5: Final score calculation
        let final_score = certainty_equivalent * regime_multiplier * weighted_pillar_score;

        Ok(final_score.max(dec!(0.0)))
    }

    fn calculate_temporal_score(&self, harmonics: &Option<TemporalHarmonics>) -> Result<Decimal> {
        match harmonics {
            Some(h) => {
                // FIXED: Use multiple components instead of just stability
                let stability_score = Decimal::from_f64(h.market_rhythm_stability)
                    .unwrap_or(NEUTRAL_SCORE);

                let cycle_alignment = self.calculate_cycle_alignment(&h.dominant_cycles_minutes)?;

                // FIXED: Include wavelet features (was ignored)
                let wavelet_score = self.calculate_wavelet_score(&h.wavelet_features)?;

                // Weighted combination of all temporal components
                let combined_score = (stability_score * dec!(0.5)) +
                                   (cycle_alignment * dec!(0.3)) +
                                   (wavelet_score * dec!(0.2));

                debug!("Temporal score: stability={:.3}, cycles={:.3}, wavelets={:.3}, combined={:.3}",
                       stability_score, cycle_alignment, wavelet_score, combined_score);

                Ok(combined_score)
            },
            None => {
                warn!("No temporal harmonics available, using neutral score");
                Ok(NEUTRAL_SCORE)
            }
        }
    }

    // IMPLEMENT: Cycle alignment calculation (was missing)
    fn calculate_cycle_alignment(&self, cycles: &[(f64, f64)]) -> Result<Decimal> {
        if cycles.is_empty() {
            return Ok(NEUTRAL_SCORE);
        }

        let strongest_cycle = &cycles[0];
        let period_minutes = strongest_cycle.0;
        let amplitude = strongest_cycle.1;

        // Calculate where we are in the cycle
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs() / 60; // Convert to minutes

        let cycle_position = (current_time as f64 % period_minutes) / period_minutes;

        // Score based on cycle phase (simplified - could use more sophisticated analysis)
        let phase_score = if cycle_position < 0.25 || cycle_position > 0.75 {
            0.8 // Good timing (cycle peaks/troughs)
        } else {
            0.4 // Poor timing (cycle transitions)
        };

        let alignment_score = phase_score * amplitude;
        Ok(Decimal::from_f64(alignment_score).unwrap_or(NEUTRAL_SCORE))
    }

    // IMPLEMENT: Wavelet score calculation (was oversimplified)
    fn calculate_wavelet_score(&self, wavelet_features: &[f64]) -> Result<Decimal> {
        if wavelet_features.is_empty() {
            return Ok(NEUTRAL_SCORE);
        }

        // Calculate energy distribution across scales
        let total_energy: f64 = wavelet_features.iter().sum();
        if total_energy == 0.0 {
            return Ok(NEUTRAL_SCORE);
        }

        // Higher energy in lower frequencies (longer-term patterns) is preferred
        let mut weighted_score = 0.0;
        for (i, &energy) in wavelet_features.iter().enumerate() {
            let scale_weight = 1.0 / (i + 1) as f64; // Higher weight for lower frequencies
            weighted_score += (energy / total_energy) * scale_weight;
        }

        Ok(Decimal::from_f64(weighted_score).unwrap_or(NEUTRAL_SCORE))
    }

    async fn calculate_geometric_score(
        &self,
        opportunity: &Opportunity,
        centrality_scores: &HashMap<String, Decimal>
    ) -> Result<Decimal> {
        let path = self.opportunity_to_arbitrage_path(opportunity);
        match self.geometric_scorer.calculate_complete_score(&path, centrality_scores).await {
            Ok(score) => {
                // Use ALL three components (FIXED)
                let complete_score = (score.convexity_ratio +
                                    score.harmonic_path_score +
                                    score.liquidity_centroid_bias) / dec!(3.0);
                Ok(complete_score)
            },
            Err(e) => {
                warn!("Geometric scoring failed: {}, using neutral score", e);
                Ok(NEUTRAL_SCORE)
            }
        }
    }

    fn calculate_network_score(&self, network_state: &Option<NetworkResonanceState>) -> Result<Decimal> {
        match network_state {
            Some(state) => {
                let coherence = Decimal::from_f64(state.network_coherence_score)
                    .unwrap_or(NEUTRAL_SCORE);
                let latency_score = self.calculate_latency_score(state.sp_time_ms);
                let stability_score = if state.is_shock_event { dec!(0.2) } else { dec!(0.8) };
                Ok((coherence + latency_score + stability_score) / dec!(3.0))
            },
            None => {
                warn!("No network resonance state available, using neutral score");
                Ok(NEUTRAL_SCORE)
            }
        }
    }
}

const NEUTRAL_SCORE: Decimal = dec!(0.5);
```

### 2. Mathematical Component Fixes

**Purpose**: Correct mathematical errors in temporal analysis, geometric calculations, and network metrics
**Priority**: CRITICAL - Mathematical errors cause systematic trading losses

#### Temporal Analysis Fixes

```rust
pub struct FixedTemporalAnalyzer {
    config: TemporalConfig,
    fft_planner: Arc<Mutex<FftPlanner<f64>>>,
}

impl FixedTemporalAnalyzer {
    // CRITICAL FIX: Market rhythm stability calculation (was measuring spectral concentration, not stability)
    fn calculate_rhythm_stability_fixed(&self, price_history: &[Decimal]) -> f64 {
        const WINDOW_SIZE: usize = 60; // 1 hour windows
        const MIN_WINDOWS: usize = 4;  // Minimum for stability calculation
        const MIN_DATA_POINTS: usize = 240; // FIXED: Increased from 60 to 240 (4 hours)

        if price_history.len() < MIN_DATA_POINTS {
            warn!("Insufficient data for temporal analysis: {} < {}",
                  price_history.len(), MIN_DATA_POINTS);
            return 0.0;
        }

        let mut cycle_periods = Vec::new();

        // Calculate dominant cycle for each overlapping window
        for window_start in (0..price_history.len() - WINDOW_SIZE).step_by(WINDOW_SIZE / 2) {
            let window_end = (window_start + WINDOW_SIZE).min(price_history.len());
            let window_data = &price_history[window_start..window_end];

            if let Some(dominant_period) = self.find_dominant_cycle_with_validation(window_data) {
                cycle_periods.push(dominant_period);
            }
        }

        if cycle_periods.len() < MIN_WINDOWS {
            return 0.0;
        }

        // FIXED: Calculate coefficient of variation for temporal consistency
        let mean_period: f64 = cycle_periods.iter().sum::<f64>() / cycle_periods.len() as f64;
        let variance: f64 = cycle_periods.iter()
            .map(|p| (p - mean_period).powi(2))
            .sum::<f64>() / cycle_periods.len() as f64;

        let coefficient_of_variation = variance.sqrt() / mean_period;

        // Stability is inverse of coefficient of variation (lower CV = higher stability)
        let stability = 1.0 / (1.0 + coefficient_of_variation);

        debug!("Rhythm stability: CV={:.3}, stability={:.3}", coefficient_of_variation, stability);
        stability
    }

    // ENHANCE: Add cycle validation for statistical significance
    fn find_dominant_cycle_with_validation(&self, data: &[Decimal]) -> Option<f64> {
        let dominant_cycle = self.find_dominant_cycle(data)?;

        // Validate cycle has sufficient amplitude and persistence
        if self.validate_cycle_significance(data, dominant_cycle) {
            Some(dominant_cycle)
        } else {
            None
        }
    }

    fn validate_cycle_significance(&self, data: &[Decimal], period: f64) -> bool {
        // Simple validation: check if cycle amplitude is above noise threshold
        let cycle_amplitude = self.calculate_cycle_amplitude(data, period);
        let noise_level = self.estimate_noise_level(data);

        cycle_amplitude > noise_level * 2.0 // Signal-to-noise ratio > 2
    }

    // Fix Hurst exponent calculation
    fn calculate_hurst_exponent_fixed(&self, price_history: &[Decimal]) -> Result<Decimal> {
        const MIN_DATA_POINTS: usize = 100; // Increased from 20

        if price_history.len() < MIN_DATA_POINTS {
            return Err(anyhow!("Insufficient data for reliable Hurst calculation: {} < {}",
                              price_history.len(), MIN_DATA_POINTS));
        }

        // Use logarithmic scales instead of fixed scales
        let max_scale = price_history.len() / 4;
        let scales = self.generate_logarithmic_scales(10, max_scale);

        let mut log_scales = Vec::new();
        let mut log_rs_values = Vec::new();

        for &scale in &scales {
            if let Ok(rs_value) = self.calculate_rs_statistic_fixed(price_history, scale) {
                if rs_value > Decimal::ZERO {
                    log_scales.push((scale as f64).ln());
                    log_rs_values.push(rs_value.ln());
                }
            }
        }

        if log_scales.len() < 3 {
            return Err(anyhow!("Insufficient valid scales for Hurst calculation"));
        }

        // Calculate slope with R² validation
        let (slope, r_squared) = self.calculate_slope_with_r_squared(&log_scales, &log_rs_values)?;

        if r_squared < 0.7 {
            warn!("Low R² ({:.3}) in Hurst calculation, result may be unreliable", r_squared);
        }

        Ok(Decimal::from_f64(slope).unwrap_or(dec!(0.5)))
    }

    fn calculate_rs_statistic_fixed(&self, data: &[Decimal], scale: usize) -> Result<Decimal> {
        if data.len() < scale || scale < 2 {
            return Err(anyhow!("Invalid scale for R/S calculation"));
        }

        let mut rs_values = Vec::new();

        for start in 0..=(data.len() - scale) {
            let window = &data[start..start + scale];

            // Calculate mean
            let mean = window.iter().sum::<Decimal>() / Decimal::from(scale);

            // Calculate cumulative deviations
            let mut cumulative_deviation = Decimal::ZERO;
            let mut deviations = Vec::new();

            for &price in window {
                cumulative_deviation += price - mean;
                deviations.push(cumulative_deviation);
            }

            // Calculate range
            let max_deviation = deviations.iter().max().unwrap_or(&Decimal::ZERO);
            let min_deviation = deviations.iter().min().unwrap_or(&Decimal::ZERO);
            let range = max_deviation - min_deviation;

            // Calculate standard deviation (FIXED: use n-1)
            let variance = window.iter()
                .map(|&price| (price - mean).powi(2))
                .sum::<Decimal>() / Decimal::from(scale - 1); // FIXED: n-1 instead of n

            let std_dev = variance.sqrt().unwrap_or(Decimal::ZERO);

            if std_dev > Decimal::ZERO {
                rs_values.push(range / std_dev);
            }
        }

        if rs_values.is_empty() {
            return Err(anyhow!("No valid R/S values calculated"));
        }

        // Return average R/S value
        Ok(rs_values.iter().sum::<Decimal>() / Decimal::from(rs_values.len()))
    }
}
```

#### Geometric Analysis Fixes

```rust
pub struct FixedGeometricScorer {
    config: GeometricConfig,
    price_oracle: Arc<dyn PriceOracle>,
    token_registry: Arc<TokenRegistry>,
}

impl FixedGeometricScorer {
    // Fix liquidity centroid bias calculation
    async fn calculate_liquidity_centroid_bias_fixed(
        &self,
        path_data: &[PoolGeometricData]
    ) -> Result<Decimal> {
        if path_data.is_empty() {
            return Ok(dec!(0.5)); // Neutral score for empty paths
        }

        // Calculate weighted centroid
        let mut total_liquidity = Decimal::ZERO;
        let mut weighted_x_sum = Decimal::ZERO;
        let mut weighted_y_sum = Decimal::ZERO;

        for (i, pool) in path_data.iter().enumerate() {
            let position_x = Decimal::from(i); // Path position
            let position_y = pool.liquidity_usd; // Liquidity as Y coordinate
            let weight = pool.liquidity_usd;

            total_liquidity += weight;
            weighted_x_sum += position_x * weight;
            weighted_y_sum += position_y * weight;
        }

        if total_liquidity == Decimal::ZERO {
            return Ok(dec!(0.5));
        }

        let centroid_x = weighted_x_sum / total_liquidity;
        let centroid_y = weighted_y_sum / total_liquidity;

        // Calculate ideal center (middle of path, average liquidity)
        let ideal_x = Decimal::from(path_data.len() - 1) / dec!(2.0);
        let ideal_y = path_data.iter()
            .map(|p| p.liquidity_usd)
            .sum::<Decimal>() / Decimal::from(path_data.len());

        // Calculate distance from ideal center
        let distance_x = (centroid_x - ideal_x).abs();
        let distance_y = (centroid_y - ideal_y).abs();
        let normalized_distance = (distance_x + distance_y) / (ideal_x + ideal_y);

        // Convert to bias score (lower distance = higher score)
        Ok((dec!(1.0) - normalized_distance).max(dec!(0.0)).min(dec!(1.0)))
    }

    // Fix price oracle integration
    async fn get_token_price_usd(&self, token_symbol: &str) -> Result<Decimal> {
        let token_address = self.token_registry.get_address(token_symbol)
            .ok_or_else(|| anyhow!("Unknown token symbol: {}", token_symbol))?;

        let price = self.price_oracle.get_price(token_address).await
            .map_err(|e| anyhow!("Failed to get price for {}: {}", token_symbol, e))?;

        if price <= Decimal::ZERO {
            return Err(anyhow!("Invalid price for {}: {}", token_symbol, price));
        }

        Ok(price)
    }

    // Complete geometric score calculation
    pub async fn calculate_complete_score(
        &self,
        path: &ArbitragePath,
        centrality_scores: &HashMap<String, Decimal>
    ) -> Result<GeometricScore> {
        let path_data = self.get_path_geometric_data_fixed(path).await?;

        let convexity_ratio = self.calculate_convexity_ratio(&path_data)?;
        let harmonic_path_score = self.calculate_harmonic_path_score(&path_data)?;
        let liquidity_centroid_bias = self.calculate_liquidity_centroid_bias_fixed(&path_data).await?;

        Ok(GeometricScore {
            convexity_ratio,
            harmonic_path_score,
            liquidity_centroid_bias, // Now properly calculated
        })
    }
}

// Token registry for address resolution
pub struct TokenRegistry {
    address_map: HashMap<String, Address>,
}

impl TokenRegistry {
    pub fn new() -> Self {
        let mut address_map = HashMap::new();

        // Populate with major tokens (FIXED: real addresses instead of zero)
        address_map.insert("WETH".to_string(),
            "******************************************".parse().unwrap());
        address_map.insert("USDC".to_string(),
            "******************************************".parse().unwrap());
        address_map.insert("USDT".to_string(),
            "******************************************".parse().unwrap());
        address_map.insert("DAI".to_string(),
            "******************************************".parse().unwrap());
        address_map.insert("WBTC".to_string(),
            "******************************************".parse().unwrap());

        Self { address_map }
    }

    pub fn get_address(&self, symbol: &str) -> Option<Address> {
        self.address_map.get(symbol).copied()
    }
}
```

#### Vesica Piscis Fixes

**Critical Bug**: Current implementation uses `amount_to_equalize.max(Decimal::ZERO)` which zeros out valid negative results, causing 50% of arbitrage opportunities to be ignored when Pool B is cheaper.

```rust
impl VesicaPiscisCalculator {
    // Fix negative price deviation handling
    pub fn calculate_amount_to_equalize_fixed(
        &self,
        pool_a_reserves: (Decimal, Decimal),
        pool_b_reserves: (Decimal, Decimal),
    ) -> Result<VesicaPiscisResult> {
        let price_a = pool_a_reserves.1 / pool_a_reserves.0; // token1/token0
        let price_b = pool_b_reserves.1 / pool_b_reserves.0;

        let price_deviation = price_a - price_b;

        // FIXED: Don't zero out negative deviations
        if price_deviation.abs() < dec!(0.0001) {
            return Ok(VesicaPiscisResult {
                amount_to_equalize: Decimal::ZERO,
                trade_direction: TradeDirection::None,
                arbitrage_depth: Decimal::ZERO,
            });
        }

        // Calculate geometric mean for equalization
        let geometric_mean_price = (price_a * price_b).sqrt()
            .ok_or_else(|| anyhow!("Cannot calculate square root of negative price product"))?;

        // Calculate amount needed to move price_a to geometric mean
        let k_a = pool_a_reserves.0 * pool_a_reserves.1;
        let new_reserve_0_a = (k_a / geometric_mean_price).sqrt()
            .ok_or_else(|| anyhow!("Cannot calculate square root in reserve calculation"))?;

        let (amount_to_equalize, trade_direction) = if price_deviation > Decimal::ZERO {
            // Pool A is more expensive, need to sell token0 to pool A
            (pool_a_reserves.0 - new_reserve_0_a, TradeDirection::SellToA)
        } else {
            // Pool B is more expensive, need to buy token0 from pool A
            (new_reserve_0_a - pool_a_reserves.0, TradeDirection::BuyFromA)
        };

        // FIXED: Return absolute value and trade direction instead of max(0)
        Ok(VesicaPiscisResult {
            amount_to_equalize: amount_to_equalize.abs(),
            trade_direction,
            arbitrage_depth: price_deviation.abs(),
        })
    }

    // Integrate with geometric scoring (MISSING INTEGRATION)
    pub fn calculate_vesica_piscis_score(&self, path: &ArbitragePath) -> Result<Decimal> {
        let mut total_depth = Decimal::ZERO;
        let mut pool_count = 0;

        for pool_pair in path.pools.windows(2) {
            let result = self.calculate_amount_to_equalize_fixed(
                pool_pair[0].reserves,
                pool_pair[1].reserves
            )?;

            total_depth += result.arbitrage_depth;
            pool_count += 1;
        }

        if pool_count == 0 {
            return Ok(dec!(0.5)); // Neutral score
        }

        let average_depth = total_depth / Decimal::from(pool_count);

        // Convert depth to score (higher depth = higher score, capped at 1.0)
        Ok((average_depth * dec!(10.0)).min(dec!(1.0)))
    }
}

#[derive(Debug, Clone)]
pub struct VesicaPiscisResult {
    pub amount_to_equalize: Decimal,
    pub trade_direction: TradeDirection,
    pub arbitrage_depth: Decimal,
}

#[derive(Debug, Clone)]
pub enum TradeDirection {
    None,
    SellToA,
    BuyFromA,
}
```

### 3. Network Integration Fixes

**Purpose**: Integrate network state monitoring with execution decisions and gas strategy
**Priority**: HIGH - Missing integration reduces competitive advantage

#### ExecutionManager Integration

```rust
pub struct FixedExecutionManager {
    latest_network_resonance_state: Arc<Mutex<Option<NetworkResonanceState>>>,
    gas_strategy: Arc<GasStrategy>,
    timing_oracle: Arc<HarmonicTimingOracle>,
    nats_client: Arc<NatsClient>,
}

impl FixedExecutionManager {
    pub async fn run_fixed(&self) -> Result<()> {
        let mut network_state_subscriber = self.nats_client
            .subscribe(NatsTopics::STATE_NETWORK_RESONANCE).await?;
        let mut opportunity_subscriber = self.nats_client
            .subscribe(NatsTopics::OPPORTUNITIES).await?;

        loop {
            tokio::select! {
                Some(msg) = network_state_subscriber.next() => {
                    if let Ok(state) = serde_json::from_slice::<NetworkResonanceState>(&msg.payload) {
                        *self.latest_network_resonance_state.lock().await = Some(state);
                        debug!("Updated network resonance state");
                    }
                }
                Some(msg) = opportunity_subscriber.next() => {
                    if let Ok(opportunity) = serde_json::from_slice::<Opportunity>(&msg.payload) {
                        self.process_opportunity_fixed(opportunity).await?;
                    }
                }
            }
        }
    }

    async fn process_opportunity_fixed(&self, opportunity: Opportunity) -> Result<()> {
        // Get current network state
        let network_state = self.latest_network_resonance_state.lock().await.clone();

        // Check timing oracle for optimal broadcast window
        if let Some(ref state) = network_state {
            let current_base_fee = self.get_current_base_fee().await?;
            let base_fee_avg = self.get_base_fee_average().await?;

            let optimal_timing = self.timing_oracle.await_broadcast_window(
                state, current_base_fee, base_fee_avg
            ).await;

            if !optimal_timing {
                warn!("Broadcasting without optimal network conditions for opportunity: {}",
                      opportunity.id());
                // Could implement delay logic here
            }
        }

        // Calculate gas with network state
        let optimal_gas = self.gas_strategy.calculate_optimal_gas_price(
            &network_state,
            &self.get_current_market_regime().await?
        );

        // Execute with network-aware parameters
        self.execute_opportunity_with_gas(opportunity, optimal_gas).await
    }
}
```

#### Network State Analysis Fixes

**Critical Issues**: Missing censorship detection (always false), inaccurate coherence formula (can produce values > 1.0), oversimplified sequencer monitoring.

```rust
pub struct FixedSeismicAnalyzer {
    config: NetworkConfig,
    censorship_detector: Arc<CensorshipDetector>,
    reorg_tracker: Arc<ReorgTracker>,
}

impl FixedSeismicAnalyzer {
    // Fix network coherence calculation (CRITICAL)
    fn calculate_coherence_score_fixed(&self, timestamps: &[u64]) -> f64 {
        if timestamps.len() < 2 {
            return 0.0;
        }

        let mean = timestamps.iter().sum::<u64>() as f64 / timestamps.len() as f64;
        let variance = timestamps.iter()
            .map(|&t| (t as f64 - mean).powi(2))
            .sum::<f64>() / timestamps.len() as f64;

        let std_dev_ms = variance.sqrt() / 1_000_000.0; // Convert to milliseconds

        // FIXED: Use configurable threshold instead of hardcoded 1,000,000.0
        let max_acceptable_std_dev = self.config.max_acceptable_coherence_std_dev;

        // FIXED: Ensure result is in [0, 1] range (was producing > 1.0)
        let normalized_score = (max_acceptable_std_dev - std_dev_ms.min(max_acceptable_std_dev))
            / max_acceptable_std_dev;

        normalized_score.max(0.0).min(1.0)
    }

    // IMPLEMENT: Basic censorship detection (was always false)
    async fn detect_censorship_basic(&self, recent_txs: &[TxHash]) -> bool {
        if recent_txs.is_empty() {
            return false;
        }

        // Check mempool vs block inclusion rates
        let mut mempool_count = 0;
        let mut included_count = 0;
        let total_count = recent_txs.len();

        for tx_hash in recent_txs {
            // Check if transaction was in mempool
            if self.was_in_mempool(tx_hash).await.unwrap_or(false) {
                mempool_count += 1;

                // Check if it was included in a block
                if self.is_transaction_included(tx_hash).await.unwrap_or(false) {
                    included_count += 1;
                }
            }
        }

        if mempool_count == 0 {
            return false; // No mempool data available
        }

        let inclusion_rate = included_count as f64 / mempool_count as f64;

        // FIXED: Consider censorship if inclusion rate is below threshold
        inclusion_rate < self.config.censorship_threshold
    }

    // ENHANCE: Multi-metric sequencer monitoring (was binary healthy/unhealthy)
    async fn check_sequencer_health_enhanced(&self) -> Result<SequencerHealth> {
        let start_time = std::time::Instant::now();

        match self.provider.get_block_number().await {
            Ok(block_number) => {
                let response_time = start_time.elapsed().as_millis() as u64;

                // FIXED: Multi-level status instead of binary
                let status = if response_time < 100 {
                    SequencerStatus::Healthy
                } else if response_time < 500 {
                    SequencerStatus::Degraded
                } else if response_time < 2000 {
                    SequencerStatus::Unhealthy
                } else {
                    SequencerStatus::Down
                };

                let block_production_rate = self.calculate_block_production_rate().await?;
                let error_rate = self.calculate_error_rate().await?;

                Ok(SequencerHealth {
                    status,
                    response_time_ms: Some(response_time),
                    last_block_number: Some(block_number.as_u64()),
                    error_rate,
                    block_production_rate,
                })
            },
            Err(e) => {
                warn!("Sequencer health check failed: {}", e);
                Ok(SequencerHealth {
                    status: SequencerStatus::Down,
                    response_time_ms: None,
                    last_block_number: None,
                    error_rate: 1.0,
                    block_production_rate: None,
                })
            }
        }
    }

    // INTEGRATE: Reorg data into NetworkResonanceState (was missing)
    pub async fn create_network_resonance_state_fixed(
        &self,
        sp_time_ms: f64,
        coherence_score: f64,
        is_shock_event: bool,
        sp_time_20th_percentile: f64,
    ) -> NetworkResonanceState {
        let sequencer_health = self.check_sequencer_health_enhanced().await
            .unwrap_or_else(|_| SequencerHealth::default_unhealthy());

        let censorship_detected = self.detect_censorship_basic(&self.get_recent_transactions()).await;

        // FIXED: Include reorg data in network state
        let recent_reorgs = self.reorg_tracker.get_recent_reorgs(Duration::from_hours(1)).await;
        let chain_stability_score = self.calculate_chain_stability(&recent_reorgs);

        NetworkResonanceState {
            sp_time_ms,
            network_coherence_score: coherence_score,
            is_shock_event,
            sp_time_20th_percentile,
            sequencer_status: format!("{:?}", sequencer_health.status),
            censorship_detected, // FIXED: Now actually calculated
            chain_stability_score, // NEW: Include chain stability
            recent_reorg_count: recent_reorgs.len(),
            sequencer_response_time_ms: sequencer_health.response_time_ms,
        }
    }

    fn calculate_chain_stability(&self, recent_reorgs: &[ReorgEvent]) -> f64 {
        if recent_reorgs.is_empty() {
            return 1.0; // Perfect stability
        }

        // Calculate stability based on reorg frequency and depth
        let total_depth: u64 = recent_reorgs.iter().map(|r| r.depth).sum();
        let avg_depth = total_depth as f64 / recent_reorgs.len() as f64;
        let reorg_frequency = recent_reorgs.len() as f64;

        // Stability decreases with more frequent and deeper reorgs
        let stability = 1.0 / (1.0 + (reorg_frequency * 0.1) + (avg_depth * 0.05));
        stability.max(0.0).min(1.0)
    }
}

#[derive(Debug, Clone)]
pub struct SequencerHealth {
    pub status: SequencerStatus,
    pub response_time_ms: Option<u64>,
    pub block_production_rate: Option<f64>,
    pub error_rate: f64,
    pub last_block_number: Option<u64>,
}

impl SequencerHealth {
    pub fn default_unhealthy() -> Self {
        Self {
            status: SequencerStatus::Down,
            response_time_ms: None,
            block_production_rate: None,
            error_rate: 1.0,
            last_block_number: None,
        }
    }
}

#[derive(Debug, Clone)]
pub enum SequencerStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Down,
}

// EXTEND: NetworkResonanceState with missing fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkResonanceState {
    pub sp_time_ms: f64,
    pub network_coherence_score: f64,
    pub is_shock_event: bool,
    pub sp_time_20th_percentile: f64,
    pub sequencer_status: String,
    pub censorship_detected: bool, // FIXED: Now properly calculated
    pub chain_stability_score: f64, // NEW: Chain stability metric
    pub recent_reorg_count: usize, // NEW: Recent reorg count
    pub sequencer_response_time_ms: Option<u64>, // NEW: Response time
}

#[derive(Debug, Clone)]
pub struct ReorgEvent {
    pub block_number: u64,
    pub depth: u64,
    pub timestamp: u64,
}
```

### 4. Centrality Score Population

**Purpose**: Initialize and maintain proper centrality scores for major tokens
**Priority**: HIGH - Empty centrality scores prevent proper path selection

```rust
pub struct CentralityScoreManager {
    scores: Arc<RwLock<HashMap<String, Decimal>>>,
    config: CentralityConfig,
}

impl CentralityScoreManager {
    pub fn new(config: CentralityConfig) -> Self {
        let scores = Arc::new(RwLock::new(Self::initialize_default_scores()));
        Self { scores, config }
    }

    fn initialize_default_scores() -> HashMap<String, Decimal> {
        let mut scores = HashMap::new();

        // Major tokens with high centrality
        scores.insert("WETH".to_string(), dec!(0.95));
        scores.insert("USDC".to_string(), dec!(0.90));
        scores.insert("USDT".to_string(), dec!(0.85));
        scores.insert("DAI".to_string(), dec!(0.80));
        scores.insert("WBTC".to_string(), dec!(0.75));

        // Secondary tokens with medium centrality
        scores.insert("UNI".to_string(), dec!(0.60));
        scores.insert("LINK".to_string(), dec!(0.55));
        scores.insert("AAVE".to_string(), dec!(0.50));

        // Default for unknown tokens
        scores.insert("DEFAULT".to_string(), dec!(0.05));

        scores
    }

    pub async fn get_centrality_score(&self, token_symbol: &str) -> Decimal {
        let scores = self.scores.read().await;
        scores.get(token_symbol)
            .or_else(|| scores.get("DEFAULT"))
            .copied()
            .unwrap_or(dec!(0.05))
    }

    pub async fn update_scores(&self, new_scores: HashMap<String, Decimal>) {
        let mut scores = self.scores.write().await;
        for (token, score) in new_scores {
            if score >= dec!(0.0) && score <= dec!(1.0) {
                scores.insert(token, score);
            }
        }
    }
}
```

## Error Handling Strategy

### Graceful Degradation Patterns

1. **Missing Data Handling**: Use neutral scores (0.5) instead of zero
2. **Component Failures**: Implement fallback mechanisms for each pillar
3. **Network Issues**: Degrade gracefully with cached data
4. **Mathematical Errors**: Validate inputs and provide safe defaults

### Error Recovery Mechanisms

```rust
pub trait ComponentWithFallback {
    type Output;
    type Error;

    async fn calculate_primary(&self) -> Result<Self::Output, Self::Error>;
    fn calculate_fallback(&self) -> Self::Output;

    async fn calculate_with_fallback(&self) -> Self::Output {
        match self.calculate_primary().await {
            Ok(result) => result,
            Err(e) => {
                warn!("Primary calculation failed: {}, using fallback", e);
                self.calculate_fallback()
            }
        }
    }
}
```

## Testing Strategy

### Component Testing Approach

1. **Unit Tests**: Test each fixed component independently
2. **Integration Tests**: Verify component interactions work correctly
3. **Regression Tests**: Ensure fixes don't break existing functionality
4. **Performance Tests**: Validate fixes don't significantly impact performance

### Validation Framework

```rust
pub struct ValidationFramework {
    test_data: TestDataProvider,
    metrics_collector: MetricsCollector,
}

impl ValidationFramework {
    pub async fn validate_scoring_engine_fixes(&self) -> ValidationResult {
        let test_cases = self.test_data.get_scoring_test_cases();
        let mut results = Vec::new();

        for test_case in test_cases {
            let result = self.run_scoring_test(test_case).await;
            results.push(result);
        }

        ValidationResult::from_test_results(results)
    }

    pub async fn validate_mathematical_fixes(&self) -> ValidationResult {
        // Test Hurst exponent fixes
        let hurst_results = self.validate_hurst_calculation().await;

        // Test vesica piscis fixes
        let vesica_results = self.validate_vesica_piscis().await;

        // Test temporal analysis fixes
        let temporal_results = self.validate_temporal_analysis().await;

        ValidationResult::combine(vec![hurst_results, vesica_results, temporal_results])
    }
}
```

## Performance Considerations

### Optimization Strategies

1. **Caching**: Cache expensive calculations where appropriate
2. **Lazy Loading**: Load components only when needed
3. **Parallel Processing**: Process independent components concurrently
4. **Memory Management**: Minimize allocations in hot paths

### Performance Monitoring

```rust
pub struct PerformanceMonitor {
    metrics: Arc<Mutex<PerformanceMetrics>>,
}

impl PerformanceMonitor {
    pub fn time_operation<F, R>(&self, operation_name: &str, operation: F) -> R
    where
        F: FnOnce() -> R,
    {
        let start = std::time::Instant::now();
        let result = operation();
        let duration = start.elapsed();

        self.record_timing(operation_name, duration);
        result
    }

    pub async fn record_timing(&self, operation: &str, duration: Duration) {
        let mut metrics = self.metrics.lock().await;
        metrics.record_operation_time(operation, duration);
    }
}
```

## Configuration Management

### Enhanced Configuration Structure

```rust
#[derive(Debug, Clone, Deserialize)]
pub struct FixedAREConfig {
    pub scoring: ScoringConfig,
    pub temporal: TemporalConfig,
    pub geometric: GeometricConfig,
    pub network: NetworkConfig,
    pub centrality: CentralityConfig,
    pub performance: PerformanceConfig,
}

#[derive(Debug, Clone, Deserialize)]
pub struct ScoringConfig {
    // FIXED: These weights are now actually used in calculations
    pub temporal_harmonics_weight: Decimal,
    pub geometric_score_weight: Decimal,
    pub network_resonance_weight: Decimal,
    pub risk_aversion_k: Decimal,
    pub quality_ratio_floor: Decimal,
    pub min_resonance_threshold: Decimal,
}

#[derive(Debug, Clone, Deserialize)]
pub struct TemporalConfig {
    pub min_data_points: usize, // FIXED: Increased from 60 to 240
    pub window_size: usize,
    pub min_windows_for_stability: usize,
    pub cycle_validation_enabled: bool,
    pub signal_to_noise_threshold: f64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct NetworkConfig {
    pub max_acceptable_coherence_std_dev: f64, // FIXED: Configurable instead of hardcoded
    pub censorship_threshold: f64,
    pub shock_threshold_ms: f64, // FIXED: Added missing field
    pub sequencer_health_interval_ms: u64,
    pub reorg_tracking_window_hours: u64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct GeometricConfig {
    pub anchor_assets: Vec<String>,
    pub centroid_calculation_enabled: bool, // FIXED: Enable proper centroid calculation
    pub vesica_piscis_integration_enabled: bool, // FIXED: Enable vesica piscis integration
    pub self_intersection_validation: bool,
}

#[derive(Debug, Clone, Deserialize)]
pub struct CentralityConfig {
    pub default_centrality_score: Decimal,
    pub major_token_scores: HashMap<String, Decimal>, // FIXED: Populate with real scores
    pub dynamic_updates_enabled: bool,
    pub update_interval_hours: u64,
}

impl ScoringConfig {
    pub fn validate(&self) -> Result<()> {
        let weight_sum = self.temporal_harmonics_weight +
                        self.geometric_score_weight +
                        self.network_resonance_weight;

        if (weight_sum - dec!(1.0)).abs() > dec!(0.01) {
            return Err(anyhow!("Pillar weights sum to {:.3}, should sum to 1.0", weight_sum));
        }

        if self.risk_aversion_k < dec!(0.0) || self.risk_aversion_k > dec!(2.0) {
            return Err(anyhow!("Risk aversion {:.2} outside reasonable range [0.0, 2.0]",
                              self.risk_aversion_k));
        }

        if self.quality_ratio_floor < dec!(0.1) || self.quality_ratio_floor > dec!(0.9) {
            return Err(anyhow!("Quality ratio floor {:.2} outside reasonable range [0.1, 0.9]",
                              self.quality_ratio_floor));
        }

        Ok(())
    }
}

impl Default for TemporalConfig {
    fn default() -> Self {
        Self {
            min_data_points: 240, // FIXED: 4 hours instead of 1 hour
            window_size: 60,
            min_windows_for_stability: 4,
            cycle_validation_enabled: true,
            signal_to_noise_threshold: 2.0,
        }
    }
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            max_acceptable_coherence_std_dev: 100.0, // FIXED: Configurable threshold
            censorship_threshold: 0.8, // 80% inclusion rate threshold
            shock_threshold_ms: 500.0, // FIXED: Added missing configuration
            sequencer_health_interval_ms: 30_000,
            reorg_tracking_window_hours: 1,
        }
    }
}

impl Default for CentralityConfig {
    fn default() -> Self {
        let mut major_token_scores = HashMap::new();

        // FIXED: Populate with actual centrality scores instead of empty map
        major_token_scores.insert("WETH".to_string(), dec!(0.95));
        major_token_scores.insert("USDC".to_string(), dec!(0.90));
        major_token_scores.insert("USDT".to_string(), dec!(0.85));
        major_token_scores.insert("DAI".to_string(), dec!(0.80));
        major_token_scores.insert("WBTC".to_string(), dec!(0.75));

        Self {
            default_centrality_score: dec!(0.05),
            major_token_scores,
            dynamic_updates_enabled: false, // Start with static scores
            update_interval_hours: 24,
        }
    }
}
```

## Deployment Strategy

### Phased Rollout Plan

1. **Phase 1**: Deploy scoring engine fixes with comprehensive testing
2. **Phase 2**: Deploy mathematical component fixes with validation
3. **Phase 3**: Deploy network integration improvements
4. **Phase 4**: Deploy centrality score management
5. **Phase 5**: Full system integration testing and optimization

### Rollback Strategy

Each phase includes rollback mechanisms to revert to previous stable state if issues are detected during deployment.

## Success Criteria

### Functional Requirements

1. All configured weights properly applied in scoring calculations
2. Mathematical calculations produce accurate, stable results
3. Components integrate seamlessly with proper data flow
4. System handles missing data gracefully without failures
5. Performance remains within acceptable bounds

### Quality Metrics

1. **Accuracy**: Mathematical calculations match theoretical expectations
2. **Reliability**: System operates without critical failures
3. **Performance**: Response times remain under specified thresholds
4. **Maintainability**: Code is well-structured and documented
5. **Testability**: Comprehensive test coverage for all components

This design provides a comprehensive framework for fixing all critical issues identified in the audit while maintaining system stability and performance.
