// Configuration Manager for Stargate Compass Integration Testing
// Handles dynamic contract address updates and configuration validation

use super::core::*;
use super::utils::*;
use ethers::types::Address;
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::fs;
use std::time::{SystemTime, UNIX_EPOCH};
use toml;

/// Configuration manager implementation
pub struct ConfigurationManager {
    config_files: Vec<PathBuf>,
    backup_files: Vec<PathBuf>,
    current_contract_address: Option<Address>,
}

impl ConfigurationManager {
    /// Create new configuration manager with automatic file discovery
    pub fn new() -> Result<Self> {
        let config_files = Self::discover_config_files()?;
        
        Ok(Self {
            config_files,
            backup_files: Vec::new(),
            current_contract_address: None,
        })
    }
    
    /// Create configuration manager with specific files
    pub fn with_files(config_files: Vec<PathBuf>) -> Self {
        Self {
            config_files,
            backup_files: Vec::new(),
            current_contract_address: None,
        }
    }
    
    /// Discover configuration files in the project
    pub fn discover_config_files() -> Result<Vec<PathBuf>> {
        let mut config_files = Vec::new();
        let config_dir = Path::new("config");
        
        if !config_dir.exists() {
            return Err(anyhow!("Config directory not found at: {}", config_dir.display()));
        }
        
        // Priority order for configuration files
        let target_files = [
            "local.toml",
            "testnet.toml", 
            "default.toml",
            "active.toml",
            "anvil.toml"
        ];
        
        for file_name in &target_files {
            let file_path = config_dir.join(file_name);
            if file_path.exists() {
                config_files.push(file_path);
            }
        }
        
        if config_files.is_empty() {
            return Err(anyhow!("No configuration files found in {}", config_dir.display()));
        }
        
        Ok(config_files)
    }
    
    /// Parse a single configuration file
    pub fn parse_config_file(&self, file_path: &Path) -> Result<toml::Value> {
        if !file_path.exists() {
            return Err(anyhow!("Configuration file not found: {}", file_path.display()));
        }
        
        let content = fs::read_to_string(file_path)
            .map_err(|e| anyhow!("Failed to read config file {}: {}", file_path.display(), e))?;
        
        if content.trim().is_empty() {
            return Err(anyhow!("Configuration file is empty: {}", file_path.display()));
        }
        
        let config: toml::Value = toml::from_str(&content)
            .map_err(|e| anyhow!("Failed to parse TOML in {}: {}", file_path.display(), e))?;
        
        Ok(config)
    }
    
    /// Validate configuration file structure and required keys
    pub fn validate_config_structure(&self, config: &toml::Value, file_path: &Path) -> ConfigValidationResult {
        let mut result = ConfigValidationResult {
            valid: true,
            config_files_found: vec![file_path.to_string_lossy().to_string()],
            missing_keys: Vec::new(),
            invalid_values: Vec::new(),
            warnings: Vec::new(),
            contract_address_valid: false,
        };
        
        // Check for required top-level sections
        let required_sections = ["contracts", "network", "execution"];
        for section in &required_sections {
            if !config.get(section).is_some() {
                result.missing_keys.push(format!("Missing section: {}", section));
                result.valid = false;
            }
        }
        
        // Validate contract addresses section
        if let Some(contracts) = config.get("contracts") {
            if let Some(contracts_table) = contracts.as_table() {
                // Look for Stargate Compass contract address
                let stargate_keys = [
                    "stargate_compass_v1",
                    "stargate_compass",
                    "compass_v1",
                    "compass"
                ];
                
                let mut found_stargate_address = false;
                for key in &stargate_keys {
                    if let Some(address_value) = contracts_table.get(*key) {
                        if let Some(address_str) = address_value.as_str() {
                            match validate_ethereum_address(address_str) {
                                Ok(_) => {
                                    found_stargate_address = true;
                                    result.contract_address_valid = true;
                                    break;
                                }
                                Err(e) => {
                                    result.invalid_values.push(format!("Invalid address for {}: {}", key, e));
                                    result.valid = false;
                                }
                            }
                        } else {
                            result.invalid_values.push(format!("Contract address {} is not a string", key));
                            result.valid = false;
                        }
                    }
                }
                
                if !found_stargate_address {
                    result.missing_keys.push("No valid Stargate Compass contract address found".to_string());
                    result.warnings.push("Expected one of: stargate_compass_v1, stargate_compass, compass_v1, compass".to_string());
                    result.valid = false;
                }
            } else {
                result.invalid_values.push("contracts section is not a table".to_string());
                result.valid = false;
            }
        }
        
        // Validate network configuration
        if let Some(network) = config.get("network") {
            if let Some(network_table) = network.as_table() {
                // Check for required network fields
                let required_network_fields = ["rpc_url", "chain_id"];
                for field in &required_network_fields {
                    if !network_table.contains_key(*field) {
                        result.missing_keys.push(format!("Missing network.{}", field));
                        result.valid = false;
                    }
                }
                
                // Validate chain_id is a number
                if let Some(chain_id) = network_table.get("chain_id") {
                    if !chain_id.is_integer() {
                        result.invalid_values.push("network.chain_id must be an integer".to_string());
                        result.valid = false;
                    }
                }
                
                // Validate RPC URL format
                if let Some(rpc_url) = network_table.get("rpc_url") {
                    if let Some(url_str) = rpc_url.as_str() {
                        if !url_str.starts_with("http://") && !url_str.starts_with("https://") && !url_str.starts_with("ws://") && !url_str.starts_with("wss://") {
                            result.warnings.push(format!("RPC URL may be invalid: {}", url_str));
                        }
                    }
                }
            }
        }
        
        result
    }
    
    /// Extract contract address from configuration
    pub fn extract_contract_address(&self, config: &toml::Value) -> Result<Address> {
        let contracts = config.get("contracts")
            .ok_or_else(|| anyhow!("No contracts section found"))?;
        
        let contracts_table = contracts.as_table()
            .ok_or_else(|| anyhow!("contracts section is not a table"))?;
        
        // Try different possible keys for Stargate Compass
        let stargate_keys = [
            "stargate_compass_v1",
            "stargate_compass", 
            "compass_v1",
            "compass"
        ];
        
        for key in &stargate_keys {
            if let Some(address_value) = contracts_table.get(*key) {
                if let Some(address_str) = address_value.as_str() {
                    return validate_ethereum_address(address_str)
                        .map_err(|e| anyhow!("Invalid address for {}: {}", key, e));
                }
            }
        }
        
        Err(anyhow!("No Stargate Compass contract address found in configuration"))
    }
    
    /// Get all configuration files and their parsed content
    pub fn get_all_configs(&self) -> Result<HashMap<PathBuf, toml::Value>> {
        let mut configs = HashMap::new();
        
        for file_path in &self.config_files {
            match self.parse_config_file(file_path) {
                Ok(config) => {
                    configs.insert(file_path.clone(), config);
                }
                Err(e) => {
                    return Err(anyhow!("Failed to parse {}: {}", file_path.display(), e));
                }
            }
        }
        
        Ok(configs)
    }
    
    /// Find configuration files that contain contract addresses
    pub fn find_files_with_contract_addresses(&self) -> Result<Vec<PathBuf>> {
        let mut files_with_addresses = Vec::new();
        
        for file_path in &self.config_files {
            match self.parse_config_file(file_path) {
                Ok(config) => {
                    if self.extract_contract_address(&config).is_ok() {
                        files_with_addresses.push(file_path.clone());
                    }
                }
                Err(_) => {
                    // Skip files that can't be parsed or don't have addresses
                    continue;
                }
            }
        }
        
        Ok(files_with_addresses)
    }
}

#[async_trait]
impl ConfigManager for ConfigurationManager {
    async fn update_contract_address(&self, new_address: Address) -> Result<ConfigUpdateResult> {
        let mut result = ConfigUpdateResult {
            success: true,
            files_modified: Vec::new(),
            backup_created: false,
            validation_passed: false,
            error_message: None,
        };
        
        // Find files that need to be updated
        let files_to_update = self.find_files_with_contract_addresses()?;
        
        if files_to_update.is_empty() {
            result.success = false;
            result.error_message = Some("No configuration files with contract addresses found".to_string());
            return Ok(result);
        }
        
        // Create backups first
        let mut backup_paths = Vec::new();
        for file_path in &files_to_update {
            match backup_file(file_path) {
                Ok(backup_path) => {
                    backup_paths.push(backup_path);
                    result.backup_created = true;
                }
                Err(e) => {
                    result.success = false;
                    result.error_message = Some(format!("Failed to backup {}: {}", file_path.display(), e));
                    return Ok(result);
                }
            }
        }
        
        // Update each configuration file
        for file_path in &files_to_update {
            match self.update_single_config_file(file_path, new_address).await {
                Ok(_) => {
                    result.files_modified.push(file_path.to_string_lossy().to_string());
                }
                Err(e) => {
                    result.success = false;
                    result.error_message = Some(format!("Failed to update {}: {}", file_path.display(), e));
                    
                    // Attempt to restore from backups
                    for (original, backup) in files_to_update.iter().zip(backup_paths.iter()) {
                        let _ = restore_file(original, backup);
                    }
                    
                    return Ok(result);
                }
            }
        }
        
        // Validate the updates
        result.validation_passed = self.validate_updates(&files_to_update, new_address).await?;
        
        Ok(result)
    }
    
    async fn validate_configuration(&self) -> Result<ConfigValidationResult> {
        let mut overall_result = ConfigValidationResult {
            valid: true,
            config_files_found: Vec::new(),
            missing_keys: Vec::new(),
            invalid_values: Vec::new(),
            warnings: Vec::new(),
            contract_address_valid: false,
        };
        
        if self.config_files.is_empty() {
            overall_result.valid = false;
            overall_result.missing_keys.push("No configuration files found".to_string());
            return Ok(overall_result);
        }
        
        let mut found_valid_address = false;
        
        for file_path in &self.config_files {
            overall_result.config_files_found.push(file_path.to_string_lossy().to_string());
            
            match self.parse_config_file(file_path) {
                Ok(config) => {
                    let file_result = self.validate_config_structure(&config, file_path);
                    
                    // Merge results
                    overall_result.missing_keys.extend(file_result.missing_keys);
                    overall_result.invalid_values.extend(file_result.invalid_values);
                    overall_result.warnings.extend(file_result.warnings);
                    
                    if !file_result.valid {
                        overall_result.valid = false;
                    }
                    
                    if file_result.contract_address_valid {
                        found_valid_address = true;
                    }
                }
                Err(e) => {
                    overall_result.valid = false;
                    overall_result.invalid_values.push(format!("Failed to parse {}: {}", file_path.display(), e));
                }
            }
        }
        
        overall_result.contract_address_valid = found_valid_address;
        
        if !found_valid_address {
            overall_result.valid = false;
            overall_result.missing_keys.push("No valid Stargate Compass contract address found in any configuration file".to_string());
        }
        
        Ok(overall_result)
    }
    
    async fn backup_configuration(&self) -> Result<()> {
        for file_path in &self.config_files {
            backup_file(file_path)
                .map_err(|e| anyhow!("Failed to backup {}: {}", file_path.display(), e))?;
        }
        Ok(())
    }
    
    async fn restore_configuration(&self) -> Result<()> {
        // Find and restore from the most recent backups
        let mut restored_files = Vec::new();
        let mut errors = Vec::new();
        
        for file_path in &self.config_files {
            match self.find_most_recent_backup(file_path) {
                Ok(Some(backup_path)) => {
                    match restore_file(file_path, &backup_path) {
                        Ok(_) => {
                            restored_files.push(file_path.to_string_lossy().to_string());
                        }
                        Err(e) => {
                            errors.push(format!("Failed to restore {}: {}", file_path.display(), e));
                        }
                    }
                }
                Ok(None) => {
                    errors.push(format!("No backup found for {}", file_path.display()));
                }
                Err(e) => {
                    errors.push(format!("Error finding backup for {}: {}", file_path.display(), e));
                }
            }
        }
        
        if !errors.is_empty() {
            return Err(anyhow!(
                "Failed to restore some configuration files: {}. Successfully restored: {:?}",
                errors.join(", "),
                restored_files
            ));
        }
        
        Ok(())
    }
    
    async fn get_current_contract_address(&self) -> Result<Address> {
        // Try to get address from cached value first
        if let Some(address) = self.current_contract_address {
            return Ok(address);
        }
        
        // Otherwise, extract from configuration files
        for file_path in &self.config_files {
            if let Ok(config) = self.parse_config_file(file_path) {
                if let Ok(address) = self.extract_contract_address(&config) {
                    return Ok(address);
                }
            }
        }
        
        Err(anyhow!("No contract address found in any configuration file"))
    }
}

impl ConfigurationManager {
    /// Update a single configuration file with new contract address
    /// Implements atomic file writes with proper error handling and validation
    async fn update_single_config_file(&self, file_path: &Path, new_address: Address) -> Result<()> {
        // Step 1: Parse the current configuration
        let mut config = self.parse_config_file(file_path)?;
        
        // Step 2: Create backup before making changes
        let backup_path = backup_file(file_path)
            .map_err(|e| anyhow!("Failed to create backup for {}: {}", file_path.display(), e))?;
        
        // Step 3: Update the contract address in the configuration
        let updated = update_contract_address_in_toml(&mut config, new_address)?;
        
        if !updated {
            // Clean up backup if no updates were needed
            let _ = fs::remove_file(&backup_path);
            return Err(anyhow!("No contract address fields found to update in {}", file_path.display()));
        }
        
        // Step 4: Validate the updated configuration structure
        let validation_result = self.validate_config_structure(&config, file_path);
        if !validation_result.valid {
            // Clean up backup and return validation errors
            let _ = fs::remove_file(&backup_path);
            return Err(anyhow!(
                "Configuration validation failed after update: {:?}", 
                validation_result.invalid_values
            ));
        }
        
        // Step 5: Perform atomic write using temporary file
        let temp_path = file_path.with_extension("tmp");
        
        // Write to temporary file first
        match write_toml_config(&temp_path, &config) {
            Ok(_) => {
                // Atomic move from temp to final location
                match fs::rename(&temp_path, file_path) {
                    Ok(_) => {
                        // Success - clean up backup
                        let _ = fs::remove_file(&backup_path);
                    }
                    Err(e) => {
                        // Failed to move - restore from backup
                        let _ = fs::remove_file(&temp_path);
                        let _ = restore_file(file_path, &backup_path);
                        return Err(anyhow!("Failed to atomically update {}: {}", file_path.display(), e));
                    }
                }
            }
            Err(e) => {
                // Failed to write temp file - restore from backup
                let _ = fs::remove_file(&temp_path);
                let _ = restore_file(file_path, &backup_path);
                return Err(anyhow!("Failed to write temporary file for {}: {}", file_path.display(), e));
            }
        }
        
        // Step 6: Final validation - verify the address was actually updated
        let final_config = self.parse_config_file(file_path)?;
        let actual_address = self.extract_contract_address(&final_config)
            .map_err(|e| anyhow!("Failed to extract address after update: {}", e))?;
        
        if actual_address != new_address {
            // Critical error - restore from backup
            if backup_path.exists() {
                let _ = restore_file(file_path, &backup_path);
            }
            return Err(anyhow!(
                "Address verification failed: expected {:?}, got {:?}", 
                new_address, actual_address
            ));
        }
        
        Ok(())
    }
    
    /// Validate that updates were applied correctly with comprehensive verification
    pub async fn validate_updates(&self, updated_files: &[PathBuf], expected_address: Address) -> Result<bool> {
        let mut validation_results = Vec::new();
        let mut overall_success = true;
        
        for file_path in updated_files {
            let mut file_validation = FileValidationResult {
                file_path: file_path.clone(),
                success: true,
                address_matches: false,
                config_structure_valid: false,
                backup_exists: false,
                file_readable: false,
                errors: Vec::new(),
            };
            
            // Step 1: Verify file is readable
            match self.parse_config_file(file_path) {
                Ok(config) => {
                    file_validation.file_readable = true;
                    
                    // Step 2: Verify configuration structure is still valid
                    let structure_validation = self.validate_config_structure(&config, file_path);
                    file_validation.config_structure_valid = structure_validation.valid;
                    
                    if !structure_validation.valid {
                        file_validation.errors.extend(structure_validation.invalid_values);
                        file_validation.errors.extend(structure_validation.missing_keys);
                        file_validation.success = false;
                        overall_success = false;
                    }
                    
                    // Step 3: Verify contract address was updated correctly
                    match self.extract_contract_address(&config) {
                        Ok(actual_address) => {
                            file_validation.address_matches = actual_address == expected_address;
                            
                            if !file_validation.address_matches {
                                file_validation.errors.push(format!(
                                    "Address mismatch: expected {:?}, got {:?}",
                                    expected_address, actual_address
                                ));
                                file_validation.success = false;
                                overall_success = false;
                            }
                        }
                        Err(e) => {
                            file_validation.errors.push(format!(
                                "Failed to extract contract address: {}", e
                            ));
                            file_validation.success = false;
                            overall_success = false;
                        }
                    }
                }
                Err(e) => {
                    file_validation.errors.push(format!(
                        "Failed to parse configuration file: {}", e
                    ));
                    file_validation.success = false;
                    overall_success = false;
                }
            }
            
            // Step 4: Verify backup exists
            if let Ok(Some(_)) = self.find_most_recent_backup(file_path) {
                file_validation.backup_exists = true;
            }
            
            validation_results.push(file_validation);
        }
        
        // Step 5: Additional cross-file validation
        if overall_success && validation_results.len() > 1 {
            // Verify all files have consistent addresses
            let addresses: Vec<Address> = validation_results
                .iter()
                .filter_map(|result| {
                    if result.success {
                        self.parse_config_file(&result.file_path)
                            .ok()
                            .and_then(|config| self.extract_contract_address(&config).ok())
                    } else {
                        None
                    }
                })
                .collect();
            
            if !addresses.is_empty() {
                let first_address = addresses[0];
                if !addresses.iter().all(|&addr| addr == first_address) {
                    overall_success = false;
                }
            }
        }
        
        // Step 6: Log validation results for debugging
        for result in &validation_results {
            if !result.success {
                eprintln!(
                    "Validation failed for {}: {}",
                    result.file_path.display(),
                    result.errors.join(", ")
                );
            }
        }
        
        Ok(overall_success)
    }
    
    /// Find the most recent backup file for a given configuration file
    pub fn find_most_recent_backup(&self, file_path: &Path) -> Result<Option<PathBuf>> {
        let parent_dir = file_path.parent().unwrap_or_else(|| Path::new("."));
        let file_name = file_path.file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| anyhow!("Invalid file name: {}", file_path.display()))?;
        
        // Look for backup files with pattern: filename.backup.timestamp
        let backup_pattern = format!("{}.backup.", file_name);
        
        let mut backup_files = Vec::new();
        
        if let Ok(entries) = fs::read_dir(parent_dir) {
            for entry in entries.flatten() {
                if let Some(entry_name) = entry.file_name().to_str() {
                    if entry_name.starts_with(&backup_pattern) {
                        // Extract timestamp from filename
                        if let Some(timestamp_str) = entry_name.strip_prefix(&backup_pattern) {
                            if let Ok(timestamp) = timestamp_str.parse::<u64>() {
                                backup_files.push((timestamp, entry.path()));
                            }
                        }
                    }
                }
            }
        }
        
        if backup_files.is_empty() {
            return Ok(None);
        }
        
        // Sort by timestamp (most recent first)
        backup_files.sort_by(|a, b| b.0.cmp(&a.0));
        
        // Return the most recent backup
        Ok(Some(backup_files[0].1.clone()))
    }
}