// src/network/censorship_detector.rs

use std::collections::HashMap;
use ethers::types::H256;
use tracing::{debug, warn, info};
use chrono::{DateTime, Utc, Duration};

/// Basic censorship detection by comparing mempool vs block inclusion patterns
/// AUDIT-FIX: Implements transaction inclusion rate monitoring as required by Task 4.2
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct CensorshipDetector {
    /// Transactions seen in mempool but not yet included
    mempool_transactions: HashMap<H256, MempoolEntry>,
    /// Recently included transactions for rate calculation
    included_transactions: HashMap<H256, InclusionEntry>,
    /// Configuration thresholds
    config: CensorshipDetectorConfig,
}

#[derive(Debug, <PERSON>lone)]
struct MempoolEntry {
    first_seen: DateTime<Utc>,
    gas_price_gwei: Option<f64>,
    priority_fee_gwei: Option<f64>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
struct InclusionEntry {
    included_at: DateTime<Utc>,
    block_number: u64,
    time_to_inclusion_ms: u64,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct CensorshipDetectorConfig {
    /// Minimum inclusion rate to consider normal (default: 0.8 = 80%)
    pub inclusion_rate_threshold: f64,
    /// Time window for inclusion rate calculation (default: 5 minutes)
    pub analysis_window_minutes: i64,
    /// Maximum time to wait for inclusion before considering dropped (default: 10 minutes)
    pub max_inclusion_wait_minutes: i64,
    /// Minimum number of transactions needed for reliable analysis
    pub min_sample_size: usize,
}

impl Default for CensorshipDetectorConfig {
    fn default() -> Self {
        Self {
            inclusion_rate_threshold: 0.8, // 80% inclusion rate
            analysis_window_minutes: 5,
            max_inclusion_wait_minutes: 10,
            min_sample_size: 20,
        }
    }
}

impl CensorshipDetector {
    /// Create a new censorship detector with default configuration
    pub fn new() -> Self {
        Self::with_config(CensorshipDetectorConfig::default())
    }
    
    /// Create a new censorship detector with custom configuration
    pub fn with_config(config: CensorshipDetectorConfig) -> Self {
        Self {
            mempool_transactions: HashMap::new(),
            included_transactions: HashMap::new(),
            config,
        }
    }
    
    /// Record a transaction seen in the mempool
    /// AUDIT-FIX: Add transaction inclusion rate monitoring
    pub fn record_mempool_transaction(
        &mut self,
        tx_hash: H256,
        gas_price_gwei: Option<f64>,
        priority_fee_gwei: Option<f64>,
    ) {
        let entry = MempoolEntry {
            first_seen: Utc::now(),
            gas_price_gwei,
            priority_fee_gwei,
        };
        
        self.mempool_transactions.insert(tx_hash, entry);
        
        debug!(
            "CENSORSHIP DETECTOR: Recorded mempool transaction {} with gas price {:?} gwei",
            tx_hash, gas_price_gwei
        );
    }
    
    /// Record a transaction that was included in a block
    /// AUDIT-FIX: Compare mempool vs block inclusion patterns
    pub fn record_block_inclusion(
        &mut self,
        tx_hash: H256,
        block_number: u64,
    ) {
        let now = Utc::now();
        
        // Calculate time-to-inclusion if we saw it in mempool first
        let time_to_inclusion_ms = if let Some(mempool_entry) = self.mempool_transactions.remove(&tx_hash) {
            let duration = now - mempool_entry.first_seen;
            duration.num_milliseconds().max(0) as u64
        } else {
            // Transaction included without being seen in mempool (possible private pool)
            0
        };
        
        let inclusion_entry = InclusionEntry {
            included_at: now,
            block_number,
            time_to_inclusion_ms,
        };
        
        self.included_transactions.insert(tx_hash, inclusion_entry);
        
        debug!(
            "CENSORSHIP DETECTOR: Transaction {} included in block {} after {}ms",
            tx_hash, block_number, time_to_inclusion_ms
        );
    }
    
    /// Detect potential censorship based on inclusion patterns
    /// AUDIT-FIX: Set censorship_detected field based on inclusion analysis
    pub fn detect_censorship(&mut self) -> bool {
        self.cleanup_old_entries();
        
        let analysis_window = Duration::minutes(self.config.analysis_window_minutes);
        let now = Utc::now();
        let window_start = now - analysis_window;
        
        // Count transactions in analysis window
        let mempool_count = self.mempool_transactions
            .values()
            .filter(|entry| entry.first_seen >= window_start)
            .count();
            
        let included_count = self.included_transactions
            .values()
            .filter(|entry| entry.included_at >= window_start)
            .count();
            
        // Count transactions that should have been included by now but weren't
        let max_wait = Duration::minutes(self.config.max_inclusion_wait_minutes);
        let dropped_count = self.mempool_transactions
            .values()
            .filter(|entry| now - entry.first_seen > max_wait)
            .count();
        
        let total_transactions = mempool_count + included_count;
        
        // Need minimum sample size for reliable analysis
        if total_transactions < self.config.min_sample_size {
            debug!(
                "CENSORSHIP DETECTOR: Insufficient sample size ({} < {}), assuming no censorship",
                total_transactions, self.config.min_sample_size
            );
            return false;
        }
        
        // Calculate inclusion rate
        let inclusion_rate = if total_transactions > 0 {
            included_count as f64 / total_transactions as f64
        } else {
            1.0 // No transactions = no censorship
        };
        
        // Detect censorship if inclusion rate is below threshold
        let censorship_detected = inclusion_rate < self.config.inclusion_rate_threshold;
        
        if censorship_detected {
            warn!(
                "CENSORSHIP DETECTED: Inclusion rate {:.2}% below threshold {:.2}% (included: {}, mempool: {}, dropped: {})",
                inclusion_rate * 100.0,
                self.config.inclusion_rate_threshold * 100.0,
                included_count,
                mempool_count,
                dropped_count
            );
        } else {
            info!(
                "CENSORSHIP DETECTOR: Normal inclusion rate {:.2}% (included: {}, mempool: {}, dropped: {})",
                inclusion_rate * 100.0,
                included_count,
                mempool_count,
                dropped_count
            );
        }
        
        censorship_detected
    }
    
    /// Get current inclusion statistics
    pub fn get_inclusion_stats(&self) -> InclusionStats {
        let analysis_window = Duration::minutes(self.config.analysis_window_minutes);
        let now = Utc::now();
        let window_start = now - analysis_window;
        
        let mempool_count = self.mempool_transactions
            .values()
            .filter(|entry| entry.first_seen >= window_start)
            .count();
            
        let included_count = self.included_transactions
            .values()
            .filter(|entry| entry.included_at >= window_start)
            .count();
        
        let total_transactions = mempool_count + included_count;
        let inclusion_rate = if total_transactions > 0 {
            included_count as f64 / total_transactions as f64
        } else {
            1.0
        };
        
        // Calculate average time-to-inclusion
        let avg_tti_ms = if included_count > 0 {
            let total_tti: u64 = self.included_transactions
                .values()
                .filter(|entry| entry.included_at >= window_start)
                .map(|entry| entry.time_to_inclusion_ms)
                .sum();
            total_tti / included_count as u64
        } else {
            0
        };
        
        InclusionStats {
            inclusion_rate,
            mempool_count,
            included_count,
            total_transactions,
            avg_time_to_inclusion_ms: avg_tti_ms,
            analysis_window_minutes: self.config.analysis_window_minutes,
        }
    }
    
    /// Clean up old entries to prevent memory leaks
    fn cleanup_old_entries(&mut self) {
        let cleanup_threshold = Utc::now() - Duration::hours(1); // Keep 1 hour of data
        
        // Remove old mempool entries
        self.mempool_transactions.retain(|_, entry| entry.first_seen > cleanup_threshold);
        
        // Remove old inclusion entries
        self.included_transactions.retain(|_, entry| entry.included_at > cleanup_threshold);
    }
}

/// Statistics about transaction inclusion patterns
#[derive(Debug, Clone)]
pub struct InclusionStats {
    pub inclusion_rate: f64,
    pub mempool_count: usize,
    pub included_count: usize,
    pub total_transactions: usize,
    pub avg_time_to_inclusion_ms: u64,
    pub analysis_window_minutes: i64,
}

impl Default for CensorshipDetector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ethers::types::H256;
    
    #[test]
    fn test_censorship_detector_creation() {
        let detector = CensorshipDetector::new();
        assert_eq!(detector.config.inclusion_rate_threshold, 0.8);
        assert_eq!(detector.config.analysis_window_minutes, 5);
    }
    
    #[test]
    fn test_mempool_transaction_recording() {
        let mut detector = CensorshipDetector::new();
        let tx_hash = H256::random();
        
        detector.record_mempool_transaction(tx_hash, Some(20.0), Some(2.0));
        assert!(detector.mempool_transactions.contains_key(&tx_hash));
    }
    
    #[test]
    fn test_block_inclusion_recording() {
        let mut detector = CensorshipDetector::new();
        let tx_hash = H256::random();
        
        // First record in mempool, then include in block
        detector.record_mempool_transaction(tx_hash, Some(20.0), Some(2.0));
        detector.record_block_inclusion(tx_hash, 12345);
        
        // Should be removed from mempool and added to included
        assert!(!detector.mempool_transactions.contains_key(&tx_hash));
        assert!(detector.included_transactions.contains_key(&tx_hash));
    }
    
    #[test]
    fn test_censorship_detection_insufficient_sample() {
        let mut detector = CensorshipDetector::new();
        
        // With no transactions, should not detect censorship
        assert!(!detector.detect_censorship());
    }
    
    #[test]
    fn test_inclusion_stats() {
        let detector = CensorshipDetector::new();
        let stats = detector.get_inclusion_stats();
        
        assert_eq!(stats.inclusion_rate, 1.0); // No transactions = 100% rate
        assert_eq!(stats.total_transactions, 0);
    }
}