// MISSION: Error Tab Rendering for TUI
// WHY: Provide comprehensive error visualization and debugging interface
// HOW: Integrate error dashboard and notification system into TUI rendering

use ratatui::{
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Tabs},
    Frame,
};
use crate::tui::app::App;

impl App {
    /// Render the Errors tab with comprehensive debugging interface
    pub fn render_errors_tab(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3), // Tab header
                Constraint::Min(10),   // Main content
                Constraint::Length(3), // Status bar
            ])
            .split(area);
        
        // Render tab header
        self.render_errors_tab_header(f, chunks[0]);
        
        // Render main error dashboard
        self.error_dashboard.render(f, chunks[1]);
        
        // Render error status bar
        self.render_error_status_bar(f, chunks[2]);
        
        // Render notification overlay
        self.notification_system.render(f, area);
    }
    
    fn render_errors_tab_header(&self, f: &mut Frame, area: Rect) {
        let error_count = self.error_dashboard.error_stats.critical_count_last_hour + 
                         self.error_dashboard.error_stats.error_count_last_hour;
        let warning_count = self.error_dashboard.error_stats.warning_count_last_hour;
        let notification_count = self.notification_system.unread_count;
        
        let title = format!(
            "Error Dashboard - {} errors, {} warnings, {} notifications",
            error_count, warning_count, notification_count
        );
        
        let header_style = if error_count > 0 {
            Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)
        } else if warning_count > 0 {
            Style::default().fg(Color::Yellow)
        } else {
            Style::default().fg(Color::Green)
        };
        
        let header = Block::default()
            .borders(Borders::ALL)
            .title(title)
            .border_style(header_style);
        
        f.render_widget(header, area);
    }
    
    fn render_error_status_bar(&self, f: &mut Frame, area: Rect) {
        let status_text = vec![
            Line::from(vec![
                Span::raw("Controls: "),
                Span::styled("Tab", Style::default().fg(Color::Yellow)),
                Span::raw(" - Switch views | "),
                Span::styled("Enter", Style::default().fg(Color::Yellow)),
                Span::raw(" - Details | "),
                Span::styled("c", Style::default().fg(Color::Yellow)),
                Span::raw(" - Clear filters | "),
                Span::styled("a", Style::default().fg(Color::Yellow)),
                Span::raw(" - Acknowledge all | "),
                Span::styled("d", Style::default().fg(Color::Yellow)),
                Span::raw(" - Debug mode | "),
                Span::styled("e", Style::default().fg(Color::Yellow)),
                Span::raw(" - Error details"),
            ])
        ];
        
        let mut status_style = Style::default().fg(Color::White);
        if self.debug_mode {
            status_style = status_style.bg(Color::DarkGray);
        }
        
        let status_bar = ratatui::widgets::Paragraph::new(status_text)
            .block(Block::default().borders(Borders::ALL))
            .style(status_style);
        
        f.render_widget(status_bar, area);
    }
    
    /// Handle key events for the errors tab
    pub fn handle_errors_tab_key(&mut self, key: crossterm::event::KeyEvent) {
        use crossterm::event::KeyCode;
        
        match key.code {
            KeyCode::Tab => {
                // Switch between error dashboard tabs
                self.error_dashboard.handle_key(key);
            }
            KeyCode::Enter => {
                // Show error details
                self.show_error_details = !self.show_error_details;
                self.error_dashboard.handle_key(key);
            }
            KeyCode::Char('c') => {
                // Clear filters
                self.error_dashboard.handle_key(key);
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "ERROR_DASHBOARD".to_string(),
                    severity: crate::tui::app::LogSeverity::Info,
                    message: "Error filters cleared".to_string(),
                });
            }
            KeyCode::Char('a') => {
                // Acknowledge all notifications
                self.notification_system.acknowledge_all();
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "NOTIFICATIONS".to_string(),
                    severity: crate::tui::app::LogSeverity::Ok,
                    message: "All notifications acknowledged".to_string(),
                });
            }
            KeyCode::Char('d') => {
                // Toggle debug mode
                self.debug_mode = !self.debug_mode;
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "DEBUG".to_string(),
                    severity: crate::tui::app::LogSeverity::Info,
                    message: format!("Debug mode {}", if self.debug_mode { "enabled" } else { "disabled" }),
                });
            }
            KeyCode::Char('e') => {
                // Toggle error details
                self.show_error_details = !self.show_error_details;
            }
            KeyCode::Char('r') => {
                // Refresh error data
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "ERROR_DASHBOARD".to_string(),
                    severity: crate::tui::app::LogSeverity::Info,
                    message: "Error dashboard refreshed".to_string(),
                });
            }
            KeyCode::Esc => {
                // Close details/popups
                self.show_error_details = false;
                self.notification_system.handle_key(key);
                self.error_dashboard.handle_key(key);
            }
            _ => {
                // Delegate to error dashboard and notification system
                self.error_dashboard.handle_key(key);
                self.notification_system.handle_key(key);
            }
        }
    }
    
    /// Update the main tab rendering to include the Errors tab
    pub fn render_tab_content_with_errors(&mut self, f: &mut Frame, area: Rect) {
        match self.active_tab {
            crate::tui::app::AppTab::Dashboard => {
                // Existing dashboard rendering with notification overlay
                self.render_dashboard_tab(f, area);
                self.notification_system.render(f, area);
            }
            crate::tui::app::AppTab::Operations => {
                // Existing operations rendering with notification overlay
                self.render_operations_tab(f, area);
                self.notification_system.render(f, area);
            }
            crate::tui::app::AppTab::Systems => {
                // Existing systems rendering with notification overlay
                self.render_systems_tab(f, area);
                self.notification_system.render(f, area);
            }
            crate::tui::app::AppTab::Config => {
                // Existing config rendering with notification overlay
                self.render_config_tab(f, area);
                self.notification_system.render(f, area);
            }
            crate::tui::app::AppTab::Errors => {
                // New errors tab
                self.render_errors_tab(f, area);
            }
        }
    }
    
    /// Update the main tab bar to include the Errors tab
    pub fn render_tab_bar_with_errors(&self, f: &mut Frame, area: Rect) {
        let tab_titles = vec![
            "Dashboard",
            "Operations", 
            "Systems",
            "Config",
            &format!("Errors ({})", self.get_error_indicator()),
        ];
        
        let tabs = Tabs::new(tab_titles)
            .block(Block::default().borders(Borders::ALL).title("Basilisk Bot - Zen Geometer"))
            .select(self.active_tab as usize)
            .style(Style::default().fg(Color::White))
            .highlight_style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD));
        
        f.render_widget(tabs, area);
    }
    
    /// Get error indicator for tab title
    fn get_error_indicator(&self) -> String {
        let critical_count = self.notification_system.critical_alerts.len();
        let error_count = self.error_dashboard.error_stats.critical_count_last_hour + 
                         self.error_dashboard.error_stats.error_count_last_hour;
        
        if critical_count > 0 {
            format!("🚨{}", critical_count)
        } else if error_count > 0 {
            format!("❌{}", error_count)
        } else {
            "✅".to_string()
        }
    }
    
    /// Cleanup expired notifications and errors (call this in update loop)
    pub fn cleanup_error_feedback(&mut self) {
        self.notification_system.cleanup_expired();
        // Error dashboard cleanup is handled internally
    }
}

// Placeholder methods for existing tab rendering (these should already exist)
impl App {
    fn render_dashboard_tab(&mut self, _f: &mut Frame, _area: Rect) {
        // Existing dashboard rendering logic
    }
    
    fn render_operations_tab(&mut self, _f: &mut Frame, _area: Rect) {
        // Existing operations rendering logic
    }
    
    fn render_systems_tab(&mut self, _f: &mut Frame, _area: Rect) {
        // Existing systems rendering logic
    }
    
    fn render_config_tab(&mut self, _f: &mut Frame, _area: Rect) {
        // Existing config rendering logic
    }
}