#![no_main]

use libfuzzer_sys::fuzz_target;
use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;
use rust_decimal::Decimal;
use chrono::Utc;

fuzz_target!(|data: &[u8]| {
    if data.len() < 8 {
        return;
    }
    
    let mut analyzer = FractalAnalyzer::new();
    let base_time = Utc::now();
    
    // Extract price data from fuzz input
    let mut i = 0;
    while i + 8 <= data.len() {
        // Extract 8 bytes and convert to price
        let price_bytes = [
            data[i], data[i+1], data[i+2], data[i+3],
            data[i+4], data[i+5], data[i+6], data[i+7]
        ];
        
        let price_raw = u64::from_le_bytes(price_bytes);
        
        // Convert to reasonable price range (avoid extreme values)
        let price_f64 = (price_raw % 1000000) as f64 / 100.0; // 0.01 to 9999.99
        
        if price_f64 > 0.0 {
            if let Some(price_decimal) = Decimal::from_f64_retain(price_f64) {
                let timestamp = base_time + chrono::Duration::seconds((i / 8) as i64);
                analyzer.add_price_point(price_decimal, timestamp);
            }
        }
        
        i += 8;
        
        // Limit iterations to prevent excessive computation
        if i > 800 { // Max 100 price points
            break;
        }
    }
    
    // Try to analyze - should not panic regardless of input
    let _analysis = analyzer.analyze();
});
