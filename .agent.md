# Zen Geometer (Basilisk Bot) - Developer Guide

## Project Overview

**Zen Geometer** (binary name: `basilisk_bot`) is a production-ready autonomous DeFi trading bot built in Rust, designed for cross-chain MEV (Maximal Extractable Value) opportunities with advanced geometric analysis of market structure. The system targets Layer 3 inefficiencies (primarily Degen Chain) while using Base as the settlement layer through sophisticated Hub and Spoke architecture.

### Core Information
- **Project Name**: Zen Geometer (Basilisk Bot)
- **Language**: Rust (Edition 2021)
- **Status**: Production Ready - All 4 phases completed
- **Architecture**: Microservices with NATS message queue
- **Primary Strategy**: Cross-chain arbitrage via Stargate protocol
- **Philosophy**: Present-moment intelligence with geometric structure analysis
- **Binary Name**: `basilisk_bot` (despite project name being Zen Geometer)
- **Core Mission**: Develop an intelligent, high-frequency trading bot capable of identifying and executing profitable opportunities in DeFi markets by moving beyond simple correlation to find predictive, causal links and understanding the quality and context of opportunities and the state of the network medium

### Guiding Principles
- **Resonance over Correlation**: Prioritize opportunities based on their "resonance" within market cycles, liquidity structures, and network conditions, rather than just simple price or volume correlation
- **Layered Analysis as Filter/Multiplier**: Each analytical layer (Chronos Sieve, Mandorla Gauge, Network Seismology) acts to progressively refine the bot's perception and action, filtering out low-quality signals and amplifying high-quality ones
- **Contextual Execution**: Time transaction broadcasts to coincide with optimal network conditions (e.g., low propagation latency) to maximize execution success and minimize risk

## Key Technologies

### Core Dependencies
- **Async Runtime**: `tokio` (full features)
- **Blockchain**: `ethers` v2.0 (WebSocket, TLS, abigen)
- **Messaging**: `async-nats` for microservice communication
- **Database**: `sqlx` v0.8 (PostgreSQL, async)
- **Caching**: `redis` with async support
- **Math**: `rust_decimal` for precise financial calculations
- **Graph Theory**: `petgraph` + `page_rank` for pathfinding
- **FFT Analysis**: `rustfft` for fractal market analysis
- **Metrics**: `prometheus` + `axum` for observability
- **TUI**: `ratatui` + `crossterm` for terminal interface

### Development Tools
- **Testing**: `criterion` (benchmarks), `mockall` (mocking), `anvil` (blockchain simulation)
- **CLI**: `clap` v4 with derive features
- **Config**: `config` crate with TOML support
- **Logging**: `tracing` + `tracing-subscriber`
- **Error Handling**: `thiserror` + `anyhow` + `eyre`

## Important Files and Directories

### Core Structure
- `src/lib.rs` - Main library entry point with module definitions
- `src/main.rs` - CLI application entry point with 5-tier deployment modes
- `Cargo.toml` - Project dependencies and binary definitions
- `config/default.toml` - Multi-chain configuration with L2/L3 support

### Key Directories
- `src/strategies/` - Core intelligence with scanners and opportunity evaluation
- `src/data/` - Data ingestion and market analysis components
- `src/execution/` - Transaction execution and management (MEV-aware)
- `src/risk/` - Risk assessment and position sizing (Kelly Criterion)
- `src/tui/` - Terminal user interface with real-time data
- `src/math/` - Mathematical foundations including sacred geometry
- `src/control/` - System monitoring and control
- `src/metrics/` - Prometheus metrics and observability

### Binary Tools
- `bin/listener.rs` - Network data listener
- `bin/data_ingestor.rs` - Data processing tool
- `bin/tui_harness.rs` - Terminal UI testing harness
- `bin/feature_exporter.rs` - Market analysis and SIGINT report generation
- `src/bin/graph_analyzer.rs` - PageRank-based token centrality analysis

### Smart Contracts
- `geometer-contracts/contracts/StargateCompassV1.sol` - Cross-chain execution contract
- `geometer-contracts/contracts/interfaces/` - External protocol integrations

### Configuration Files
- `config/default.toml` - Main configuration with chain settings
- `.env` - Environment variables (create from .env.example)
- `config/prometheus.yml` - Prometheus monitoring configuration

### Documentation
- `README.md` - Strategic overview and Hub and Spoke architecture
- `docs/USER_GUIDE.md` - Comprehensive operator's playbook
- `PRODUCTION_DEPLOYMENT_GUIDE.md` - Production readiness guide
- `docs/strategies/` - Strategy implementation documentation

## Development Guidelines

### Code Style and Quality
- Follow Rust idioms and best practices
- Use async/await for all I/O operations
- Implement proper error handling with thiserror/anyhow
- Maintain the "tactical language" style in comments and logs
- Use `rust_decimal::Decimal` for all financial calculations
- Format code with `cargo fmt` before commits
- Run `cargo clippy -- -D warnings` to catch issues
- **NO SUPPRESSED WARNINGS**: Code quality enforcement is active (removed from Cargo.toml)

### Architecture Patterns
- Implement new features as independent modules
- Use NATS for inter-service communication
- Follow the microservice design pattern
- Maintain clear separation between data, strategy, and execution
- Use the type-safe opportunity system with state transitions
- Implement proper metrics for all new components

### Testing Requirements
- Write unit tests for all core functionality
- Use integration tests with Anvil for blockchain interactions
- Test across multiple chains when implementing multi-chain features
- Verify mathematical correctness in all algorithms
- Include performance benchmarks for critical paths
- Test MEV protection and transaction broadcasting

### Documentation Standards
- Document all public APIs with rustdoc
- Maintain README files in each directory
- Follow the established documentation style
- Include educational explanations where appropriate
- Update user guide for new features
- Document configuration options

## Core Architecture & Components

### System Components

#### 1. Data Ingestion & Handling
- **Concept**: Responsible for connecting to external data sources (e.g., NATS streams, RPC nodes, CEX feeds), consuming raw market data (trades, gas prices, block propagation), validating it, and making it available for analysis
- **Implementation**: Standalone services like `data_ingestor` and `listener` (see `[[bin]]` section in `Cargo.toml`). Core logic is in modules under `src/data/`, including `src/data/block_ingestor.rs` and `src/data/chain_monitor.rs`. Uses networking libraries like `tokio` and `tungstenite`

#### 2. Chronos Sieve (Temporal Analysis)
- **Concept**: Analyzes time series data (trades, gas prices) using spectral analysis (FFT) to identify dominant market cycles and assess market rhythm stability. Publishes `TemporalHarmonics` state
- **Implementation**: Primarily implemented in `src/data/fractal_analyzer.rs`, utilizing libraries like `rustfft` for FFT calculations. It is spawned as a background task from `main.rs`

#### 3. Mandorla Gauge (Geometric & Liquidity Analysis)
- **Concept**: Represents potential arbitrage paths geometrically, analyzes their shape (e.g., convexity ratio), and incorporates asset centrality scores (from Axis Mundi) to assess the robustness and stability of opportunities. Calculates a `GeometricScore`. This is a key analytical pillar for the Zen Geometer strategy
- **Implementation**: Leverages modules like `src/math/geometry.rs` and `src/math/vesica.rs` for geometric calculations. Interacts with strategy components like `src/strategies/scanners/swap.rs` (for risk-adjusted arbitrage pathfinding) and `src/strategies/manager.rs`. Uses libraries like `geo` and `petgraph`

#### 4. Network Seismology (Network State Analysis)
- **Concept**: Observes block propagation times across multiple nodes, applying seismology concepts (P-wave, S-wave, S-P time) to measure network latency and coherence. Identifies network stress or shock events. Publishes `NetworkResonanceState`
- **Implementation**: Implemented as standalone services `network_observer` and `seismic_analyzer` (see `[[bin]]` section in `Cargo.toml`)

#### 5. Strategy & Regime Management
- **Concept**: The `StrategyManager` (`src/strategies/manager.rs`) receives potential opportunities from scanners. The `RegimeManager` (`src/strategies/regime_manager.rs`), spawned as a background task, likely uses this data to determine the overall market state. The Strategy Manager then queries the analytical pillars (Chronos Sieve, Mandorla Gauge, Network Seismology) for their respective states, synthesizes this information into a final `AethericResonanceScore`, and decides whether to proceed
- **Implementation**: Located in `src/strategies/manager.rs` and `src/strategies/regime_manager.rs`. Coordinates interactions between scanners (`src/strategies/scanners/`) and the analytical components

#### 6. Execution Manager
- **Concept**: Handles the final stages of processing a high-scoring opportunity, including timing the transaction broadcast based on real-time network conditions and interacting with the blockchain
- **Implementation**: Found in `src/execution/manager.rs` and `src/execution/dispatcher.rs`. It is spawned as a background task from `main.rs`

### Key Operational Flow (Zen Geometer Strategy)

1. **Opportunity Detection (Scanners)**: Scanners like `SwapScanner` (`src/strategies/scanners/swap.rs`) identify potential trading opportunities (e.g., a multi-hop arbitrage path) based on raw market data

2. **Strategy Evaluation & Scoring (Strategy Manager)**: The `StrategyManager` (`src/strategies/manager.rs`) receives the detected opportunity. It queries the `FractalAnalyzer` (Chronos Sieve) for `TemporalHarmonics`, the geometric/math modules (Mandorla Gauge) for the `GeometricScore`, and the network analysis component (Network Seismology) for the `NetworkResonanceState`. It synthesizes these inputs to calculate the `AethericResonanceScore`. This is where the Zen Geometer's core decision-making occurs

3. **Decision and Execution Preparation**: If the `AethericResonanceScore` exceeds a predefined `min_resonance_threshold`, the `StrategyManager` passes the opportunity to the `ExecutionManager` (`src/execution/manager.rs`)

4. **Timed Transaction Broadcast**: The `ExecutionManager`, potentially consulting a `HarmonicTimingOracle` (derived from the `NetworkResonanceState`), waits for optimal network conditions before broadcasting the transaction to the network

5. **CLI Command Handling (User Interaction)**: Users can interact with the bot via the main binary. The `run` command starts the bot, `validate` checks the configuration, `config` manages settings (show, validate, set parameters), and `utils` provides tools to check balances, ping nodes, and test strategies

## Core Concepts

### Hub and Spoke Architecture
- **Base (L2)**: Capital settlement hub with Aave flash loans
- **Degen Chain (L3)**: Execution venue with emerging market inefficiencies
- **StargateCompass**: Custom smart contract for atomic cross-chain execution
- **Operator Curation**: Human-in-the-loop asset selection via manifold configuration

### Aetheric Resonance Engine (ARE)
Three-pillar autonomous decision system:
- **Chronos Sieve**: Real-time fractal market state classification using FFT
- **Mandorla Gauge**: Opportunity depth qualification using Vesica Piscis
- **Axis Mundi Heuristic**: PageRank-based pathfinding optimization

### Dimension 10 Market Making Framework
- **Topological Manifold**: Operator-curated worthy assets
- **Phase Space Prism**: Real-time market phase analysis
- **Aetheric Loom**: Atomic cross-chain execution weaving

### SIGINT Workflow
- Autonomous operation as default (Zen Geometer mode)
- Intelligence Officer override capability
- Feature exporter for market analysis and report generation

### MEV Protection
- Intelligent broadcaster selection (public vs private relay)
- Bundle submission for MEV-sensitive transactions
- Transaction simulation and validation
- Advanced nonce management with recovery

## Operational Modes (5-Tier Deployment Ladder)

### 1. Simulate Mode (No Risk)
```bash
cargo run -- run --mode simulate
```
- Educational trading with live data analysis
- All transactions intercepted - no real money at risk
- Perfect for learning system behavior

### 2. Shadow Mode (No Risk)
```bash
cargo run -- run --mode shadow
```
- Live simulation with on-chain verification using Anvil forks
- Tests transactions on forked state without broadcasting
- Validates profitability without mainnet risk

### 3. Sentinel Mode (Minimal Risk)
```bash
cargo run -- run --mode sentinel
```
- Live monitoring with test transactions (max $10 USD)
- Contract health monitoring and basic functionality testing
- Real-world gas estimation and network connectivity validation

### 4. Low-Capital Mode (Low Risk)
```bash
cargo run -- run --mode low-capital
```
- Conservative live trading with hardcoded limits
- Maximum daily loss: $50 USD, maximum position: $100 USD
- Kelly fraction capped at 2% for ultra-conservative operation

### 5. Live Mode (Full Risk)
```bash
cargo run -- run --mode live
```
- Full production trading with configured risk parameters
- Complete strategy suite active with advanced MEV protection
- Real-time monitoring and comprehensive alerting

### Guided Deployment
```bash
./scripts/deployment_ladder.sh
```
Interactive deployment with safety confirmations and progressive risk management

## Environment Setup

### Required Environment Variables
- `BASILISK_EXECUTION_PRIVATE_KEY` - Bot wallet private key (without 0x prefix)
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `NATS_URL` - NATS server URL
- `BASE_RPC_API_KEY` - Base network RPC API key (optional but recommended)
- `ARBITRUM_RPC_API_KEY` - Arbitrum network RPC API key (optional)
- `MEV_RELAY_AUTH_KEY` - MEV relay authentication key (optional)
- `COINBASE_API_KEY` / `COINBASE_API_SECRET` - CEX API credentials (optional)
- `HONEYPOT_API_KEY` - GoPlus API key for honeypot detection (optional)

### Quick Setup
```bash
# 1. Install dependencies
cargo build --release

# 2. Setup environment
cp .env.example .env
# Edit .env with your configuration

# 3. Run preflight checks
./scripts/preflight.sh

# 4. Start with simulation
cargo run -- run --mode simulate
```

### Development Environment Bootstrap
```bash
# Automated setup script
./scripts/local_dev_setup.sh

# Manual setup
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
curl -L https://foundry.paradigm.xyz | bash
foundryup
docker-compose up -d
```

## Common Development Tasks

### Adding a New Scanner
1. Create a new file in `src/strategies/scanners/`
2. Implement the Scanner trait
3. Add the scanner to the module exports in `src/strategies/scanners/mod.rs`
4. Register the scanner in the StrategyManager
5. Add appropriate metrics and logging
6. Write integration tests

### Adding Multi-Chain Support
1. Add chain configuration to `config/default.toml`
2. Implement chain-specific adapters if needed
3. Test with chain-specific RPC endpoints
4. Update documentation and user guide
5. Add chain-specific metrics

### Implementing a New Strategy
1. Document the strategy in `docs/strategies/`
2. Create implementation files in `src/strategies/`
3. Add integration with the Aetheric Resonance Engine
4. Create tests in the `tests/` directory
5. Add metrics and monitoring
6. Update configuration options

### Adding New Metrics
1. Add metric definitions to `src/metrics.rs`
2. Register metrics in the ZenGeometerMetrics constructor
3. Add recording functions for the new metrics
4. Use metrics in relevant components
5. Document metric meanings and usage

## Mathematical Foundations

### Kelly Criterion Implementation
- Advanced position sizing with regime adaptation
- Formula: `f* = (Edge / Variance) * Regime_Multiplier * Volatility_Adjustment`
- Multi-layer risk management caps
- Dynamic volatility adjustment

### Golden Ratio Bidding
- Gas strategy optimization using phi (1.618)
- Competitive positioning in MEV environments
- Mathematical precision in execution timing

### Geometric Analysis
- Vesica Piscis for opportunity depth calculation
- Fractal market structure analysis
- Sacred geometry principles in market analysis

## Security Considerations

### Development Security
- Always use simulation before live execution
- Implement proper error handling and circuit breakers
- Validate all external data inputs
- Use the HoneypotChecker for contract security validation
- Follow the principle of least privilege for RPC connections
- Never commit private keys or sensitive data

### Production Security
- Use secure key management for private keys
- Implement proper access controls
- Monitor for unusual activity patterns
- Use circuit breakers for risk management
- Validate all smart contract interactions
- Implement proper logging without exposing secrets

## Performance Optimization

### Real-time Requirements
- Optimize for sub-second decision making
- Use parallel processing where appropriate
- Minimize memory allocations in hot paths
- Leverage async Rust for I/O-bound operations
- Use efficient data structures for high-throughput components

### Memory Management
- Use `Arc` for shared data structures
- Implement proper cleanup for long-running processes
- Monitor memory usage with metrics
- Use streaming for large data sets
- Implement backpressure for data pipelines

## Troubleshooting

### Common Issues
- **RPC connection failures**: Check endpoint configuration and fallback settings
- **NATS communication issues**: Verify NATS server is running and properly configured
- **Performance bottlenecks**: Check for blocking operations in async code
- **Memory leaks**: Look for unbounded collections or improper resource management
- **MEV relay failures**: Verify authentication keys and relay endpoints
- **Nonce synchronization**: Use NonceManager recovery functions

### Debugging Tools
- Use the TUI harness for real-time system monitoring
- Check logs with appropriate tracing levels
- Use the simulator for risk-free testing
- Leverage the feature exporter for market analysis
- Monitor Prometheus metrics for system health
- Use Anvil for integration testing

## Task Master Integration

### Essential Commands
```bash
# Project Setup
task-master init                                    # Initialize Task Master in current project
task-master parse-prd .taskmaster/docs/prd.txt      # Generate tasks from PRD document
task-master models --setup                        # Configure AI models interactively

# Daily Development Workflow
task-master list                                   # Show all tasks with status
task-master next                                   # Get next available task to work on
task-master show <id>                             # View detailed task information (e.g., task-master show 1.2)
task-master set-status --id=<id> --status=done    # Mark task complete

# Task Management
task-master add-task --prompt="description" --research        # Add new task with AI assistance
task-master expand --id=<id> --research --force              # Break task into subtasks
task-master update-task --id=<id> --prompt="changes"         # Update specific task
task-master update --from=<id> --prompt="changes"            # Update multiple tasks from ID onwards
task-master update-subtask --id=<id> --prompt="notes"        # Add implementation notes to subtask

# Analysis & Planning
task-master analyze-complexity --research          # Analyze task complexity
task-master complexity-report                      # View complexity analysis
task-master expand --all --research               # Expand all eligible tasks

# Dependencies & Organization
task-master add-dependency --id=<id> --depends-on=<id>       # Add task dependency
task-master move --from=<id> --to=<id>                       # Reorganize task hierarchy
task-master validate-dependencies                            # Check for dependency issues
task-master generate                                         # Update task markdown files (usually auto-called)
```

### API Keys Required
- `ANTHROPIC_API_KEY` for Claude models
- Check `.mcp.json` configuration
- Use `.taskmaster/config.json` for project settings

### MCP Integration
- Task Master integrates with Claude via MCP (Model Context Protocol)
- Requires proper configuration in `.mcp.json`
- Enables AI-assisted task management and code analysis

## Quality Assurance

### Pre-commit Checklist
- [ ] Code formatted with `cargo fmt`
- [ ] No clippy warnings
- [ ] All tests passing
- [ ] Documentation updated
- [ ] Metrics added for new features
- [ ] Security considerations reviewed
- [ ] Performance impact assessed

### Code Quality Checks
```bash
# Format code
cargo fmt

# Lint code
cargo clippy -- -D warnings

# Security audit
cargo audit

# Run benchmarks
cargo bench

# Integration tests
cargo test test_integration_anvil
```

## Binary Tools and Utilities

### Core Binaries
- **`listener`**: Network data listener for real-time market data
- **`data_ingestor`**: Data processing and validation tool
- **`tui_harness`**: Terminal UI testing harness and dashboard
- **`feature_exporter`**: Market analysis and SIGINT report generation
- **`graph_analyzer`**: PageRank-based token centrality analysis

### Analysis Tools
- **`seismic_analyzer`**: Network seismology for block propagation analysis
- **`network_observer`**: Blockchain network monitoring with sequencer tracking
- **`backtester`**: Strategy backtesting against historical data
- **`mempool_backtester`**: Mempool-specific backtesting for MEV strategies
- **`optimizer`**: Strategy parameter optimization

### Development Tools
- **`demo_data_generator`**: Synthetic data generation for testing
- **`wallet_checker`**: Wallet balance and connectivity verification
- **`visualization_demo`**: Market visualization and analysis demos

### Usage Examples
```bash
# Launch TUI dashboard
cargo run --bin tui_harness

# Generate SIGINT report
cargo run --bin feature_exporter --output sigint/analysis_$(date +%Y%m%d_%H%M%S).json

# Analyze token centrality
cargo run --bin graph_analyzer --chain-id 8453

# Monitor network health
cargo run --bin network_observer --monitor-sequencer

# Run strategy backtests
cargo run --bin backtester --strategy zen_geometer
```

## Production Deployment

### Deployment Checklist
- [ ] Preflight checks pass without errors
- [ ] Configuration validated and tested
- [ ] Network connectivity confirmed
- [ ] Wallet funded and accessible
- [ ] Risk parameters configured appropriately
- [ ] Monitoring systems operational
- [ ] Emergency procedures understood
- [ ] Deployment mode selected and confirmed

### Monitoring
- **Prometheus Metrics**: 40+ KPIs for system monitoring
- **TUI Interface**: Real-time terminal dashboard
- **Grafana Dashboards**: Visual monitoring and alerting
- **Structured Logging**: JSON format with trace IDs

## IDE-Specific Rules and Conventions

### Code Style Guidelines
- **Financial Calculations**: Always use `rust_decimal::Decimal` instead of `f64`
- **Error Handling**: Use `thiserror` for library errors, `anyhow` for application errors
- **Async Code**: Use `tokio` runtime with proper async/await patterns
- **Configuration**: Use TOML files with the `config` crate
- **Logging**: Use `tracing` with structured logging (JSON format in production)

### Code Examples
```rust
// ✅ DO: Use Decimal for financial calculations
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

let price = dec!(1.23456789);
let amount = dec!(100.0);
let total = price * amount;

// ❌ DON'T: Use floating point for financial calculations
let price = 1.23456789_f64; // Never do this!

// ✅ DO: Proper error handling
use thiserror::Error;

#[derive(Error, Debug)]
pub enum StrategyError {
    #[error("Insufficient liquidity: {available} < {required}")]
    InsufficientLiquidity { available: Decimal, required: Decimal },
}

// ✅ DO: Structured logging
use tracing::{info, warn, error};

info!(
    opportunity_id = %opportunity.id,
    profit_usd = %opportunity.profit_usd,
    "Opportunity detected"
);
```

### File Organization Conventions
- **Module Structure**: Follow the existing pattern in `src/lib.rs`
- **Configuration**: Chain-specific configs in `config/` directory
- **Documentation**: Strategy docs in `docs/strategies/`
- **Tests**: Integration tests in `tests/` directory
- **Binaries**: Utility binaries in `bin/` and `src/bin/`

### Testing Requirements
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Use Anvil for blockchain simulation
- **Property Tests**: Use `proptest` for mathematical properties
- **Benchmarks**: Use `criterion` for performance testing

## Project Glossary

### Core Terms
- **Strategy Manager**: The central decision-making component of the Basilisk Bot. It synthesizes inputs from analytical pillars to score opportunities and decide on execution
- **Regime Manager**: A component that runs in the background to determine the current market state or "regime"
- **Zen Geometer**: The primary trading strategy implemented in the Basilisk Bot. It leverages fractal analysis (Chronos Sieve), geometric and liquidity analysis (Mandorla Gauge), and network state analysis (Network Seismology) to identify and execute profitable opportunities
- **CLI Command Handlers**: A module responsible for processing command-line interface commands

### Analytical Pillars
- **Chronos Sieve**: The analytical pillar focused on identifying market cycles and temporal patterns from time series data using spectral analysis
- **Mandorla Gauge**: The analytical pillar focused on assessing the structural robustness and asset stability of trading opportunities using geometric analysis and asset centrality
- **Network Seismology**: The analytical pillar focused on measuring and interpreting network propagation latency and coherence to understand the state of the blockchain network medium

### State Outputs
- **Temporal Harmonics**: The output state from the Chronos Sieve, indicating dominant market cycles and rhythm stability
- **Geometric Score**: The output score from the Mandorla Gauge, reflecting the convexity ratio of an opportunity's geometric representation and the centrality of its assets
- **Network Resonance State**: The output state from the Network Seismology, indicating network propagation latency (`sp_time_ms`) and coherence
- **Aetheric Resonance Score**: The final, synthesized score calculated by the Strategy Manager, combining inputs from all three analytical pillars to determine the overall quality and context of an opportunity

### Components & Services
- **Fractal Analyzer**: The specific service/module responsible for implementing the Chronos Sieve
- **Swap Scanner**: A type of scanner that identifies potential arbitrage paths across decentralized exchanges
- **Axis Mundi**: A conceptual or actual component responsible for calculating and providing asset centrality scores

### Network Seismology Terms
- **P-Wave (Primary Wave)**: In Network Seismology, the timestamp of the first node reporting a new block
- **S-Wave (Secondary Wave)**: In Network Seismology, the timestamp when a significant percentage (e.g., 80%) of nodes have reported a new block
- **S-P Time**: The time delta between the S-Wave and P-Wave, used as a measure of network propagation latency

## AI Collaboration Guidelines

### Code Quality Standards
- **Adherence to Core Principles**: All code contributions, documentation, and architectural suggestions must align with the Guiding Principles outlined above. A technically correct but philosophically misaligned solution is considered invalid
- **Code Embodies Concept**: Abstract goals and principles must be reflected in the concrete implementation. Naming conventions, module structures, and data flows should intuitively map back to the project's core concepts (e.g., harmonic analysis, geometric shapes, network waves)
- **Architectural Sanctity**: The established high-level architecture (modular Rust services, data ingestion, strategy evaluation, execution) is a foundational constraint. Any proposed changes to this core structure must be explicitly discussed and justified
- **Documentation Standard**: All new public functions, modules, and complex logic must be accompanied by clear, concise documentation explaining both the "what" (its function) and the "why" (its purpose within the broader system), particularly how it contributes to the core analytical pillars or operational flows

### Development Workflow Integration
- **Task Master Integration**: Use Task Master for AI-assisted task management and code analysis
- **MCP Protocol**: Leverage Model Context Protocol for structured AI interactions
- **Research-Driven Development**: Use the `research` tool frequently to get fresh information beyond knowledge cutoff dates
- **Iterative Implementation**: Follow the iterative subtask implementation pattern with detailed logging

### Configuration Management
- **Environment Variables**: Use `.env` file for sensitive API keys and credentials
- **TOML Configuration**: Use `config/default.toml` for system configuration
- **Profile Management**: Leverage configuration profiles for different deployment scenarios
- **Validation**: Always validate configuration changes with `cargo run -- config validate --strict`

---

*"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."* - **The Zen Geometer**