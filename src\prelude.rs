//! Prelude module for commonly used types and traits across the Basilisk Bot

// Re-export common error types
pub use crate::error::{
    BasiliskError, ConfigError, ConfigR<PERSON>ult, DataError, DataResult, ExecutionError,
    ExecutionResult, Result, RiskError, RiskResult, StrategyError, StrategyResult,
};

// Re-export common shared types
pub use crate::shared_types::{
    CexTrade, ExecutionPriority, ExecutionRequest, ExecutionResult as ExecResult, MarketCharacter,
    MarketRegime, NatsTopics, Opportunity, OpportunityType, OrderBookUpdate, ServiceStatus,
    StrategyType, SystemHealth, TradeSide,
};

// Re-export commonly used external types
pub use anyhow::{Context, Result as AnyhowResult};
pub use ethers::types::{Address, H256, U256};
pub use serde::{Deserialize, Serialize};
pub use std::sync::Arc;
pub use tokio::sync::{<PERSON>te<PERSON>, RwLock};
pub use tracing::{debug, error, info, trace, warn};

// Re-export async traits and utilities
pub use async_trait::async_trait;
pub use futures_util::{SinkExt, StreamExt};

// Re-export time utilities
pub use chrono::{DateTime, Utc};
pub use std::time::{Duration, SystemTime, UNIX_EPOCH};

// Re-export collections
pub use std::collections::{BTreeMap, HashMap, HashSet, VecDeque};
