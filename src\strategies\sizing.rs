use ethers::types::{Address, U256};
use rust_decimal::prelude::*;
use std::error::Error;
use tracing::{debug, error, info};
use crate::shared_types::{SimpleOpportunity, StrategyType};
use anyhow::Result;

/// Calculates the optimal input amount for a two-pool x*y=k arbitrage.
///
/// This implements the closed-form solution for constant product pools.
///
/// # Arguments
/// * `x1`, `y1` - Reserves of token0 and token1 in the first pool
/// * `fee1` - Fee of the first pool (e.g., 0.003 for 0.3%)
/// * `x2`, `y2` - Reserves of token0 and token1 in the second pool
/// * `fee2` - Fee of the second pool
///
/// # Returns
/// * `Some(U256)` - The optimal input amount if an arbitrage opportunity exists
/// * `None` - If no arbitrage opportunity exists
pub fn calculate_optimal_amount_v2(
    x1: U256,
    y1: U256,
    fee1: Decimal,
    x2: U256,
    y2: U256,
    fee2: Decimal,
) -> Option<U256> {
    // Convert U256 to Decimal for high-precision math
    let x1_d = u256_to_decimal(x1)?;
    let y1_d = u256_to_decimal(y1)?;
    let x2_d = u256_to_decimal(x2)?;
    let y2_d = u256_to_decimal(y2)?;

    // Calculate the optimal input amount using the formula:
    // sqrt(x1*y1*x2*y2 * (1-f1)*(1-f2)) - (x1*y2)
    // -------------------------------------------
    //          y2*(1-f1) + y1

    let one = Decimal::ONE;
    let fee1_factor = one - fee1;
    let fee2_factor = one - fee2;

    // Calculate the numerator
    let term1 = x1_d * y1_d * x2_d * y2_d * fee1_factor * fee2_factor;
    // Use approximation for square root since rust_decimal doesn't have sqrt
    let term1_sqrt = approximate_sqrt(term1)?;
    let term2 = x1_d * y2_d;

    // Check if there's an arbitrage opportunity
    if term1_sqrt <= term2 {
        debug!(
            "No arbitrage opportunity: term1_sqrt ({}) <= term2 ({})",
            term1_sqrt, term2
        );
        return None;
    }

    let numerator = term1_sqrt - term2;

    // Calculate the denominator
    let denominator = y2_d * fee1_factor + y1_d;

    // Calculate the optimal input amount
    let optimal_amount = numerator / denominator;

    // Convert back to U256
    decimal_to_u256(optimal_amount)
}

/// DEPRECATED: Ternary search is replaced by Kelly Criterion
/// This function is kept for backward compatibility but should not be used
/// Use calculate_kelly_position_size instead for optimal capital allocation
#[deprecated(note = "Use calculate_kelly_position_size for mathematically optimal sizing")]
pub async fn find_optimal_amount_iterative<F, Fut>(profit_calculator: F, max_input: U256) -> U256
where
    F: Fn(U256) -> Fut,
    Fut: std::future::Future<Output = i128>,
{
    let mut low = U256::zero();
    let mut high = max_input;

    // Perform ternary search for ~20 iterations
    for _ in 0..20 {
        if high <= low + U256::from(100) {
            // If the range is small enough, we're done
            break;
        }

        let range = high - low;
        let mid1 = low + range / 3;
        let mid2 = high - range / 3;

        // Calculate profit at both midpoints
        let profit1 = profit_calculator(mid1).await;
        let profit2 = profit_calculator(mid2).await;

        // Update the search range based on which midpoint has higher profit
        if profit1 > profit2 {
            high = mid2;
        } else {
            low = mid1;
        }
    }

    // Return the midpoint of the final range
    (low + high) / 2
}

/// Calculate optimal position size using the Kelly Criterion
/// Formula: f* = Edge / Variance
/// Where Edge is the risk-adjusted expected profit margin
/// And Variance is the square of the asset's volatility
///
/// This is the mathematically optimal way to size trades for long-term capital growth
///
/// # Arguments
/// * `total_capital_usd` - Total available capital in USD
/// * `expected_profit_usd` - Expected profit from the opportunity in USD
/// * `volatility` - Asset volatility (from FractalAnalyzer)
/// * `confidence` - Confidence in the opportunity (0.0 to 1.0)
/// * `max_position_fraction` - Maximum fraction of capital to risk (safety limit)
///
/// # Returns
/// * `Decimal` - Optimal position size in USD
pub fn calculate_kelly_position_size(
    total_capital_usd: Decimal,
    expected_profit_usd: Decimal,
    volatility: Decimal,
    confidence: Decimal,
    max_position_fraction: Decimal,
) -> Decimal {
    if total_capital_usd <= Decimal::ZERO || expected_profit_usd <= Decimal::ZERO {
        return Decimal::ZERO;
    }

    // Calculate the Edge: risk-adjusted expected profit margin
    let profit_margin = expected_profit_usd / total_capital_usd;
    let edge = profit_margin * confidence; // Adjust for confidence in the opportunity

    // Calculate the Variance: square of volatility
    let variance = volatility * volatility;

    // Prevent division by zero
    if variance <= Decimal::ZERO {
        debug!("KELLY CRITERION: Zero variance detected, using conservative sizing");
        return total_capital_usd * Decimal::new(1, 2); // 10% of capital
    }

    // Kelly Criterion: f* = Edge / Variance
    let kelly_fraction = edge / variance;

    // Apply safety limits
    let safe_kelly_fraction = kelly_fraction
        .max(Decimal::ZERO) // Never go negative
        .min(max_position_fraction); // Respect maximum position limit

    // Calculate position size
    let position_size = total_capital_usd * safe_kelly_fraction;

    debug!(
        "KELLY CRITERION: Edge: {:.4} | Variance: {:.4} | Kelly Fraction: {:.4} | Safe Fraction: {:.4} | Position Size: ${:.2}",
        edge, variance, kelly_fraction, safe_kelly_fraction, position_size
    );

    position_size
}

/// Calculate fractional Kelly position size for reduced volatility
/// Uses half-Kelly or quarter-Kelly to reduce portfolio volatility while maintaining growth
///
/// # Arguments
/// * `total_capital_usd` - Total available capital in USD
/// * `expected_profit_usd` - Expected profit from the opportunity in USD
/// * `volatility` - Asset volatility (from FractalAnalyzer)
/// * `confidence` - Confidence in the opportunity (0.0 to 1.0)
/// * `kelly_fraction` - Fraction of Kelly to use (0.5 for half-Kelly, 0.25 for quarter-Kelly)
/// * `max_position_fraction` - Maximum fraction of capital to risk
///
/// # Returns
/// * `Decimal` - Optimal fractional Kelly position size in USD
pub fn calculate_fractional_kelly_position_size(
    total_capital_usd: Decimal,
    expected_profit_usd: Decimal,
    volatility: Decimal,
    confidence: Decimal,
    kelly_fraction: Decimal,
    max_position_fraction: Decimal,
) -> Decimal {
    let full_kelly_size = calculate_kelly_position_size(
        total_capital_usd,
        expected_profit_usd,
        volatility,
        confidence,
        max_position_fraction,
    );

    let fractional_size = full_kelly_size * kelly_fraction;

    debug!(
        "FRACTIONAL KELLY: Full Kelly: ${:.2} | Fraction: {:.2} | Fractional Size: ${:.2}",
        full_kelly_size, kelly_fraction, fractional_size
    );

    fractional_size
}

// Helper function to convert U256 to Decimal
fn u256_to_decimal(value: U256) -> Option<Decimal> {
    // Handle potential overflow
    if value > U256::from(u128::MAX) {
        error!("U256 value too large for Decimal conversion: {}", value);
        return None;
    }

    let value_u128 = value.as_u128();
    Decimal::from_u128(value_u128)
}

// Helper function to convert Decimal to U256
fn decimal_to_u256(value: Decimal) -> Option<U256> {
    // Truncate to integer
    let value_int = value.floor();

    // Convert to string and parse as U256
    let value_str = value_int.to_string();
    match value_str.parse::<u128>() {
        Ok(value_u128) => Some(U256::from(value_u128)),
        Err(e) => {
            error!("Failed to convert Decimal to U256: {}", e);
            None
        }
    }
}

// Helper function to approximate square root using Newton's method
fn approximate_sqrt(value: Decimal) -> Option<Decimal> {
    if value.is_zero() {
        return Some(Decimal::ZERO);
    }

    if value < Decimal::ZERO {
        return None;
    }

    let mut x = value / Decimal::TWO;
    let tolerance = Decimal::new(1, 10); // 0.0000000001

    for _ in 0..50 {
        // Max 50 iterations
        let x_new = (x + value / x) / Decimal::TWO;
        if (x - x_new).abs() < tolerance {
            return Some(x_new);
        }
        x = x_new;
    }

    Some(x)
}
