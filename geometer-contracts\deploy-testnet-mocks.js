async function main() {
  console.log("🚀 Deploying to Base Sepolia with mock contracts...");
  
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  const balance = await ethers.provider.getBalance(address);
  
  console.log(`Deployer: ${address}`);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  console.log(`Network: ${hre.network.name} (${hre.network.config.chainId})`);
  
  if (balance === 0n) {
    console.log("❌ Need testnet ETH first!");
    console.log(`Fund address: ${address}`);
    return;
  }
  
  console.log("📦 Deploying mock contracts first...");
  
  // Deploy mock contracts
  const MockAavePool = await ethers.getContractFactory("MockAavePool");
  const mockPool = await MockAavePool.deploy();
  await mockPool.waitForDeployment();
  const mockPoolAddress = await mockPool.getAddress();
  console.log(`✅ MockAavePool: ${mockPoolAddress}`);
  
  const MockAaveProvider = await ethers.getContractFactory("MockAaveProvider");
  const mockProvider = await MockAaveProvider.deploy(mockPoolAddress);
  await mockProvider.waitForDeployment();
  const mockProviderAddress = await mockProvider.getAddress();
  console.log(`✅ MockAaveProvider: ${mockProviderAddress}`);
  
  const MockStargateRouter = await ethers.getContractFactory("MockStargateRouter");
  const mockRouter = await MockStargateRouter.deploy();
  await mockRouter.waitForDeployment();
  const mockRouterAddress = await mockRouter.getAddress();
  console.log(`✅ MockStargateRouter: ${mockRouterAddress}`);
  
  console.log("📦 Deploying main contract...");
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  const contract = await StargateCompassV1.deploy(mockProviderAddress, mockRouterAddress);
  await contract.waitForDeployment();
  
  const contractAddress = await contract.getAddress();
  const deployTx = contract.deploymentTransaction();
  
  console.log("\n🎉 Testnet deployment successful!");
  console.log(`📍 StargateCompassV1: ${contractAddress}`);
  console.log(`🔗 Transaction: ${deployTx.hash}`);
  console.log(`🔍 BaseScan: https://sepolia.basescan.org/address/${contractAddress}`);
  
  console.log("\n📋 Mock Contracts:");
  console.log(`MockAaveProvider: ${mockProviderAddress}`);
  console.log(`MockStargateRouter: ${mockRouterAddress}`);
  console.log(`MockAavePool: ${mockPoolAddress}`);
  
  return { contractAddress, mockProviderAddress, mockRouterAddress };
}

main().catch(console.error);
