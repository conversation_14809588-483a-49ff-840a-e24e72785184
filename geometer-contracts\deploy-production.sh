#!/bin/bash

# Production deployment script for StargateCompassV1
echo "🚀 Deploying StargateCompassV1 to Base Mainnet..."

# Check environment
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    exit 1
fi

# Source environment variables
source .env

# Validate required variables
if [ -z "$BASILISK_EXECUTION_PRIVATE_KEY" ]; then
    echo "❌ BASILISK_EXECUTION_PRIVATE_KEY not set in .env"
    exit 1
fi

if [ -z "$BASE_RPC_URL" ]; then
    echo "❌ BASE_RPC_URL not set in .env"
    exit 1
fi

echo "✅ Environment validated"
echo "📡 RPC URL: $BASE_RPC_URL"
echo "🔑 Using private key: ${BASILISK_EXECUTION_PRIVATE_KEY:0:10}..."

# Deploy to Base mainnet
echo "🔨 Deploying contracts..."
npx hardhat ignition deploy ignition/modules/StargateCompassV1Production.ts --network base

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "📋 Contract addresses saved in: ignition/deployments/chain-8453/"
    echo "🔍 Verify contracts on BaseScan if BASESCAN_API_KEY is set"
    
    if [ ! -z "$BASESCAN_API_KEY" ]; then
        echo "🔍 Verifying contracts..."
        # Verification will be handled by Hardhat Ignition automatically
    fi
else
    echo "❌ Deployment failed"
    exit 1
fi