use ethers::providers::{Provider, Http, Middleware};
use ethers::types::{U256, Address};
use tracing::{debug, info, warn, error};
use std::sync::Arc;
use tokio::time::{interval, Duration, Instant};
use crate::error::{BasiliskError, Result};
use crate::shared_types::NetworkResonanceState;
use parking_lot::RwLock;
use serde::{Serialize, Deserialize};
use std::collections::VecDeque;

/// AUDIT-FIX: Enhanced sequencer health states for multi-metric assessment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SequencerHealth {
    Healthy,      // All metrics within normal ranges
    Degraded,     // Some metrics showing issues but still functional
    Unhealthy,    // Multiple metrics showing problems
    Down,         // Sequencer appears to be offline
}

/// AUDIT-FIX: Comprehensive sequencer metrics for health assessment
#[derive(Debug, Clone)]
pub struct SequencerMetrics {
    pub response_times_ms: VecDeque<u64>,
    pub block_production_intervals: VecDeque<u64>,
    pub last_block_number: Option<u64>,
    pub last_block_time: Option<Instant>,
    pub consecutive_failures: u32,
    pub last_successful_check: Option<Instant>,
}

impl Default for SequencerMetrics {
    fn default() -> Self {
        Self {
            response_times_ms: VecDeque::with_capacity(100), // Keep last 100 measurements
            block_production_intervals: VecDeque::with_capacity(50), // Keep last 50 intervals
            last_block_number: None,
            last_block_time: None,
            consecutive_failures: 0,
            last_successful_check: None,
        }
    }
}

/// AUDIT-FIX: Enhanced sequencer monitor with multi-metric assessment
pub struct SequencerMonitor {
    provider: Arc<Provider<Http>>,
    sequencer_address: Address, // Address of the sequencer's RPC endpoint or a known contract
    interval_seconds: u64,
    network_resonance_state: Arc<RwLock<NetworkResonanceState>>,
    // AUDIT-FIX: Add metrics tracking for comprehensive health assessment
    metrics: SequencerMetrics,
    config: SequencerMonitorConfig,
}

/// AUDIT-FIX: Configurable thresholds for sequencer health monitoring
#[derive(Debug, Clone)]
pub struct SequencerMonitorConfig {
    pub max_response_time_ms: u64,        // 5000ms default
    pub max_block_interval_seconds: u64,  // 15 seconds default for L2
    pub max_consecutive_failures: u32,    // 3 failures before marking as down
    pub degraded_response_threshold_ms: u64, // 2000ms for degraded state
    pub degraded_block_interval_threshold: u64, // 8 seconds for degraded state
}

impl Default for SequencerMonitorConfig {
    fn default() -> Self {
        Self {
            max_response_time_ms: 5000,
            max_block_interval_seconds: 15,
            max_consecutive_failures: 3,
            degraded_response_threshold_ms: 2000,
            degraded_block_interval_threshold: 8,
        }
    }
}

impl SequencerMonitor {
    /// AUDIT-FIX: Enhanced constructor with configurable monitoring parameters
    pub fn new(
        provider: Arc<Provider<Http>>,
        sequencer_address: Address,
        interval_seconds: u64,
        network_resonance_state: Arc<RwLock<NetworkResonanceState>>,
    ) -> Self {
        Self::with_config(
            provider,
            sequencer_address,
            interval_seconds,
            network_resonance_state,
            SequencerMonitorConfig::default(),
        )
    }
    
    /// Create sequencer monitor with custom configuration
    pub fn with_config(
        provider: Arc<Provider<Http>>,
        sequencer_address: Address,
        interval_seconds: u64,
        network_resonance_state: Arc<RwLock<NetworkResonanceState>>,
        config: SequencerMonitorConfig,
    ) -> Self {
        Self {
            provider,
            sequencer_address,
            interval_seconds,
            network_resonance_state,
            metrics: SequencerMetrics::default(),
            config,
        }
    }

    /// AUDIT-FIX: Enhanced monitoring with multi-metric health assessment
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting enhanced sequencer monitor for {:?}", self.sequencer_address);
        let mut interval = interval(Duration::from_secs(self.interval_seconds));

        loop {
            interval.tick().await;
            
            // AUDIT-FIX: Comprehensive health check with detailed metrics
            let health_status = self.check_comprehensive_health().await;
            
            let mut state = self.network_resonance_state.write();
            match health_status {
                SequencerHealth::Healthy => {
                    info!("Sequencer {:?} is healthy - all metrics within normal ranges", self.sequencer_address);
                    state.sequencer_status = "Healthy".to_string();
                    state.is_shock_event = false;
                }
                SequencerHealth::Degraded => {
                    warn!("Sequencer {:?} is degraded - some metrics showing issues", self.sequencer_address);
                    state.sequencer_status = "Degraded".to_string();
                    state.is_shock_event = false; // Still functional
                }
                SequencerHealth::Unhealthy => {
                    warn!("Sequencer {:?} is unhealthy - multiple metrics showing problems", self.sequencer_address);
                    state.sequencer_status = "Unhealthy".to_string();
                    state.is_shock_event = true;
                }
                SequencerHealth::Down => {
                    error!("Sequencer {:?} appears to be down", self.sequencer_address);
                    state.sequencer_status = "Down".to_string();
                    state.is_shock_event = true;
                    state.network_coherence_score = 0.0; // Min coherence when down
                }
            }
            
            // Log current metrics for debugging
            self.log_current_metrics();
        }
    }

    /// AUDIT-FIX: Comprehensive health check with multi-metric assessment
    async fn check_comprehensive_health(&mut self) -> SequencerHealth {
        let start_time = Instant::now();
        
        // 1. Response time tracking
        match self.provider.get_block_number().await {
            Ok(block_number) => {
                let response_time_ms = start_time.elapsed().as_millis() as u64;
                self.record_successful_check(block_number.as_u64(), response_time_ms);
                
                // 2. Block production rate tracking
                self.update_block_production_metrics(block_number.as_u64());
                
                // 3. Analyze all metrics to determine health
                self.analyze_health_metrics()
            }
            Err(e) => {
                error!("Failed to get block number from sequencer {:?}: {}", self.sequencer_address, e);
                self.record_failed_check();
                
                // If we have too many consecutive failures, mark as down
                if self.metrics.consecutive_failures >= self.config.max_consecutive_failures {
                    SequencerHealth::Down
                } else {
                    SequencerHealth::Unhealthy
                }
            }
        }
    }
    
    /// Record successful health check with metrics
    fn record_successful_check(&mut self, block_number: u64, response_time_ms: u64) {
        self.metrics.consecutive_failures = 0;
        self.metrics.last_successful_check = Some(Instant::now());
        
        // Track response times (keep last 100)
        self.metrics.response_times_ms.push_back(response_time_ms);
        if self.metrics.response_times_ms.len() > 100 {
            self.metrics.response_times_ms.pop_front();
        }
        
        debug!(
            "Sequencer {:?} responded in {}ms, block: {}",
            self.sequencer_address, response_time_ms, block_number
        );
    }
    
    /// Record failed health check
    fn record_failed_check(&mut self) {
        self.metrics.consecutive_failures += 1;
        warn!(
            "Sequencer {:?} check failed (consecutive failures: {})",
            self.sequencer_address, self.metrics.consecutive_failures
        );
    }
    
    /// Update block production metrics
    fn update_block_production_metrics(&mut self, current_block: u64) {
        let now = Instant::now();
        
        if let (Some(last_block), Some(last_time)) = (self.metrics.last_block_number, self.metrics.last_block_time) {
            if current_block > last_block {
                let block_interval_ms = now.duration_since(last_time).as_millis() as u64;
                let blocks_produced = current_block - last_block;
                let avg_interval_per_block = block_interval_ms / blocks_produced.max(1);
                
                // Track block production intervals (keep last 50)
                self.metrics.block_production_intervals.push_back(avg_interval_per_block);
                if self.metrics.block_production_intervals.len() > 50 {
                    self.metrics.block_production_intervals.pop_front();
                }
                
                debug!(
                    "Sequencer {:?} produced {} blocks in {}ms (avg: {}ms/block)",
                    self.sequencer_address, blocks_produced, block_interval_ms, avg_interval_per_block
                );
            }
        }
        
        self.metrics.last_block_number = Some(current_block);
        self.metrics.last_block_time = Some(now);
    }
    
    /// AUDIT-FIX: Analyze all metrics to determine overall health status
    fn analyze_health_metrics(&self) -> SequencerHealth {
        let mut issues = Vec::new();
        
        // Check response times
        if let Some(avg_response_time) = self.calculate_average_response_time() {
            if avg_response_time > self.config.max_response_time_ms {
                issues.push("High response time");
            } else if avg_response_time > self.config.degraded_response_threshold_ms {
                issues.push("Elevated response time");
            }
        }
        
        // Check block production intervals
        if let Some(avg_block_interval) = self.calculate_average_block_interval() {
            let avg_block_interval_seconds = avg_block_interval / 1000;
            if avg_block_interval_seconds > self.config.max_block_interval_seconds {
                issues.push("Slow block production");
            } else if avg_block_interval_seconds > self.config.degraded_block_interval_threshold {
                issues.push("Elevated block intervals");
            }
        }
        
        // Check for recent failures
        if self.metrics.consecutive_failures > 0 {
            issues.push("Recent failures");
        }
        
        // Determine health based on issues
        match issues.len() {
            0 => SequencerHealth::Healthy,
            1 => {
                if issues.iter().any(|issue| issue.contains("Elevated")) {
                    SequencerHealth::Degraded
                } else {
                    SequencerHealth::Unhealthy
                }
            }
            2 => SequencerHealth::Unhealthy,
            _ => SequencerHealth::Down,
        }
    }
    
    /// Calculate average response time from recent measurements
    fn calculate_average_response_time(&self) -> Option<u64> {
        if self.metrics.response_times_ms.is_empty() {
            return None;
        }
        
        let sum: u64 = self.metrics.response_times_ms.iter().sum();
        Some(sum / self.metrics.response_times_ms.len() as u64)
    }
    
    /// Calculate average block production interval
    fn calculate_average_block_interval(&self) -> Option<u64> {
        if self.metrics.block_production_intervals.is_empty() {
            return None;
        }
        
        let sum: u64 = self.metrics.block_production_intervals.iter().sum();
        Some(sum / self.metrics.block_production_intervals.len() as u64)
    }
    
    /// Log current metrics for debugging and monitoring
    fn log_current_metrics(&self) {
        let avg_response = self.calculate_average_response_time()
            .map(|t| format!("{}ms", t))
            .unwrap_or_else(|| "N/A".to_string());
            
        let avg_block_interval = self.calculate_average_block_interval()
            .map(|t| format!("{}ms", t))
            .unwrap_or_else(|| "N/A".to_string());
            
        debug!(
            "Sequencer {:?} metrics - Avg response: {}, Avg block interval: {}, Consecutive failures: {}, Samples: {} response, {} intervals",
            self.sequencer_address,
            avg_response,
            avg_block_interval,
            self.metrics.consecutive_failures,
            self.metrics.response_times_ms.len(),
            self.metrics.block_production_intervals.len()
        );
    }
    
    /// Get current health status without performing a check
    pub fn get_current_health(&self) -> SequencerHealth {
        // If we haven't had a successful check recently, consider it down
        if let Some(last_check) = self.metrics.last_successful_check {
            let time_since_last_check = Instant::now().duration_since(last_check);
            if time_since_last_check > Duration::from_secs(self.interval_seconds * 3) {
                return SequencerHealth::Down;
            }
        } else {
            return SequencerHealth::Down;
        }
        
        // Otherwise analyze current metrics
        self.analyze_health_metrics()
    }
}
