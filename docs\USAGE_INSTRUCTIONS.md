# Zen Geometer Usage Instructions

## Mission Brief: Complete Operational Procedures Guide

This comprehensive tactical manual covers all operational modes, deployment procedures, and interface controls for the **Zen Geometer** autonomous trading system. Master these procedures to achieve optimal trading performance through sacred geometry and present-moment intelligence.

**Mission Objective**: Complete operational mastery of the autonomous trading system  
**Tactical Approach**: Progressive skill development through the 5-tier deployment ladder  
**Production Status**: ✅ **OPERATIONS READY** - All procedures validated and battle-tested

## Table of Contents

1. [Quick Start](#quick-start)
2. [5-Tier Deployment Ladder](#5-tier-deployment-ladder)
3. [TUI Interface Guide](#tui-interface-guide)
4. [Binary Tools Usage](#binary-tools-usage)
5. [Configuration Management](#configuration-management)
6. [Operational Procedures](#operational-procedures)
7. [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### First Time Setup

```bash
# 1. Validate system requirements
cargo run -- validate

# 2. Run configuration wizard
cargo run -- config wizard

# 3. Start in safe simulation mode
cargo run -- run --mode simulate

# 4. Launch TUI for monitoring
cargo run -- tui
```

### Daily Operations

```bash
# Check system status
cargo run -- validate

# Start trading in appropriate mode
cargo run -- run --mode [simulate|shadow|sentinel|low-capital|live]

# Monitor with TUI
cargo run -- tui
```

## 🎯 5-Tier Deployment Ladder

The **Zen Geometer** implements a progressive risk management system with five operational modes designed for safe scaling from educational simulation to full production trading:

### 1. Simulate Mode (Educational)

**Purpose**: Safe learning environment with live market data but no real transactions

```bash
# Basic simulation
cargo run -- run --mode simulate

# Detailed opportunity analysis
cargo run -- simulate --detailed

# Filter by specific scanner
cargo run -- simulate --scanner zen_geometer --min-profit 5.0
```

**Features**:

- Connects to live market data feeds
- Analyzes real opportunities in real-time
- Shows detailed lifecycle reports for each opportunity
- Never broadcasts transactions to blockchain
- Perfect for learning and strategy development

**Use Cases**:

- New user onboarding
- Strategy development and testing
- Educational demonstrations
- Market analysis without risk

### 2. Shadow Mode (Live Simulation)

**Purpose**: Fork-based validation with real network conditions

```bash
# Shadow mode with verbose logging
cargo run -- run --mode shadow --verbose
```

**Features**:

- Tests transactions on forked blockchain state
- Uses real market data and network timing
- Validates transaction success before execution
- No real money at risk

**Use Cases**:

- Pre-deployment validation
- Strategy backtesting with current conditions
- Network timing analysis
- Gas estimation validation

### 3. Sentinel Mode (Monitoring)

**Purpose**: Live contract monitoring with minimal capital exposure

```bash
# Contract monitoring mode
cargo run -- run --mode sentinel --verbose
```

**Features**:

- Monitors deployed smart contracts
- Performs contract health checks
- Tests basic contract functionality
- Minimal capital requirements

**Use Cases**:

- Contract deployment verification
- Network health monitoring
- Basic functionality testing
- Pre-production validation

### 4. Low-Capital Mode (Conservative Trading)

**Purpose**: Real money trading with hardcoded safety limits

```bash
# Conservative live trading
cargo run -- run --mode low-capital --verbose
```

**Safety Limits**:

- Maximum position size: $500
- Maximum daily loss: $200
- Kelly fraction: 5%
- Enhanced opportunity filtering

**Features**:

- Real blockchain transactions
- Conservative risk parameters
- Enhanced opportunity scanning
- Automatic position sizing

**Use Cases**:

- First real money deployment
- Conservative trading strategies
- Risk-averse operations
- Strategy validation with real capital

### 5. Live Mode (Production Trading)

**Purpose**: Full production trading with configured parameters

```bash
# Full production trading (REAL MONEY AT RISK)
cargo run -- run --mode live --verbose
```

**Features**:

- Uses full configured risk parameters
- No hardcoded safety limits
- Maximum trading capabilities
- Full strategy suite activation

**Use Cases**:

- Production trading operations
- Maximum profit potential
- Full strategy deployment
- Experienced operator use only

## 🎛️ TUI Interface Guide

### Launching the TUI

```bash
# Main TUI interface
cargo run -- tui

# Testing harness with mock data
cargo run --bin tui_harness
```

### Navigation Overview

The TUI consists of four main tabs representing different operational aspects:

1. **Dashboard** - "The Bridge": High-level situational awareness
2. **Operations** - "The Cockpit": Daily trading operations
3. **Systems** - "The Engine Room": Component health monitoring
4. **Config** - "The Control Panel": Configuration management

### Global Controls

| Key     | Action                    | Context                   |
| ------- | ------------------------- | ------------------------- |
| `Q`     | Quit TUI                  | Any tab                   |
| `1-4`   | Switch to specific tab    | Any tab                   |
| `Tab`   | Cycle through tabs/panels | Any tab                   |
| `↑/↓`   | Navigate lists/tables     | Any tab                   |
| `Enter` | Select/Confirm action     | Any tab                   |
| `Esc`   | Cancel/Go back            | Any tab                   |
| `I`     | Open Strategy Inspector   | When opportunity selected |

### Dashboard Tab - "The Bridge"

**Purpose**: Aetheric Resonance Engine visualization and system overview

**Layout**:

- **Network Seismology**: S-P latency and network coherence
- **Chronos Sieve**: Market rhythm and temporal analysis
- **Mandorla Gauge**: Opportunity scoring and quality metrics
- **Execution & PnL**: Trading performance and wallet status

**Controls**:

- `Tab`: Cycle through ARE widgets
- `Enter`: Drill down to detailed analysis

**Data Indicators**:

- 🟢 **LIVE**: Real data from ARE components (<30 seconds)
- 🟡 **CACHED**: Real data but stale (>30 seconds)
- 🔴 **MOCK**: Simulation data when real feeds unavailable

### Operations Tab - "The Cockpit"

**Purpose**: Real-time trading operations and control

**Master Control Panel**:

- Bot status display with color coding
- Emergency controls for immediate response

**Controls**:
| Key | Action | Description |
|-----|--------|-------------|
| `S` | Start/Stop | Toggle bot operation |
| `P` | Pause | Temporary halt (can resume) |
| `R` | Restart | Full restart sequence |
| `E` | Emergency Stop | Immediate halt with confirmation |
| `G` | Graceful Stop | Complete current operations then stop |

**Activity Log**:

- Real-time stream of bot activities
- Color-coded severity levels
- Filtering capabilities (`F` to filter, `C` to clear)

**Trade History**:

- Interactive table of executed trades
- Detailed trade information
- Performance metrics

### Systems Tab - "The Engine Room"

**Purpose**: Component health and performance monitoring

**Component Status Matrix**:

- All Aetheric Resonance Engine components
- Real-time health indicators
- Performance metrics

**Controls**:
| Key | Action | Description |
|-----|--------|-------------|
| `Tab` | Switch panels | Cycle through Logs → Components → Services → Nodes |
| `R` | Restart | Restart selected component/reconnect node |
| `S` | Start | Start selected service |
| `K` | Stop | Stop selected service |
| `F` | Filter | Filter log messages |
| `C` | Clear | Clear current filter |
| `/` | Search | Search through logs |
| `E` | Export | Export logs to file |
| `X` | Clear All | Clear all logs (with confirmation) |

**Network Monitoring**:

- Multi-endpoint connectivity status
- Latency measurements
- Connection health indicators

### Config Tab - "The Control Panel"

**Purpose**: Safe configuration management with hot-reload

**Configuration Sections**:

- Strategy parameters
- Execution settings
- Risk management
- Network configuration
- System settings

**Controls**:
| Key | Action | Description |
|-----|--------|-------------|
| `S` | Save & Hot-Reload | Apply changes without restart |
| `R` | Reset | Revert to defaults |
| `L` | Load Profile | Switch configuration profile |
| `V` | Validate | Check configuration validity |
| `Enter` | Edit | Start editing selected parameter |

**Safety Features**:

- Real-time validation
- Confirmation dialogs for critical operations
- Rollback capability
- Atomic updates

### Strategy Inspector

**Purpose**: Deep analysis of trading opportunities

**Accessing**:

1. Navigate to any tab with opportunities
2. Select an opportunity (if available)
3. Press `I` to open inspector
4. Review detailed analysis

**Inspector Layout**:

- Opportunity details and summary
- Score breakdown by ARE pillar
- Financial analysis and projections
- Risk factors and strengths
- Interactive controls

**Controls**:
| Key | Action | Description |
|-----|--------|-------------|
| `I` | Open | Open inspector for selected opportunity |
| `ESC` | Close | Close inspector and return |
| `E` | Execute | Execute the opportunity |
| `R` | Reject | Reject the opportunity |

## 🔧 Binary Tools Usage

The **Zen Geometer** includes **13 specialized binary tools** for various operational tasks designed to support every aspect of autonomous trading operations:

### Data Management Tools

#### `data_ingestor`

```bash
# Start real-time market data ingestion
cargo run --bin data_ingestor

# With specific configuration
cargo run --bin data_ingestor -- --config config/production.toml
```

#### `listener`

```bash
# Monitor NATS message bus
cargo run --bin listener

# With custom NATS URL
NATS_URL=nats://localhost:4222 cargo run --bin listener
```

### Analysis Tools

#### `graph_analyzer`

```bash
# Token network analysis with PageRank centrality
cargo run --bin graph_analyzer
```

#### `seismic_analyzer`

```bash
# Network seismology and P-Wave/S-Wave analysis
cargo run --bin seismic_analyzer
```

#### `feature_exporter`

```bash
# Generate SIGINT intelligence reports
cargo run --bin feature_exporter

# With market stress scenario
MARKET_STRESS=high cargo run --bin feature_exporter
```

### Optimization Tools

#### `optimizer`

```bash
# Optimize strategy parameters
cargo run --bin optimizer -- --param min_profit_threshold_usd --start 1.0 --end 10.0 --steps 10
```

#### `backtester`

```bash
# Simple strategy backtesting
cargo run --bin backtester
```

#### `mempool_backtester`

```bash
# Historical mempool analysis with Anvil
cargo run --bin mempool_backtester -- --mempool-data data.json --start-block 100 --end-block 200
```

### Monitoring Tools

#### `network_observer`

```bash
# Enhanced network monitoring with reorg detection
cargo run --bin network_observer
```

#### `mempool_observer`

```bash
# Time-to-Inclusion analysis
cargo run --bin mempool_observer
```

### Testing Tools

#### `tui_harness`

```bash
# TUI testing environment with mock data
cargo run --bin tui_harness
```

#### `demo_data_generator`

```bash
# Generate demo data for TUI showcase
cargo run --bin demo_data_generator
```

## ⚙️ Configuration Management

### Configuration Wizard

```bash
# Interactive setup for new users
cargo run -- config wizard
```

The wizard guides through:

- Profile selection (Beginner, Developer, Production)
- Network configuration (Base, Arbitrum, Degen Chain)
- Strategy parameter recommendations
- Risk management settings
- Infrastructure setup

### Profile Management

```bash
# List all profiles
cargo run -- config profile list --detailed

# Create new profile
cargo run -- config profile create my-profile --from production-conservative

# Show profile details
cargo run -- config profile show my-profile

# Validate profile
cargo run -- config profile validate my-profile --strict

# Search profiles
cargo run -- config profile search "production"
```

### Import/Export

```bash
# Export profile as JSON
cargo run -- config export my-profile --format json --path config.json

# Import configuration
cargo run -- config import --path config.json --format json --name imported-config
```

### Validation

```bash
# Basic validation
cargo run -- config validate

# Strict validation with network tests
cargo run -- config validate --strict --check-network

# JSON output for automation
cargo run -- config validate --format json
```

## 🎯 Operational Procedures

### Daily Startup Procedure

1. **System Validation**

   ```bash
   cargo run -- validate
   ```

2. **Configuration Check**

   ```bash
   cargo run -- config validate --strict
   ```

3. **Infrastructure Verification**

   ```bash
   cargo run -- utils ping-nodes
   cargo run -- utils balances
   ```

4. **Mode Selection and Launch**

   ```bash
   # Choose appropriate mode based on market conditions
   cargo run -- run --mode [simulate|shadow|sentinel|low-capital|live] --verbose
   ```

5. **TUI Monitoring**
   ```bash
   cargo run -- tui
   ```

### Emergency Procedures

#### Emergency Stop

1. In TUI Operations tab: Press `E` for emergency stop
2. Confirm the action when prompted
3. Verify bot status shows "EMERGENCY STOP"

#### System Recovery

1. Check Systems tab for component health
2. Restart failed components with `R`
3. Verify network connectivity
4. Restart bot with `R` in Operations tab

#### Configuration Rollback

1. In Config tab: Press `R` to reset to defaults
2. Or load previous working profile with `L`
3. Validate configuration with `V`
4. Apply changes with `S`

### Performance Monitoring

#### Key Metrics to Monitor

- **S-P Latency**: Network timing (lower is better)
- **Success Rate**: Trade execution efficiency
- **PnL Trend**: Profit/loss over time
- **Component Health**: All systems operational
- **Network Coherence**: Blockchain stability

#### Alert Conditions

- High S-P latency (>200ms)
- Low success rate (<90%)
- Component failures
- Network instability
- Unusual gas costs

## 🛠️ Troubleshooting

### Common Issues

#### TUI Won't Start

```bash
# Check system requirements
cargo run -- validate

# Verify NATS connection
cargo run --bin listener

# Check for port conflicts
netstat -tulpn | grep :4222
```

#### Bot Status Shows Error

1. Check Operations tab Activity Log for details
2. Review Systems tab Component Status
3. Verify network connectivity
4. Check configuration validity

#### No Trading Opportunities

1. Verify data ingestion is running
2. Check market conditions
3. Review strategy parameters
4. Confirm network connectivity

#### High Gas Costs

1. Check network congestion in Systems tab
2. Review gas strategy settings
3. Consider adjusting timing parameters
4. Monitor S-P latency for optimal timing

### Diagnostic Commands

```bash
# System health check
cargo run -- validate --format json

# Network connectivity test
cargo run -- utils ping-nodes

# Configuration validation
cargo run -- config validate --strict --check-network

# Component status check
cargo run --bin listener  # Monitor NATS messages

# Balance verification
cargo run -- utils balances
```

### Log Analysis

#### TUI Log Viewer

- Use Systems tab Enhanced Log Viewer
- Filter logs with `F` key
- Search with `/` key
- Export logs with `E` key

#### External Log Analysis

```bash
# Export logs for analysis
cargo run -- systems export-logs --format json --output system-logs.json

# Monitor specific components
cargo run --bin listener | grep "component_name"
```

### Recovery Procedures

#### Soft Recovery

1. Pause bot operations (`P` in Operations tab)
2. Check system status in Systems tab
3. Restart problematic components
4. Resume operations (`S` in Operations tab)

#### Hard Recovery

1. Emergency stop (`E` in Operations tab)
2. Exit TUI (`Q`)
3. Restart entire system
4. Validate configuration
5. Restart in appropriate mode

#### Configuration Recovery

1. Load known good profile
2. Reset to defaults if necessary
3. Re-run configuration wizard
4. Validate before deployment

---

This comprehensive tactical manual covers all aspects of operating the **Zen Geometer** system safely and effectively across all deployment tiers and operational scenarios.

_"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."_ - **The Zen Geometer**
