// use crate::math::decimal_ext::DecimalExt;
// TODO: Swap <PERSON>anner - Currently disabled for Zen Geometer strategy focus
// MISSION: The Swap Scanner - Long-tail Arbitrage Hunter
// WHY: Find profitable paths in uncontested asset pairs
// HOW: Listen to swap events and run MMBF pathfinding on arbitrage graph
//
// STATUS: Implementation exists but needs integration with new opportunity system
// PRIORITY: Medium - implement after Zen Geometer is stable

use async_nats::Client as NatsClient;
use rand::Rng;
use rust_decimal_macros::dec;
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;
use uuid::Uuid;
use ethers::providers::{Http, Provider};
use ethers::types::{Address, U256};

use crate::shared_types::{
    DecodedSwapLog, DexArbitrageData, MarketState, NatsTopics, Opportunity, OpportunityBase,
};
use rust_decimal::prelude::*;
use rust_decimal::Decimal;
use chrono;

// use crate::contracts::i_uniswap_v2_factory::IUniswapV2Factory;
// use crate::contracts::i_uniswap_v2_pair::IUniswapV2Pair;

// Represents an edge in our arbitrage graph
#[derive(Debug, Clone)]
pub struct PoolEdge {
    pub pool_address: Address,
    pub token_in: Address,
    pub token_out: Address,
    pub reserve_in: U256,
    pub reserve_out: U256,
}

// Simple graph representation for arbitrage paths
#[derive(Debug, Default)]
pub struct PoolGraph {
    pub pools: HashMap<Address, PoolEdge>,
    pub tokens: HashMap<Address, Vec<Address>>, // token_address -> list of pool_addresses it's in
}

impl PoolGraph {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_pool(&mut self, pool_address: Address, token_a: Address, token_b: Address, reserve_a: U256, reserve_b: U256) {
        // Add two directed edges for each pool
        // Token A -> Token B
        self.pools.insert(pool_address, PoolEdge {
            pool_address,
            token_in: token_a,
            token_out: token_b,
            reserve_in: reserve_a,
            reserve_out: reserve_b,
        });
        // Token B -> Token A
        self.pools.insert(pool_address, PoolEdge {
            pool_address,
            token_in: token_b,
            token_out: token_a,
            reserve_in: reserve_b,
            reserve_out: reserve_a,
        });

        self.tokens.entry(token_a).or_default().push(pool_address);
        self.tokens.entry(token_b).or_default().push(pool_address);
    }

    pub fn update_reserves(&mut self, pool_address: Address, token_a: Address, token_b: Address, reserve_a: U256, reserve_b: U256) {
        if let Some(edge) = self.pools.get_mut(&pool_address) {
            if edge.token_in == token_a {
                edge.reserve_in = reserve_a;
                edge.reserve_out = reserve_b;
            } else {
                edge.reserve_in = reserve_b;
                edge.reserve_out = reserve_a;
            }
        }
    }
}

pub async fn scan(
    nats_client: NatsClient,
    opportunity_tx: mpsc::Sender<Opportunity>,
    risk_adjustment_k: Decimal,
    centrality_scores: Arc<HashMap<String, Decimal>>, // AXIS MUNDI: Token centrality scores
    degen_provider: Arc<Provider<Http>>,
    config: Arc<crate::config::AppConfig>,
) -> Result<(), Box<dyn std::error::Error>> {
    info!("SwapScanner (Risk-Adjusted Pathfinder) starting - monitoring swap events for arbitrage paths");

    let mut pool_graph = PoolGraph::new();

    // Initialize the pool graph by querying the DegenSwap factory
    let factory_address = config.chains.get(&666666666).unwrap().dex.degen_swap_router.clone().unwrap().parse::<Address>()?;
    // let factory = IUniswapV2Factory::new(factory_address, degen_provider.clone());

    // let all_pairs_length = factory.all_pairs_length().call().await?.as_u64();
    let all_pairs_length = 0u64; // Temporarily disabled
    info!("Found {} DegenSwap pairs", all_pairs_length);

    for i in 0..all_pairs_length {
        // let pair_address = factory.all_pairs(U256::from(i)).call().await?;
        // let pair_contract = IUniswapV2Pair::new(pair_address, degen_provider.clone());
        continue; // Skip for now

        // let token0 = pair_contract.token_0().call().await?;
        // let token1 = pair_contract.token_1().call().await?;
        // let (reserve0, reserve1, _) = pair_contract.get_reserves().call().await?;

        // pool_graph.add_pool(pair_address, token0, token1, reserve0.into(), reserve1.into());
        // debug!("Added pool: {:?} (Token0: {:?}, Token1: {:?})", pair_address, token0, token1);
    }

    // Subscribe to processed swap events
    let mut swap_subscriber = nats_client
        .subscribe(NatsTopics::DATA_CHAIN_LOGS_PROCESSED_SWAPS)
        .await?;

    // ZEN GEOMETER: Subscribe to market state for volatility data
    let mut market_state_subscriber = nats_client
        .subscribe(NatsTopics::STATE_MARKET_REGIME)
        .await?;

    info!("SwapScanner subscribed to swap events and market state");

    let mut current_market_state: Option<MarketState> = None;

    loop {
        tokio::select! {
            // Handle swap events
            Some(msg) = swap_subscriber.next() => {
                debug!("SwapScanner received swap event");

                // Deserialize the swap log
                match serde_json::from_slice::<DecodedSwapLog>(&msg.payload) {
                    Ok(swap_log) => {
                        // ZEN GEOMETER: Risk-adjusted arbitrage path finding with AXIS MUNDI
                        if let Some(opportunity) = risk_adjusted_swap_arbitrage(&swap_log, &current_market_state, risk_adjustment_k, &centrality_scores, &pool_graph, config.clone(), degen_provider.clone()).await {
                            info!("SENSE: SwapScanner found risk-adjusted arbitrage opportunity {}", opportunity.base().id);

                            // Publish narrative log
                            let narrative = format!("SENSE [{}] SwapScanner: Found risk-adjusted arbitrage path #{}.",
                                chrono::Utc::now().format("%H:%M:%S"),
                                &opportunity.base().id[..8]
                            );
                            publish_narrative_log(&nats_client, &narrative).await?;

                            // Send to StrategyManager
                            if let Err(e) = opportunity_tx.send(opportunity).await {
                                error!("Failed to send opportunity to StrategyManager: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to deserialize swap log: {}", e);
                    }
                }
            }

            // ZEN GEOMETER: Handle market state updates
            Some(msg) = market_state_subscriber.next() => {
                if let Ok(market_state) = serde_json::from_slice::<MarketState>(&msg.payload) {
                    debug!("SwapScanner received market state update");
                    current_market_state = Some(market_state);
                }
            }

            else => {
                warn!("SwapScanner: All streams ended, shutting down");
                break;
            }
        }
    }

    Ok(())
}

pub async fn risk_adjusted_swap_arbitrage(
    swap_log: &DecodedSwapLog,
    market_state: &Option<MarketState>,
    risk_adjustment_k: Decimal,
    centrality_scores: &HashMap<String, Decimal>, // AXIS MUNDI: Token centrality scores
    pool_graph: &PoolGraph,
    config: Arc<crate::config::AppConfig>,
    degen_provider: Arc<Provider<Http>>,
) -> Option<Opportunity> {
    use crate::data::price_oracle::PriceOracle;

    // Instantiate PriceOracle for Degen Chain
    // Price oracle would be injected in real implementation
    // let price_oracle = PriceOracle::new(provider, chainlink_feeds);

    // ZEN GEOMETER: Risk-adjusted arbitrage path finding with MMBF algorithm

    // Get current volatility for risk adjustment
    let edge_volatility = if let Some(state) = market_state {
        // Use 1-minute volatility as edge volatility
        state.volatility_1m
    } else {
        // AUDIT-FIX: Fail safe. If market state is not available, we cannot assess risk.
        warn!("SwapScanner: No market state available, cannot assess volatility risk. Skipping cycle.");
        return None;
    };

    // Risk-Adjusted Weight Formula: w_adj = -ln(Rate) + (k * V_edge)
    // For simulation, we'll use this to determine if an opportunity passes the filter
    let base_rate = Decimal::from(1); // Simulated exchange rate
    let raw_weight = Decimal::ZERO; // ln(1) = 0, using exact decimal representation
    let risk_adjusted_weight = raw_weight + (risk_adjustment_k * edge_volatility);

    // Higher risk-adjusted weight means less attractive path
    // Only proceed if the risk-adjusted weight is below threshold
    let risk_threshold = Decimal::from(2); // Tunable parameter

    if risk_adjusted_weight > risk_threshold {
        debug!("SwapScanner: EDUCATIONAL - Path rejected due to high risk (weight: {:.3} > {:.1} threshold). Why: High volatility assets increase risk-adjusted weight using formula w_adj = -ln(Rate) + (k * V_edge). This protects against unstable trades.", risk_adjusted_weight, risk_threshold);
        return None;
    }

    // --- REAL ARBITRAGE PATHFINDING ---
    const MAX_HOPS: usize = 4; // Max path length (e.g., A->B->C->A is 3 hops)

    let start_token = swap_log.token0;
    let mut best_profit = Decimal::ZERO;
    let mut best_path: Vec<Address> = Vec::new();
    let mut best_pools: Vec<Address> = Vec::new();
    let mut best_input_amount = U256::zero();
    let mut best_bottleneck_liquidity_usd = Decimal::ZERO;

    // Queue for BFS: (current_token, current_path_tokens, current_path_pools, current_amount_in)
    let mut queue: VecDeque<(Address, Vec<Address>, Vec<Address>, U256)> = VecDeque::new();

    // Initialize queue with direct pools from start_token
    if let Some(first_pools) = pool_graph.tokens.get(&start_token) {
        for &pool_addr in first_pools {
            if let Some(edge) = pool_graph.pools.get(&pool_addr) {
                // Ensure we are going from start_token
                let (token_in, token_out, reserve_in, reserve_out) = if edge.token_in == start_token {
                    (edge.token_in, edge.token_out, edge.reserve_in, edge.reserve_out)
                } else if edge.token_out == start_token {
                    (edge.token_out, edge.token_in, edge.reserve_out, edge.reserve_in)
                } else {
                    continue;
                };

                let initial_input = U256::from(100000000000000000u64); // 0.1 ETH or equivalent
                let amount_out = get_amount_out(initial_input, reserve_in, reserve_out, U256::from(997));

                queue.push_back((
                    token_out,
                    vec![start_token, token_out],
                    vec![pool_addr],
                    amount_out,
                ));
            }
        }
    }

    while let Some((current_token, current_path_tokens, current_path_pools, current_amount_in)) = queue.pop_front() {
        if current_path_tokens.len() > MAX_HOPS {
            continue; // Path too long
        }

        // Check if we can close the loop back to start_token
        if current_path_tokens.len() >= 2 { // Need at least 2 tokens in path to form a cycle
            if let Some(return_pools) = pool_graph.tokens.get(&current_token) {
                for &return_pool_addr in return_pools {
                    if let Some(return_edge) = pool_graph.pools.get(&return_pool_addr) {
                        let (token_in_return, token_out_return, reserve_in_return, reserve_out_return) = if return_edge.token_in == current_token {
                            (return_edge.token_in, return_edge.token_out, return_edge.reserve_in, return_edge.reserve_out)
                        } else if return_edge.token_out == current_token {
                            (return_edge.token_out, return_edge.token_in, return_edge.reserve_out, return_edge.reserve_in)
                        } else {
                            continue;
                        };

                        if token_out_return == start_token {
                            // Found a cycle!
                            let amount_out_final = get_amount_out(current_amount_in, reserve_in_return, reserve_out_return, U256::from(997));

                            let profit_wei = amount_out_final.checked_sub(U256::from(100000000000000000u64)).unwrap_or_default(); // Compare to initial input

                            if profit_wei > U256::zero() {
                                let token_price_usd = rust_decimal_macros::dec!(1.0); // Placeholder - price oracle not available in this context
                                let profit_usd = Decimal::from_u128(profit_wei.as_u128()).unwrap_or_default() * token_price_usd / Decimal::new(1, 18); // 1e18

                                if profit_usd > best_profit {
                                    best_profit = profit_usd;
                                    best_input_amount = U256::from(100000000000000000u64);
                                    best_path = current_path_tokens.clone();
                                    best_path.push(start_token); // Close the cycle
                                    best_pools = current_path_pools.clone();
                                    best_pools.push(return_pool_addr);

                                    // Simplified bottleneck liquidity (needs proper calculation)
                                    best_bottleneck_liquidity_usd = Decimal::from_u128(U256::from(1000000000000000000u64).as_u128()).unwrap_or_default() * token_price_usd / Decimal::new(1, 18);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Continue exploring if path is not too long
        if current_path_tokens.len() < MAX_HOPS {
            if let Some(next_pools) = pool_graph.tokens.get(&current_token) {
                for &next_pool_addr in next_pools {
                    // Avoid using the same pool consecutively
                    if let Some(&last_pool_in_path) = current_path_pools.last() {
                        if next_pool_addr == last_pool_in_path { continue; }
                    }

                    if let Some(next_edge) = pool_graph.pools.get(&next_pool_addr) {
                        let (token_in_next, token_out_next, reserve_in_next, reserve_out_next) = if next_edge.token_in == current_token {
                            (next_edge.token_in, next_edge.token_out, next_edge.reserve_in, next_edge.reserve_out)
                        } else if next_edge.token_out == current_token {
                            (next_edge.token_out, next_edge.token_in, next_edge.reserve_out, next_edge.reserve_in)
                        } else {
                            continue;
                        };

                        let amount_out_next = get_amount_out(current_amount_in, reserve_in_next, reserve_out_next, U256::from(997));

                        let mut new_path_tokens = current_path_tokens.clone();
                        new_path_tokens.push(token_out_next);
                        let mut new_path_pools = current_path_pools.clone();
                        new_path_pools.push(next_pool_addr);

                        queue.push_back((
                            token_out_next,
                            new_path_tokens,
                            new_path_pools,
                            amount_out_next,
                        ));
                    }
                }
            }
        }
    }

    if best_profit > Decimal::ZERO {
        // AXIS MUNDI: Calculate path centrality score
        let path_centrality_score = calculate_path_centrality_score(
            &best_path,
            centrality_scores,
        );

        // AXIS MUNDI: Apply centrality bonus to profit (paths through central tokens are more valuable)
        let centrality_bonus = Decimal::from(1)
            + (path_centrality_score * Decimal::from_str("0.5").unwrap_or_default()); // Up to 50% bonus for high-centrality paths
        let adjusted_profit = best_profit * centrality_bonus;

        debug!("SwapScanner: REAL - Risk-adjusted arbitrage opportunity APPROVED! Volatility: {:.3} | Risk weight: {:.3} (acceptable) | Base Profit: ${:.2} | Centrality Score: {:.3} | Adjusted Profit: ${:.2} | Intersection: ${:.2} | Why: AXIS MUNDI prioritizes paths through central tokens (WETH/USDC) for stability", 
               edge_volatility, risk_adjusted_weight, best_profit, path_centrality_score, adjusted_profit, best_bottleneck_liquidity_usd);

        Some(Opportunity::DexArbitrage {
            base: OpportunityBase {
                id: Uuid::new_v4().to_string(),
                source_scanner: "SwapScanner".to_string(),
                estimated_gross_profit_usd: adjusted_profit,
                associated_volatility: edge_volatility,
                requires_flash_liquidity: true, // Assume flash liquidity for now
                chain_id: swap_log.chain_id,
                timestamp: swap_log.timestamp,
                intersection_value_usd: best_bottleneck_liquidity_usd,
                aetheric_resonance_score: None,
            },
            data: DexArbitrageData {
                path: best_path,
                pools: best_pools,
                input_amount: best_input_amount,
                estimated_output_amount: adjusted_profit, // Use adjusted profit as estimated output
                bottleneck_liquidity_usd: best_bottleneck_liquidity_usd,
            },
        })
    } else {
        None
    }
}

// Helper function to calculate amount out for Uniswap V2 like pools
// x_1 * y_1 = x_2 * y_2
// (reserve_in + amount_in) * (reserve_out - amount_out) = reserve_in * reserve_out
// amount_out = (amount_in * reserve_out) / (reserve_in + amount_in)
// With fee: amount_out = (amount_in * 0.997 * reserve_out) / (reserve_in + amount_in * 0.997)
fn get_amount_out(amount_in: U256, reserve_in: U256, reserve_out: U256, fee_numerator: U256) -> U256 {
    if amount_in.is_zero() || reserve_in.is_zero() || reserve_out.is_zero() {
        return U256::zero();
    }

    let amount_in_with_fee = amount_in * fee_numerator;
    let numerator = amount_in_with_fee * reserve_out;
    let denominator = reserve_in * U256::from(1000) + amount_in_with_fee;

    numerator / denominator
}

async fn publish_narrative_log(
    nats_client: &NatsClient,
    message: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let payload = serde_json::to_vec(message)?;
    nats_client
        .publish(NatsTopics::LOG_EVENTS_NARRATIVE, payload.into())
        .await?;
    Ok(())
}

// AETHERIC RESONANCE ENGINE - AXIS MUNDI: Calculate path centrality score
fn calculate_path_centrality_score(
    path: &[ethers::types::Address],
    centrality_scores: &HashMap<String, Decimal>,
) -> Decimal {
    if path.is_empty() {
        return Decimal::ZERO;
    }

    // Convert addresses to token symbols for lookup
    // In production, this would use a proper address-to-symbol mapping
    let mut total_centrality = Decimal::ZERO;
    let mut valid_tokens = 0;

    for address in path {
        // Simulate token identification based on address patterns
        let token_symbol = identify_token_symbol(*address);

        // Get centrality score with automatic fallback to UNKNOWN token score
        let centrality = centrality_scores.get(&token_symbol)
            .copied()
            .or_else(|| centrality_scores.get("UNKNOWN").copied())
            .unwrap_or(dec!(0.05)); // Final fallback if UNKNOWN is not in the map
        
        total_centrality += centrality;
        valid_tokens += 1;
    }

    if valid_tokens > 0 {
        total_centrality / Decimal::from(valid_tokens)
    } else {
        Decimal::ZERO
    }
}

// Helper function to identify token symbols from addresses
// In production, this would use a proper token registry
fn identify_token_symbol(address: ethers::types::Address) -> String {
    // Simulate token identification based on address patterns
    // This is a simplified approach for demonstration
    let addr_str = format!("{:?}", address);

    // Check for common patterns or use a hash-based approach
    match addr_str.chars().nth(2).unwrap_or('0') {
        '0' | '1' => "WETH".to_string(),
        '2' | '3' => "USDC".to_string(),
        '4' | '5' => "USDT".to_string(),
        '6' | '7' => "DAI".to_string(),
        '8' | '9' => "WBTC".to_string(),
        'a' | 'b' => "UNI".to_string(),
        'c' | 'd' => "LINK".to_string(),
        'e' | 'f' => "AAVE".to_string(),
        _ => "UNKNOWN".to_string(),
    }
}
