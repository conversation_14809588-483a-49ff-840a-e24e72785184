async function main() {
  console.log("🚀 Deploying to Base Sepolia with mock dependencies...");
  
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  const balance = await ethers.provider.getBalance(address);
  
  console.log(`Deployer: ${address}`);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  console.log(`Network: ${hre.network.name} (${hre.network.config.chainId})`);
  
  // Step 1: Deploy mock dependencies
  console.log("\n📦 Step 1: Deploying mock dependencies...");
  
  const MockAavePool = await ethers.getContractFactory("MockAavePool");
  const mockPool = await MockAavePool.deploy();
  await mockPool.waitForDeployment();
  const mockPoolAddress = await mockPool.getAddress();
  console.log(`✅ MockAavePool deployed: ${mockPoolAddress}`);
  
  const MockAaveProvider = await ethers.getContractFactory("MockAaveProvider");
  const mockProvider = await MockAaveProvider.deploy(mockPoolAddress);
  await mockProvider.waitForDeployment();
  const mockProviderAddress = await mockProvider.getAddress();
  console.log(`✅ MockAaveProvider deployed: ${mockProviderAddress}`);
  
  const MockStargateRouter = await ethers.getContractFactory("MockStargateRouter");
  const mockRouter = await MockStargateRouter.deploy();
  await mockRouter.waitForDeployment();
  const mockRouterAddress = await mockRouter.getAddress();
  console.log(`✅ MockStargateRouter deployed: ${mockRouterAddress}`);
  
  // Step 2: Deploy main contract with mock addresses
  console.log("\n📦 Step 2: Deploying StargateCompassV1...");
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  const contract = await StargateCompassV1.deploy(mockProviderAddress, mockRouterAddress);
  await contract.waitForDeployment();
  
  const contractAddress = await contract.getAddress();
  const deployTx = contract.deploymentTransaction();
  
  console.log("\n🎉 TESTNET DEPLOYMENT SUCCESSFUL!");
  console.log(`📍 StargateCompassV1: ${contractAddress}`);
  console.log(`🔗 Transaction Hash: ${deployTx.hash}`);
  console.log(`⛽ Gas Used: ${deployTx.gasLimit}`);
  console.log(`🔍 BaseScan: https://sepolia.basescan.org/address/${contractAddress}`);
  
  console.log("\n📋 Mock Dependencies:");
  console.log(`MockAaveProvider: ${mockProviderAddress}`);
  console.log(`MockStargateRouter: ${mockRouterAddress}`);
  console.log(`MockAavePool: ${mockPoolAddress}`);
  
  console.log("\n🔧 UPDATE YOUR TESTNET CONFIG:");
  console.log(`stargate_compass_v1 = "${contractAddress}"`);
  
  return {
    stargateCompass: contractAddress,
    mockProvider: mockProviderAddress,
    mockRouter: mockRouterAddress,
    mockPool: mockPoolAddress
  };
}

main()
  .then((addresses) => {
    console.log("\n✅ All contracts deployed successfully!");
    console.log("🧪 Ready for testnet integration testing!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ Deployment failed:", error.message);
    process.exit(1);
  });
