# Execution Module

This module is responsible for the final stage of the trading process: executing trades. It receives vetted opportunities from the `StrategyManager` and uses a variety of techniques to execute them in a profitable and safe manner.

## Table of Contents

- [Core Components](#core-components)
- [Execution Flow](#execution-flow)

## Core Components

-   **`manager.rs`**: The `ExecutionManager` is the central component of this module. It receives opportunities, calculates the optimal gas bid, waits for the optimal broadcast window, and executes the transaction.
-   **`dispatcher.rs`**: The `Dispatcher` creates and sends transactions to the blockchain.
-   **`broadcaster.rs`**: The `Broadcaster` broadcasts transactions to the network, with support for both public and MEV-aware broadcasting.
-   **`gas_estimator.rs`**: The `GasEstimator` estimates the gas cost of a transaction, which is used to calculate the optimal gas bid.
-   **`nonce_manager.rs`**: The `NonceManager` manages the nonce of the trading account to ensure that transactions are processed in the correct order.
-   **`slippage_calculator.rs`**: The `SlippageCalculator` calculates the expected slippage for a trade to prevent execution at an unfavorable price.
-   **`simulator.rs`**: The `Simulator` simulates trades before execution to ensure they are likely to be profitable.

## Execution Flow

1.  The `ExecutionManager` receives a vetted opportunity from the `StrategyManager`.
2.  It calculates the optimal gas bid using the "Golden Ratio Gaze" bidding strategy.
3.  It waits for the optimal broadcast window using the `HarmonicTimingOracle`.
4.  It executes the transaction using the `Dispatcher`.