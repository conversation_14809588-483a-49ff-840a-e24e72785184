#!/bin/bash

# Alternative compilation script for when Node.js has compatibility issues
echo "🔧 Compiling Geometer Contracts..."

# Check if we have the right Node.js version
if ! node --version 2>/dev/null; then
    echo "❌ Node.js compatibility issue detected"
    echo "💡 Suggested solutions:"
    echo "   1. Install Node.js v18 or v20: curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash - && sudo apt-get install -y nodejs"
    echo "   2. Use nvm: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash && nvm install 20"
    echo "   3. Use Docker: docker run -v \$(pwd):/workspace -w /workspace node:20 npm install && npx hardhat compile"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Compile contracts
echo "🔨 Compiling contracts..."
npx hardhat compile

if [ $? -eq 0 ]; then
    echo "✅ Contracts compiled successfully!"
    echo "📁 Artifacts available in: artifacts/"
    echo "📁 TypeChain types available in: typechain-types/"
else
    echo "❌ Compilation failed"
    exit 1
fi