#!/bin/bash

# Zen Geometer Mainnet Production Deployment Script
# CRITICAL: This script deploys to LIVE MAINNET with REAL FUNDS
# 
# Purpose: Safe production deployment with multiple safety checks
# Usage: ./scripts/mainnet_deploy.sh [--force] [--skip-preflight]
# Environment Variables:
#   - DEPLOYMENT_ENV: Target environment (production, staging)
#   - SKIP_CONFIRMATION: Skip interactive confirmations (CI/CD only)
# 
# Safety Features:
# 1. Mandatory pre-flight check
# 2. Multiple confirmation prompts
# 3. Environment validation
# 4. Deployment verification

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BOLD}${CYAN}$1${NC}"
}

print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}SUCCESS${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}WARNING${NC} $1"
}

print_error() {
    echo -e "${RED}ERROR${NC} $1"
}

print_critical() {
    echo -e "${BOLD}${RED}CRITICAL${NC} $1"
}

echo "CRITICAL: Zen Geometer Mainnet Deployment Script"
echo "This script will deploy the bot to LIVE MAINNET with REAL FUNDS"
echo ""

# ============================================================================
# MANDATORY PRE-FLIGHT CHECK
# ============================================================================
print_status "Running mandatory pre-flight systems check..."

if [ -f "scripts/preflight.sh" ] && [ -x "scripts/preflight.sh" ]; then
    echo ""
    echo "--- Pre-Flight Check Output ---"
    if ./scripts/preflight.sh; then
        print_success "Pre-flight check passed"
    else
        print_error "Pre-flight check failed. Deployment aborted for safety."
        echo ""
        echo "DEPLOYMENT ABORTED"
        echo "Please fix all pre-flight issues before attempting deployment."
        exit 1
    fi
    echo "--- End Pre-Flight Check ---"
    echo ""
else
    print_error "Pre-flight script not found or not executable"
    print_error "Cannot proceed with deployment without pre-flight validation"
    exit 1
fi

# ============================================================================
# CRITICAL SAFETY WARNINGS
# ============================================================================
print_critical "CRITICAL MAINNET DEPLOYMENT WARNING"
echo ""
echo "You are about to deploy the Zen Geometer to LIVE MAINNET."
echo ""
echo "This means:"
echo "  • The bot will trade with REAL MONEY"
echo "  • Losses can and will occur"
echo "  • Smart contract risks are present"
echo "  • MEV attacks are possible"
echo "  • Network congestion can cause failed transactions"
echo ""
echo "Before proceeding, ensure:"
echo "  ✓ You understand the risks involved"
echo "  ✓ You have tested thoroughly in simulation mode"
echo "  ✓ Your configuration is correct"
echo "  ✓ Your private key is secure"
echo "  ✓ You have appropriate risk management settings"
echo ""

# First confirmation
read -p "Do you understand these risks and wish to proceed? (type 'YES' to continue): " RISK_CONFIRMATION
if [ "$RISK_CONFIRMATION" != "YES" ]; then
    print_status "Deployment cancelled by user"
    exit 0
fi

# Second confirmation - must type the exact environment name
echo ""
print_warning "Final confirmation required"
echo "To proceed with LIVE MAINNET deployment, type exactly: mainnet"
read -p "Target environment: " ENV_CONFIRMATION
if [ "$ENV_CONFIRMATION" != "mainnet" ]; then
    print_status "Deployment cancelled - incorrect environment confirmation"
    exit 0
fi

echo ""
print_status "Proceeding with mainnet deployment..."

# ============================================================================
# Security and Environment Checks
# ============================================================================
print_status "Performing security and environment checks..."

# Check for required environment variables
if [ -z "$BASILISK_EXECUTION_PRIVATE_KEY" ]; then
    print_error "BASILISK_EXECUTION_PRIVATE_KEY not set"
    exit 1
fi

# Run mandatory preflight check
if [ "$1" != "--skip-preflight" ]; then
    print_step "Running preflight systems check" "PREFLIGHT"
    if ! ./scripts/preflight.sh; then
        print_error "Preflight check failed. Deployment aborted."
        exit 1
    fi
    print_success "Preflight check passed"
fi

# Validate configuration for mainnet
print_status "Validating mainnet configuration..."
if cargo run --bin basilisk_bot -- validate; then
    print_success "Configuration validation passed"
else
    print_error "Configuration validation failed"
    exit 1
fi

# ============================================================================
# Build Production Image
# ============================================================================
print_status "Building production Docker image..."

if docker build -t zen-geometer:latest -f Dockerfile .; then
    print_success "Production image built successfully"
else
    print_error "Failed to build production image"
    exit 1
fi

# ============================================================================
# Deploy to Production
# ============================================================================
print_status "Deploying to production environment..."

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down || true

# Start production services
print_status "Starting production services..."
if docker-compose -f docker-compose.prod.yml up -d; then
    print_success "Production services started"
else
    print_error "Failed to start production services"
    exit 1
fi

# ============================================================================
# Post-Deployment Verification
# ============================================================================
print_status "Performing post-deployment verification..."

# Wait for services to be ready
sleep 10

# Check if the bot is running
if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    print_success "Bot is running in production"
else
    print_error "Bot failed to start properly"
    exit 1
fi

# ============================================================================
# Deployment Complete
# ============================================================================
print_header "Mainnet Deployment Complete"
echo ""
print_success "Zen Geometer has been deployed to LIVE MAINNET"
echo ""
echo "IMPORTANT REMINDERS:"
echo "  • Monitor the bot closely for the first few hours"
echo "  • Check logs regularly: docker-compose -f docker-compose.prod.yml logs -f"
echo "  • Have emergency stop procedures ready"
echo "  • Monitor your wallet balance and positions"
echo ""
echo "Emergency Commands:"
echo "  • Stop bot: docker-compose -f docker-compose.prod.yml down"
echo "  • View logs: docker-compose -f docker-compose.prod.yml logs"
echo "  • Check status: docker-compose -f docker-compose.prod.yml ps"
echo ""
print_warning "Remember: You are now trading with REAL MONEY on LIVE MAINNET"
echo ""
print_success "Deployment successful. Good luck!"