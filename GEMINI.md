### **1. Project Genesis & Core Philosophy**

**Objective:** Define the project's purpose and guiding vision.
**Content:**
-   **Project Name:** Basilisk Bot
-   **Core Mission:** To develop an intelligent, high-frequency trading bot capable of identifying and executing profitable opportunities in decentralized finance (DeFi) markets by moving beyond simple correlation to find predictive, causal links and understanding the quality and context of opportunities and the state of the network medium.
-   **Guiding Principle(s):**
    *   **Resonance over Correlation:** Prioritize opportunities based on their "resonance" within market cycles, liquidity structures, and network conditions, rather than just simple price or volume correlation.
    *   **Layered Analysis as Filter/Multiplier:** Each analytical layer (Chronos Sieve, Mandorla Gauge, Network Seismology) acts to progressively refine the bot's perception and action, filtering out low-quality signals and amplifying high-quality ones.
    *   **Contextual Execution:** Time transaction broadcasts to coincide with optimal network conditions (e.g., low propagation latency) to maximize execution success and minimize risk.

---

### **2. Directives for AI Collaboration**

**Objective:** Establish the rules of engagement and quality standards for AI-driven development.
**Content:**
-   **Adherence to Core Principles:** All code contributions, documentation, and architectural suggestions must align with the Guiding Principles outlined in Section 1. A technically correct but philosophically misaligned solution is considered invalid.
-   **Code Embodies Concept:** Abstract goals and principles must be reflected in the concrete implementation. Naming conventions, module structures, and data flows should intuitively map back to the project's core concepts (e.g., harmonic analysis, geometric shapes, network waves).
-   **Architectural Sanctity:** The established high-level architecture (modular Rust services, data ingestion, strategy evaluation, execution) is a foundational constraint. Any proposed changes to this core structure must be explicitly discussed and justified.
-   **Documentation Standard:** All new public functions, modules, and complex logic must be accompanied by clear, concise documentation explaining both the "what" (its function) and the "why" (its purpose within the broader system), particularly how it contributes to the core analytical pillars or operational flows.

---

### **3. Core Architecture & Components**

**Objective:** Detail the technical blueprint of the system.
**Content:** Describe the primary components or services of the application. For each component, provide its **Concept** and **Implementation**.

-   **Component #1: Data Ingestion & Handling**
    -   **Concept:** Responsible for connecting to external data sources (e.g., NATS streams, RPC nodes, CEX feeds), consuming raw market data (trades, gas prices, block propagation), validating it, and making it available for analysis.
    -   **Implementation:** Standalone services like `data_ingestor` and `listener` (see `[[bin]]` section in `Cargo.toml`). Core logic is in modules under `src/data/`, including `src/data/block_ingestor.rs` and `src/data/chain_monitor.rs`. Uses networking libraries like `tokio` and `tungstenite`.

-   **Component #2: Chronos Sieve (Temporal Analysis)**
    -   **Concept:** Analyzes time series data (trades, gas prices) using spectral analysis (FFT) to identify dominant market cycles and assess market rhythm stability. Publishes `TemporalHarmonics` state.
    -   **Implementation:** Primarily implemented in `src/data/fractal_analyzer.rs`, utilizing libraries like `rustfft` for FFT calculations. It is spawned as a background task from `main.rs`.

-   **Component #3: Mandorla Gauge (Geometric & Liquidity Analysis)**
    -   **Concept:** Represents potential arbitrage paths geometrically, analyzes their shape (e.g., convexity ratio), and incorporates asset centrality scores (from Axis Mundi) to assess the robustness and stability of opportunities. Calculates a `GeometricScore`. This is a key analytical pillar for the Zen Geometer strategy.
    -   **Implementation:** Leverages modules like `src/math/geometry.rs` and `src/math/vesica.rs` for geometric calculations. Interacts with strategy components like `src/strategies/scanners/swap.rs` (for risk-adjusted arbitrage pathfinding) and `src/strategies/manager.rs`. Uses libraries like `geo` and `petgraph`.

-   **Component #4: Network Seismology (Network State Analysis)**
    -   **Concept:** Observes block propagation times across multiple nodes, applying seismology concepts (P-wave, S-wave, S-P time) to measure network latency and coherence. Identifies network stress or shock events. Publishes `NetworkResonanceState`.
    -   **Implementation:** Implemented as standalone services `network_observer` and `seismic_analyzer` (see `[[bin]]` section in `Cargo.toml`).

-   **Component #5: Strategy & Regime Management**
    -   **Concept:** The `StrategyManager` (`src/strategies/manager.rs`) receives potential opportunities from scanners. The `RegimeManager` (`src/strategies/regime_manager.rs`), spawned as a background task, likely uses this data to determine the overall market state. The Strategy Manager then queries the analytical pillars (Chronos Sieve, Mandorla Gauge, Network Seismology) for their respective states, synthesizes this information into a final `AethericResonanceScore`, and decides whether to proceed to execution. This is the central decision-making component for the Zen Geometer strategy.
    -   **Implementation:** Located in `src/strategies/manager.rs` and `src/strategies/regime_manager.rs`. Coordinates interactions between scanners (`src/strategies/scanners/`) and the analytical components.

-   **Component #6: CLI Command Handlers**
    -   **Concept:** Encapsulates the logic for handling various command-line interface commands, including configuration management, system validation, and utility functions like balance checks and node pings. This promotes a leaner `main.rs`.
    -   **Implementation:** Located in `src/cli_handlers.rs` and `src/cli.rs`. Contains functions like `handle_config_command` and `handle_utils_command` which process subcommands like `show`, `validate-config`, `set-gaze`, `balances`, `ping-nodes`, and `test-gaze`.

-   **Component #7: Execution Manager**
    -   **Concept:** Handles the final stages of processing a high-scoring opportunity, including timing the transaction broadcast based on real-time network conditions and interacting with the blockchain.
    -   **Implementation:** Found in `src/execution/manager.rs` and `src/execution/dispatcher.rs`. It is spawned as a background task from `main.rs`.

-   **Component #8: Mathematical Utilities**
    -   **Concept:** Provides core mathematical functions and structures used across various components, particularly for geometric analysis and other quantitative tasks.
    -   **Implementation:** Modules under `src/math/`, including `src/math/geometry.rs` and `src/math/vesica.rs`.

-   **Component #9: Configuration & Shared Types**
    -   **Concept:** Manages application configuration and defines common data structures used throughout the project to ensure consistency.
    -   **Implementation:** `src/config.rs` and `src/shared_types.rs`. Configuration files are located in the `config/` directory.
    
-   **Component #10: Auxiliary Binaries**
    -   **Concept:** A collection of command-line tools for various tasks including backtesting, data generation, and analysis.
    -   **Implementation:** Located in `src/bin/` and `bin/`. Includes tools like `backtester.rs`, `demo_data_generator.rs`, `graph_analyzer.rs`, `feature_exporter.rs`, and `optimizer.rs`.

---

### **4. Key Operational Flow / Decision Logic (Zen Geometer Strategy)**

**Objective:** Explain the most critical data flow for the Zen Geometer strategy, illustrating how the components interact.
**Content:** Describe a primary sequence of events from input to output.

-   **Step 1: Opportunity Detection (Scanners)**
    -   **Description:** Scanners like `SwapScanner` (`src/strategies/scanners/swap.rs`) identify potential trading opportunities (e.g., a multi-hop arbitrage path) based on raw market data.

-   **Step 2: Strategy Evaluation & Scoring (Strategy Manager)**
    -   **Description:** The `StrategyManager` (`src/strategies/manager.rs`) receives the detected opportunity. It queries the `FractalAnalyzer` (Chronos Sieve) for `TemporalHarmonics`, the geometric/math modules (Mandorla Gauge) for the `GeometricScore`, and the network analysis component (Network Seismology) for the `NetworkResonanceState`. It synthesizes these inputs to calculate the `AethericResonanceScore`. This is where the Zen Geometer's core decision-making occurs.

-   **Step 3: Decision and Execution Preparation**
    -   **Description:** If the `AethericResonanceScore` exceeds a predefined `min_resonance_threshold`, the `StrategyManager` passes the opportunity to the `ExecutionManager` (`src/execution/manager.rs`).

-   **Step 4: Timed Transaction Broadcast**
    -   **Description:** The `ExecutionManager`, potentially consulting a `HarmonicTimingOracle` (derived from the `NetworkResonanceState`), waits for optimal network conditions before broadcasting the transaction to the network.

-   **Step 5: CLI Command Handling (User Interaction)**
    -   **Description:** Users can interact with the bot via the main binary. The `run` command starts the bot, `validate` checks the configuration, `config` manages settings (show, validate, set parameters), and `utils` provides tools to check balances, ping nodes, and test strategies.

---

### **5. Project Glossary**

**Objective:** Create an unambiguous dictionary of the project's unique terminology to ensure the AI understands the domain language.
**Content:** Define key terms that are specific to this project or have a special meaning in this context.

-   **Strategy Manager:** The central decision-making component of the Basilisk Bot. It synthesizes inputs from analytical pillars to score opportunities and decide on execution.
-   **Regime Manager:** A component that runs in the background to determine the current market state or "regime."
-   **Zen Geometer:** The primary trading strategy implemented in the Basilisk Bot. It leverages fractal analysis (Chronos Sieve), geometric and liquidity analysis (Mandorla Gauge), and network state analysis (Network Seismology) to identify and execute profitable opportunities.
-   **CLI Command Handlers:** A module responsible for processing command-line interface commands.
-   **Chronos Sieve:** The analytical pillar focused on identifying market cycles and temporal patterns from time series data using spectral analysis.
-   **Mandorla Gauge:** The analytical pillar focused on assessing the structural robustness and asset stability of trading opportunities using geometric analysis and asset centrality.
-   **Network Seismology:** The analytical pillar focused on measuring and interpreting network propagation latency and coherence to understand the state of the blockchain network medium.
-   **Temporal Harmonics:** The output state from the Chronos Sieve, indicating dominant market cycles and rhythm stability.
-   **Geometric Score:** The output score from the Mandorla Gauge, reflecting the convexity ratio of an opportunity's geometric representation and the centrality of its assets.
-   **Network Resonance State:** The output state from the Network Seismology, indicating network propagation latency (`sp_time_ms`) and coherence.
-   **Aetheric Resonance Score:** The final, synthesized score calculated by the Strategy Manager, combining inputs from all three analytical pillars to determine the overall quality and context of an opportunity.
-   **Fractal Analyzer:** The specific service/module responsible for implementing the Chronos Sieve.
-   **Swap Scanner:** A type of scanner that identifies potential arbitrage paths across decentralized exchanges.
-   **Axis Mundi:** A conceptual or actual component responsible for calculating and providing asset centrality scores.
-   **P-Wave (Primary Wave):** In Network Seismology, the timestamp of the first node reporting a new block.
-   **S-Wave (Secondary Wave):** In Network Seismology, the timestamp when a significant percentage (e.g., 80%) of nodes have reported a new block.
-   **S-P Time:** The time delta between the S-Wave and P-Wave, used as a measure of network propagation latency.