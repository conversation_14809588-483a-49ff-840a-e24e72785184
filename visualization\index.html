<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basilisk Bot - Strategy Showcase</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-color: #1a1a1d;
            --primary-color: #282c34;
            --secondary-color: #444b58;
            --text-color: #c5c8c6;
            --header-color: #ffffff;
            --accent-color: #61afef;
            --success-color: #98c379;
            --warning-color: #e5c07b;
            --error-color: #e06c75;
            --border-color: #3a3f4b;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: auto;
        }
        h1, h2 {
            color: var(--header-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
        }
        .summary {
            background-color: var(--primary-color);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .summary-item {
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: 5px;
        }
        .summary-item h3 {
            margin-top: 0;
            color: var(--accent-color);
        }
        #opportunities {
            margin-top: 20px;
        }
        .opportunity {
            background-color: var(--primary-color);
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .opportunity:hover {
            background-color: var(--secondary-color);
        }
        .opportunity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .opportunity-header h3 {
            margin: 0;
            color: var(--header-color);
        }
        .opportunity-details {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .opportunity-details.visible {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .score-breakdown {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .score {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px;
            background-color: var(--secondary-color);
            border-radius: 4px;
        }
        .score-label {
            font-weight: bold;
        }
        .score-value {
            font-family: 'Courier New', Courier, monospace;
            font-weight: bold;
        }
        .decision-go { color: var(--success-color); }
        .decision-no-go { color: var(--error-color); }
    </style>
</head>
<body>
    <div class="container">
        <h1>Zen Geometer - Strategy Showcase</h1>
        <div class="summary">
            <div class="summary-item">
                <h3>Export Timestamp</h3>
                <p id="export-timestamp">Loading...</p>
            </div>
            <div class="summary-item">
                <h3>Total Opportunities</h3>
                <p id="total-opportunities">Loading...</p>
            </div>
            <div class="summary-item">
                <h3>Profitable Opportunities</h3>
                <p id="profitable-opportunities">Loading...</p>
            </div>
        </div>

        <h2>Evaluated Opportunities</h2>
        <div id="opportunities-container"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            fetch('sigint_features.json')
                .then(response => response.json())
                .then(data => {
                    renderSummary(data);
                    renderOpportunities(data.opportunities);
                })
                .catch(error => {
                    console.error('Error loading strategy data:', error);
                    document.getElementById('opportunities-container').innerText = 'Error loading data. Make sure sigint_features.json is in the same directory.';
                });
        });

        function renderSummary(data) {
            document.getElementById('export-timestamp').textContent = new Date(data.timestamp).toLocaleString();
            document.getElementById('total-opportunities').textContent = data.opportunities.length;
            const profitableCount = data.opportunities.filter(op => op.final_decision === 'Go').length;
            document.getElementById('profitable-opportunities').textContent = `${profitableCount}`;
        }

        function renderOpportunities(opportunities) {
            const container = document.getElementById('opportunities-container');
            container.innerHTML = ''; // Clear loading text

            opportunities.forEach((op, index) => {
                const opElement = document.createElement('div');
                opElement.className = 'opportunity';
                opElement.innerHTML = `
                    <div class="opportunity-header">
                        <h3>Opportunity #${index + 1}: ${op.opportunity_type}</h3>
                        <span class="score-value decision-${op.final_decision === 'Go' ? 'go' : 'no-go'}">${op.final_decision.toUpperCase()}</span>
                    </div>
                    <div class="opportunity-details" id="details-${index}">
                        <div class="score-breakdown">
                            <h4>Scores</h4>
                            <div class="score"><span class="score-label">Aetheric Resonance</span><span class="score-value">${op.aetheric_resonance_score.toFixed(4)}</span></div>
                            <div class="score"><span class="score-label">Geometric Score</span><span class="score-value">${op.scores.geometric_score.toFixed(4)}</span></div>
                            <div class="score"><span class="score-label">Temporal Harmonics</span><span class="score-value">${op.scores.temporal_harmonics.toFixed(4)}</span></div>
                            <div class="score"><span class="score-label">Network Resonance</span><span class="score-value">${op.scores.network_resonance.toFixed(4)}</span></div>
                            <div class="score"><span class="score-label">Asset Centrality</span><span class="score-value">${op.scores.asset_centrality.toFixed(4)}</span></div>
                        </div>
                        <div class="chart-container">
                            <canvas id="chart-${index}"></canvas>
                        </div>
                    </div>
                `;
                container.appendChild(opElement);

                opElement.addEventListener('click', () => {
                    const details = document.getElementById(`details-${index}`);
                    const wasVisible = details.classList.contains('visible');
                    document.querySelectorAll('.opportunity-details').forEach(d => d.classList.remove('visible'));
                    if (!wasVisible) {
                        details.classList.add('visible');
                        renderChart(op, index);
                    }
                });
            });
        }

        function renderChart(opportunity, index) {
            const ctx = document.getElementById(`chart-${index}`).getContext('2d');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Geometric', 'Temporal', 'Network', 'Centrality'],
                    datasets: [{
                        label: 'Opportunity Fingerprint',
                        data: [
                            opportunity.scores.geometric_score,
                            opportunity.scores.temporal_harmonics,
                            opportunity.scores.network_resonance,
                            opportunity.scores.asset_centrality
                        ],
                        fill: true,
                        backgroundColor: 'rgba(97, 175, 239, 0.2)',
                        borderColor: 'rgb(97, 175, 239)',
                        pointBackgroundColor: 'rgb(97, 175, 239)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(97, 175, 239)'
                    }]
                },
                options: {
                    elements: {
                        line: {
                            borderWidth: 3
                        }
                    },
                    scales: {
                        r: {
                            angleLines: { display: false },
                            suggestedMin: 0,
                            suggestedMax: 1,
                            grid: { color: 'rgba(197, 200, 198, 0.2)' },
                            pointLabels: { color: '#c5c8c6', font: { size: 14 } },
                            ticks: {
                                backdropColor: 'rgba(26, 26, 29, 0.8)',
                                color: '#c5c8c6'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
