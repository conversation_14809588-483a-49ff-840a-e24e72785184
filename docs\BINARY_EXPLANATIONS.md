# Basilisk Bot: Binary Explanations

This document provides a clear, high-level explanation of each executable binary in the Basilisk Bot project. The binaries are grouped by their primary function within the system's architecture.

---

## 1. Core Services

These are the main, long-running services that form the backbone of the bot's operations.

### `basilisk_bot` (The Main Application)
-   **Purpose:** This is the central nervous system of the bot. It's the main executable you run to start the entire trading operation.
-   **Functionality:** It initializes all the core components, including the configuration, the various scanners, the `StrategyManager`, and the `ExecutionManager`. It orchestrates the entire flow of data, from detecting opportunities to making trading decisions and executing them.
-   **Interaction:** It spawns and manages all other core services and acts as the primary entry point for the bot.

### `listener`
-   **Purpose:** To act as a raw data firehose for on-chain events.
-   **Functionality:** The `listener` connects directly to the WebSocket (WSS) endpoint of an Ethereum node. It subscribes to raw event logs (like `Swap`, `Transfer`, `PoolCreated`) and new pending transactions from the mempool. It does very little processing; its main job is to grab this raw data as fast as possible and publish it onto the NATS messaging bus.
-   **Interaction:**
    -   **Listens to:** Ethereum Node (via WSS).
    -   **Publishes to:** NATS topics like `data.chain.events` and `data.mempool.txs`.

### `data_ingestor`
-   **Purpose:** To process, clean, and enrich the raw data provided by the `listener`.
-   **Functionality:** The `data_ingestor` subscribes to the raw data topics on NATS. It takes the raw event logs, decodes them into a structured format (e.g., a `DecodedSwapLog` struct), adds human-readable context (like token symbols), and then publishes these clean, enriched data objects back to NATS for other services to use.
-   **Interaction:**
    -   **Subscribes to:** NATS topics like `data.chain.events`.
    -   **Publishes to:** NATS topics like `data.chain.logs.processed.swaps`.

---

## 2. Analytical Engines (The "Senses")

These services perform the deep analysis that forms the bot's understanding of the market.

### `seismic_analyzer`
-   **Purpose:** To implement the "Network Seismology" pillar of the Aetheric Resonance Engine.
-   **Functionality:** This binary analyzes block propagation times received from multiple `network_observer` instances. It calculates metrics like "S-P Time" (the delay between the first and majority observation of a block) and network coherence. This gives the bot a sense of the blockchain network's health and speed.
-   **Interaction:**
    -   **Subscribes to:** NATS topic `data.network.propagation`.
    -   **Publishes to:** NATS topic `state.network.resonance` with `NetworkResonanceState` data.

### `network_observer`
-   **Purpose:** To act as a sensor for the `seismic_analyzer`.
-   **Functionality:** You would run multiple instances of this binary, each pointed at a different Ethereum node. Its only job is to watch for new blocks and immediately publish a message to NATS with the block hash and the precise time it saw the block.
-   **Interaction:**
    -   **Listens to:** A specific Ethereum Node.
    -   **Publishes to:** NATS topic `data.network.propagation`.

---

## 3. User Interface & Tooling

These binaries are for human interaction, testing, and data management.

### `tui_harness`
-   **Purpose:** To provide a Terminal User Interface (TUI) for monitoring and controlling the bot.
-   **Functionality:** This is the "mission control" screen. It runs in your terminal and subscribes to various state and log topics on NATS to give you a real-time view of the bot's health, ongoing strategies, P&L, and any important log messages.
-   **Interaction:**
    -   **Subscribes to:** Many NATS topics, including `state.system.health`, `state.strategy.metrics`, and `log.events.narrative`.
    -   **Publishes to:** Control topics like `control.command` if you issue commands through the interface.

### `feature_exporter`
-   **Purpose:** To extract and save trading data for offline analysis or machine learning.
-   **Functionality:** This tool subscribes to the bot's final opportunity and execution data. It then formats this data into a structured file (like JSON or CSV) that can be used to train predictive models, analyze strategy performance, or create historical charts.
-   **Interaction:**
    -   **Subscribes to:** NATS topics like `execution.result` and `living_codex.are_analysis`.
    -   **Outputs:** A data file (e.g., `sigint_feature_export.json`).

### `graph_analyzer`
-   **Purpose:** To analyze the liquidity pool graph of a DEX.
-   **Functionality:** This is a utility tool that can be run to inspect the relationships between different liquidity pools and tokens. It can be used to find the most central assets (like WETH and USDC) or identify potential arbitrage paths for manual inspection.
-   **Interaction:** Reads on-chain data and outputs its analysis to the console or a file.

---

## 4. Backtesting & Simulation

These binaries are used to test strategies against historical or simulated data without risking real funds.

### `backtester`
-   **Purpose:** To test the performance of a trading strategy on historical data.
-   **Functionality:** The `backtester` reads a file of historical market data (e.g., swap events) and feeds it into the strategy logic. It simulates the execution of trades and calculates the hypothetical P&L that the strategy would have achieved over that period.
-   **Interaction:** Reads historical data from a file and outputs performance reports.

### `mempool_backtester`
-   **Purpose:** A specialized backtester for MEV strategies like Pilot Fish.
-   **Functionality:** This tool replays historical mempool data to test how a strategy would have performed in the highly competitive and time-sensitive MEV environment. It's more complex than a standard backtester because it needs to simulate transaction ordering and gas price auctions.
-   **Interaction:** Reads historical mempool data and outputs detailed MEV performance metrics.

### `demo_data_generator`
-   **Purpose:** To create realistic-looking fake market data for testing.
-   **Functionality:** When you don't have real historical data, this tool can generate files containing simulated swap events, price movements, and other data. This is useful for testing the basic functionality of other components, like the `backtester` or `data_ingestor`.
-   **Interaction:** Outputs a file with simulated market data.

### `optimizer`
-   **Purpose:** To find the best parameter settings for a trading strategy.
-   **Functionality:** The `optimizer` runs the `backtester` hundreds or thousands of times, each time with slightly different strategy parameters (e.g., changing the `risk_aversion_k` or `min_execution_score`). It systematically searches for the combination of parameters that results in the highest profitability.
-   **Interaction:** Interacts closely with the `backtester` and outputs a set of optimal configuration parameters.

---
---

## 5. Operational Setups & Workflows

This section details how to run the binaries for different operational environments.

### Environment 1: Local Development & Testing

-   **Purpose:** For writing and debugging code on your local machine. This setup does not typically connect to live networks or require the full suite of services.
-   **Required Binaries:** None are required to be running constantly. You will run tools on an as-needed basis.
-   **Common Commands:**
    ```bash
    # Run the entire test suite
    cargo test

    # Run the backtester with a specific configuration file
    cargo run --bin backtester -- --config config/test-config.toml

    # Run the optimizer to find the best strategy parameters
    cargo run --bin optimizer

    # Analyze the DEX graph for a specific chain
    cargo run --bin graph_analyzer
    ```

### Environment 2: Live Simulation (Shadow Mode)

-   **Purpose:** To test the entire bot infrastructure against live mainnet data without executing real trades. This is the final and most important testing phase before production.
-   **Required Binaries:** The full data and analysis pipeline must be running.
    -   `listener`
    -   `data_ingestor`
    -   `network_observer` (at least 2 instances recommended)
    -   `seismic_analyzer`
    -   `basilisk_bot` (the main application)
    -   `tui_harness` (for monitoring)
-   **Workflow & Commands:**
    *You will need at least two terminal windows.*

    **Terminal 1: Start Background Services**
    *These services will run in the background (`&`) and provide the data pipeline.*
    ```bash
    # Start the raw data listener
    cargo run --bin listener &

    # Start the data processing and enrichment service
    cargo run --bin data_ingestor &

    # Start the network observers (replace with your actual RPC URLs)
    cargo run --bin network_observer -- --node-url <YOUR_WSS_URL_1> &
    cargo run --bin network_observer -- --node-url <YOUR_WSS_URL_2> &

    # Start the network analysis engine
    cargo run --bin seismic_analyzer &

    # Start the main bot application in "simulate" mode
    # This will process opportunities but not execute them on-chain.
    cargo run -- --run-mode simulate
    ```

    **Terminal 2: Start Monitoring UI**
    *This runs in the foreground and gives you a live view of the entire system.*
    ```bash
    # Start the Terminal User Interface
    cargo run --bin tui_harness
    ```

### Environment 3: Full Production Trading

-   **Purpose:** Live trading with real capital on the mainnet.
-   **Required Binaries:** Same as Live Simulation.
-   **Workflow & Commands:** The setup is identical to Simulation Mode, but with two crucial changes:
    1.  All binaries are run with the `--release` flag for optimized performance.
    2.  The main `basilisk_bot` application is run with `--run-mode live` and pointed to a `production.toml` configuration file.

    **Terminal 1: Start Background Services (Optimized for Release)**
    ```bash
    # Start the raw data listener
    cargo run --release --bin listener &

    # Start the data processing and enrichment service
    cargo run --release --bin data_ingestor &

    # Start the network observers (replace with your actual RPC URLs)
    cargo run --release --bin network_observer -- --node-url <YOUR_WSS_URL_1> &
    cargo run --release --bin network_observer -- --node-url <YOUR_WSS_URL_2> &

    # Start the network analysis engine
    cargo run --release --bin seismic_analyzer &

    # Start the main bot application in "live" mode with production config
    # This WILL execute real trades. Double-check your configuration.
    cargo run --release -- --run-mode live --config config/production.toml
    ```

    **Terminal 2: Start Monitoring UI (Optimized for Release)**
    ```bash
    # Start the Terminal User Interface
    cargo run --release --bin tui_harness
    ```