### **`/docs/tasks/implement_nomadic_hunter.md`**

# **Task List: Forging the Nomadic Predator with OROBOROS Resilience**

**To:** Coder Agent
**From:** The Architect
**Subject:** Technical Checklist for Territory Hopping & Automated Failover

**Mission:** You will now execute the implementation of the **"Nomadic Predator"** doctrine. Follow this task list precisely. Each item represents a discrete, verifiable unit of work required to build this new, resilient, and multi-chain-aware system.

### **[EPIC] 1: Implement the `Atlas` (Resilient, Multi-Chain Configuration)**

**Objective:** Refactor the configuration system to be inherently multi-chain and support prioritized RPC failover for the OROBOROS protocol.

-   [ ] **Task 1.1: Refactor `config.toml` Structure.**
    -   **Action:** Modify the `config.toml` file to use the "Atlas" structure. The top level will have an `active_chain_id`. There will be a `[chains]` table containing sub-tables for each chain ID (e.g., `[chains.8453]`, `[chains.137]`).
    -   **Action:** Within each chain's config, the `rpc_endpoints` key must be a list of tables, with each table having a `url: String` and a `priority: u32`.
    -   **Verification:** The `config.toml` file is syntactically correct and contains at least two chains (e.g., Base and Polygon) with prioritized RPC lists.

-   [ ] **Task 1.2: Update Rust `Config` Structs.**
    -   **Action:** In `src/config.rs`, create a new `pub struct RpcEndpoint { url: String, priority: u32 }` that derives `Deserialize` and `Clone`.
    -   **Action:** Modify the `ChainConfig` struct to contain `pub rpc_endpoints: Vec<RpcEndpoint>`.
    -   **Action:** Ensure the top-level `Config` struct can correctly deserialize the entire new Atlas structure.
    -   **Verification:** The project compiles, and a unit test confirms that `config-rs` correctly loads the nested chain configurations and the list of `RpcEndpoint` structs.

---

### **[EPIC] 2: Implement the `EcologicalSurveyor`**

**Objective:** Upgrade the `MarketStateAnalyzer` to monitor all configured territories and advise the operator.

-   [ ] **Task 2.1: Implement Multi-Chain Connections.**
    -   **Action:** In `src/control/analyzer.rs`, the `MarketStateAnalyzer` struct must hold a `HashMap<u64, Arc<Provider<Http>>>` to maintain connections to all chains defined in the config.
    -   **Action:** On startup, the service must loop through the `config.chains` map and initialize a provider for each one.
    -   **Verification:** On startup, the analyzer logs that it has successfully connected to all configured chains.

-   [ ] **Task 2.2: Implement the Survey Loop.**
    -   **Action:** Create a new `async fn run_ecological_survey(&self)` task within the analyzer.
    -   **Action:** This task will loop every 5 minutes. In each loop, it iterates over its map of chain providers and calls a `calculate_territory_health()` function for each.
    -   **Action:** The results (a list of `TerritoryHealth` structs) must be published to the `intelligence.ecosystem.health` NATS topic.
    -   **Verification:** A NATS listener subscribed to the topic receives a correctly formatted list of health scores for all monitored chains.

---

### **[EPIC] 3: Upgrade `Protocol: OROBOROS` (Tactical Failover)**

**Objective:** Enhance the `SystemMonitor` to use the new prioritized RPC list for intelligent, automated failover within a single territory.

-   [ ] **Task 3.1: Enhance Service Heartbeats.**
    -   **Action:** Any service that uses an RPC connection (e.g., `GazeScanner`, `LogIngestor`) must include its `current_rpc_url: String` in its `state.health.*` heartbeat message.
    -   **Verification:** The JSON payload of a heartbeat message on NATS contains the new field.

-   [ ] **Task 3.2: Implement Prioritized Failover Logic.**
    -   **Action:** In `src/control/monitor.rs`, the `SystemMonitor`'s failover logic must be upgraded.
    -   **Action:** When it detects an RPC failure from a service's heartbeat, it must:
        1.  Read the `active_chain_id` and the failing `current_rpc_url`.
        2.  Find the failing RPC's `priority` in the config's `rpc_endpoints` list for that chain.
        3.  Find the `RpcEndpoint` in the list with the next highest `priority`.
        4.  Construct and publish a targeted `control.config.update` message containing the new backup URL.
    -   **Verification:** A unit test confirms that given a failing URL with `priority: 0`, the monitor correctly identifies and publishes the URL associated with `priority: 1`.

-   [ ] **Task 3.3: Implement Healing in Services.**
    -   **Action:** All services that consume RPCs must have logic in their NATS subscription handler for `control.config.update` to check if a message is targeted at them.
    -   **Action:** If so, they must re-initialize their `Provider` with the new URL from the message. Their next heartbeat must report a `Warning("Operating on Backup RPC")` status.
    -   **Verification:** A test service, when sent a targeted NATS command, logs that it is re-initializing and its subsequent heartbeat message contains the new `Warning` status.

---

### **[EPIC] 4: Implement the `MigrationController` (Strategic Relocation)**

**Objective:** Build the operator-commanded workflow for safely migrating the bot's entire operation to a new chain.

-   [ ] **Task 4.1: Implement the TUI "Ecosystem" Tab.**
    -   **Action:** Create a new tab in the TUI that subscribes to `intelligence.ecosystem.health` and displays the list of territories, their health scores, and their status (`ACTIVE`, `RECOMMENDED`).
    -   **Action:** Implement the `[M]` keybinding to trigger a confirmation dialog for migration.
    -   **Verification:** The TUI correctly displays the health scores received from the `MarketStateAnalyzer`.

-   [ ] **Task 4.2: Create the `MigrationController` Service.**
    -   **Action:** Create the new service at `src/control/migration.rs`. It must subscribe to `control.system.migrate`.
    -   **Action:** Implement the state machine logic:
        1.  On receiving the command, publish `control.system.pause_all`.
        2.  Construct and dispatch the on-chain transaction to the configured `bridge_contract`.
        3.  Poll the destination chain until funds are confirmed.
        4.  Publish the final `MIGRATION COMPLETE... Please restart...` message to the TUI's event feed.
        5.  Trigger a `std::process::exit(0)` for a graceful shutdown.
    -   **Verification:** On a forked testnet, triggering a migration command correctly pauses the bot, executes a (mocked) bridge transaction, and then shuts the application down after printing the final message.