// Stargate Compass Integration Test Execution Binary
// Main binary that can be executed via `cargo test --test stargate_compass_integration`
// Implements command-line argument parsing for test configuration options
// Creates test execution with proper setup, execution, and cleanup phases
// Writes comprehensive test result output with clear pass/fail indicators

use anyhow::{Result, Context};
use clap::{Arg, Command};
use std::env;
use std::time::{Duration, Instant};
use tokio;
use tracing::{info, warn, error, debug};
use tracing_subscriber;

// Import integration test modules
mod integration {
    pub mod stargate_compass;
}

use integration::stargate_compass::{
    IntegrationTestController,
    IntegrationTestConfig,
    TestExecutionState,
    TestPhase,
    IntegrationTestResult as MockIntegrationTestResult,
    TestExecutionSummary,
    PerformanceMetrics,
};

/// Configuration for the integration test execution
#[derive(Debug, Clone)]
pub struct TestExecutionConfig {
    pub anvil_url: String,
    pub contract_address: String,
    pub timeout_seconds: u64,
    pub retry_attempts: u32,
    pub parallel_execution: bool,
    pub verbose: bool,
    pub save_report: bool,
    pub report_path: String,
    pub skip_tui_tests: bool,
    pub skip_backend_tests: bool,
    pub skip_workflow_tests: bool,
}

impl Default for TestExecutionConfig {
    fn default() -> Self {
        Self {
            anvil_url: "http://localhost:8545".to_string(),
            contract_address: "0x1234567890123456789012345678901234567890".to_string(),
            timeout_seconds: 300,
            retry_attempts: 3,
            parallel_execution: false,
            verbose: false,
            save_report: true,
            report_path: "test_reports".to_string(),
            skip_tui_tests: false,
            skip_backend_tests: false,
            skip_workflow_tests: false,
        }
    }
}

/// Main integration test orchestrator
pub struct IntegrationTestOrchestrator {
    config: TestExecutionConfig,
    start_time: Instant,
    test_controller: Option<IntegrationTestController>,
}

impl IntegrationTestOrchestrator {
    pub fn new(config: TestExecutionConfig) -> Self {
        Self {
            config,
            start_time: Instant::now(),
            test_controller: None,
        }
    }

    /// Initialize the test environment and controller
    pub async fn initialize(&mut self) -> Result<()> {
        info!("Initializing Stargate Compass Integration Test Environment");
        info!("Configuration: {:?}", self.config);

        // Initialize tracing based on verbosity
        let log_level = if self.config.verbose {
            tracing::Level::DEBUG
        } else {
            tracing::Level::INFO
        };

        tracing_subscriber::fmt()
            .with_max_level(log_level)
            .with_target(false)
            .with_thread_ids(true)
            .with_file(true)
            .with_line_number(true)
            .init();

        // Create integration test configuration
        let integration_config = IntegrationTestConfig {
            anvil_url: self.config.anvil_url.clone(),
            contract_address: self.config.contract_address.parse()
                .context("Failed to parse contract address")?,
            test_timeout: Duration::from_secs(self.config.timeout_seconds),
            retry_attempts: self.config.retry_attempts,
            parallel_execution: self.config.parallel_execution,
        };

        // Initialize the test controller
        self.test_controller = Some(IntegrationTestController::new(integration_config).await?);

        info!("Integration test environment initialized successfully");
        Ok(())
    }

    /// Execute the complete integration test suite
    pub async fn execute_tests(&mut self) -> Result<IntegrationTestResult> {
        info!("Starting Stargate Compass Integration Test Execution");
        
        let controller = self.test_controller.as_mut()
            .ok_or_else(|| anyhow::anyhow!("Test controller not initialized"))?;

        // Setup phase
        info!("Phase 1: Test Environment Setup");
        controller.setup_test_environment().await
            .context("Failed to setup test environment")?;

        let mut test_results = IntegrationTestResult::new();
        test_results.start_time = Some(chrono::Utc::now());

        // Configuration phase
        info!("Phase 2: Configuration Management");
        if let Err(e) = controller.test_configuration_management().await {
            error!("Configuration management tests failed: {}", e);
            test_results.configuration_passed = false;
            test_results.errors.push(format!("Configuration: {}", e));
        } else {
            info!("✅ Configuration management tests passed");
            test_results.configuration_passed = true;
        }

        // Backend integration phase
        if !self.config.skip_backend_tests {
            info!("Phase 3: Backend Integration Testing");
            match controller.test_backend_integration().await {
                Ok(backend_results) => {
                    info!("✅ Backend integration tests completed");
                    test_results.backend_passed = backend_results.overall_success;
                    test_results.backend_test_count = backend_results.total_tests;
                    test_results.backend_passed_count = backend_results.passed_tests;
                }
                Err(e) => {
                    error!("Backend integration tests failed: {}", e);
                    test_results.backend_passed = false;
                    test_results.errors.push(format!("Backend: {}", e));
                }
            }
        } else {
            info!("⏭️  Skipping backend integration tests");
        }

        // TUI functionality phase
        if !self.config.skip_tui_tests {
            info!("Phase 4: TUI Functionality Testing");
            match controller.test_tui_functionality().await {
                Ok(tui_results) => {
                    info!("✅ TUI functionality tests completed");
                    test_results.tui_passed = tui_results.overall_success;
                    test_results.tui_test_count = tui_results.total_tests;
                    test_results.tui_passed_count = tui_results.passed_tests;
                }
                Err(e) => {
                    error!("TUI functionality tests failed: {}", e);
                    test_results.tui_passed = false;
                    test_results.errors.push(format!("TUI: {}", e));
                }
            }
        } else {
            info!("⏭️  Skipping TUI functionality tests");
        }

        // End-to-end workflow phase
        if !self.config.skip_workflow_tests {
            info!("Phase 5: End-to-End Workflow Testing");
            match controller.test_end_to_end_workflow().await {
                Ok(workflow_results) => {
                    info!("✅ End-to-end workflow tests completed");
                    test_results.workflow_passed = workflow_results.overall_success;
                    test_results.workflow_test_count = workflow_results.total_tests;
                    test_results.workflow_passed_count = workflow_results.passed_tests;
                }
                Err(e) => {
                    error!("End-to-end workflow tests failed: {}", e);
                    test_results.workflow_passed = false;
                    test_results.errors.push(format!("Workflow: {}", e));
                }
            }
        } else {
            info!("⏭️  Skipping end-to-end workflow tests");
        }

        // Cleanup phase
        info!("Phase 6: Test Environment Cleanup");
        if let Err(e) = controller.cleanup_test_environment().await {
            warn!("Test environment cleanup failed: {}", e);
            test_results.warnings.push(format!("Cleanup: {}", e));
        }

        test_results.end_time = Some(chrono::Utc::now());
        test_results.total_duration = self.start_time.elapsed();

        // Calculate overall success
        test_results.overall_success = test_results.configuration_passed
            && test_results.backend_passed
            && test_results.tui_passed
            && test_results.workflow_passed;

        info!("Integration test execution completed");
        Ok(test_results)
    }

    /// Generate and save comprehensive test report
    pub async fn generate_report(&self, results: &IntegrationTestResult) -> Result<()> {
        info!("Generating comprehensive test report");

        let report = self.create_test_report(results)?;
        
        if self.config.save_report {
            self.save_report_to_file(&report).await?;
        }

        self.print_test_summary(&report);

        Ok(())
    }

    /// Create detailed test report
    fn create_test_report(&self, results: &IntegrationTestResult) -> Result<TestReport> {
        let total_tests = results.backend_test_count + results.tui_test_count + results.workflow_test_count;
        let total_passed = results.backend_passed_count + results.tui_passed_count + results.workflow_passed_count;
        let success_rate = if total_tests > 0 {
            total_passed as f64 / total_tests as f64
        } else {
            0.0
        };

        Ok(TestReport {
            session_id: format!("stargate_compass_{}", chrono::Utc::now().timestamp()),
            start_time: results.start_time.unwrap_or_else(chrono::Utc::now),
            end_time: results.end_time.unwrap_or_else(chrono::Utc::now),
            total_duration: results.total_duration,
            overall_success: results.overall_success,
            total_tests,
            total_passed,
            success_rate,
            configuration_passed: results.configuration_passed,
            backend_passed: results.backend_passed,
            tui_passed: results.tui_passed,
            workflow_passed: results.workflow_passed,
            errors: results.errors.clone(),
            warnings: results.warnings.clone(),
            recommendations: self.generate_recommendations(results),
        })
    }

    /// Generate actionable recommendations based on test results
    fn generate_recommendations(&self, results: &IntegrationTestResult) -> Vec<String> {
        let mut recommendations = Vec::new();

        if !results.configuration_passed {
            recommendations.push("❌ Configuration management failed - verify contract addresses and network settings".to_string());
        }

        if !results.backend_passed {
            recommendations.push("❌ Backend integration failed - check ExecutionManager and ExecutionDispatcher implementations".to_string());
        }

        if !results.tui_passed {
            recommendations.push("❌ TUI functionality failed - verify TUI commands and contract interaction detection".to_string());
        }

        if !results.workflow_passed {
            recommendations.push("❌ End-to-end workflow failed - check complete data pipeline from decision to execution".to_string());
        }

        if results.overall_success {
            recommendations.push("✅ All integration tests passed - system is ready for production deployment".to_string());
        } else {
            recommendations.push("⚠️  Some integration tests failed - address issues before production deployment".to_string());
        }

        if results.total_duration > Duration::from_secs(600) {
            recommendations.push("⏱️  Test execution took longer than 10 minutes - consider optimizing test performance".to_string());
        }

        recommendations
    }

    /// Save test report to file
    async fn save_report_to_file(&self, report: &TestReport) -> Result<()> {
        use std::fs;

        // Create report directory if it doesn't exist
        fs::create_dir_all(&self.config.report_path)?;

        let filename = format!("{}/stargate_compass_integration_report_{}.json", 
                              self.config.report_path, 
                              chrono::Utc::now().format("%Y%m%d_%H%M%S"));

        let json_content = serde_json::to_string_pretty(report)
            .context("Failed to serialize test report")?;

        fs::write(&filename, json_content)
            .context(format!("Failed to write report to file: {}", filename))?;

        info!("Test report saved to: {}", filename);
        Ok(())
    }

    /// Print comprehensive test summary to console
    fn print_test_summary(&self, report: &TestReport) {
        println!("\n🔍 STARGATE COMPASS INTEGRATION TEST REPORT");
        println!("═══════════════════════════════════════════════════");
        println!("Session ID: {}", report.session_id);
        println!("Start Time: {}", report.start_time.format("%Y-%m-%d %H:%M:%S UTC"));
        println!("End Time: {}", report.end_time.format("%Y-%m-%d %H:%M:%S UTC"));
        println!("Duration: {:?}", report.total_duration);
        println!();

        // Overall Results
        let status_icon = if report.overall_success { "✅" } else { "❌" };
        println!("📊 OVERALL RESULTS {}", status_icon);
        println!("─────────────────────────");
        println!("Overall Success: {}", if report.overall_success { "PASS" } else { "FAIL" });
        println!("Total Tests: {}", report.total_tests);
        println!("Passed: {} ({:.1}%)", report.total_passed, report.success_rate * 100.0);
        println!("Failed: {}", report.total_tests - report.total_passed);
        println!();

        // Component Results
        println!("🔧 COMPONENT RESULTS");
        println!("───────────────────");
        println!("Configuration: {}", if report.configuration_passed { "✅ PASS" } else { "❌ FAIL" });
        println!("Backend Integration: {}", if report.backend_passed { "✅ PASS" } else { "❌ FAIL" });
        println!("TUI Functionality: {}", if report.tui_passed { "✅ PASS" } else { "❌ FAIL" });
        println!("End-to-End Workflow: {}", if report.workflow_passed { "✅ PASS" } else { "❌ FAIL" });
        println!();

        // Errors
        if !report.errors.is_empty() {
            println!("❌ ERRORS");
            println!("────────");
            for error in &report.errors {
                println!("  • {}", error);
            }
            println!();
        }

        // Warnings
        if !report.warnings.is_empty() {
            println!("⚠️  WARNINGS");
            println!("──────────");
            for warning in &report.warnings {
                println!("  • {}", warning);
            }
            println!();
        }

        // Recommendations
        if !report.recommendations.is_empty() {
            println!("💡 RECOMMENDATIONS");
            println!("─────────────────");
            for recommendation in &report.recommendations {
                println!("  • {}", recommendation);
            }
            println!();
        }

        println!("Report completed at: {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
        println!("═══════════════════════════════════════════════════");
    }
}

/// Test report structure
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct TestReport {
    pub session_id: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub total_duration: Duration,
    pub overall_success: bool,
    pub total_tests: usize,
    pub total_passed: usize,
    pub success_rate: f64,
    pub configuration_passed: bool,
    pub backend_passed: bool,
    pub tui_passed: bool,
    pub workflow_passed: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Integration test result structure
#[derive(Debug, Clone)]
pub struct IntegrationTestResult {
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub total_duration: Duration,
    pub overall_success: bool,
    pub configuration_passed: bool,
    pub backend_passed: bool,
    pub backend_test_count: usize,
    pub backend_passed_count: usize,
    pub tui_passed: bool,
    pub tui_test_count: usize,
    pub tui_passed_count: usize,
    pub workflow_passed: bool,
    pub workflow_test_count: usize,
    pub workflow_passed_count: usize,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

impl IntegrationTestResult {
    pub fn new() -> Self {
        Self {
            start_time: None,
            end_time: None,
            total_duration: Duration::from_secs(0),
            overall_success: false,
            configuration_passed: false,
            backend_passed: false,
            backend_test_count: 0,
            backend_passed_count: 0,
            tui_passed: false,
            tui_test_count: 0,
            tui_passed_count: 0,
            workflow_passed: false,
            workflow_test_count: 0,
            workflow_passed_count: 0,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
}

/// Parse command line arguments
fn parse_args() -> TestExecutionConfig {
    let matches = Command::new("Stargate Compass Integration Tests")
        .version("1.0")
        .author("Basilisk Bot Team")
        .about("Comprehensive integration tests for Stargate Compass contract interaction")
        .arg(
            Arg::new("anvil-url")
                .long("anvil-url")
                .value_name("URL")
                .help("Anvil testnet URL")
                .default_value("http://localhost:8545")
        )
        .arg(
            Arg::new("contract-address")
                .long("contract-address")
                .value_name("ADDRESS")
                .help("StargateCompassV1 contract address")
                .default_value("0x1234567890123456789012345678901234567890")
        )
        .arg(
            Arg::new("timeout")
                .long("timeout")
                .value_name("SECONDS")
                .help("Test timeout in seconds")
                .default_value("300")
        )
        .arg(
            Arg::new("retry-attempts")
                .long("retry-attempts")
                .value_name("COUNT")
                .help("Number of retry attempts for failed tests")
                .default_value("3")
        )
        .arg(
            Arg::new("parallel")
                .long("parallel")
                .help("Enable parallel test execution")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .help("Enable verbose logging")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("no-report")
                .long("no-report")
                .help("Skip saving test report to file")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("report-path")
                .long("report-path")
                .value_name("PATH")
                .help("Directory to save test reports")
                .default_value("test_reports")
        )
        .arg(
            Arg::new("skip-tui")
                .long("skip-tui")
                .help("Skip TUI functionality tests")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("skip-backend")
                .long("skip-backend")
                .help("Skip backend integration tests")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("skip-workflow")
                .long("skip-workflow")
                .help("Skip end-to-end workflow tests")
                .action(clap::ArgAction::SetTrue)
        )
        .get_matches();

    TestExecutionConfig {
        anvil_url: matches.get_one::<String>("anvil-url").unwrap().clone(),
        contract_address: matches.get_one::<String>("contract-address").unwrap().clone(),
        timeout_seconds: matches.get_one::<String>("timeout").unwrap().parse().unwrap_or(300),
        retry_attempts: matches.get_one::<String>("retry-attempts").unwrap().parse().unwrap_or(3),
        parallel_execution: matches.get_flag("parallel"),
        verbose: matches.get_flag("verbose"),
        save_report: !matches.get_flag("no-report"),
        report_path: matches.get_one::<String>("report-path").unwrap().clone(),
        skip_tui_tests: matches.get_flag("skip-tui"),
        skip_backend_tests: matches.get_flag("skip-backend"),
        skip_workflow_tests: matches.get_flag("skip-workflow"),
    }
}

/// Main test execution function
#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let config = parse_args();

    // Create and initialize test orchestrator
    let mut orchestrator = IntegrationTestOrchestrator::new(config);
    
    // Initialize test environment
    orchestrator.initialize().await
        .context("Failed to initialize test environment")?;

    // Execute integration tests
    let results = orchestrator.execute_tests().await
        .context("Failed to execute integration tests")?;

    // Generate and save test report
    orchestrator.generate_report(&results).await
        .context("Failed to generate test report")?;

    // Exit with appropriate code
    if results.overall_success {
        info!("All integration tests passed successfully");
        std::process::exit(0);
    } else {
        error!("Some integration tests failed");
        std::process::exit(1);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_config_parsing() {
        let config = TestExecutionConfig::default();
        assert_eq!(config.anvil_url, "http://localhost:8545");
        assert_eq!(config.timeout_seconds, 300);
        assert_eq!(config.retry_attempts, 3);
        assert!(!config.parallel_execution);
        assert!(!config.verbose);
        assert!(config.save_report);
    }

    #[tokio::test]
    async fn test_orchestrator_creation() {
        let config = TestExecutionConfig::default();
        let orchestrator = IntegrationTestOrchestrator::new(config);
        
        assert!(orchestrator.test_controller.is_none());
        assert!(orchestrator.start_time.elapsed() < Duration::from_secs(1));
    }

    #[tokio::test]
    async fn test_integration_result_creation() {
        let result = IntegrationTestResult::new();
        
        assert!(result.start_time.is_none());
        assert!(result.end_time.is_none());
        assert_eq!(result.total_duration, Duration::from_secs(0));
        assert!(!result.overall_success);
        assert!(!result.configuration_passed);
        assert!(!result.backend_passed);
        assert!(!result.tui_passed);
        assert!(!result.workflow_passed);
        assert!(result.errors.is_empty());
        assert!(result.warnings.is_empty());
    }

    #[test]
    fn test_recommendation_generation() {
        let orchestrator = IntegrationTestOrchestrator::new(TestExecutionConfig::default());
        let mut results = IntegrationTestResult::new();
        
        // Test with all failures
        results.configuration_passed = false;
        results.backend_passed = false;
        results.tui_passed = false;
        results.workflow_passed = false;
        
        let recommendations = orchestrator.generate_recommendations(&results);
        
        assert!(recommendations.len() >= 5); // At least 4 failure recommendations + 1 overall
        assert!(recommendations.iter().any(|r| r.contains("Configuration management failed")));
        assert!(recommendations.iter().any(|r| r.contains("Backend integration failed")));
        assert!(recommendations.iter().any(|r| r.contains("TUI functionality failed")));
        assert!(recommendations.iter().any(|r| r.contains("End-to-end workflow failed")));
    }

    #[test]
    fn test_success_scenario() {
        let orchestrator = IntegrationTestOrchestrator::new(TestExecutionConfig::default());
        let mut results = IntegrationTestResult::new();
        
        // Test with all successes
        results.overall_success = true;
        results.configuration_passed = true;
        results.backend_passed = true;
        results.tui_passed = true;
        results.workflow_passed = true;
        
        let recommendations = orchestrator.generate_recommendations(&results);
        
        assert!(recommendations.iter().any(|r| r.contains("ready for production deployment")));
    }
}