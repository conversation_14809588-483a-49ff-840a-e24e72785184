// MISSION: Geometric Analysis Module for the Aetheric Resonance Engine
// WHY: Apply sacred geometry principles to understand market structure
// HOW: Mathematical functions based on Vesica Piscis and other geometric forms

use crate::shared_types::{ArbitragePath, ArbitragePool, GeometricScore, GeometricScorer, Pool};
use crate::data::oracle::PriceOracle;
use crate::token_registry::TokenRegistry;
use crate::math::vesica; // AUDIT-FIX: Import vesica piscis module for depth calculation
use ethers::types::Address;

/// A simplified GeometryScorer implementation for the TUI
pub struct GeometryScorer {}

#[async_trait::async_trait]
impl GeometricScorer for GeometryScorer {
    async fn calculate_score(&self, _path: &ArbitragePath) -> anyhow::Result<GeometricScore> {
        // Return a default score for the TUI
        Ok(GeometricScore {
            convexity_ratio: rust_decimal::Decimal::new(5, 1), // 0.5
            liquidity_centroid_bias: rust_decimal::Decimal::new(5, 1), // 0.5
            harmonic_path_score: rust_decimal::Decimal::new(5, 1), // 0.5
            // AUDIT-FIX: Add vesica piscis depth to default score
            vesica_piscis_depth: rust_decimal::Decimal::new(5, 1), // 0.5
        })
    }
}

use async_trait::async_trait;
use geo::{
    algorithm::convex_hull::ConvexHull,
    prelude::*,
    Point,
    Polygon,
    LineString,
    Coord,
    MultiPoint,
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use num_traits::{FromPrimitive, ToPrimitive};
use tracing::debug;
use anyhow::{anyhow, Result};

/// Represents the geometric properties of a single pool in USD-space.
struct PoolGeometricData {
    /// The 2D point representing the pool's reserves in USD.
    point: Point,
    /// The total liquidity of the pool in USD.
    liquidity_usd: Decimal,
    /// The symbols of the two tokens in the pool.
    token_symbols: (String, String),
}



/// The production implementation of the GeometricScorer.
/// It calculates scores based on the geometric properties of liquidity pools.
pub struct RealGeometricScorer<O: PriceOracle> {
    /// A price oracle to convert token amounts to USD.
    price_oracle: O,
    /// Configuration for anchor assets (central market assets)
    pub anchor_assets: Vec<String>,
    /// AUDIT-FIX: Token registry for address resolution
    token_registry: TokenRegistry,
}

impl<O: PriceOracle + Send + Sync> RealGeometricScorer<O> {
    /// Create a new RealGeometricScorer with a price oracle and default anchor assets
    pub fn new(price_oracle: O) -> Self {
        Self {
            price_oracle,
            anchor_assets: vec![
                "WETH".to_string(),
                "USDC".to_string(),
                "USDT".to_string(),
                "DAI".to_string(),
                "WBTC".to_string(),
            ],
            token_registry: TokenRegistry::new(),
        }
    }

    /// Create a new RealGeometricScorer with a price oracle and custom anchor assets
    pub fn with_anchor_assets(price_oracle: O, anchor_assets: Vec<String>) -> Self {
        Self { 
            price_oracle, 
            anchor_assets,
            token_registry: TokenRegistry::new(),
        }
    }

    /// Fetches and converts all necessary data for a path into a geometric format.
    async fn get_path_geometric_data(&self, path: &ArbitragePath) -> Result<Vec<PoolGeometricData>> {
        let mut data = Vec::with_capacity(path.len());
        for pool in path {
            let token_a_symbol = pool.token0_symbol.clone();
            let token_b_symbol = pool.token1_symbol.clone();

            // AUDIT-FIX: Use token registry to resolve addresses instead of Address::zero()
            let token_a_address = self.token_registry.get_address(&token_a_symbol)
                .unwrap_or_else(|| {
                    tracing::warn!("Unknown token symbol: {}, using fallback price", token_a_symbol);
                    Address::zero()
                });
            let token_b_address = self.token_registry.get_address(&token_b_symbol)
                .unwrap_or_else(|| {
                    tracing::warn!("Unknown token symbol: {}, using fallback price", token_b_symbol);
                    Address::zero()
                });
            
            // Get prices from oracle with proper error handling
            let token_a_price_usd = if token_a_address == Address::zero() {
                self.get_fallback_price(&token_a_symbol)
            } else {
                match self.price_oracle.get_price(token_a_address).await {
                    Ok(price) if price > rust_decimal::Decimal::ZERO => price,
                    _ => self.get_fallback_price(&token_a_symbol),
                }
            };
            
            let token_b_price_usd = if token_b_address == Address::zero() {
                self.get_fallback_price(&token_b_symbol)
            } else {
                match self.price_oracle.get_price(token_b_address).await {
                    Ok(price) if price > rust_decimal::Decimal::ZERO => price,
                    _ => self.get_fallback_price(&token_b_symbol),
                }
            };
            
            // Convert reserves to USD using price oracle
            let reserve_a_usd = pool.reserve0 * token_a_price_usd;
            let reserve_b_usd = pool.reserve1 * token_b_price_usd;
            
            data.push(PoolGeometricData {
                point: geo::Point::new(
                    reserve_a_usd.to_f64().ok_or_else(|| anyhow::anyhow!("Failed to convert reserve_a_usd to f64"))?,
                    reserve_b_usd.to_f64().ok_or_else(|| anyhow::anyhow!("Failed to convert reserve_b_usd to f64"))?
                ),
                liquidity_usd: reserve_a_usd + reserve_b_usd,
                token_symbols: (token_a_symbol, token_b_symbol),
            });
        }
        Ok(data)
    }
}


#[async_trait]
impl<O: PriceOracle + Send + Sync> GeometricScorer for RealGeometricScorer<O> {
    async fn calculate_score(&self, path: &ArbitragePath) -> Result<GeometricScore> {
        if path.len() < 3 {
            debug!("MANDORLA GAUGE: Path too short for geometric analysis ({})", path.len());
            return Ok(GeometricScore {
                convexity_ratio: dec!(0.5), // Neutral score
                liquidity_centroid_bias: dec!(0.5), // Neutral bias
                harmonic_path_score: dec!(0.5), // Neutral bias
                // AUDIT-FIX: Add vesica piscis depth to short path fallback
                vesica_piscis_depth: dec!(0.5), // Neutral depth
            });
        }

        let geometric_data = self.get_path_geometric_data(path).await?;

        let convexity_ratio = self.calculate_convexity_ratio(&geometric_data)?;
        let harmonic_path_score = self.calculate_harmonic_path_score(&geometric_data)?;
        // AUDIT-FIX: Calculate vesica piscis depth for arbitrage opportunity analysis
        let vesica_piscis_depth = self.calculate_vesica_piscis_depth(&geometric_data)?;

        debug!(
            "MANDORLA GAUGE: Real Geometric Score | Convexity: {:.4} | Harmonic Path Score: {:.4} | Vesica Depth: {:.4}",
            convexity_ratio, harmonic_path_score, vesica_piscis_depth
        );

        Ok(GeometricScore {
            convexity_ratio,
            liquidity_centroid_bias: self.calculate_liquidity_centroid_bias(&geometric_data, harmonic_path_score)?,
            harmonic_path_score,
            // AUDIT-FIX: Include vesica piscis depth in geometric score
            vesica_piscis_depth,
        })
    }
}


impl<O: PriceOracle + Send + Sync> RealGeometricScorer<O> {
    /// Calculates the Convexity Ratio as defined in our research.
    /// This quantifies the "fullness" of an opportunity by comparing the area of the
    /// arbitrage path polygon to the area of its convex hull in USD-space.
    fn calculate_convexity_ratio(&self, data: &[PoolGeometricData]) -> Result<Decimal> {
        let points: Vec<Point> = data.iter().map(|d| d.point).collect();

        // 2. Calculate the convex hull of these points
        let multi_point = geo::MultiPoint(points.clone());
        let convex_hull_polygon: Polygon = multi_point.convex_hull();

        // 3. Calculate the area of the convex hull
        let hull_area_f64 = convex_hull_polygon.unsigned_area();
        if hull_area_f64 < 1e-9 {
            debug!("MANDORLA GAUGE: Degenerate convex hull, returning perfect convexity");
            return Ok(Decimal::ONE);
        }

        // 4. Create a polygon from the path (in order)
        let path_coords: Vec<Coord> = points.iter().map(|p| p.0).collect();
        
        let mut closed_coords = path_coords.clone();
        if !closed_coords.is_empty() && closed_coords.first() != closed_coords.last() {
            closed_coords.push(closed_coords[0]);
        }
        
        let path_linestring = LineString::new(closed_coords);
        let path_polygon = Polygon::new(path_linestring, vec![]);
        let path_area_f64 = path_polygon.unsigned_area();

        // 5. Calculate the convexity ratio
        let convexity_ratio_f64 = if hull_area_f64 > 1e-9 {
            (path_area_f64 / hull_area_f64).min(1.0) // Cap at 1.0
        } else {
            1.0
        };

        let convexity_ratio = Decimal::from_f64(convexity_ratio_f64)
            .ok_or_else(|| anyhow::anyhow!("Failed to convert convexity_ratio_f64 to Decimal"))?
            .max(Decimal::ZERO)
            .min(Decimal::ONE);

        debug!(
            "MANDORLA GAUGE: Convexity calculation | Path area: {:.6} | Hull area: {:.6} | Ratio: {:.4}",
            path_area_f64, hull_area_f64, convexity_ratio
        );

        Ok(convexity_ratio)
    }

    /// Calculates the Harmonic Path Score, replacing the old centroid bias.
    /// This score measures the path's alignment with high-liquidity, central assets,
    /// reflecting the stability and significance of the arbitrage opportunity.
    fn calculate_harmonic_path_score(&self, data: &[PoolGeometricData]) -> Result<Decimal> {
        let mut total_liquidity = Decimal::ZERO;
        let mut anchor_asset_weight = Decimal::ZERO;

        for pool_data in data.iter() {
            total_liquidity += pool_data.liquidity_usd;

            let (token_a, token_b) = &pool_data.token_symbols;
            let has_anchor_asset = self.anchor_assets.contains(token_a) || self.anchor_assets.contains(token_b);
            
            if has_anchor_asset {
                anchor_asset_weight += pool_data.liquidity_usd;
            }
        }

        if total_liquidity.is_zero() {
            debug!("MANDORLA GAUGE: No liquidity in path, returning zero score");
            return Ok(Decimal::ZERO);
        }

        // The score is the ratio of anchor asset liquidity to total path liquidity.
        let score = anchor_asset_weight / total_liquidity;

        debug!(
            "MANDORLA GAUGE: Harmonic Path Score calculation | Anchor liquidity: {:.2} | Total liquidity: {:.2} | Score: {:.4}",
            anchor_asset_weight, total_liquidity, score
        );

        Ok(score.max(Decimal::ZERO).min(Decimal::ONE))
    }

    /// Calculates the liquidity centroid bias.
    /// AUDIT-FIX: Now calculates actual weighted centroid instead of simple inverse
    /// A lower bias indicates the path is more centered around highly liquid, central assets.
    /// This measures how far the liquidity-weighted centroid is from the ideal center.
    fn calculate_liquidity_centroid_bias(&self, data: &[PoolGeometricData], _harmonic_path_score: Decimal) -> Result<Decimal> {
        if data.is_empty() {
            return Ok(dec!(0.5)); // Neutral score for empty paths
        }
        
        if data.len() == 1 {
            return Ok(dec!(0.8)); // Single pool is well-centered
        }
        
        // Calculate weighted centroid
        let mut total_liquidity = Decimal::ZERO;
        let mut weighted_x_sum = Decimal::ZERO;
        let mut weighted_y_sum = Decimal::ZERO;
        
        for (i, pool) in data.iter().enumerate() {
            let position_x = Decimal::from(i); // Path position
            let position_y = pool.liquidity_usd; // Liquidity as Y coordinate
            let weight = pool.liquidity_usd;
            
            total_liquidity += weight;
            weighted_x_sum += position_x * weight;
            weighted_y_sum += position_y * weight;
        }
        
        if total_liquidity == Decimal::ZERO {
            return Ok(dec!(0.5)); // Neutral score for zero liquidity
        }
        
        let centroid_x = weighted_x_sum / total_liquidity;
        let centroid_y = weighted_y_sum / total_liquidity;
        
        // Calculate ideal center (middle of path, average liquidity)
        let ideal_x = Decimal::from(data.len() - 1) / dec!(2.0);
        let ideal_y = data.iter()
            .map(|p| p.liquidity_usd)
            .sum::<Decimal>() / Decimal::from(data.len());
        
        // Calculate normalized distance from ideal center
        let distance_x = (centroid_x - ideal_x).abs();
        let distance_y = if ideal_y > Decimal::ZERO {
            (centroid_y - ideal_y).abs() / ideal_y
        } else {
            Decimal::ZERO
        };
        
        let max_distance_x = ideal_x.max(Decimal::from(data.len() - 1) - ideal_x);
        let normalized_distance_x = if max_distance_x > Decimal::ZERO {
            distance_x / max_distance_x
        } else {
            Decimal::ZERO
        };
        
        // Combine X and Y distance components
        let total_distance = (normalized_distance_x + distance_y) / dec!(2.0);
        
        // Convert to bias score (lower distance = higher score)
        let bias_score = (dec!(1.0) - total_distance).max(dec!(0.0)).min(dec!(1.0));
        
        debug!(
            "MANDORLA GAUGE: Centroid bias calculation - Centroid: ({:.2}, {:.2}), Ideal: ({:.2}, {:.2}), Distance: {:.3}, Score: {:.3}",
            centroid_x, centroid_y, ideal_x, ideal_y, total_distance, bias_score
        );
        
        Ok(bias_score)
    }
    
    /// AUDIT-FIX: Calculate vesica piscis depth for arbitrage opportunity analysis
    /// This integrates the sacred geometry depth calculation into the geometric scoring system
    fn calculate_vesica_piscis_depth(&self, data: &[PoolGeometricData]) -> Result<Decimal> {
        if data.len() < 2 {
            return Ok(dec!(0.5)); // Neutral score for insufficient data
        }
        
        let mut total_depth = Decimal::ZERO;
        let mut depth_count = 0;
        
        // Calculate vesica piscis depth between consecutive pools in the path
        for i in 0..data.len() - 1 {
            let pool_a = &data[i];
            let pool_b = &data[i + 1];
            
            // Extract X and Y coordinates (reserve values in USD)
            let pool_a_x = Decimal::from_f64(pool_a.point.x()).unwrap_or(dec!(1000.0));
            let pool_a_y = Decimal::from_f64(pool_a.point.y()).unwrap_or(dec!(1000.0));
            let pool_b_x = Decimal::from_f64(pool_b.point.x()).unwrap_or(dec!(1000.0));
            let pool_b_y = Decimal::from_f64(pool_b.point.y()).unwrap_or(dec!(1000.0));
            
            // Calculate price deviation between pools
            let price_a = if pool_a_x > Decimal::ZERO { pool_a_y / pool_a_x } else { dec!(1.0) };
            let price_b = if pool_b_x > Decimal::ZERO { pool_b_y / pool_b_x } else { dec!(1.0) };
            
            let price_deviation = if price_a > Decimal::ZERO {
                (price_b - price_a) / price_a
            } else {
                Decimal::ZERO
            };
            
            // Use the smaller pool's reserves as the base for depth calculation
            let smaller_pool_reserves = pool_a_x.min(pool_b_x);
            
            // Calculate vesica piscis depth using the sacred geometry formula
            let depth = vesica::calculate_amount_to_equalize(
                pool_a_x,
                pool_b_x,
                price_deviation,
            );
            
            // Normalize depth relative to pool size (0.0 to 1.0 scale)
            let normalized_depth = if smaller_pool_reserves > Decimal::ZERO {
                (depth / smaller_pool_reserves).min(dec!(1.0))
            } else {
                dec!(0.0)
            };
            
            total_depth += normalized_depth;
            depth_count += 1;
            
            debug!(
                "VESICA PISCIS: Pool {} -> {} | Price A: {:.4}, Price B: {:.4}, Deviation: {:.4}, Depth: {:.4}, Normalized: {:.4}",
                i, i + 1, price_a, price_b, price_deviation, depth, normalized_depth
            );
        }
        
        // Average depth across all pool pairs
        let average_depth = if depth_count > 0 {
            total_depth / Decimal::from(depth_count)
        } else {
            dec!(0.5) // Neutral fallback
        };
        
        debug!(
            "VESICA PISCIS: Average arbitrage depth across {} pool pairs: {:.4}",
            depth_count, average_depth
        );
        
        Ok(average_depth.max(dec!(0.0)).min(dec!(1.0)))
    }
    
    /// AUDIT-FIX: Get fallback price for tokens based on common knowledge
    fn get_fallback_price(&self, token_symbol: &str) -> rust_decimal::Decimal {
        use rust_decimal_macros::dec;
        
        match token_symbol {
            "WETH" | "ETH" => dec!(2000.0),  // Approximate ETH price
            "WBTC" | "BTC" => dec!(40000.0), // Approximate BTC price
            "USDC" | "USDT" | "DAI" => dec!(1.0), // Stablecoins
            "UNI" => dec!(10.0),
            "LINK" => dec!(15.0),
            "AAVE" => dec!(100.0),
            _ => dec!(1.0), // Default fallback for unknown tokens
        }
    }
}


#[cfg(test)]
mod real_geometric_scorer_tests {
    use super::*;
    use ethers::types::Address;

    fn create_test_pool(reserve0: f64, reserve1: f64, token0: &str, token1: &str) -> ArbitragePool {
        ArbitragePool {
            address: Address::zero(),
            reserve0: Decimal::from_f64(reserve0).unwrap(),
            reserve1: Decimal::from_f64(reserve1).unwrap(),
            token0_symbol: token0.to_string(),
            token1_symbol: token1.to_string(),
            protocol: "Test".to_string(),
        }
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_basic() {
        // Create a mock price oracle for testing
        use crate::data::oracle::MockPriceOracle;
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        let path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
            create_test_pool(1200.0, 2200.0, "USDC", "DAI"),
        ];

        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Should have reasonable scores
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.convexity_ratio <= Decimal::ONE);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias <= Decimal::ONE);
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_anchor_assets() {
        // Create a mock price oracle for testing with realistic prices
        use crate::data::oracle::MockPriceOracle;
        use rust_decimal_macros::dec;
        
        let mut mock_oracle = MockPriceOracle::new();
        // Set up realistic prices for anchor assets
        mock_oracle.set_price(Address::zero(), dec!(2000.0)); // Default price for all tokens
        
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        // Path with all anchor assets (should have low bias)
        let anchor_path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
            create_test_pool(1200.0, 2200.0, "USDC", "DAI"),
        ];

        // Path with no anchor assets (should have high bias)
        let non_anchor_path = vec![
            create_test_pool(1000.0, 2000.0, "PEPE", "DOGE"),
            create_test_pool(1500.0, 1800.0, "SHIB", "FLOKI"),
            create_test_pool(1200.0, 2200.0, "MEME", "WIF"),
        ];

        let anchor_score = scorer.calculate_score(&anchor_path).await.unwrap();
        let non_anchor_score = scorer.calculate_score(&non_anchor_path).await.unwrap();

        // AUDIT-FIX: Anchor path should have lower bias than non-anchor path
        // (Lower bias means more central/stable, which anchor assets should be)
        assert!(
            anchor_score.liquidity_centroid_bias < non_anchor_score.liquidity_centroid_bias,
            "Anchor assets should have lower liquidity centroid bias (more central). Anchor: {:.3}, Non-anchor: {:.3}",
            anchor_score.liquidity_centroid_bias,
            non_anchor_score.liquidity_centroid_bias
        );
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_short_path() {
        // Create a mock price oracle for testing
        use crate::data::oracle::MockPriceOracle;
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        // Path with only 2 pools (too short for meaningful geometric analysis)
        let short_path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
        ];

        let score = scorer.calculate_score(&short_path).await.unwrap();
        
        // Should return neutral scores
        assert_eq!(score.convexity_ratio, dec!(0.5));
        assert_eq!(score.liquidity_centroid_bias, dec!(0.5));
    }
}

