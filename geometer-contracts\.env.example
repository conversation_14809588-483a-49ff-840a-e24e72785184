# Geometer Contracts Environment Configuration
# Copy this file to .env and fill in your actual values

# CRITICAL: Private key for contract deployment (without 0x prefix)
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here

# RPC URLs for deployment
BASE_RPC_URL=https://mainnet.base.org
DEGEN_RPC_URL=https://rpc.degen.tips

# API Keys for contract verification
BASESCAN_API_KEY=your_basescan_api_key

# Contract addresses for deployment (will be auto-populated after deployment)
AAVE_POOL_ADDRESSES_PROVIDER=0xe20fCBdBfFC4Dd138cE8b2E6FBb6CB49777ad64D
STARGATE_ROUTER=0x53Bf833A5d6c4ddA888F69c22C88E1f34A4451a5

# Gas settings for deployment
GAS_PRICE_GWEI=1
GAS_LIMIT=5000000