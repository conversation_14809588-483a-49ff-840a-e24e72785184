# Configuration Guide

This document provides a comprehensive guide to configuring the Basilisk Bot. The configuration is managed through a set of TOML files located in this directory.

## Table of Contents

- [Configuration Files](#configuration-files)
- [Configuration Sections](#configuration-sections)
  - [Global Settings](#global-settings)
  - [Core Infrastructure](#core-infrastructure)
  - [Blockchain & Chains](#blockchain--chains)
  - [Execution](#execution)
  - [Risk Management](#risk-management)
  - [Strategies](#strategies)
  - [Scanners](#scanners)
- [Local Configuration](#local-configuration)

## Configuration Files

-   **`default.toml`**: This file contains the default configuration for the bot. It is not recommended to edit this file directly.
-   **`local.toml`**: This file is used to override the default settings. It is not tracked by version control, making it a safe place to store sensitive information like private keys.
-   **`simulation.toml`**: This file contains a configuration specifically for running the bot in simulation mode.

## Configuration Sections

### Global Settings

These settings control the bot's overall behavior.

-   `dry_run`: If `true`, the bot will not execute real trades. Defaults to `true`.
-   `active_chain_id`: The primary blockchain the bot will operate on.

### Core Infrastructure

These settings configure the bot's connections to external services.

-   **`[database]`**:
    -   `url`: The URL of the PostgreSQL database.
-   **`[redis]`**:
    -   `url`: The URL of the Redis server.
-   **`[nats]`**:
    -   `url`: The URL of the NATS server.

### Blockchain & Chains

This section contains the configuration for each supported blockchain.

-   **`[bridges]`**:
    -   `routes`: A list of bridge routes for cross-chain migration.
-   **`[chains.<chain_id>]`**:
    -   `name`: The name of the chain (e.g., "Base").
    -   `enabled`: Whether the chain is enabled.
    -   `native_currency`: The native currency of the chain (e.g., "ETH").
    -   `rpc_endpoints`: A list of RPC endpoints for the chain.

### Execution

These settings control how the bot executes transactions.

-   `private_key`: The private key of the trading account. **It is strongly recommended to set this using the `BASILISK_EXECUTION_PRIVATE_KEY` environment variable.**
-   `relay_url`: The URL of the Flashbots relay. **It is strongly recommended to set this using the `FLASHBOTS_RELAY_URL` environment variable.**
-   `gas_limit`: The gas limit for transactions.
-   `max_priority_fee`: The maximum priority fee for transactions.
-   `default_slippage_tolerance`: The default slippage tolerance for trades.

### Risk Management

These settings configure the bot's risk management parameters.

-   `max_position_size`: The maximum size of a single position in ETH.
-   `max_slippage`: The maximum allowable slippage for a trade.
-   `kelly_fraction`: The fraction of the Kelly Criterion to use for position sizing.
-   `max_daily_loss`: The maximum daily loss in USD before the circuit breaker is triggered.

### Strategies

This section contains the configuration for the different trading strategies.

-   `min_potential_profit_usd`: The minimum potential profit in USD for an opportunity to be considered.
-   `min_net_profit_usd`: The minimum net profit in USD for an opportunity to be executed.
-   **`[strategies.unified]`**:
    -   `enabled`: Whether the unified strategy is enabled.
    -   `min_execution_score`: The minimum score required for an opportunity to be executed.
    -   `risk_aversion_k`: A parameter that controls the strategy's risk aversion.
    -   `min_quality_ratio`: The minimum quality ratio for an opportunity to be considered.

### Scanners

These settings configure the different opportunity scanners.

-   **`[scanners.gaze_scanner]`**:
    -   `min_price_deviation_pct`: The minimum price deviation in percent for an opportunity to be considered.
    -   `block_check_delay_ms`: The delay in milliseconds between block checks.
-   **`[scanners.pilot_fish_scanner]`**:
    -   `min_whale_trade_usd`: The minimum trade size in USD to be considered a "whale trade".
    -   `profit_multiplier`: A multiplier used to calculate the potential profit from a whale trade.

## Local Configuration

To override the default settings, create a `local.toml` file in the `config` directory. For example:

```toml
# Global settings
dry_run = false # Enable live trading

# Chain-specific configurations
[chains.8453] # Base Mainnet
enabled = true

# Risk management parameters
[risk]
max_position_size = 2.5 # ETH
max_daily_loss = -2000.0 # Max daily loss in USD

# Strategy configuration
[strategies]
min_potential_profit_usd = 100.0
min_net_profit_usd = 25.0
```

**Note**: It is strongly recommended to manage sensitive information, such as your private key, through environment variables rather than storing them in the `local.toml` file.