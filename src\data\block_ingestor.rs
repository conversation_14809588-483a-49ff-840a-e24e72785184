use futures_util::StreamExt;
// BlockIngestor: Low-latency block ingestion service optimized for MEV strategies
//
// This specialized service connects to a high-performance block relay provider (like bloXroute)
// and ingests full blocks with all transaction data as quickly as possible. It normalizes
// the block data into our internal FullBlock format and publishes it to NATS.
//
// The service is critical for time-sensitive strategies like the CL Tick Raider that need
// to analyze and react to new blocks within milliseconds to capture MEV opportunities.
//
// Unlike the generic chain monitor, this service is focused solely on block data and
// optimized for minimal latency.

use crate::shared_types::NatsTopics;
use async_nats::Client as NatsClient;
use ethers::{
    providers::{Middleware, Provider, Ws},
    types::{Block, Transaction, H256, U256},
};
use serde::{Deserialize, Serialize};
use std::error::Error;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

// Full block structure that includes all transactions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FullBlock {
    pub chain_id: u64,
    pub block_number: u64,
    pub block_hash: H256,
    pub parent_hash: H256,
    pub timestamp: u64,
    pub gas_used: U256,
    pub gas_limit: U256,
    pub base_fee_per_gas: Option<U256>,
    pub transactions: Vec<Transaction>,
    pub received_timestamp: u64,
}

pub struct BlockIngestor {
    chain_id: u64,
    ws_url: String,
    nats_client: NatsClient,
    provider_name: String, // e.g., "bloxroute", "infura", etc.
}

impl BlockIngestor {
    pub fn new(
        chain_id: u64,
        ws_url: String,
        nats_client: NatsClient,
        provider_name: String,
    ) -> Self {
        Self {
            chain_id,
            ws_url,
            nats_client,
            provider_name,
        }
    }

    pub async fn start(&self) -> Result<(), Box<dyn Error>> {
        info!(
            "Starting block ingestor for chain ID {} using provider {}",
            self.chain_id, self.provider_name
        );

        // Reconnection loop
        let mut retry_count = 0;
        let max_retries = 10;
        let mut backoff_duration = Duration::from_secs(1);

        loop {
            info!(
                "Connecting to {} WebSocket for chain ID {} (attempt {})",
                self.provider_name,
                self.chain_id,
                retry_count + 1
            );

            match self.connect_and_subscribe().await {
                Ok(_) => {
                    // If we reach here, the subscription has ended
                    warn!(
                        "Block subscription ended for chain ID {}. Reconnecting...",
                        self.chain_id
                    );

                    // Reset retry count on successful connection that later disconnected
                    retry_count = 0;
                    backoff_duration = Duration::from_secs(1);
                }
                Err(e) => {
                    error!(
                        "Failed to connect to {} WebSocket for chain ID {}: {}",
                        self.provider_name, self.chain_id, e
                    );

                    // Increment retry count
                    retry_count += 1;

                    // Check if we've exceeded max retries
                    if retry_count >= max_retries {
                        error!(
                            "Max retries ({}) exceeded for chain ID {}",
                            max_retries, self.chain_id
                        );
                        return Err(format!(
                            "Failed to connect to {} after {} attempts",
                            self.provider_name, max_retries
                        )
                        .into());
                    }
                }
            }

            // Exponential backoff with jitter
            let jitter = rand::random::<u64>() % 1000;
            let backoff_ms = backoff_duration.as_millis() as u64 + jitter;
            info!(
                "Waiting {}ms before reconnecting to {} for chain ID {}",
                backoff_ms, self.provider_name, self.chain_id
            );
            time::sleep(Duration::from_millis(backoff_ms)).await;

            // Increase backoff for next attempt (capped at 30 seconds)
            backoff_duration = std::cmp::min(backoff_duration * 2, Duration::from_secs(30));
        }
    }

    async fn connect_and_subscribe(&self) -> Result<(), Box<dyn Error>> {
        // Connect to WebSocket provider
        let ws_provider = Provider::<Ws>::connect(&self.ws_url).await?;
        let provider = Arc::new(ws_provider);
        info!(
            "Connected to {} WebSocket for chain ID {}",
            self.provider_name, self.chain_id
        );

        // Subscribe to new blocks
        let mut block_stream = provider.subscribe_blocks().await?;
        info!(
            "Subscribed to new blocks for chain ID {} via {}",
            self.chain_id, self.provider_name
        );

        // Process incoming blocks
        while let Some(block) = block_stream.next().await {
            if let Some(block_number) = block.number {
                info!(
                    "Received block #{} for chain ID {} via {}",
                    block_number, self.chain_id, self.provider_name
                );

                // Get full block with transactions
                match provider.get_block_with_txs(block_number).await {
                    Ok(Some(full_block)) => {
                        debug!(
                            "Retrieved full block #{} with {} transactions",
                            block_number,
                            full_block.transactions.len()
                        );

                        // Convert to our internal FullBlock type
                        let normalized_block = self.normalize_block(full_block);

                        // Publish to NATS
                        if let Err(e) = self.publish_block(normalized_block).await {
                            error!("Failed to publish block: {}", e);
                        }
                    }
                    Ok(None) => {
                        warn!(
                            "Block #{} not found when fetching transactions",
                            block_number
                        );
                    }
                    Err(e) => {
                        error!("Error fetching block with transactions: {}", e);
                    }
                }
            }
        }

        warn!(
            "Block subscription ended for chain ID {} via {}",
            self.chain_id, self.provider_name
        );
        Ok(())
    }

    fn normalize_block(&self, block: Block<Transaction>) -> FullBlock {
        // Create a timestamp for when we received this block
        let received_timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        FullBlock {
            chain_id: self.chain_id,
            block_number: block.number.unwrap_or_default().as_u64(),
            block_hash: block.hash.unwrap_or_default(),
            parent_hash: block.parent_hash,
            timestamp: block.timestamp.as_u64(),
            gas_used: block.gas_used,
            gas_limit: block.gas_limit,
            base_fee_per_gas: block.base_fee_per_gas,
            transactions: block.transactions,
            received_timestamp,
        }
    }

    async fn publish_block(&self, block: FullBlock) -> Result<(), Box<dyn Error>> {
        // Serialize the block
        let payload = serde_json::to_string(&block)?;

        // Publish to NATS with provider-specific topic
        let topic = format!("data.chain.blocks.raw.{}", self.provider_name);
        self.nats_client.publish(topic, payload.into()).await?;
        debug!(
            "Published block #{} to NATS via {}",
            block.block_number, self.provider_name
        );

        Ok(())
    }
}
