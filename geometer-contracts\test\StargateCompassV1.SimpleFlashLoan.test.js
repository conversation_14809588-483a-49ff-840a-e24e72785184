const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 - Simple Flash Loan Test', function () {
  async function deploySimpleFixture() {
    const [owner] = await ethers.getSigners();

    // Deploy a real ERC20 token for testing
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    await mockAaveProvider.setPool(mockPool.target);

    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('1'),
    });

    return {
      stargateCompass,
      owner,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  it('Should test what happens when we provide real USDC tokens', async function () {
    const { stargateCompass, owner, mockPool, mockStargateRouter, mockUSDC } =
      await loadFixture(deploySimpleFixture);

    const loanAmount = ethers.parseUnits('1000', 6);
    const expectedProfit = ethers.parseUnits('10', 6);
    const minAmountOut = ethers.parseUnits('980', 6);

    console.log('=== Testing with Real USDC Tokens ===');

    // Step 1: Deploy USDC at the expected address using a different approach
    // Since we can't deploy at a specific address easily, let's modify our mock pool
    // to actually transfer real tokens

    // Mint USDC to the mock pool
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('100000', 6));
    console.log(
      `Mock pool USDC balance: ${await mockUSDC.balanceOf(mockPool.target)}`
    );

    // Set up mocks
    await mockPool.setFlashLoanSuccess(true);
    await mockStargateRouter.setQuoteLayerZeroFee(ethers.parseEther('0.01'), 0);
    await mockStargateRouter.setSwapSuccess(true);

    // The issue is that our contract expects the real USDC address
    // but we're using a mock USDC. Let's see what happens if we try to use
    // the mock USDC address instead

    console.log(
      `Real USDC address expected by contract: ******************************************`
    );
    console.log(`Mock USDC address: ${mockUSDC.target}`);

    // Try the operation - this will likely fail because of address mismatch
    try {
      await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        '0x1234',
        owner.address,
        minAmountOut,
        expectedProfit
      );
      console.log('✅ Operation succeeded!');
    } catch (error) {
      console.log('❌ Operation failed as expected due to address mismatch');
      console.log(`Error: ${error.message}`);
    }
  });

  it('Should test the mock pool balance system', async function () {
    const { mockPool } = await loadFixture(deploySimpleFixture);

    const realUSDCAddress = '******************************************';
    const testAmount = ethers.parseUnits('1000', 6);

    console.log('=== Testing Mock Pool Balance System ===');

    // Set mock balance
    await mockPool.setMockBalance(realUSDCAddress, testAmount);
    console.log(`Set mock balance for ${realUSDCAddress}: ${testAmount}`);

    // The mock pool should now "have" this balance for the real USDC address
    console.log('Mock balance system configured successfully');
  });
});
