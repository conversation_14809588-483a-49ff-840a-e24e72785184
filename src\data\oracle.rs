// Price Oracle for geometric calculations
use ethers::types::{Address, U256};
use rust_decimal::Decimal;
use std::collections::HashMap;
use async_trait::async_trait;
use crate::error::Result;

#[async_trait]
pub trait PriceOracle: Send + Sync {
    async fn get_price(&self, token: Address) -> Result<Decimal>;
    async fn get_prices(&self, tokens: &[Address]) -> Result<HashMap<Address, Decimal>>;
    async fn get_token_symbols(&self, token_a: Address, token_b: Address) -> Result<(String, String)>;
}

#[derive(Debug, Clone)]
pub struct MockPriceOracle {
    prices: HashMap<Address, Decimal>,
    symbols: HashMap<Address, String>,
}

impl MockPriceOracle {
    pub fn new() -> Self {
        Self {
            prices: HashMap::new(),
            symbols: HashMap::new(),
        }
    }

    pub fn set_price(&mut self, token: Address, price: Decimal) {
        self.prices.insert(token, price);
    }

    pub fn set_symbol(&mut self, token: Address, symbol: String) {
        self.symbols.insert(token, symbol);
    }
}

#[async_trait]
impl PriceOracle for MockPriceOracle {
    async fn get_price(&self, token: Address) -> Result<Decimal> {
        self.prices.get(&token)
            .copied()
            .ok_or_else(|| crate::error::BasiliskError::data_ingestion(format!("Price not found for token: {:?}", token)).into())
    }

    async fn get_prices(&self, tokens: &[Address]) -> Result<HashMap<Address, Decimal>> {
        let mut result = HashMap::new();
        for token in tokens {
            if let Some(price) = self.prices.get(token) {
                result.insert(*token, *price);
            }
        }
        Ok(result)
    }

    async fn get_token_symbols(&self, token_a: Address, token_b: Address) -> Result<(String, String)> {
        let symbol_a = self.symbols.get(&token_a)
            .cloned()
            .unwrap_or_else(|| format!("TOKEN_{:?}", token_a));
        let symbol_b = self.symbols.get(&token_b)
            .cloned()
            .unwrap_or_else(|| format!("TOKEN_{:?}", token_b));
        Ok((symbol_a, symbol_b))
    }
}