### **`/docs/strategies/nomadic_hunter_guide.md` (OROBOROS Edition)**

# **Implementation Guide: The Nomadic Predator Doctrine**

**To:** Coder Agent
**From:** The Architect
**Subject:** Mission Directive: Implementing "Territory Hopping" with OROBOROS Resilience

**Mission:** You will now implement the **"Nomadic Predator"** doctrine. This is a meta-strategy that gives the Basilisk "legs," allowing it to survey the entire EVM ecosystem and migrate its entire operation to the most profitable territory. This implementation will be deeply integrated with **`Protocol: OROBOROS`**, creating a bot with two distinct layers of resilience: automated tactical failover and operator-commanded strategic relocation.

### **Doctrine: The Self-Healing Nomad**

The philosophy is now a two-tiered survival strategy:

1.  **Tactical Resilience (OROBOROS):** When a single "watering hole" (a primary RPC endpoint) dries up, the bot does not immediately abandon the entire territory. Its autonomic nervous system, `OROBOROS`, triggers an **automated tactical failover**, seamlessly switching to the next-best RPC endpoint within the _same_ territory to continue the hunt with minimal disruption.
2.  **Strategic Relocation (Nomadic Hunting):** When the _entire territory_ becomes barren or too dangerous (as determined by the `EcologicalSurveyor`), the operator is advised of a better hunting ground. On a single command, the operator can initiate a **strategic migration**, moving the bot's entire capital base and focus to a new, more fertile ecosystem.

This creates a predator that can both survive immediate hardship and has the wisdom to know when to abandon a failed ecosystem entirely.

### **Phase 1: The `Atlas` - A Resilient, Multi-Chain Configuration**

Before the bot can travel or heal, its map of the world must support deep redundancy.

- **The Concept:** We will refactor our `config.toml` file into an "Atlas." The key change is that the `rpc_endpoints` for each chain will no longer be a simple list of strings. It will be a **prioritized list of objects**, allowing `OROBOROS` to know which endpoint to try next.
- **Implementation:** The `Config` struct in `src/config.rs` will be refactored to deserialize this richer structure. A new `RpcEndpoint` struct (`{ url: String, priority: u32 }`) will be created. The `ChainConfig` struct will now contain a `Vec<RpcEndpoint>`. This powerful Rust pattern makes the configuration both extensible and readable, providing all the necessary data for the OROBOROS protocol to function.

### **Phase 2: The `EcologicalSurveyor` - Eyes on All Horizons**

This module's implementation remains the same, but its importance is now elevated as it provides the strategic intelligence that complements OROBOROS's tactical healing.

- **The Concept:** The `MarketStateAnalyzer` maintains lightweight connections to _all_ chains defined in the "Atlas." It continuously calculates a "Territory Health Score" for each one.
- **The "Health Score" Formula:** The heuristic remains `Health_Score = (Avg_DEX_Volume / Avg_Gas_Price) * Volatility`. This score provides the high-level strategic overview.
- **Implementation:** The `run_ecological_survey` loop within the `MarketStateAnalyzer` calculates these scores and publishes the full list to the `intelligence.ecosystem.health` NATS topic. The TUI subscribes to this topic to advise the operator.

### **Phase 3: The `OROBOROS` Protocol - Automated Tactical Failover**

This is where we integrate the new configuration into the bot's self-healing instinct.

- **The Concept:** The `SystemMonitor` service is the heart of `OROBOROS`. When it detects a service failure related to an RPC connection, it will now act as a "pathfinder," finding the next available endpoint in the prioritized list for the bot's _current_ territory.
- **The Logic Flow:**
  1.  A service's (e.g., `GazeScanner`) heartbeat to `state.health.gaze` reports an `Error("RPC Connection Failed")` and includes its `current_rpc` URL.
  2.  The `SystemMonitor` detects this error.
  3.  It reads the `active_chain_id` from its shared `Config`.
  4.  It looks up the configuration for that chain and finds the `rpc_endpoints` list.
  5.  It finds the entry corresponding to the `current_rpc` and gets its `priority`.
  6.  It then searches the list for the endpoint with the next sequential `priority`.
  7.  Having found the `backup_url`, it constructs and publishes a targeted `control.config.update` message containing this new URL, specifically for the `GazeScanner`.
- **The Healing:** All services with RPC connections must listen for these targeted updates. Upon receiving one, they gracefully tear down their old connection and re-initialize with the new URL. Their next heartbeat will report a `Warning("Operating on Backup RPC")` state, making the failover transparent to the operator via the TUI.

### **Phase 4: "The Migration" - Operator-Commanded Strategic Relocation**

The migration workflow remains an elegant, safety-first, operator-driven process. The integration with OROBOROS is implicit: if the bot is already running on a backup RPC within a territory, it's a strong signal to the operator that the territory's infrastructure is weak and a full migration might be the best strategic choice.

- **The Concept:** The operator, advised by the `EcologicalSurveyor`, makes the final call to move. This triggers a controlled shutdown and asset bridge, orchestrated by the `MigrationController`.
- **The `MigrationController` (`src/control/migration.rs`):** This service executes its state machine:
  1.  **PAUSE:** Broadcasts a system-wide pause command.
  2.  **BRIDGE:** Executes the on-chain transaction to move the single asset pool via a trusted bridge.
  3.  **CONFIRM:** Polls the destination chain for fund arrival.
  4.  **ADVISE & SHUTDOWN:** Publishes a final, clear message to the TUI (`MIGRATION COMPLETE... Please update config and restart.`) and then triggers a graceful shutdown of the application.

This two-layered approach creates the ultimate Nomadic Predator: a bot that can automatically survive tactical problems (a single bad RPC) while providing the operator with the high-level intelligence needed to make strategic decisions about larger environmental changes (a failing ecosystem).

Of course. The doctrine has evolved from a simple nomad to a strategic general who sends out scouts before moving the main army. This requires a clear, descriptive guide to implement.

This document provides the complete, descriptive implementation guide for the **"Scout & Advance Party"** protocol. It details the philosophy, the architectural changes, and the operational flow needed to transform the Basilisk into a truly capital-efficient, multi-chain predator.

---

### **`/docs/strategies/nomadic_hunter_guide.md` (Scout & Advance Party Edition)**

# **Implementation Guide: The Nomadic Predator Doctrine**

**To:** Coder Agent
**From:** The Architect
**Subject:** Mission Directive: Implementing the "Scout & Advance Party" Protocol

**Mission:** You will now implement the **"Scout & Advance Party"** protocol. This is a profound evolution of the Nomadic Hunter doctrine. We will move beyond the "all-or-nothing" migration strategy and forge a leaner, more intelligent system of territorial expansion. The bot will learn to send a small, autonomous "scout" to new hunting grounds to verify their profitability _before_ the operator commits the main treasury to a full migration.

### **Doctrine: The General and the Scout**

The core philosophy is a shift from a simple nomad to a strategic general. A general does not move their entire army based on a map alone. They first send a small, fast reconnaissance party to probe the terrain, test enemy defenses, and confirm the value of the new territory.

The Basilisk will now operate with this same wisdom:

1.  **The Main Army (Treasury Wallet):** The majority of the bot's capital remains safe and productive on its current, proven hunting ground. **It never stops hunting.**
2.  **The Recon Party (Scout Wallet):** A small, firewalled percentage of the capital is deployed in a nimble "scout" bot.
3.  **Data-Driven Decisions:** The decision to execute a full, costly migration is no longer based on a theoretical "Health Score." It is based on **hard, empirical P&L data** returned by the successful scouting mission.

This doctrine dramatically reduces risk, minimizes capital downtime, and lowers the cost of exploring new ecosystems, making it the perfect strategy for a capital-efficient operator.

---

### **The Three Phases of Implementation**

This is not a single service, but a series of precise upgrades to our existing modules and a new, more nuanced role for the `MigrationController`.

#### **Phase 1: The Armory - A Multi-Tiered Capital Configuration**

Before we can dispatch a scout, we must equip it. This requires refactoring our configuration to support a segregated capital structure.

- **The Concept:** We will define two distinct wallets in our `config.toml`: a main `treasury_wallet` and a `scout_wallet`. We will also define the percentage of capital to be allocated to scouting missions.
- **Implementation:** The `Config` struct in `src/config.rs` will be refactored to hold these new parameters. The `[wallets]` table will contain the environment variable names for the two private keys. The `[capital_allocation]` table will define the `scout_allocation_percent` and the `scout_mission_duration_hours`. This structure provides the bot with the necessary tools to manage its two "armies."

#### **Phase 2: The Watchtower - Identifying "Scouting Missions"**

The `EcologicalSurveyor` must evolve from a simple reporter into a "Scout Commander" that actively identifies and proposes missions.

- **The Concept:** The surveyor will not just report on which territory is "healthy." It will look for a specific, prime opportunity: a territory that is not only healthy but also has **extremely low gas fees**. This combination represents the perfect, low-risk environment for a scouting run.
- **Implementation:** The `EcologicalSurveyor`'s logic will be enhanced. After calculating the `Territory Health Score`, it will perform a check: `if score > X && avg_gas < Y`. If this condition is met, it will not just update the TUI status. It will publish a new, specific intelligence packet to a dedicated NATS topic: `intelligence.migration.proposal`. This packet contains the `target_chain_id` and the `reason` for the proposal, transforming a passive observation into an actionable mission plan.

#### **Phase 3: The Scouting Mission - A New, Lean Migration**

This is the heart of the new doctrine. The `MigrationController` and the TUI will be upgraded to support a new, lightweight "Send Scout" command.

- **The TUI Enhancement:** The "Ecosystem" tab becomes more interactive. When a `intelligence.migration.proposal` is received, the TUI will display a new status for that territory: `🌟 SCOUT MISSION RECOMMENDED`. Selecting this territory will now give the operator two distinct choices: `[S]end Scout` or `[M]igrate All`.

- **The `MigrationController`'s New Workflow (`Send Scout`):**

  1.  **No Pause:** Upon receiving the `SendScout` command, the controller **does not pause the main bot**. The main army continues its hunt unabated.
  2.  **Fund the Scout:** The controller calculates the `scout_allocation_percent` of the treasury's capital. It then constructs and executes a bridge transaction to move this small, specific amount from the `treasury_wallet` to the `scout_wallet` on the destination chain.
  3.  **Deploy the Scout Instance:** This is the key architectural innovation. The `MigrationController` will **spawn a new, separate OS process** that runs a lightweight instance of the Basilisk bot. This "scout bot" is configured with:
      - The `scout_wallet_pk`.
      - The `target_chain_id`.
      - A simplified set of strategies (e.g., only `GazeScanner` and `MempoolScanner`).
      - A built-in timer set to `scout_mission_duration_hours`.
  4.  **Report & Monitor:** The controller logs a message to the main TUI—`SYSTEM: Scout party dispatched to Polygon for a 12-hour mission.`—and then returns to an idle state.

- **The Scout's Lifecycle (Autonomous Return):**
  1.  **The Hunt:** The temporary scout bot operates independently on the new chain, logging its trades to the same shared `SANCTUM` database, but tagged with its unique `chain_id` and a `mission_id`.
  2.  **The Return:** Upon its timer expiring, the scout bot automatically triggers its "return home" protocol. It pauses its own hunting, bridges its capital _plus any profits_ back to the `treasury_wallet` on the source chain, and then gracefully terminates its own process.

This new workflow is the pinnacle of capital efficiency. It allows the bot to gather **real, empirical P&L data** from new ecosystems with minimal cost, zero downtime for the main bot, and a dramatically reduced risk profile. It transforms a risky migration into a data-driven strategic decision.
