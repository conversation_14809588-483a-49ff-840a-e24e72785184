# Zen Geometer Pre-Deployment Audit Environment
# Comprehensive Docker Compose setup for exhaustive pre-deployment validation
# 
# Usage:
#   Full audit environment:    docker compose -f docker-compose.audit.yml up -d
#   Core services only:        docker compose -f docker-compose.audit.yml up -d nats timescaledb redis
#   With monitoring:           docker compose -f docker-compose.audit.yml up -d nats timescaledb redis prometheus grafana
#   Cleanup:                   docker compose -f docker-compose.audit.yml down -v
#   View logs:                 docker compose -f docker-compose.audit.yml logs -f
#   Check status:              docker compose -f docker-compose.audit.yml ps

services:
  # NATS Message Bus - Core communication backbone for audit testing
  nats:
    image: nats:2.10-alpine
    container_name: basilisk_audit_nats
    ports:
      - "4222:4222"  # Client connections
      - "8222:8222"  # HTTP monitoring
      - "6222:6222"  # Cluster connections
    command: [
      "nats-server",
      "--jetstream",
      "--store_dir=/data",
      "--http_port=8222",
      "--max_payload=8MB",
      "--max_pending=256MB"
    ]
    volumes:
      - audit_nats_data:/data
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    labels:
      - "com.basilisk_audit.service=message_bus"
      - "com.basilisk_audit.component=core_infrastructure"

  # TimescaleDB - Time-series data storage for comprehensive audit analysis
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: basilisk_audit_timescaledb
    environment:
      POSTGRES_DB: basilisk_bot
      POSTGRES_USER: basilisk_bot
      POSTGRES_PASSWORD: basilisk_bot_secure_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
      # Audit-specific PostgreSQL tuning
      POSTGRES_SHARED_PRELOAD_LIBRARIES: timescaledb
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
    ports:
      - "5432:5432"
    volumes:
      - audit_postgres_data:/var/lib/postgresql/data
      - ./scripts/init_timescaledb.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U basilisk_bot -d basilisk_bot"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    labels:
      - "com.basilisk_audit.service=database"
      - "com.basilisk_audit.component=core_infrastructure"

  # Redis - Caching and state management for audit validation
  redis:
    image: redis:7-alpine
    container_name: basilisk_audit_redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru --save 60 1000
    volumes:
      - audit_redis_data:/data
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    labels:
      - "com.basilisk_audit.service=cache"
      - "com.basilisk_audit.component=core_infrastructure"

  # Prometheus - Metrics collection for audit observability
  prometheus:
    image: prom/prometheus:latest
    container_name: basilisk_audit_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - audit_prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'  # Shorter retention for audit
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.basilisk_audit.service=metrics"
      - "com.basilisk_audit.component=monitoring"

  # Grafana - Metrics visualization for audit dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: basilisk_audit_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: basilisk_audit_admin
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_SECURITY_ALLOW_EMBEDDING: true
      # Audit-specific settings
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
    volumes:
      - audit_grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    depends_on:
      prometheus:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.basilisk_audit.service=visualization"
      - "com.basilisk_audit.component=monitoring"

  # Loki - Log aggregation for audit trail analysis
  loki:
    image: grafana/loki:latest
    container_name: basilisk_audit_loki
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - audit_loki_data:/loki
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3100/ready"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.basilisk_audit.service=log_aggregation"
      - "com.basilisk_audit.component=monitoring"

  # pgAdmin - Database administration for audit data inspection
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: basilisk_audit_pgadmin
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: audit_admin_password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    volumes:
      - audit_pgadmin_data:/var/lib/pgadmin
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    depends_on:
      timescaledb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80/misc/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "com.basilisk_audit.service=database_admin"
      - "com.basilisk_audit.component=development_tools"

  # NATS CLI - Message bus debugging and audit validation
  nats-cli:
    image: natsio/nats-box:latest
    container_name: basilisk_audit_nats_cli
    networks:
      - basilisk_audit_network
    command: ["sleep", "infinity"]
    restart: unless-stopped
    depends_on:
      nats:
        condition: service_healthy
    labels:
      - "com.basilisk_audit.service=debug_tools"
      - "com.basilisk_audit.component=development_tools"

  # Redis CLI - Cache debugging and state inspection
  redis-cli:
    image: redis:7-alpine
    container_name: basilisk_audit_redis_cli
    networks:
      - basilisk_audit_network
    command: ["sleep", "infinity"]
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    labels:
      - "com.basilisk_audit.service=debug_tools"
      - "com.basilisk_audit.component=development_tools"

  # Anvil - Local blockchain simulation for contract audit testing
  anvil:
    image: ghcr.io/foundry-rs/foundry:latest
    container_name: basilisk_audit_anvil
    ports:
      - "8545:8545"
    command: [
      "anvil",
      "--host", "0.0.0.0",
      "--port", "8545",
      "--accounts", "10",
      "--balance", "10000",
      "--gas-limit", "********",
      "--gas-price", "**********",
      "--block-time", "1",
      "--chain-id", "31337"
    ]
    networks:
      - basilisk_audit_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8545"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    labels:
      - "com.basilisk_audit.service=blockchain_simulation"
      - "com.basilisk_audit.component=testing_infrastructure"

volumes:
  audit_nats_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=nats_storage"
      - "com.basilisk_audit.purpose=message_persistence"
  audit_postgres_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=database_storage"
      - "com.basilisk_audit.purpose=audit_data_persistence"
  audit_redis_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=cache_storage"
      - "com.basilisk_audit.purpose=state_persistence"
  audit_prometheus_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=metrics_storage"
      - "com.basilisk_audit.purpose=observability_data"
  audit_grafana_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=dashboard_storage"
      - "com.basilisk_audit.purpose=visualization_config"
  audit_loki_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=log_storage"
      - "com.basilisk_audit.purpose=audit_trail"
  audit_pgadmin_data:
    driver: local
    labels:
      - "com.basilisk_audit.volume=admin_storage"
      - "com.basilisk_audit.purpose=database_admin_config"

networks:
  basilisk_audit_network:
    driver: bridge
    labels:
      - "com.basilisk_audit.network=audit_environment"
      - "com.basilisk_audit.purpose=isolated_testing"