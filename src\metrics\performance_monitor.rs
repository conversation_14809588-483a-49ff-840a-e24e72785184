// src/metrics/performance_monitor.rs

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use serde::{Serialize, Deserialize};

/// AUDIT-FIX: Performance monitoring for operation timing - Task 5.2
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PerformanceMonitor {
    metrics: Arc<RwLock<HashMap<String, OperationMetrics>>>,
    config: PerformanceConfig,
}

#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    pub max_samples: usize,
    pub warning_threshold_ms: u64,
    pub critical_threshold_ms: u64,
    pub enable_detailed_logging: bool,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_samples: 1000,
            warning_threshold_ms: 1000,  // 1 second
            critical_threshold_ms: 5000, // 5 seconds
            enable_detailed_logging: false,
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct OperationMetrics {
    pub operation_name: String,
    pub execution_times_ms: Vec<u64>,
    pub total_executions: u64,
    pub successful_executions: u64,
    pub failed_executions: u64,
    pub last_execution: Option<Instant>,
    pub min_time_ms: u64,
    pub max_time_ms: u64,
    pub avg_time_ms: f64,
}

impl OperationMetrics {
    fn new(operation_name: String) -> Self {
        Self {
            operation_name,
            execution_times_ms: Vec::new(),
            total_executions: 0,
            successful_executions: 0,
            failed_executions: 0,
            last_execution: None,
            min_time_ms: u64::MAX,
            max_time_ms: 0,
            avg_time_ms: 0.0,
        }
    }
    
    fn add_execution(&mut self, duration_ms: u64, success: bool, max_samples: usize) {
        self.total_executions += 1;
        self.last_execution = Some(Instant::now());
        
        if success {
            self.successful_executions += 1;
        } else {
            self.failed_executions += 1;
        }
        
        // Update timing statistics
        self.execution_times_ms.push(duration_ms);
        if self.execution_times_ms.len() > max_samples {
            self.execution_times_ms.remove(0);
        }
        
        self.min_time_ms = self.min_time_ms.min(duration_ms);
        self.max_time_ms = self.max_time_ms.max(duration_ms);
        
        // Recalculate average
        if !self.execution_times_ms.is_empty() {
            let sum: u64 = self.execution_times_ms.iter().sum();
            self.avg_time_ms = sum as f64 / self.execution_times_ms.len() as f64;
        }
    }
    
    pub fn success_rate(&self) -> f64 {
        if self.total_executions == 0 {
            0.0
        } else {
            self.successful_executions as f64 / self.total_executions as f64
        }
    }
    
    pub fn get_percentile(&self, percentile: f64) -> Option<u64> {
        if self.execution_times_ms.is_empty() {
            return None;
        }
        
        let mut sorted_times = self.execution_times_ms.clone();
        sorted_times.sort_unstable();
        
        let index = ((percentile / 100.0) * (sorted_times.len() - 1) as f64).round() as usize;
        sorted_times.get(index).copied()
    }
}

impl PerformanceMonitor {
    pub fn new(config: PerformanceConfig) -> Self {
        Self {
            metrics: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }
    
    /// Start timing an operation
    pub fn start_operation(&self, operation_name: &str) -> OperationTimer {
        OperationTimer::new(operation_name.to_string(), self.clone())
    }
    
    /// Record a completed operation
    pub async fn record_operation(&self, operation_name: &str, duration: Duration, success: bool) {
        let duration_ms = duration.as_millis() as u64;
        
        // Check thresholds and log warnings
        if duration_ms > self.config.critical_threshold_ms {
            warn!(
                "PERFORMANCE CRITICAL: Operation '{}' took {}ms (threshold: {}ms)",
                operation_name, duration_ms, self.config.critical_threshold_ms
            );
        } else if duration_ms > self.config.warning_threshold_ms {
            warn!(
                "PERFORMANCE WARNING: Operation '{}' took {}ms (threshold: {}ms)",
                operation_name, duration_ms, self.config.warning_threshold_ms
            );
        } else if self.config.enable_detailed_logging {
            debug!(
                "PERFORMANCE: Operation '{}' completed in {}ms (success: {})",
                operation_name, duration_ms, success
            );
        }
        
        // Update metrics
        let mut metrics = self.metrics.write().await;
        let operation_metrics = metrics
            .entry(operation_name.to_string())
            .or_insert_with(|| OperationMetrics::new(operation_name.to_string()));
        
        operation_metrics.add_execution(duration_ms, success, self.config.max_samples);
    }
    
    /// Get metrics for a specific operation
    pub async fn get_operation_metrics(&self, operation_name: &str) -> Option<OperationMetrics> {
        let metrics = self.metrics.read().await;
        metrics.get(operation_name).cloned()
    }
    
    /// Get all operation metrics
    pub async fn get_all_metrics(&self) -> HashMap<String, OperationMetrics> {
        self.metrics.read().await.clone()
    }
    
    /// Get performance summary
    pub async fn get_performance_summary(&self) -> PerformanceSummary {
        let metrics = self.metrics.read().await;
        
        let mut total_operations = 0;
        let mut total_success = 0;
        let mut total_failures = 0;
        let mut slowest_operation = None;
        let mut fastest_operation = None;
        let mut slowest_time = 0;
        let mut fastest_time = u64::MAX;
        
        for (name, metric) in metrics.iter() {
            total_operations += metric.total_executions;
            total_success += metric.successful_executions;
            total_failures += metric.failed_executions;
            
            if metric.max_time_ms > slowest_time {
                slowest_time = metric.max_time_ms;
                slowest_operation = Some(name.clone());
            }
            
            if metric.min_time_ms < fastest_time && metric.min_time_ms > 0 {
                fastest_time = metric.min_time_ms;
                fastest_operation = Some(name.clone());
            }
        }
        
        let overall_success_rate = if total_operations > 0 {
            total_success as f64 / total_operations as f64
        } else {
            0.0
        };
        
        PerformanceSummary {
            total_operations,
            overall_success_rate,
            total_failures,
            slowest_operation,
            slowest_time_ms: if slowest_time > 0 { Some(slowest_time) } else { None },
            fastest_operation,
            fastest_time_ms: if fastest_time < u64::MAX { Some(fastest_time) } else { None },
            operations_count: metrics.len(),
        }
    }
    
    /// Clear all metrics (useful for testing or periodic cleanup)
    pub async fn clear_metrics(&self) {
        let mut metrics = self.metrics.write().await;
        metrics.clear();
        info!("Performance metrics cleared");
    }
    
    /// Get operations exceeding thresholds
    pub async fn get_slow_operations(&self) -> Vec<SlowOperation> {
        let metrics = self.metrics.read().await;
        let mut slow_ops = Vec::new();
        
        for metric in metrics.values() {
            if metric.avg_time_ms > self.config.warning_threshold_ms as f64 {
                slow_ops.push(SlowOperation {
                    name: metric.operation_name.clone(),
                    avg_time_ms: metric.avg_time_ms,
                    max_time_ms: metric.max_time_ms,
                    executions: metric.total_executions,
                    success_rate: metric.success_rate(),
                });
            }
        }
        
        // Sort by average time descending
        slow_ops.sort_by(|a, b| b.avg_time_ms.partial_cmp(&a.avg_time_ms).unwrap());
        slow_ops
    }
}

/// Timer for measuring operation duration
pub struct OperationTimer {
    operation_name: String,
    start_time: Instant,
    monitor: PerformanceMonitor,
}

impl OperationTimer {
    fn new(operation_name: String, monitor: PerformanceMonitor) -> Self {
        Self {
            operation_name,
            start_time: Instant::now(),
            monitor,
        }
    }
    
    /// Complete the operation and record metrics
    pub async fn complete(self, success: bool) {
        let duration = self.start_time.elapsed();
        self.monitor
            .record_operation(&self.operation_name, duration, success)
            .await;
    }
    
    /// Complete with automatic success detection based on Result
    pub async fn complete_with_result<T, E>(self, result: &Result<T, E>) {
        self.complete(result.is_ok()).await;
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSummary {
    pub total_operations: u64,
    pub overall_success_rate: f64,
    pub total_failures: u64,
    pub slowest_operation: Option<String>,
    pub slowest_time_ms: Option<u64>,
    pub fastest_operation: Option<String>,
    pub fastest_time_ms: Option<u64>,
    pub operations_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlowOperation {
    pub name: String,
    pub avg_time_ms: f64,
    pub max_time_ms: u64,
    pub executions: u64,
    pub success_rate: f64,
}

/// AUDIT-FIX: Memory usage monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryMetrics {
    pub heap_used_bytes: u64,
    pub heap_total_bytes: u64,
    pub stack_used_bytes: u64,
    pub process_memory_bytes: u64,
}

impl MemoryMetrics {
    /// Get current memory usage (platform-specific implementation)
    pub fn current() -> Self {
        // This is a simplified implementation
        // In production, you'd use platform-specific APIs or crates like `sysinfo`
        Self {
            heap_used_bytes: 0,
            heap_total_bytes: 0,
            stack_used_bytes: 0,
            process_memory_bytes: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};
    
    #[tokio::test]
    async fn test_performance_monitor_basic() {
        let monitor = PerformanceMonitor::new(PerformanceConfig::default());
        
        // Record some operations
        monitor.record_operation("test_op", Duration::from_millis(100), true).await;
        monitor.record_operation("test_op", Duration::from_millis(200), true).await;
        monitor.record_operation("test_op", Duration::from_millis(150), false).await;
        
        let metrics = monitor.get_operation_metrics("test_op").await.unwrap();
        assert_eq!(metrics.total_executions, 3);
        assert_eq!(metrics.successful_executions, 2);
        assert_eq!(metrics.failed_executions, 1);
        assert_eq!(metrics.success_rate(), 2.0 / 3.0);
    }
    
    #[tokio::test]
    async fn test_operation_timer() {
        let monitor = PerformanceMonitor::new(PerformanceConfig::default());
        
        let timer = monitor.start_operation("timed_op");
        sleep(Duration::from_millis(50)).await;
        timer.complete(true).await;
        
        let metrics = monitor.get_operation_metrics("timed_op").await.unwrap();
        assert_eq!(metrics.total_executions, 1);
        assert!(metrics.avg_time_ms >= 45.0); // Allow some variance
    }
    
    #[tokio::test]
    async fn test_performance_summary() {
        let monitor = PerformanceMonitor::new(PerformanceConfig::default());
        
        monitor.record_operation("fast_op", Duration::from_millis(10), true).await;
        monitor.record_operation("slow_op", Duration::from_millis(1000), true).await;
        
        let summary = monitor.get_performance_summary().await;
        assert_eq!(summary.total_operations, 2);
        assert_eq!(summary.overall_success_rate, 1.0);
        assert_eq!(summary.slowest_operation, Some("slow_op".to_string()));
        assert_eq!(summary.fastest_operation, Some("fast_op".to_string()));
    }
    
    #[tokio::test]
    async fn test_slow_operations_detection() {
        let mut config = PerformanceConfig::default();
        config.warning_threshold_ms = 100;
        let monitor = PerformanceMonitor::new(config);
        
        monitor.record_operation("fast_op", Duration::from_millis(50), true).await;
        monitor.record_operation("slow_op", Duration::from_millis(200), true).await;
        
        let slow_ops = monitor.get_slow_operations().await;
        assert_eq!(slow_ops.len(), 1);
        assert_eq!(slow_ops[0].name, "slow_op");
    }
}