const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 Parameter Validation Tests', function () {
  // Test fixture for contract deployment
  async function deployStargateCompassFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USDC', 'USDC', 6);

    // Set up mock provider to return mock pool
    await mockAaveProvider.setPool(mockPool.target);

    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  describe('Parameter Validation Constants', function () {
    it('Should have correct maximum calldata size constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.MAX_CALLDATA_SIZE()).to.equal(10000);
    });
  });

  describe('Address Validation', function () {
    describe('executeRemoteDegenSwap Address Validation', function () {
      it('Should revert when remoteSwapRouter is zero address', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const loanAmount = ethers.parseUnits('1000', 6);
        const remoteCalldata = '0x1234';
        const remoteSwapRouter = ethers.ZeroAddress; // Invalid zero address
        const minAmountOut = ethers.parseUnits('990', 6);
        const expectedProfit = ethers.parseUnits('10', 6);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            remoteCalldata,
            remoteSwapRouter,
            minAmountOut,
            expectedProfit
          )
        )
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAddress')
          .withArgs(ethers.ZeroAddress, 'remoteSwapRouter');
      });
    });

    describe('emergencyWithdraw Address Validation', function () {
      it('Should revert when token address is zero address', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        await expect(stargateCompass.emergencyWithdraw(ethers.ZeroAddress))
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAddress')
          .withArgs(ethers.ZeroAddress, 'token');
      });
    });
  });

  describe('Amount Validation', function () {
    describe('executeRemoteDegenSwap Amount Validation', function () {
      it('Should revert when loanAmount is zero', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const loanAmount = 0; // Invalid zero amount
        const remoteCalldata = '0x1234';
        const remoteSwapRouter = ethers.Wallet.createRandom().address;
        const minAmountOut = ethers.parseUnits('990', 6);
        const expectedProfit = ethers.parseUnits('10', 6);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            remoteCalldata,
            remoteSwapRouter,
            minAmountOut,
            expectedProfit
          )
        )
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount')
          .withArgs(0, 'loanAmount');
      });

      it('Should revert when minAmountOut is zero', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const loanAmount = ethers.parseUnits('1000', 6);
        const remoteCalldata = '0x1234';
        const remoteSwapRouter = ethers.Wallet.createRandom().address;
        const minAmountOut = 0; // Invalid zero amount
        const expectedProfit = ethers.parseUnits('10', 6);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            remoteCalldata,
            remoteSwapRouter,
            minAmountOut,
            expectedProfit
          )
        )
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount')
          .withArgs(0, 'minAmountOut');
      });

      it('Should revert when expectedProfit is zero', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        const loanAmount = ethers.parseUnits('1000', 6);
        const remoteCalldata = '0x1234';
        const remoteSwapRouter = ethers.Wallet.createRandom().address;
        const minAmountOut = ethers.parseUnits('990', 6);
        const expectedProfit = 0; // Invalid zero amount

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            remoteCalldata,
            remoteSwapRouter,
            minAmountOut,
            expectedProfit
          )
        )
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount')
          .withArgs(0, 'expectedProfit');
      });
    });

    describe('depositETH Amount Validation', function () {
      it('Should revert when deposit amount is zero', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        await expect(stargateCompass.depositETH({ value: 0 }))
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount')
          .withArgs(0, 'depositAmount');
      });
    });

    describe('withdrawETH Amount Validation', function () {
      it('Should revert when withdrawal amount is zero', async function () {
        const { stargateCompass } = await loadFixture(
          deployStargateCompassFixture
        );

        await expect(stargateCompass.withdrawETH(0))
          .to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount')
          .withArgs(0, 'withdrawalAmount');
      });
    });
  });

  describe('Calldata Size Validation', function () {
    it('Should revert when calldata exceeds maximum size', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      const loanAmount = ethers.parseUnits('1000', 6);
      // Create calldata that exceeds MAX_CALLDATA_SIZE (10,000 bytes)
      const largeCalldata = '0x' + '00'.repeat(10001); // 10,001 bytes
      const remoteSwapRouter = ethers.Wallet.createRandom().address;
      const minAmountOut = ethers.parseUnits('990', 6);
      const expectedProfit = ethers.parseUnits('10', 6);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          largeCalldata,
          remoteSwapRouter,
          minAmountOut,
          expectedProfit
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'CalldataTooLarge')
        .withArgs(10001, 10000);
    });

    it('Should accept calldata at maximum size limit', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      const loanAmount = ethers.parseUnits('1000', 6);
      // Create calldata exactly at MAX_CALLDATA_SIZE (10,000 bytes)
      const maxCalldata = '0x' + '00'.repeat(10000); // Exactly 10,000 bytes
      const remoteSwapRouter = ethers.Wallet.createRandom().address;
      const minAmountOut = ethers.parseUnits('990', 6);
      const expectedProfit = ethers.parseUnits('10', 6);

      // Should pass calldata size validation (may fail later due to other validations)
      try {
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          maxCalldata,
          remoteSwapRouter,
          minAmountOut,
          expectedProfit
        );
      } catch (error) {
        // Should not fail due to calldata size validation
        expect(error.message).to.not.include('CalldataTooLarge');
      }
    });
  });
});
