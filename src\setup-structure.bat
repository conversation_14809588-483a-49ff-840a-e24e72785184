@echo off
setlocal

set BASEDIR=C:\Users\<USER>\OneDrive\Documents\GitHub\basilisk_bot\src

:: Top-level files
echo. > "%BASEDIR%\config.rs"
echo. > "%BASEDIR%\shared_types.rs"

:: data/
mkdir "%BASEDIR%\data"
echo. > "%BASEDIR%\data\mod.rs"
echo. > "%BASEDIR%\data\cex_feed.rs"
echo. > "%BASEDIR%\data\chain_monitor.rs"

:: strategies/
mkdir "%BASEDIR%\strategies"
echo. > "%BASEDIR%\strategies\mod.rs"
echo. > "%BASEDIR%\strategies\dex_dex.rs"

:: execution/
mkdir "%BASEDIR%\execution"
echo. > "%BASEDIR%\execution\mod.rs"
echo. > "%BASEDIR%\execution\simulator.rs"
echo. > "%BASEDIR%\execution\dispatcher.rs"

:: risk/
mkdir "%BASEDIR%\risk"
echo. > "%BASEDIR%\risk\mod.rs"
echo. > "%BASEDIR%\risk\manager.rs"

:: tui/
mkdir "%BASEDIR%\tui"
echo. > "%BASEDIR%\tui\mod.rs"
echo. > "%BASEDIR%\tui\app.rs"

echo Folder structure created!