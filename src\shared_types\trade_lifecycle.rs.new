// Add Default implementation for SimpleOpportunity
impl Default for SimpleOpportunity {
    fn default() -> Self {
        Self {
            id: String::new(),
            source_scanner: String::new(),
            estimated_gross_profit_usd: rust_decimal::Decimal::ZERO,
            path: Vec::new(),
            pools: Vec::new(),
            input_amount: ethers::types::U256::zero(),
            bottleneck_liquidity_usd: rust_decimal::Decimal::ZERO,
            estimated_output_amount: rust_decimal::Decimal::ZERO,
            requires_flash_liquidity: false,
            chain_id: 0,
            timestamp: 0,
        }
    }
}
