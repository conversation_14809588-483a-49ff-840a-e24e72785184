// MISSION: TransactionRecovery - Stuck Transaction Recovery and Replacement
// WHY: Ensure transactions don't get stuck in mempool, maintaining system reliability
// HOW: Monitor transaction status and create replacement transactions with higher gas prices

use anyhow::Result;
use ethers::types::{TransactionRequest, TransactionReceipt, TxHash, U256};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

use super::broadcaster::Broadcaster;

/// Configuration for transaction recovery
#[derive(Debug, Clone)]
pub struct TransactionRecoveryConfig {
    /// Multiplier to increase gas price for replacement transactions
    pub gas_bump_multiplier: Decimal,
    /// Maximum number of replacement attempts
    pub max_replacement_attempts: u32,
    /// Time to wait before considering a transaction stuck (in seconds)
    pub stuck_transaction_timeout_seconds: u64,
    /// Maximum gas price we're willing to pay (in gwei)
    pub max_gas_price_gwei: Decimal,
    /// Minimum gas price increase for replacement (in gwei)
    pub min_gas_price_increase_gwei: Decimal,
    // MEDIUM PRIORITY FIX #2: Recovery loop protection
    pub max_retry_attempts: u32,
    pub retry_backoff_seconds: u64,
}

impl Default for TransactionRecoveryConfig {
    fn default() -> Self {
        Self {
            gas_bump_multiplier: dec!(1.15), // 15% increase
            max_replacement_attempts: 3,
            stuck_transaction_timeout_seconds: 300, // 5 minutes
            max_gas_price_gwei: dec!(200.0), // 200 gwei max
            min_gas_price_increase_gwei: dec!(5.0), // 5 gwei minimum increase
            // MEDIUM PRIORITY FIX #2: Recovery loop protection
            max_retry_attempts: 5, // Maximum 5 retry attempts total
            retry_backoff_seconds: 30, // 30 second backoff between retries
        }
    }
}

/// Information about a pending transaction that might need recovery
#[derive(Debug, Clone)]
pub struct PendingTransaction {
    pub hash: TxHash,
    pub tx_request: TransactionRequest,
    pub submitted_at: Instant,
    pub nonce: U256,
    pub replacement_attempts: u32,
    pub original_gas_price: U256,
}

/// Result of a transaction recovery attempt
#[derive(Debug, Clone)]
pub struct RecoveryResult {
    pub success: bool,
    pub new_hash: Option<TxHash>,
    pub new_gas_price: Option<U256>,
    pub error: Option<String>,
    pub attempts_used: u32,
}

/// MEDIUM PRIORITY FIX #2: Track retry attempts to prevent infinite loops
#[derive(Debug, Clone)]
pub struct RetryAttempt {
    pub count: u32,
    pub last_attempt: Instant,
    pub transaction_hash: TxHash,
}

/// Transaction recovery service for handling stuck transactions
pub struct TransactionRecovery {
    config: TransactionRecoveryConfig,
    // MEDIUM PRIORITY FIX #2: Track retry attempts to prevent infinite loops
    retry_attempts: Arc<Mutex<HashMap<String, RetryAttempt>>>,
    broadcaster: Arc<Broadcaster>,
}

impl TransactionRecovery {
    /// Create a new TransactionRecovery service
    pub fn new(config: TransactionRecoveryConfig, broadcaster: Arc<Broadcaster>) -> Self {
        info!(
            "TransactionRecovery initialized - Gas bump: {}%, Max attempts: {}, Timeout: {}s",
            config.gas_bump_multiplier * dec!(100.0),
            config.max_replacement_attempts,
            config.stuck_transaction_timeout_seconds
        );
        
        Self {
            config,
            broadcaster,
        }
    }
    
    /// Attempt to recover a stuck transaction by creating a replacement with higher gas
    pub async fn recover_stuck_transaction(&self, stuck_tx: &PendingTransaction) -> Result<RecoveryResult> {
        info!(
            "Attempting to recover stuck transaction - Hash: {:?}, Attempts: {}/{}",
            stuck_tx.hash,
            stuck_tx.replacement_attempts,
            self.config.max_replacement_attempts
        );
        
        // Check if we've exceeded maximum attempts
        if stuck_tx.replacement_attempts >= self.config.max_replacement_attempts {
            return Ok(RecoveryResult {
                success: false,
                new_hash: None,
                new_gas_price: None,
                error: Some(format!(
                    "Maximum replacement attempts ({}) exceeded",
                    self.config.max_replacement_attempts
                )),
                attempts_used: stuck_tx.replacement_attempts,
            });
        }
        
        // Check if transaction is actually stuck
        if !self.is_transaction_stuck(stuck_tx) {
            return Ok(RecoveryResult {
                success: false,
                new_hash: None,
                new_gas_price: None,
                error: Some("Transaction is not considered stuck yet".to_string()),
                attempts_used: stuck_tx.replacement_attempts,
            });
        }
        
        // Calculate new gas price
        let new_gas_price = self.calculate_replacement_gas_price(stuck_tx)?;
        
        // Validate new gas price
        if !self.validate_gas_price(new_gas_price) {
            return Ok(RecoveryResult {
                success: false,
                new_hash: None,
                new_gas_price: Some(new_gas_price),
                error: Some(format!(
                    "New gas price {} gwei exceeds maximum {} gwei",
                    self.wei_to_gwei(new_gas_price),
                    self.config.max_gas_price_gwei
                )),
                attempts_used: stuck_tx.replacement_attempts,
            });
        }
        
        // Create replacement transaction
        let replacement_tx = self.create_replacement_transaction(stuck_tx, new_gas_price)?;
        
        // Broadcast replacement transaction
        match self.broadcaster.send_and_confirm(replacement_tx).await {
            Ok(receipt) => {
                info!(
                    "Replacement transaction successful - New hash: {:?}, Gas price: {} gwei",
                    receipt.transaction_hash,
                    self.wei_to_gwei(new_gas_price)
                );
                
                Ok(RecoveryResult {
                    success: true,
                    new_hash: Some(receipt.transaction_hash),
                    new_gas_price: Some(new_gas_price),
                    error: None,
                    attempts_used: stuck_tx.replacement_attempts + 1,
                })
            },
            Err(e) => {
                error!("Replacement transaction failed: {}", e);
                
                Ok(RecoveryResult {
                    success: false,
                    new_hash: None,
                    new_gas_price: Some(new_gas_price),
                    error: Some(e.to_string()),
                    attempts_used: stuck_tx.replacement_attempts + 1,
                })
            }
        }
    }
    
    /// Check if a transaction should be considered stuck
    fn is_transaction_stuck(&self, tx: &PendingTransaction) -> bool {
        let elapsed = tx.submitted_at.elapsed();
        let timeout = Duration::from_secs(self.config.stuck_transaction_timeout_seconds);
        
        let is_stuck = elapsed > timeout;
        
        if is_stuck {
            debug!(
                "Transaction {:?} is stuck - Elapsed: {:?}, Timeout: {:?}",
                tx.hash,
                elapsed,
                timeout
            );
        }
        
        is_stuck
    }
    
    /// Calculate the new gas price for a replacement transaction
    fn calculate_replacement_gas_price(&self, stuck_tx: &PendingTransaction) -> Result<U256> {
        let current_gas_price = stuck_tx.tx_request.gas_price.unwrap_or(stuck_tx.original_gas_price);
        
        // Apply multiplier
        let multiplier_f64 = self.config.gas_bump_multiplier.to_f64()
            .ok_or_else(|| BasiliskError::NumericConversionError("Invalid gas bump multiplier".to_string()))?;
        
        let new_gas_price_f64 = current_gas_price.as_u64() as f64 * multiplier_f64;
        let new_gas_price = U256::from(new_gas_price_f64 as u64);
        
        // Ensure minimum increase
        let min_increase_wei = self.gwei_to_wei(self.config.min_gas_price_increase_gwei);
        let min_new_gas_price = current_gas_price + min_increase_wei;
        
        let final_gas_price = new_gas_price.max(min_new_gas_price);
        
        debug!(
            "Gas price calculation - Current: {} gwei, Multiplier: {}%, Min increase: {} gwei, New: {} gwei",
            self.wei_to_gwei(current_gas_price),
            self.config.gas_bump_multiplier * dec!(100.0),
            self.config.min_gas_price_increase_gwei,
            self.wei_to_gwei(final_gas_price)
        );
        
        Ok(final_gas_price)
    }
    
    /// Validate that the new gas price is within acceptable limits
    fn validate_gas_price(&self, gas_price: U256) -> bool {
        let gas_price_gwei = self.wei_to_gwei(gas_price);
        let is_valid = gas_price_gwei <= self.config.max_gas_price_gwei;
        
        if !is_valid {
            warn!(
                "Gas price {} gwei exceeds maximum {} gwei",
                gas_price_gwei,
                self.config.max_gas_price_gwei
            );
        }
        
        is_valid
    }
    
    /// Create a replacement transaction with the new gas price
    fn create_replacement_transaction(
        &self,
        stuck_tx: &PendingTransaction,
        new_gas_price: U256,
    ) -> Result<TransactionRequest> {
        let mut replacement_tx = stuck_tx.tx_request.clone();
        
        // Update gas price
        replacement_tx.gas_price = Some(new_gas_price);
        
        // Keep the same nonce to replace the stuck transaction
        replacement_tx.nonce = Some(stuck_tx.nonce);
        
        debug!(
            "Created replacement transaction - Nonce: {}, Gas price: {} gwei",
            stuck_tx.nonce,
            self.wei_to_gwei(new_gas_price)
        );
        
        Ok(replacement_tx)
    }
    
    /// Convert wei to gwei for display purposes
    fn wei_to_gwei(&self, wei: U256) -> Decimal {
        let wei_str = wei.to_string();
        let wei_decimal = Decimal::from_str_exact(&wei_str).unwrap_or_default();
        wei_decimal / dec!(1_000_000_000) // 1 gwei = 10^9 wei
    }
    
    /// Convert gwei to wei
    fn gwei_to_wei(&self, gwei: Decimal) -> U256 {
        let wei_decimal = gwei * dec!(1_000_000_000);
        let wei_str = wei_decimal.to_string();
        let wei_str = wei_str.split('.').next().unwrap_or(&wei_str);
        U256::from_dec_str(wei_str).unwrap_or_default()
    }
    
    /// Create a cancellation transaction (sends 0 ETH to self with higher gas)
    pub async fn create_cancellation_transaction(
        &self,
        stuck_tx: &PendingTransaction,
        wallet_address: ethers::types::Address,
    ) -> Result<RecoveryResult> {
        info!("Creating cancellation transaction for stuck TX: {:?}", stuck_tx.hash);
        
        let new_gas_price = self.calculate_replacement_gas_price(stuck_tx)?;
        
        if !self.validate_gas_price(new_gas_price) {
            return Ok(RecoveryResult {
                success: false,
                new_hash: None,
                new_gas_price: Some(new_gas_price),
                error: Some("Gas price too high for cancellation".to_string()),
                attempts_used: stuck_tx.replacement_attempts,
            });
        }
        
        // Create cancellation transaction (0 ETH to self)
        let cancellation_tx = TransactionRequest::new()
            .to(wallet_address)
            .value(U256::zero())
            .gas_price(new_gas_price)
            .nonce(stuck_tx.nonce)
            .gas(U256::from(21000)); // Standard ETH transfer gas
        
        match self.broadcaster.send_and_confirm(cancellation_tx).await {
            Ok(receipt) => {
                info!(
                    "Cancellation transaction successful - Hash: {:?}",
                    receipt.transaction_hash
                );
                
                Ok(RecoveryResult {
                    success: true,
                    new_hash: Some(receipt.transaction_hash),
                    new_gas_price: Some(new_gas_price),
                    error: None,
                    attempts_used: stuck_tx.replacement_attempts + 1,
                })
            },
            Err(e) => {
                error!("Cancellation transaction failed: {}", e);
                
                Ok(RecoveryResult {
                    success: false,
                    new_hash: None,
                    new_gas_price: Some(new_gas_price),
                    error: Some(e.to_string()),
                    attempts_used: stuck_tx.replacement_attempts + 1,
                })
            }
        }
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: TransactionRecoveryConfig) {
        info!("Updating TransactionRecovery configuration");
        self.config = config;
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> &TransactionRecoveryConfig {
        &self.config
    }
    
    /// Get recovery statistics
    pub fn get_recovery_stats(&self) -> RecoveryStats {
        // In a full implementation, this would track actual statistics
        RecoveryStats {
            total_recovery_attempts: 0,
            successful_recoveries: 0,
            failed_recoveries: 0,
            average_gas_increase_percentage: dec!(15.0),
            average_recovery_time_seconds: 120,
        }
    }
}

/// Statistics for transaction recovery operations
#[derive(Debug, Clone)]
pub struct RecoveryStats {
    pub total_recovery_attempts: u64,
    pub successful_recoveries: u64,
    pub failed_recoveries: u64,
    pub average_gas_increase_percentage: Decimal,
    pub average_recovery_time_seconds: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Instant;
    
    #[test]
    fn test_transaction_recovery_config() {
        let config = TransactionRecoveryConfig::default();
        
        assert_eq!(config.gas_bump_multiplier, dec!(1.15));
        assert_eq!(config.max_replacement_attempts, 3);
        assert_eq!(config.stuck_transaction_timeout_seconds, 300);
    }
    
    #[test]
    fn test_gas_price_conversion() {
        let config = TransactionRecoveryConfig::default();
        let broadcaster = Arc::new(Broadcaster::new_legacy(
            Arc::new(ethers::providers::Provider::<ethers::providers::Http>::try_from("http://localhost:8545").unwrap()),
            true,
            false,
            crate::shared_types::RunMode::Simulate,
            None,
        ));
        let recovery = TransactionRecovery::new(config, broadcaster);
        
        // Test gwei to wei conversion
        let gwei_amount = dec!(50.0);
        let wei_amount = recovery.gwei_to_wei(gwei_amount);
        let expected_wei = U256::from(50_000_000_000u64); // 50 * 10^9
        assert_eq!(wei_amount, expected_wei);
        
        // Test wei to gwei conversion
        let converted_back = recovery.wei_to_gwei(wei_amount);
        assert_eq!(converted_back, gwei_amount);
    }
    
    #[test]
    fn test_stuck_transaction_detection() {
        let config = TransactionRecoveryConfig {
            stuck_transaction_timeout_seconds: 1, // 1 second for testing
            ..Default::default()
        };
        let broadcaster = Arc::new(Broadcaster::new_legacy(
            Arc::new(ethers::providers::Provider::<ethers::providers::Http>::try_from("http://localhost:8545").unwrap()),
            true,
            false,
            crate::shared_types::RunMode::Simulate,
            None,
        ));
        let recovery = TransactionRecovery::new(config, broadcaster);
        
        let pending_tx = PendingTransaction {
            hash: TxHash::zero(),
            tx_request: TransactionRequest::new(),
            submitted_at: Instant::now() - Duration::from_secs(2), // 2 seconds ago
            nonce: U256::from(1),
            replacement_attempts: 0,
            original_gas_price: U256::from(20_000_000_000u64), // 20 gwei
        };
        
        assert!(recovery.is_transaction_stuck(&pending_tx));
    }
    
    #[test]
    fn test_gas_price_calculation() {
        let config = TransactionRecoveryConfig::default();
        let broadcaster = Arc::new(Broadcaster::new_legacy(
            Arc::new(ethers::providers::Provider::<ethers::providers::Http>::try_from("http://localhost:8545").unwrap()),
            true,
            false,
            crate::shared_types::RunMode::Simulate,
            None,
        ));
        let recovery = TransactionRecovery::new(config, broadcaster);
        
        let mut tx_request = TransactionRequest::new();
        tx_request.gas_price = Some(U256::from(20_000_000_000u64)); // 20 gwei
        
        let pending_tx = PendingTransaction {
            hash: TxHash::zero(),
            tx_request,
            submitted_at: Instant::now(),
            nonce: U256::from(1),
            replacement_attempts: 0,
            original_gas_price: U256::from(20_000_000_000u64),
        };
        
        let new_gas_price = recovery.calculate_replacement_gas_price(&pending_tx).unwrap();
        let expected_min = U256::from(23_000_000_000u64); // 20 * 1.15 = 23 gwei
        
        assert!(new_gas_price >= expected_min);
    }
}