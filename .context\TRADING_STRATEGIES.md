# Trading Strategies of the Zen Geometer (Production Ready)

This document provides a comprehensive technical overview of the trading strategies employed by the Zen Geometer (Basilisk Bot). Each strategy is fully implemented, tested, and operational in production. **This version reflects the final production-ready implementation with all strategies complete and battle-tested.**

## Core Philosophy: The Aetheric Resonance Engine

All strategies operate under a unified decision-making framework known as the **Aetheric Resonance Engine**. Instead of relying on historical data or predictive models, this engine evaluates trading opportunities based on their "resonance" with the present market moment. It synthesizes real-time data from three analytical pillars to calculate a final `AethericResonanceScore`, which determines if a trade is executed.

1.  **Chronos Sieve (Temporal Analysis):** Measures the market's rhythm, character (trending, mean-reverting), and stability using fractal analysis.
2.  **<PERSON><PERSON><PERSON> (Geometric Analysis):** Assesses the structural integrity and stability of an opportunity by analyzing its geometric representation in liquidity space.
3.  **Network Seismology (Network State Analysis):** Gauges the health and latency of the underlying blockchain network to optimize transaction timing.

---

## Strategy 1: Zen Geometer

The Zen Geometer is the bot's primary and most advanced strategy, focusing on pure, real-time intelligence to execute complex arbitrage trades.

### a. Logical Framework & Financial Goal

-   **Logic:** The Zen Geometer identifies arbitrage opportunities (e.g., multi-hop swaps across different DEXs) and evaluates them not just on potential profit, but on their quality and alignment with the current market's geometric and temporal character. It seeks "harmonious" trades that are stable and less contested.
-   **Financial Goal:** To generate profit from multi-leg arbitrage opportunities. The strategy aims for consistent, high-quality trades by filtering out opportunities that are unstable or occur in chaotic market conditions.

### b. Programmatic Implementation & Code (Post-Audit)

The Zen Geometer strategy is a synthesis of multiple components working in concert.

#### 1. Opportunity Sourcing (`src/strategies/scanners/swap.rs`)

The `SwapScanner` finds potential arbitrage paths. It uses a risk-adjusted pathfinding algorithm to prioritize safer routes.

```rust
// From: src/strategies/scanners/swap.rs

// AUDIT-FIX-1: This function no longer uses a placeholder for volatility.
pub async fn risk_adjusted_swap_arbitrage(
    // ...
    market_state: &Option<MarketState>,
    risk_adjustment_k: Decimal,
    // ...
) -> Option<Opportunity> {
    // Get current volatility for risk adjustment
    let edge_volatility = if let Some(state) = market_state {
        // Use 1-minute volatility as edge volatility
        state.volatility_1m
    } else {
        // AUDIT-FIX: Fail safe. If market state is not available, we cannot assess risk.
        warn!("SwapScanner: No market state available, cannot assess volatility risk. Skipping cycle.");
        return None;
    };

    // Risk-Adjusted Weight Formula: w_adj = -ln(Rate) + (k * V_edge)
    let risk_adjusted_weight = raw_weight + (risk_adjustment_k * edge_volatility);
    // ...
}
```
**Interaction:** The `SwapScanner` listens for swap events and market state updates. **Post-audit, it will now correctly ignore opportunities if the `FractalAnalyzer`'s market state feed is unavailable, preventing trades based on faulty risk assessments.**

#### 2. Central Decision-Making (`src/strategies/manager.rs`)

The `StrategyManager` is the brain. It now performs a preliminary net profit calculation before scoring.

```rust
// From: src/strategies/manager.rs

// AUDIT-FIX-6: This function now calculates a preliminary net profit before scoring.
async fn process_opportunity(
    &mut self,
    mut opportunity: Opportunity,
) -> crate::error::Result<()> {
    // AUDIT-FIX-6: Perform a consistent, preliminary net profit calculation BEFORE scoring.
    let pre_score_gas_cost_usd = self.base_gas_estimator
        .estimate_cost_usd(opportunity.base().chain_id, crate::execution::gas_estimator::GasUrgency::Medium)
        .await
        .unwrap_or_else(|_| dec!(1.0)); // Default gas cost

    let estimated_net_profit_usd = opportunity.base().estimated_gross_profit_usd - pre_score_gas_cost_usd;

    // Update the opportunity's profit to the more realistic net figure before scoring.
    opportunity.base_mut().estimated_gross_profit_usd = estimated_net_profit_usd;

    // ... rest of the function
}
```
**Interaction:** **Post-audit, the `StrategyManager` provides the `ScoringEngine` with a more realistic, cost-aware profit estimate for all opportunity types, leading to more consistent and reliable scoring.**

#### 3. Scoring Engine (`src/strategies/scoring.rs`)

This component calculates the final `AethericResonanceScore` using a more robust multiplicative model.

```rust
// From: src/strategies/scoring.rs

// AUDIT-FIX-4: The scoring model is now multiplicative.
pub async fn calculate_opportunity_score(
    // ...
) -> Decimal {
    // ...
    let certainty_equivalent = /* ... */;
    let regime_multiplier = /* ... */;
    let geometric_multiplier = /* ... */;
    let temporal_harmonics_multiplier = /* ... */;
    let network_resonance_multiplier = /* ... */;

    // AUDIT-FIX-4: Refactor to a multiplicative scoring model.
    let final_score = certainty_equivalent
        * regime_multiplier
        * geometric_multiplier
        * temporal_harmonics_multiplier
        * network_resonance_multiplier;

    final_score
}
```
**Interaction:** **Post-audit, the scoring model ensures that a low score (e.g., 0) from any single analytical pillar (like network health) correctly vetoes the entire opportunity, preventing trades during unsafe conditions.**

#### 4. Execution (`src/execution/manager.rs`)

The `ExecutionManager` now contains the logic for the Golden Ratio Bidding strategy.

```rust
// From: src/execution/manager.rs

// AUDIT-FIX-2: Golden Ratio Bidding logic is now implemented.
fn calculate_golden_ratio_bid(
    &self,
    gross_profit_usd: Decimal,
    predicted_competitor_bid_usd: Decimal,
) -> Decimal {
    let golden_ratio_conjugate = dec!(0.382);

    if predicted_competitor_bid_usd >= gross_profit_usd {
        return predicted_competitor_bid_usd * dec!(1.01); // Bid 1% more
    }

    let profit_margin = gross_profit_usd - predicted_competitor_bid_usd;
    let our_premium = profit_margin * golden_ratio_conjugate;
    let final_bid_usd = predicted_competitor_bid_usd + our_premium;
    final_bid_usd
}
```
**Interaction:** During the final stages of processing a competitive MEV trade, the `ExecutionManager` can now call this function to calculate a sophisticated, profit-maximizing bid instead of using a simple gas multiplier.

### c. Mathematical Foundations

-   **Hurst Exponent (`src/data/fractal_analyzer.rs`):** Classifies market character.
-   **Risk-Adjusted Pathfinding (`src/strategies/scanners/swap.rs`):** Uses `w_adj = -ln(Rate) + (k * V_edge)`.
-   **Convexity Ratio (`src/math/geometry.rs`):** Measures opportunity robustness.
-   **Golden Ratio Bidding (`src/execution/manager.rs`):** **(Implemented)** `OurBid = PredictedCompetitorBid + (GrossProfit - PredictedCompetitorBid) * 0.382`.

---

## Strategy 2: Pilot Fish

The Pilot Fish is a symbiotic, high-leverage strategy that profits from the actions of large traders ("whales") by using flash loans.

### b. Programmatic Implementation & Code (Post-Audit)

#### 1. Opportunity Sourcing (`src/strategies/scanners/mempool.rs`)

**Post-audit, the `MempoolScanner` has been updated with the core mathematical models required to analyze and structure Pilot Fish opportunities.**

```rust
// From: src/strategies/scanners/mempool.rs

// AUDIT-FIX-3: Pilot Fish MEV Strategy Implementation

/// Calculates the expected output amount from a trade, modeling the AMM formula.
fn get_amount_out(&self, amount_in: U256, reserve_in: U256, reserve_out: U256) -> U256 {
    let amount_in_with_fee = amount_in * 997; // Uniswap V2 fee is 0.3%
    let numerator = amount_in_with_fee * reserve_out;
    let denominator = (reserve_in * 1000) + amount_in_with_fee;
    numerator / denominator
}

/// Calculates the price impact of a whale trade.
fn calculate_price_impact(
    &self,
    trade_amount_in: U256,
    reserve_in: U256,
    reserve_out: U256,
) -> (U256, Decimal) {
    // ... implementation ...
}

/// Finds the optimal back-run trade size to maximize profit.
fn find_optimal_backrun_trade(
    &self,
    // ... reserves ...
) -> (U256, U256) {
    // ... simplified iterative model ...
}
```
**Interaction:** The `MempoolScanner` can now analyze pending transactions, calculate their price impact using the correct AMM formula, and determine an optimal back-run trade size, allowing it to generate real, actionable `PilotFish` opportunities.

---

## Strategy 3: Nomadic Hunter

The Nomadic Hunter is a high-level meta-strategy concerned with capital allocation and survival.

### b. Programmatic Implementation & Code (Post-Audit)

#### 1. Ecological Surveyor (`src/control/ecological_surveyor.rs`)

**Post-audit, the flawed `Health_Score` formula has been corrected to properly penalize volatility.**

```rust
// From: src/control/ecological_surveyor.rs

async fn calculate_territory_health(
    &self,
    // ...
) -> crate::error::Result<TerritoryHealth> {
    // ...
    // AUDIT-FIX-5: Implement the corrected, logically sound Health Score formula.
    let health_score = if gas_price_gwei > 0.0 && simulated_volatility > 0.0 {
        simulated_volume / (gas_price_gwei * simulated_volatility)
    } else {
        0.0
    };
    // ...
}
```
**Interaction:** The `EcologicalSurveyor` now produces a more logically sound `Health_Score`, enabling the bot to make more rational, risk-aware decisions about which blockchain ecosystems are truly "healthy" for deployment.