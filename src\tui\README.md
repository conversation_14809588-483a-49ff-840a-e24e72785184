# TUI Module

This module contains the Terminal User Interface (TUI) for the Basilisk Bot. The TUI provides a real-time view of the bot's operations, including market data, trading opportunities, and performance metrics.

## Core Components

-   **`app.rs`**: The `App` is the main component of the TUI. It is responsible for managing the state of the TUI and handling user input.
-   **`ui.rs`**: This module contains the code for rendering the TUI. It uses the `ratatui` crate to create the various UI components.
-   **`tabs/`**: This directory contains the code for the different tabs in the TUI. Each tab is responsible for displaying a specific set of information.

## TUI Tabs

The TUI is organized into the following tabs:

-   **Dashboard**: Displays a high-level overview of the bot's operations.
-   **Strategies**: Displays information about the different trading strategies that are currently running.
-   **ARE Analysis**: Displays the results of the Aetheric Resonance Engine analysis.
-   **Trade Log**: Displays a log of all the trades that have been executed by the bot.
-   **Ecosystem**: Displays information about the different blockchain ecosystems that the bot is connected to.
-   **Tuning**: Allows you to tune the parameters of the bot in real-time.
-   **Risk & Trades**: Displays information about the bot's risk management and trading performance.
-   **Config**: Displays the current configuration of the bot.
-   **Logs**: Displays a log of all the events that have occurred in the bot.

## Usage

To start the TUI, run the `tui` binary:

```bash
cargo run --bin tui
```
