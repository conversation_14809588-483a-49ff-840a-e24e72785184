# Zen Geometer - Production Environment Configuration
# CRITICAL: This file contains sensitive production credentials
# Never commit this file to version control

# ===== EXECUTION WALLET =====
# CRITICAL: Production wallet private key (without 0x prefix)
BASILISK_EXECUTION_PRIVATE_KEY=your_production_private_key_here

# ===== RPC ENDPOINTS =====
# Use dedicated, authenticated RPC endpoints for production
BASE_RPC_API_KEY=your_base_rpc_api_key
BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/${BASE_RPC_API_KEY}
BASE_WS_URL=wss://base-mainnet.g.alchemy.com/v2/${BASE_RPC_API_KEY}

# Alternative RPC providers for redundancy
INFURA_API_KEY=your_infura_api_key
QUICKNODE_API_KEY=your_quicknode_api_key

# ===== MEV PROTECTION =====
# MEV relay authentication for private mempool
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
TITAN_RELAY_URL=https://rpc.titanbuilder.xyz
MEV_RELAY_AUTH_KEY=your_mev_relay_auth_key

# ===== DATABASE =====
# Production database with SSL and authentication
DATABASE_URL=**********************************************************/basilisk_prod?sslmode=require

# ===== REDIS =====
# Production Redis with authentication
REDIS_URL=redis://username:password@prod-redis-host:6379

# ===== NATS =====
# Production NATS with authentication
NATS_URL=nats://username:password@prod-nats-host:4222

# ===== SECURITY =====
# API keys for security services
HONEYPOT_API_KEY=your_goplus_api_key
SECURITY_SCANNER_API_KEY=your_security_api_key

# ===== MONITORING & ALERTING =====
# Discord webhook for alerts
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url

# Telegram bot for notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Slack webhook for team notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your_slack_webhook

# ===== EXTERNAL APIS =====
# CEX API credentials for price feeds
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret
COINBASE_PASSPHRASE=your_coinbase_passphrase

BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# ===== DEPLOYMENT CONFIGURATION =====
# Environment identifier
ENVIRONMENT=production
DEPLOYMENT_MODE=live

# Logging configuration
LOG_LEVEL=info
LOG_FORMAT=json

# ===== BACKUP & RECOVERY =====
# Backup wallet private key (emergency use only)
BACKUP_PRIVATE_KEY=your_backup_private_key_here

# Recovery endpoints
RECOVERY_RPC_URL=your_recovery_rpc_endpoint
RECOVERY_DATABASE_URL=your_backup_database_url