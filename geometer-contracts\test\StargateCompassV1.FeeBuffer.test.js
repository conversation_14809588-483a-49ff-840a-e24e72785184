const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 Fee Buffer System Tests', function () {
  // Test fixture for contract deployment
  async function deployStargateCompassFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockAaveProvider.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory('MockPool');
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockStargateRouter.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USDC', 'USDC', 6);

    // Set up mock provider to return mock pool
    await mockAaveProvider.setPool(mockPool.target);

    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  describe('Fee Buffer Constants', function () {
    it('Should have correct FEE_BUFFER_BPS constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.FEE_BUFFER_BPS()).to.equal(200); // 2%
    });

    it('Should have correct MAX_NATIVE_FEE constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.MAX_NATIVE_FEE()).to.equal(
        ethers.parseEther('0.1')
      ); // 0.1 ETH
    });
  });

  describe('calculateBufferedFee Function', function () {
    it('Should calculate correct buffered fee for normal fee amounts', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      // Test with 0.01 ETH base fee
      const baseFee = ethers.parseEther('0.01');
      const expectedBuffer = (baseFee * 200n) / 10000n; // 2% buffer
      const expectedBufferedFee = baseFee + expectedBuffer;

      // Call calculateBufferedFee through a view function or test contract
      // Since calculateBufferedFee is internal, we'll test it through executeOperation
      // For now, let's test the calculation logic manually
      const calculatedBuffer = (baseFee * 200n) / 10000n;
      const calculatedBufferedFee = baseFee + calculatedBuffer;

      expect(calculatedBufferedFee).to.equal(expectedBufferedFee);
      expect(calculatedBuffer).to.equal(ethers.parseEther('0.0002')); // 0.0002 ETH buffer
    });

    it('Should handle zero fee correctly', async function () {
      const baseFee = 0n;
      const expectedBufferedFee = 0n;

      // Manual calculation since function is internal
      const calculatedBufferedFee = baseFee + (baseFee * 200n) / 10000n;
      expect(calculatedBufferedFee).to.equal(expectedBufferedFee);
    });

    it('Should calculate correct buffer for various fee amounts', async function () {
      const testCases = [
        {
          baseFee: ethers.parseEther('0.001'), // 0.001 ETH
          expectedBuffer: ethers.parseEther('0.00002'), // 0.00002 ETH (2%)
        },
        {
          baseFee: ethers.parseEther('0.05'), // 0.05 ETH
          expectedBuffer: ethers.parseEther('0.001'), // 0.001 ETH (2%)
        },
        {
          baseFee: ethers.parseEther('0.1'), // 0.1 ETH (max fee)
          expectedBuffer: ethers.parseEther('0.002'), // 0.002 ETH (2%)
        },
      ];

      for (const testCase of testCases) {
        const calculatedBuffer = (testCase.baseFee * 200n) / 10000n;
        const calculatedBufferedFee = testCase.baseFee + calculatedBuffer;

        expect(calculatedBuffer).to.equal(testCase.expectedBuffer);
        expect(calculatedBufferedFee).to.equal(
          testCase.baseFee + testCase.expectedBuffer
        );
      }
    });
  });

  describe('Fee Buffer Integration', function () {
    it('Should calculate correct buffered fee with 2% buffer', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC, mockPool } =
        await loadFixture(deployStargateCompassFixture);

      // Set up mock router to return specific fees
      const baseFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(baseFee, 0);

      // Fund contract with sufficient ETH for buffered fee
      const expectedBuffer = (baseFee * 200n) / 10000n; // 2% buffer
      const bufferedFee = baseFee + expectedBuffer;

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee + ethers.parseEther('0.01'), // Extra for safety
      });

      // Mint USDC to mock pool for flash loan
      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount);

      // Set up parameters for executeRemoteDegenSwap
      const remoteCalldata = '0x1234';
      const remoteSwapRouter = ethers.Wallet.createRandom().address;
      const minAmountOut = ethers.parseUnits('990', 6);
      const expectedProfit = ethers.parseUnits('10', 6);

      // Execute the remote swap which will trigger the fee buffer system
      const tx = await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        remoteCalldata,
        remoteSwapRouter,
        minAmountOut,
        expectedProfit
      );

      // Check for FeeBufferApplied event
      await expect(tx)
        .to.emit(stargateCompass, 'FeeBufferApplied')
        .withArgs(baseFee, bufferedFee, expectedBuffer);
    });

    it('Should validate profitability before applying fee buffer', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC, mockPool } =
        await loadFixture(deployStargateCompassFixture);

      // Set up mock router to return specific fees
      const baseFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(baseFee, 0);

      // Fund contract with sufficient ETH
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('0.1'),
      });

      // Mint USDC to mock pool for flash loan
      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount);

      // Set up parameters with insufficient profit
      const remoteCalldata = '0x1234';
      const remoteSwapRouter = ethers.Wallet.createRandom().address;
      const minAmountOut = ethers.parseUnits('990', 6);
      const insufficientProfit = ethers.parseUnits('1', 6); // Too low profit

      // Should revert due to insufficient profit before fee buffer is applied
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          remoteCalldata,
          remoteSwapRouter,
          minAmountOut,
          insufficientProfit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
    });
  });

  describe('Fee Volatility Scenarios', function () {
    it('Should handle sudden fee spikes with buffer protection', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      // Simulate high volatility scenario with 0.08 ETH fee (near max)
      const highVolatilityFee = ethers.parseEther('0.08');
      const expectedBuffer = (highVolatilityFee * 200n) / 10000n;
      const bufferedFee = highVolatilityFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFeeResponse(
        highVolatilityFee,
        0
      );

      // Fund contract with sufficient ETH for buffered fee
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee + ethers.parseEther('0.01'),
      });

      const loanAmount = ethers.parseUnits('1000', 6);
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6),
        ]
      );

      const tx = await stargateCompass.executeOperation(
        mockUSDC.target,
        loanAmount,
        (loanAmount * 5n) / 10000n,
        stargateCompass.target,
        params
      );

      // Should emit FeeBufferApplied event with high volatility values
      await expect(tx)
        .to.emit(stargateCompass, 'FeeBufferApplied')
        .withArgs(highVolatilityFee, bufferedFee, expectedBuffer);
    });

    it('Should reject operations when fees exceed maximum even with buffer', async function () {
      const { stargateCompass, mockStargateRouter, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      // Set fee above maximum allowed (0.1 ETH)
      const excessiveFee = ethers.parseEther('0.11');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(excessiveFee, 0);

      const loanAmount = ethers.parseUnits('1000', 6);
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6),
        ]
      );

      // Should revert due to excessive fee before buffer calculation
      await expect(
        stargateCompass.executeOperation(
          mockUSDC.target,
          loanAmount,
          (loanAmount * 5n) / 10000n,
          stargateCompass.target,
          params
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ExcessiveFee')
        .withArgs(excessiveFee, ethers.parseEther('0.1'));
    });

    it('Should handle multiple fee volatility scenarios in sequence', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      // Fund contract with sufficient ETH for all scenarios
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('0.5'),
      });

      const volatilityScenarios = [
        ethers.parseEther('0.001'), // Low fee
        ethers.parseEther('0.05'), // Medium fee
        ethers.parseEther('0.09'), // High fee (near max)
        ethers.parseEther('0.002'), // Back to low fee
      ];

      for (let i = 0; i < volatilityScenarios.length; i++) {
        const baseFee = volatilityScenarios[i];
        const expectedBuffer = (baseFee * 200n) / 10000n;
        const bufferedFee = baseFee + expectedBuffer;

        await mockStargateRouter.setQuoteLayerZeroFeeResponse(baseFee, 0);

        const loanAmount = ethers.parseUnits('1000', 6);
        const params = ethers.AbiCoder.defaultAbiCoder().encode(
          ['bytes', 'address', 'uint256', 'uint256'],
          [
            '0x1234',
            ethers.Wallet.createRandom().address,
            ethers.parseUnits('990', 6),
            ethers.parseUnits('10', 6),
          ]
        );

        const tx = await stargateCompass.executeOperation(
          mockUSDC.target,
          loanAmount,
          (loanAmount * 5n) / 10000n,
          stargateCompass.target,
          params
        );

        // Each scenario should emit correct FeeBufferApplied event
        await expect(tx)
          .to.emit(stargateCompass, 'FeeBufferApplied')
          .withArgs(baseFee, bufferedFee, expectedBuffer);
      }
    });
  });

  describe('Fee Buffer Edge Cases', function () {
    it('Should handle minimum possible fee with buffer', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      // Test with 1 wei fee
      const minFee = 1n;
      const expectedBuffer = (minFee * 200n) / 10000n; // Should be 0 due to integer division
      const bufferedFee = minFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFeeResponse(minFee, 0);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('0.01'),
      });

      const loanAmount = ethers.parseUnits('1000', 6);
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6),
        ]
      );

      const tx = await stargateCompass.executeOperation(
        mockUSDC.target,
        loanAmount,
        (loanAmount * 5n) / 10000n,
        stargateCompass.target,
        params
      );

      await expect(tx)
        .to.emit(stargateCompass, 'FeeBufferApplied')
        .withArgs(minFee, bufferedFee, expectedBuffer);
    });

    it('Should handle maximum allowed fee with buffer', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      // Test with exactly maximum allowed fee
      const maxFee = ethers.parseEther('0.1');
      const expectedBuffer = (maxFee * 200n) / 10000n; // 0.002 ETH
      const bufferedFee = maxFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFeeResponse(maxFee, 0);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee + ethers.parseEther('0.01'),
      });

      const loanAmount = ethers.parseUnits('1000', 6);
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6),
        ]
      );

      const tx = await stargateCompass.executeOperation(
        mockUSDC.target,
        loanAmount,
        (loanAmount * 5n) / 10000n,
        stargateCompass.target,
        params
      );

      await expect(tx)
        .to.emit(stargateCompass, 'FeeBufferApplied')
        .withArgs(maxFee, bufferedFee, expectedBuffer);

      expect(expectedBuffer).to.equal(ethers.parseEther('0.002'));
    });
  });

  describe('Balance Warning Integration with Fee Buffer', function () {
    it('Should emit balance warnings when ETH balance is insufficient for buffered fee', async function () {
      const { stargateCompass, mockStargateRouter, owner, mockUSDC } =
        await loadFixture(deployStargateCompassFixture);

      const baseFee = ethers.parseEther('0.01');
      const expectedBuffer = (baseFee * 200n) / 10000n;
      const bufferedFee = baseFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFeeResponse(baseFee, 0);

      // Fund with less than buffered fee but more than base fee
      const insufficientBalance = baseFee + expectedBuffer / 2n;
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: insufficientBalance,
      });

      const loanAmount = ethers.parseUnits('1000', 6);
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6),
        ]
      );

      // Should emit balance warning and then revert
      const tx = stargateCompass.executeOperation(
        mockUSDC.target,
        loanAmount,
        (loanAmount * 5n) / 10000n,
        stargateCompass.target,
        params
      );

      // Check for balance warning event
      await expect(tx)
        .to.emit(stargateCompass, 'BalanceWarning')
        .withArgs(
          insufficientBalance,
          bufferedFee,
          'Insufficient balance for operation'
        );

      // Check for revert
      await expect(tx).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });
  });
});
