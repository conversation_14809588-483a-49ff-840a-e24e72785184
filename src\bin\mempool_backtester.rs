// src/bin/mempool_backtester.rs

use clap::Parser;
use ethers::{
    core::utils::Anvil,
    providers::{Http, Provider, Middleware},
    types::{Transaction, U64},
};
use std::{fs::File, io::BufReader, sync::Arc};
use basilisk_bot::data::historical_mempool::HistoricalMempoolTx;

#[derive(Parser, Debug)]
#[clap(author, version, about, long_about = None)]
struct Args {
    /// Path to the historical mempool data file (JSON format)
    #[clap(short, long)]
    mempool_data: String,

    /// Starting block number for the backtest
    #[clap(short, long)]
    start_block: u64,

    /// Ending block number for the backtest
    #[clap(short, long)]
    end_block: u64,

    /// Anvil fork URL
    #[clap(short, long, default_value = "https://eth.llamarpc.com")]
    fork_url: String,
}

/// A simplified mempool scanner for backtesting.
struct MempoolScanner {
    provider: Arc<Provider<Http>>,
}

impl MempoolScanner {
    fn new(provider: Arc<Provider<Http>>) -> Self {
        Self { provider }
    }

    /// Process a single pending transaction from the historical mempool data.
    async fn process_pending_tx(&self, tx: &Transaction) {
        // In a real scenario, the bot's logic would be here.
        // For example, it might decode the transaction data, check for arbitrage opportunities, etc.
        println!("Processing tx: {}", tx.hash);
    }
}

#[tokio::main]
async fn main() {
    let args = Args::parse();

    println!("Mempool-Aware Backtester");
    println!("Mempool data: {}", args.mempool_data);
    println!("Block range: {} - {}", args.start_block, args.end_block);
    println!("Fork URL: {}", args.fork_url);

    // Spawn Anvil instance
    let anvil = Anvil::new().fork(args.fork_url).spawn();
    println!("Anvil spawned at: {}", anvil.endpoint());
    let provider = Arc::new(Provider::<Http>::try_from(anvil.endpoint()).unwrap());

    // Initialize the mempool scanner
    let mempool_scanner = MempoolScanner::new(provider.clone());

    // Load mempool data
    let file = File::open(&args.mempool_data).expect("Failed to open mempool data file");
    let reader = BufReader::new(file);
    let mempool_data: Vec<HistoricalMempoolTx> =
        serde_json::from_reader(reader).expect("Failed to parse mempool data");

    // Group transactions by block number
    let mut txs_by_block: std::collections::HashMap<U64, Vec<Transaction>> =
        std::collections::HashMap::new();
    for historical_tx in mempool_data {
        txs_by_block
            .entry(historical_tx.block_number)
            .or_default()
            .push(historical_tx.tx);
    }

    // Process blocks
    for block_number in args.start_block..=args.end_block {
        let block_number_u64 = U64::from(block_number);
        println!("--- Processing Block: {} ---", block_number);

        if let Some(txs) = txs_by_block.get(&block_number_u64) {
            // Disable automine before processing mempool transactions
            provider.request::<_, ()>("evm_setAutomine", [false]).await.unwrap();

            for tx in txs {
                mempool_scanner.process_pending_tx(tx).await;
            }

            // Mine the block
            provider.request::<_, ()>("evm_mine", ()).await.unwrap();
            println!("Mined block {}", block_number);

            // Re-enable automine after processing the block
            provider.request::<_, ()>("evm_setAutomine", [true]).await.unwrap();
        } else {
            println!("No mempool transactions for block {}", block_number);
        }
    }

    println!("Backtest finished.");
}
