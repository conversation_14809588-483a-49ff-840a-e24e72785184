// MISSION: Circuit Breaker Pattern Implementation
// WHY: Prevent cascading failures and provide intelligent fallback mechanisms
// HOW: Monitor failure rates and automatically open circuits to protect system stability

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

use crate::error::BasiliskError;
use crate::shared_types::degradation::{DegradationLevel, DegradationState};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CircuitState {
    Closed,    // Normal operation
    Open,      // Failing fast, not attempting operations
    HalfOpen,  // Testing if service has recovered
}

#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub recovery_timeout: Duration,
    pub success_threshold: u32, // Successes needed in half-open to close
    pub request_timeout: Duration,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            recovery_timeout: Duration::from_secs(60),
            success_threshold: 3,
            request_timeout: Duration::from_secs(30),
        }
    }
}

#[derive(Debug)]
struct CircuitBreakerState {
    state: CircuitState,
    failure_count: u32,
    success_count: u32,
    last_failure_time: Option<Instant>,
    last_success_time: Option<Instant>,
    config: CircuitBreakerConfig,
}

impl CircuitBreakerState {
    fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            state: CircuitState::Closed,
            failure_count: 0,
            success_count: 0,
            last_failure_time: None,
            last_success_time: None,
            config,
        }
    }

    fn record_success(&mut self) {
        self.last_success_time = Some(Instant::now());
        
        match self.state {
            CircuitState::Closed => {
                self.failure_count = 0; // Reset failure count on success
            }
            CircuitState::HalfOpen => {
                self.success_count += 1;
                if self.success_count >= self.config.success_threshold {
                    self.state = CircuitState::Closed;
                    self.failure_count = 0;
                    self.success_count = 0;
                    info!("Circuit breaker closed - service recovered");
                }
            }
            CircuitState::Open => {
                // Should not happen, but reset if it does
                warn!("Received success while circuit is open - this should not happen");
            }
        }
    }

    fn record_failure(&mut self) {
        self.last_failure_time = Some(Instant::now());
        
        match self.state {
            CircuitState::Closed => {
                self.failure_count += 1;
                if self.failure_count >= self.config.failure_threshold {
                    self.state = CircuitState::Open;
                    warn!("Circuit breaker opened due to {} failures", self.failure_count);
                }
            }
            CircuitState::HalfOpen => {
                self.state = CircuitState::Open;
                self.success_count = 0;
                warn!("Circuit breaker reopened due to failure during recovery test");
            }
            CircuitState::Open => {
                // Already open, just update the failure time
            }
        }
    }

    fn can_execute(&mut self) -> bool {
        match self.state {
            CircuitState::Closed => true,
            CircuitState::Open => {
                // Check if we should transition to half-open
                if let Some(last_failure) = self.last_failure_time {
                    if last_failure.elapsed() >= self.config.recovery_timeout {
                        self.state = CircuitState::HalfOpen;
                        self.success_count = 0;
                        info!("Circuit breaker transitioning to half-open for recovery test");
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => true,
        }
    }

    fn get_degradation_level(&self) -> DegradationLevel {
        match self.state {
            CircuitState::Closed => {
                if self.failure_count > 0 {
                    DegradationLevel::MinorDegradation
                } else {
                    DegradationLevel::Operational
                }
            }
            CircuitState::HalfOpen => DegradationLevel::SafeMode,
            CircuitState::Open => DegradationLevel::Emergency,
        }
    }
}

pub struct CircuitBreaker {
    circuits: Arc<RwLock<HashMap<String, CircuitBreakerState>>>,
}

impl CircuitBreaker {
    pub fn new() -> Self {
        Self {
            circuits: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn register_service(&self, service_name: String, config: CircuitBreakerConfig) {
        let mut circuits = self.circuits.write().await;
        circuits.insert(service_name.clone(), CircuitBreakerState::new(config));
        debug!("Registered circuit breaker for service: {}", service_name);
    }

    pub async fn execute<T, F, Fut>(&self, service_name: &str, operation: F) -> Result<T, BasiliskError>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, BasiliskError>>,
    {
        // Check if we can execute
        {
            let mut circuits = self.circuits.write().await;
            if let Some(circuit) = circuits.get_mut(service_name) {
                if !circuit.can_execute() {
                    return Err(BasiliskError::CircuitBreakerOpen {
                        service: service_name.to_string(),
                        reason: "Circuit breaker is open due to repeated failures".to_string(),
                    });
                }
            } else {
                // Register with default config if not found
                circuits.insert(service_name.to_string(), CircuitBreakerState::new(CircuitBreakerConfig::default()));
            }
        }

        // Execute the operation
        let start = Instant::now();
        let result = tokio::time::timeout(
            Duration::from_secs(30), // Default timeout
            operation()
        ).await;

        // Record the result
        let mut circuits = self.circuits.write().await;
        if let Some(circuit) = circuits.get_mut(service_name) {
            match result {
                Ok(Ok(value)) => {
                    circuit.record_success();
                    debug!("Circuit breaker success for {}: {}ms", service_name, start.elapsed().as_millis());
                    Ok(value)
                }
                Ok(Err(e)) => {
                    circuit.record_failure();
                    warn!("Circuit breaker failure for {}: {}", service_name, e);
                    Err(e)
                }
                Err(_) => {
                    circuit.record_failure();
                    let error = BasiliskError::Timeout {
                        operation: format!("{} operation", service_name),
                    };
                    warn!("Circuit breaker timeout for {}", service_name);
                    Err(error)
                }
            }
        } else {
            // This should not happen, but handle gracefully
            match result {
                Ok(Ok(value)) => Ok(value),
                Ok(Err(e)) => Err(e),
                Err(_) => Err(BasiliskError::Timeout {
                    operation: format!("{} operation", service_name),
                }),
            }
        }
    }

    pub async fn get_service_state(&self, service_name: &str) -> Option<CircuitState> {
        let circuits = self.circuits.read().await;
        circuits.get(service_name).map(|c| c.state.clone())
    }

    pub async fn get_degradation_state(&self, service_name: &str) -> Option<DegradationState> {
        let circuits = self.circuits.read().await;
        circuits.get(service_name).map(|circuit| {
            let level = circuit.get_degradation_level();
            let reason = match circuit.state {
                CircuitState::Closed => {
                    if circuit.failure_count > 0 {
                        format!("Service experiencing {} recent failures", circuit.failure_count)
                    } else {
                        "Service operating normally".to_string()
                    }
                }
                CircuitState::HalfOpen => "Service in recovery testing mode".to_string(),
                CircuitState::Open => format!("Service failed {} times, circuit opened", circuit.failure_count),
            };

            DegradationState::new(level, reason)
                .with_affected_services(vec![service_name.to_string()])
        })
    }

    pub async fn force_open(&self, service_name: &str, reason: String) {
        let mut circuits = self.circuits.write().await;
        if let Some(circuit) = circuits.get_mut(service_name) {
            circuit.state = CircuitState::Open;
            circuit.last_failure_time = Some(Instant::now());
            warn!("Manually opened circuit breaker for {}: {}", service_name, reason);
        }
    }

    pub async fn force_close(&self, service_name: &str) {
        let mut circuits = self.circuits.write().await;
        if let Some(circuit) = circuits.get_mut(service_name) {
            circuit.state = CircuitState::Closed;
            circuit.failure_count = 0;
            circuit.success_count = 0;
            info!("Manually closed circuit breaker for {}", service_name);
        }
    }

    pub async fn get_all_states(&self) -> HashMap<String, (CircuitState, DegradationLevel)> {
        let circuits = self.circuits.read().await;
        circuits.iter().map(|(name, circuit)| {
            (name.clone(), (circuit.state.clone(), circuit.get_degradation_level()))
        }).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_circuit_breaker_basic_flow() {
        let cb = CircuitBreaker::new();
        let service = "test_service";
        
        cb.register_service(service.to_string(), CircuitBreakerConfig {
            failure_threshold: 2,
            recovery_timeout: Duration::from_millis(100),
            success_threshold: 1,
            request_timeout: Duration::from_secs(1),
        }).await;

        // Should start closed
        assert!(matches!(cb.get_service_state(service).await, Some(CircuitState::Closed)));

        // Simulate failures
        for _ in 0..2 {
            let result: Result<(), BasiliskError> = cb.execute(service, || async {
                Err(BasiliskError::Network(crate::error::NetworkError::RpcConnectionFailed {
                    endpoint: "test".to_string(),
                    reason: "Test failure".to_string()
                }))
            }).await;
            assert!(result.is_err());
        }

        // Should be open now
        assert!(matches!(cb.get_service_state(service).await, Some(CircuitState::Open)));

        // Should fail fast
        let result = cb.execute(service, || async {
            Ok("Should not execute")
        }).await;
        assert!(matches!(result, Err(BasiliskError::CircuitBreakerOpen { .. })));

        // Wait for recovery timeout
        sleep(Duration::from_millis(150)).await;

        // Should allow one test request (half-open)
        let result = cb.execute(service, || async {
            Ok("Success")
        }).await;
        assert!(result.is_ok());

        // Should be closed again
        assert!(matches!(cb.get_service_state(service).await, Some(CircuitState::Closed)));
    }
}