# 🚀 Zen Geometer Quick Start Guide

## 🔧 Starting the Bot

### Basic Command Structure
```bash
RUST_LOG=info cargo run -- [OPTIONS] COMMAND [ARGS]
```

### Release Mode (Better Performance)
```bash
RUST_LOG=info ./target/release/basilisk_bot [OPTIONS] COMMAND [ARGS]
```

## 📊 Operational Modes

### 1. Simulate Mode (Educational)
```bash
# Educational simulation with detailed reports
RUST_LOG=info cargo run -- -c config/testnet.toml simulate --detailed

# Filter by minimum profit
RUST_LOG=info cargo run -- -c config/testnet.toml simulate --min-profit 50

# Filter by scanner type
RUST_LOG=info cargo run -- -c config/testnet.toml simulate --scanner pilot
```

### 2. Shadow Mode (Fork Testing)
```bash
# Test with fork-based validation
RUST_LOG=info cargo run -- -c config/testnet.toml run --mode shadow --verbose
```

### 3. Sentinel Mode (Contract Monitoring)
```bash
# Monitor deployed contract health
RUST_LOG=info cargo run -- -c config/testnet.toml run --mode sentinel --verbose
```

### 4. Low-Capital Mode (Conservative Trading)
```bash
# Conservative trading with strict limits
RUST_LOG=info cargo run -- -c config/testnet.toml run --mode low-capital --verbose
```

### 5. Live Mode (Production Trading)
```bash
# Full production trading
RUST_LOG=info cargo run -- -c config/testnet.toml run --mode live --verbose
```

## 🖥️ TUI (Terminal User Interface)

### Basic TUI
```bash
# Start TUI with default config
cargo run -- tui
```

### TUI with Testnet Config
```bash
# Use testnet configuration
cargo run -- -c config/testnet.toml tui
```

### TUI with NATS Connection
```bash
# Connect to NATS message queue
NATS_URL=nats://localhost:4222 cargo run -- tui
```

### TUI in Release Mode
```bash
# Better performance
./target/release/basilisk_bot -c config/testnet.toml tui
```

## 🔍 Validation & Configuration

### Validate Configuration
```bash
# Basic validation
cargo run -- validate

# Detailed validation with reasons
cargo run -- validate --reason
```

### Configuration Management
```bash
# Show current configuration
cargo run -- config show

# Create new configuration profile
cargo run -- config create
```

## 📋 Utility Commands

### Utils
```bash
# Show available utilities
cargo run -- utils --help
```

## 🔧 Troubleshooting

### No Output Visible?
If you don't see any output, make sure to:

1. Add `RUST_LOG=info` before your command
2. Redirect stderr to stdout with `2>&1`
3. Use `--verbose` flag for more detailed output

Example:
```bash
RUST_LOG=info cargo run -- -c config/testnet.toml run --mode sentinel --verbose 2>&1 | cat
```

### TUI Not Working?
The TUI requires:
- Terminal with ANSI color support
- Proper terminal size (at least 80x24)
- NATS server for full functionality

### Contract Issues?
Verify the contract is deployed and accessible:
```bash
RUST_LOG=debug cargo run -- -c config/testnet.toml run --mode sentinel --verbose
```

## 📊 Logging & Output

### Log Levels
- `RUST_LOG=trace` - Most verbose (development)
- `RUST_LOG=debug` - Detailed debugging information
- `RUST_LOG=info` - Standard information (recommended)
- `RUST_LOG=warn` - Warnings and errors only
- `RUST_LOG=error` - Only errors

### Saving Output to File
```bash
# Save simulation output to file
RUST_LOG=info cargo run -- -c config/testnet.toml simulate --detailed > simulation_results.log
```

## 🚀 Production Deployment

### Build for Production
```bash
cargo build --release
```

### Run in Production
```bash
RUST_LOG=info ./target/release/basilisk_bot -c config/production.toml run --mode live --verbose
```

### Recommended Deployment Ladder
1. Start with `simulate` mode
2. Progress to `shadow` mode
3. Test with `sentinel` mode
4. Try `low-capital` mode
5. Finally use `live` mode

## 📈 Example Workflow

```bash
# 1. Build release version
cargo build --release

# 2. Run simulation to analyze market
RUST_LOG=info ./target/release/basilisk_bot -c config/testnet.toml simulate --detailed

# 3. Monitor contract health
RUST_LOG=info ./target/release/basilisk_bot -c config/testnet.toml run --mode sentinel --verbose

# 4. Start TUI for real-time monitoring
RUST_LOG=info ./target/release/basilisk_bot -c config/testnet.toml tui

# 5. Run in low-capital mode
RUST_LOG=info ./target/release/basilisk_bot -c config/testnet.toml run --mode low-capital --verbose
```