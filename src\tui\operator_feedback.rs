// MISSION: Operator Feedback Integration
// WHY: Bridge advanced error handling with operator-facing interfaces
// HOW: Real-time error processing, contextual alerts, and actionable guidance

use crate::logging::{ErrorCode, AlertSeverity, TradingContext, StructuredLogEntry};
use crate::tui::components::{TuiErrorEntry, Notification, NotificationType};
use crate::tui::app::App;
use tracing::{info, warn, error};
use std::sync::Arc;
use tokio::sync::mpsc;
use serde_json;

/// Error event processor for TUI integration
pub struct OperatorFeedbackProcessor {
    error_tx: mpsc::UnboundedSender<TuiErrorEntry>,
    notification_tx: mpsc::UnboundedSender<Notification>,
}

impl OperatorFeedbackProcessor {
    pub fn new() -> (Self, mpsc::UnboundedReceiver<TuiErrorEntry>, mpsc::UnboundedReceiver<Notification>) {
        let (error_tx, error_rx) = mpsc::unbounded_channel();
        let (notification_tx, notification_rx) = mpsc::unbounded_channel();
        
        (
            Self {
                error_tx,
                notification_tx,
            },
            error_rx,
            notification_rx,
        )
    }
    
    /// Process structured log entry and convert to operator feedback
    pub fn process_log_entry(&self, log_entry: &StructuredLogEntry) {
        // Convert to TUI error entry
        if let Some(error_code) = &log_entry.error_code {
            let tui_error = TuiErrorEntry::new(
                log_entry.severity.clone().unwrap_or(AlertSeverity::Info),
                error_code.clone(),
                log_entry.message.clone(),
                log_entry.context.clone(),
            );
            
            // Send to error dashboard
            if let Err(e) = self.error_tx.send(tui_error.clone()) {
                error!("Failed to send error to TUI dashboard: {}", e);
            }
            
            // Create notification for high-priority errors
            if matches!(log_entry.severity, Some(AlertSeverity::Critical) | Some(AlertSeverity::Error)) {
                let notification = Notification::from(tui_error);
                if let Err(e) = self.notification_tx.send(notification) {
                    error!("Failed to send notification to TUI: {}", e);
                }
            }
        }
    }
    
    /// Process trading events and create operator notifications
    pub fn process_trading_event(&self, event_type: &str, context: &TradingContext, details: &str) {
        let notification = match event_type {
            "opportunity_detected" => {
                Notification::new(
                    NotificationType::TradeAlert,
                    "Trading Opportunity",
                    format!("New {} opportunity detected: {}", 
                        context.strategy_type.as_deref().unwrap_or("unknown"), details)
                ).with_duration(8)
            }
            "trade_executed" => {
                Notification::new(
                    NotificationType::Success,
                    "Trade Executed",
                    format!("Successfully executed trade: {}", details)
                ).with_duration(10)
            }
            "trade_failed" => {
                Notification::new(
                    NotificationType::Error,
                    "Trade Failed",
                    format!("Trade execution failed: {}", details)
                ).requires_action()
            }
            "risk_alert" => {
                Notification::new(
                    NotificationType::Warning,
                    "Risk Alert",
                    details.to_string()
                ).requires_action()
            }
            _ => return,
        };
        
        if let Err(e) = self.notification_tx.send(notification) {
            error!("Failed to send trading notification: {}", e);
        }
    }
    
    /// Process system health updates
    pub fn process_system_health(&self, component: &str, status: &str, details: &str) {
        let notification = Notification::new(
            match status {
                "healthy" => NotificationType::Success,
                "degraded" => NotificationType::Warning,
                "failed" => NotificationType::Error,
                _ => NotificationType::Info,
            },
            format!("{} Status", component),
            details.to_string()
        ).with_duration(5);
        
        if let Err(e) = self.notification_tx.send(notification) {
            error!("Failed to send system health notification: {}", e);
        }
    }
}

/// Enhanced App methods for operator feedback
impl App {
    /// Initialize operator feedback channels
    pub fn setup_operator_feedback(&mut self) -> (mpsc::UnboundedReceiver<TuiErrorEntry>, mpsc::UnboundedReceiver<Notification>) {
        let (processor, error_rx, notification_rx) = OperatorFeedbackProcessor::new();
        
        // Store processor for later use (you might want to add this to App struct)
        // self.feedback_processor = Some(processor);
        
        info!("Operator feedback system initialized");
        (error_rx, notification_rx)
    }
    
    /// Process incoming error entries
    pub fn process_error_entry(&mut self, error_entry: TuiErrorEntry) {
        // Add to error dashboard
        self.error_dashboard.add_error(error_entry.clone());
        
        // Create notification if needed
        let notification = Notification::from(error_entry);
        self.notification_system.add_notification(notification);
        
        // Log for debugging
        info!("Processed error entry for operator feedback");
    }
    
    /// Process incoming notifications
    pub fn process_notification(&mut self, notification: Notification) {
        self.notification_system.add_notification(notification);
    }
    
    /// Handle error-related key events
    pub fn handle_error_dashboard_key(&mut self, key: KeyEvent) {
        match key.code {
            KeyCode::Char('e') => {
                // Toggle error details
                self.show_error_details = !self.show_error_details;
            }
            KeyCode::Char('d') => {
                // Toggle debug mode
                self.debug_mode = !self.debug_mode;
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "DEBUG".to_string(),
                    severity: crate::tui::app::LogSeverity::Info,
                    message: format!("Debug mode {}", if self.debug_mode { "enabled" } else { "disabled" }),
                });
            }
            KeyCode::Char('c') if self.current_tab == AppTab::Errors => {
                // Clear error filters
                self.error_dashboard.handle_key(key);
            }
            KeyCode::Char('a') => {
                // Acknowledge all notifications
                self.notification_system.acknowledge_all();
                self.add_log_event(crate::tui::app::LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "NOTIFICATIONS".to_string(),
                    severity: crate::tui::app::LogSeverity::Ok,
                    message: "All notifications acknowledged".to_string(),
                });
            }
            _ => {
                // Delegate to error dashboard
                self.error_dashboard.handle_key(key);
                self.notification_system.handle_key(key);
            }
        }
    }
    
    /// Create contextual error notifications based on system state
    pub fn create_contextual_error_feedback(&mut self, error_code: ErrorCode, context: &TradingContext) {
        let (title, message, action_hint) = match error_code {
            ErrorCode::ERpcTimeout => (
                "RPC Connection Issue",
                format!("RPC timeout on chain {}", context.chain_id.unwrap_or(0)),
                "Check network connectivity and consider switching RPC endpoints"
            ),
            ErrorCode::EHighSlippage => (
                "High Slippage Detected",
                format!("Slippage exceeded threshold for {}", 
                    context.token_path.as_ref().map(|p| p.join("->")).unwrap_or("unknown path".to_string())),
                "Consider reducing trade size or increasing slippage tolerance"
            ),
            ErrorCode::EInsufficientLiquidity => (
                "Liquidity Issue",
                format!("Insufficient liquidity for {} strategy", 
                    context.strategy_type.as_deref().unwrap_or("unknown")),
                "Wait for better market conditions or reduce position size"
            ),
            ErrorCode::EWalletAccessFailed => (
                "CRITICAL: Wallet Access Failed",
                "Unable to access trading wallet - all trading halted",
                "IMMEDIATE ACTION REQUIRED: Check wallet configuration and private key security"
            ),
            ErrorCode::EMevAttackDetected => (
                "SECURITY ALERT: MEV Attack",
                format!("Potential MEV attack detected on opportunity {}", 
                    context.opportunity_id.as_deref().unwrap_or("unknown")),
                "Consider using private mempool or adjusting MEV protection settings"
            ),
            _ => (
                "System Error",
                format!("Error in {}: {}", context.component, error_code.as_str()),
                "Check logs for detailed information"
            ),
        };
        
        let notification_type = match error_code.severity() {
            AlertSeverity::Critical => NotificationType::Critical,
            AlertSeverity::Error => NotificationType::Error,
            AlertSeverity::Warning => NotificationType::Warning,
            AlertSeverity::Info => NotificationType::Info,
        };
        
        let mut notification = Notification::new(notification_type, title, message)
            .with_error_code(error_code)
            .with_context(context.component.clone());
        
        // Make critical errors persistent and require action
        if matches!(notification.notification_type, NotificationType::Critical) {
            notification = notification.requires_action();
        }
        
        self.notification_system.add_notification(notification);
        
        // Add to log with action hint
        self.add_log_event(crate::tui::app::LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "ERROR_HANDLER".to_string(),
            severity: match error_code.severity() {
                AlertSeverity::Critical => crate::tui::app::LogSeverity::Error,
                AlertSeverity::Error => crate::tui::app::LogSeverity::Error,
                AlertSeverity::Warning => crate::tui::app::LogSeverity::Warn,
                AlertSeverity::Info => crate::tui::app::LogSeverity::Info,
            },
            message: format!("{} | Action: {}", message, action_hint),
        });
    }
    
    /// Update system status and create notifications
    pub fn update_system_status_with_feedback(&mut self, component: &str, status: &str, details: &str) {
        // Update notification system
        let component_status = match status {
            "healthy" => crate::tui::components::notification_system::SystemComponentStatus::Healthy,
            "degraded" => crate::tui::components::notification_system::SystemComponentStatus::Degraded,
            "failed" => crate::tui::components::notification_system::SystemComponentStatus::Failed,
            _ => crate::tui::components::notification_system::SystemComponentStatus::Unknown,
        };
        
        self.notification_system.update_system_status(
            component.to_string(),
            component_status,
            Some(details.to_string())
        );
        
        // Create notification for status changes
        if status != "healthy" {
            let notification = Notification::new(
                match status {
                    "degraded" => NotificationType::Warning,
                    "failed" => NotificationType::Error,
                    _ => NotificationType::Info,
                },
                format!("{} Status Change", component),
                format!("Status: {} - {}", status, details)
            );
            
            self.notification_system.add_notification(notification);
        }
    }
    
    /// Cleanup expired notifications and errors
    pub fn cleanup_operator_feedback(&mut self) {
        self.notification_system.cleanup_expired();
        // Error dashboard cleanup is handled internally
    }
    
    /// Get operator feedback summary for status display
    pub fn get_operator_feedback_summary(&self) -> String {
        let critical_count = self.notification_system.critical_alerts.len();
        let unread_count = self.notification_system.unread_count;
        let error_count = self.error_dashboard.error_stats.critical_count_last_hour + 
                         self.error_dashboard.error_stats.error_count_last_hour;
        
        if critical_count > 0 {
            format!("🚨 {} CRITICAL", critical_count)
        } else if error_count > 0 {
            format!("❌ {} errors", error_count)
        } else if unread_count > 0 {
            format!("📬 {} unread", unread_count)
        } else {
            "✅ All clear".to_string()
        }
    }
}

/// Utility functions for creating common operator feedback scenarios
pub mod feedback_utils {
    use super::*;
    
    pub fn create_startup_feedback(app: &mut App) {
        app.notification_system.add_notification(
            Notification::new(
                NotificationType::Success,
                "Basilisk Bot Started",
                "Enhanced error handling and operator feedback system active"
            ).with_duration(10)
        );
    }
    
    pub fn create_connection_feedback(app: &mut App, service: &str, success: bool, details: &str) {
        let notification = if success {
            Notification::new(
                NotificationType::Success,
                format!("{} Connected", service),
                details.to_string()
            ).with_duration(5)
        } else {
            Notification::new(
                NotificationType::Error,
                format!("{} Connection Failed", service),
                details.to_string()
            ).requires_action()
        };
        
        app.notification_system.add_notification(notification);
    }
    
    pub fn create_performance_feedback(app: &mut App, metric: &str, value: f64, threshold: f64) {
        if value > threshold {
            let notification = Notification::new(
                NotificationType::Warning,
                "Performance Alert",
                format!("{}: {:.2} exceeds threshold of {:.2}", metric, value, threshold)
            ).with_duration(8);
            
            app.notification_system.add_notification(notification);
        }
    }
    
    pub fn create_trade_opportunity_feedback(app: &mut App, strategy: &str, profit_estimate: rust_decimal::Decimal) {
        let notification = Notification::new(
            NotificationType::TradeAlert,
            "Opportunity Detected",
            format!("{} strategy found ${:.2} profit opportunity", strategy, profit_estimate)
        ).with_duration(6);
        
        app.notification_system.add_notification(notification);
    }
}