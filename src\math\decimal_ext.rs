use rust_decimal::Decimal;
use num_traits::{ToPrimitive, FromPrimitive};

/// Extension trait for Decimal to add missing methods
pub trait DecimalExt {
    /// Returns the default value if self is None
    fn unwrap_or_default(self) -> Decimal;
    
    /// Square root implementation for Decimal
    fn sqrt(&self) -> Option<Decimal>;
}

impl DecimalExt for Option<Decimal> {
    fn unwrap_or_default(self) -> Decimal {
        self.unwrap_or(Decimal::ZERO)
    }
    
    fn sqrt(&self) -> Option<Decimal> {
        match self {
            Some(decimal) => decimal.sqrt(),
            None => None,
        }
    }
}

impl DecimalExt for Decimal {
    fn unwrap_or_default(self) -> Decimal {
        self
    }
    
    /// Calculate square root using f64 conversion
    fn sqrt(&self) -> Option<Decimal> {
        let float_val = self.to_f64()?;
        if float_val < 0.0 {
            return None;
        }
        Decimal::from_f64(float_val.sqrt())
    }
}