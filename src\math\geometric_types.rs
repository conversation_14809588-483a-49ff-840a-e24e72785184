
use ethers::types::{Address, U256};
use rust_decimal::Decimal;
use anyhow::Result;
use async_trait::async_trait;
use geo::Point;

use crate::data::oracle::PriceOracle;

// Represents the specific data for different AMM models.
pub enum AmmModelData {
    // For Constant Product Market Makers (CPMMs) like Uniswap V2
    Cpmm { k_value: Decimal },
    // For Concentrated Liquidity (CLMMs) like Uniswap V3
    Clmm { 
        sqrt_price_x96: U256, // Current price tick
        liquidity: u128,      // Liquidity in the active range
    },
    // Future: Csm, Balancer, etc.
}

// Represents a single liquidity pool, abstracting over the AMM model.
pub struct LiquidityPool {
    pub address: Address,
    pub token_a_address: Address,
    pub token_b_address: Address,
    pub reserve_a: Decimal,
    pub reserve_b: Decimal,
    pub model_data: AmmModelData,
}

pub type ArbitragePath = Vec<LiquidityPool>;

#[async_trait]
pub trait ToGeometricPoint {
    /// Converts the pool into a 2D point in a USD-normalized value space.
    async fn to_usd_point(&self, oracle: &dyn PriceOracle) -> Result<Point>;
    
    /// Calculates the total economic weight (liquidity) of the pool in USD.
    async fn get_liquidity_usd(&self, oracle: &dyn PriceOracle) -> Result<Decimal>;
}

#[async_trait]
impl ToGeometricPoint for LiquidityPool {
    async fn to_usd_point(&self, oracle: &dyn PriceOracle) -> Result<Point> {
        // Get prices for both tokens in a single call for efficiency
        let tokens = vec![self.token_a_address, self.token_b_address];
        let prices = oracle.get_prices(&tokens).await?;
        
        let price_a = prices.get(&self.token_a_address)
            .ok_or_else(|| anyhow::anyhow!("Price not found for token A: {:?}", self.token_a_address))?;
        let price_b = prices.get(&self.token_b_address)
            .ok_or_else(|| anyhow::anyhow!("Price not found for token B: {:?}", self.token_b_address))?;

        let reserve_a_usd = self.reserve_a * *price_a;
        let reserve_b_usd = self.reserve_b * *price_b;

        Ok(Point::new(reserve_a_usd.try_into()?, reserve_b_usd.try_into()?))
    }

    async fn get_liquidity_usd(&self, oracle: &dyn PriceOracle) -> Result<Decimal> {
        // Get prices for both tokens in a single call for efficiency
        let tokens = vec![self.token_a_address, self.token_b_address];
        let prices = oracle.get_prices(&tokens).await?;
        
        let price_a = prices.get(&self.token_a_address)
            .ok_or_else(|| anyhow::anyhow!("Price not found for token A: {:?}", self.token_a_address))?;
        let price_b = prices.get(&self.token_b_address)
            .ok_or_else(|| anyhow::anyhow!("Price not found for token B: {:?}", self.token_b_address))?;

        match &self.model_data {
            AmmModelData::Cpmm { .. } => {
                Ok((self.reserve_a * *price_a) + (self.reserve_b * *price_b))
            }
            AmmModelData::Clmm { .. } => {
                // For CLMMs, the reserves are not the full TVL.
                // A more accurate calculation would involve the active liquidity range.
                // For now, we'll fall back to the same logic as CPMM.
                // CRITICAL: Accurate CLMM TVL calculation is complex and requires
                // understanding Uniswap V3's liquidity accounting (sqrt_price_x96, liquidity, ticks).
                // This is a placeholder and will lead to inaccurate liquidity assessment for CLMMs.
                // A dedicated implementation or external library is needed here.
                Ok((self.reserve_a * *price_a) + (self.reserve_b * *price_b))
            }
        }
    }
}
