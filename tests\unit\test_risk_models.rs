// Unit Tests for Risk Management Models
// Tests Kelly Criterion, VaR calculations, and risk invariants

use basilisk_bot::risk::manager::RiskManager;
use basilisk_bot::shared_types::{Opportunity, OpportunityBase, DexArbitrageData, MarketRegime};
use ethers::types::{Address, U256};
use proptest::prelude::*;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::str::FromStr;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_kelly_criterion_basic() {
        let risk_manager = RiskManager::new();
        
        // Test with known profitable scenario
        let win_rate = dec!(0.6); // 60% win rate
        let avg_win = dec!(150.0); // Average win $150
        let avg_loss = dec!(100.0); // Average loss $100
        
        let kelly_fraction = risk_manager.calculate_kelly_fraction(win_rate, avg_win, avg_loss);
        
        // Kelly fraction should be positive for profitable scenarios
        assert!(kelly_fraction > dec!(0.0));
        // <PERSON> fraction should be reasonable (not over-leveraged)
        assert!(kelly_fraction < dec!(1.0));
    }

    #[test]
    fn test_kelly_criterion_unprofitable() {
        let risk_manager = RiskManager::new();
        
        // Test with unprofitable scenario
        let win_rate = dec!(0.3); // 30% win rate
        let avg_win = dec!(100.0); // Average win $100
        let avg_loss = dec!(200.0); // Average loss $200
        
        let kelly_fraction = risk_manager.calculate_kelly_fraction(win_rate, avg_win, avg_loss);
        
        // Kelly fraction should be zero or negative for unprofitable scenarios
        assert!(kelly_fraction <= dec!(0.0));
    }

    #[test]
    fn test_position_sizing_with_regime() {
        let risk_manager = RiskManager::new();
        let base_capital = dec!(10000.0); // $10,000 base capital
        
        // Test different market regimes
        let regimes = vec![
            (MarketRegime::CalmOrderly, dec!(1.0)),
            (MarketRegime::HighVolatility, dec!(0.5)),
            (MarketRegime::BotGasWar, dec!(0.25)),
            (MarketRegime::RetailFomo, dec!(1.2)),
        ];
        
        for (regime, expected_multiplier) in regimes {
            let position_size = risk_manager.calculate_position_size(
                base_capital,
                dec!(0.1), // 10% Kelly fraction
                regime,
            );
            
            let expected_size = base_capital * dec!(0.1) * expected_multiplier;
            assert!(
                (position_size - expected_size).abs() < dec!(0.01),
                "Position size mismatch for regime {:?}: expected {}, got {}",
                regime,
                expected_size,
                position_size
            );
        }
    }

    #[test]
    fn test_risk_limits_enforcement() {
        let risk_manager = RiskManager::new();
        
        // Test maximum position size limit
        let large_kelly = dec!(0.8); // 80% Kelly (very aggressive)
        let capital = dec!(100000.0);
        
        let position_size = risk_manager.calculate_position_size(
            capital,
            large_kelly,
            MarketRegime::CalmOrderly,
        );
        
        // Position should be capped at reasonable maximum
        let max_position = capital * dec!(0.25); // 25% max
        assert!(position_size <= max_position);
    }

    #[test]
    fn test_volatility_adjustment() {
        let risk_manager = RiskManager::new();
        
        // Test volatility-based position adjustment
        let base_position = dec!(1000.0);
        let low_volatility = dec!(0.1); // 10% volatility
        let high_volatility = dec!(0.5); // 50% volatility
        
        let low_vol_position = risk_manager.adjust_for_volatility(base_position, low_volatility);
        let high_vol_position = risk_manager.adjust_for_volatility(base_position, high_volatility);
        
        // Higher volatility should result in smaller position
        assert!(high_vol_position < low_vol_position);
        assert!(high_vol_position < base_position);
    }

    #[test]
    fn test_drawdown_protection() {
        let mut risk_manager = RiskManager::new();
        
        // Simulate series of losses
        let initial_capital = dec!(10000.0);
        let mut current_capital = initial_capital;
        
        // Simulate 10% loss
        current_capital = current_capital * dec!(0.9);
        risk_manager.update_drawdown(current_capital, initial_capital);
        
        // Position sizing should be reduced due to drawdown
        let position_after_loss = risk_manager.calculate_position_size(
            current_capital,
            dec!(0.1),
            MarketRegime::CalmOrderly,
        );
        
        let normal_position = current_capital * dec!(0.1);
        assert!(position_after_loss <= normal_position);
    }
}

// Property-based tests for risk invariants
proptest! {
    #[test]
    fn test_kelly_fraction_bounds(
        win_rate in 0.01..0.99_f64,
        avg_win in 1.0..1000.0_f64,
        avg_loss in 1.0..1000.0_f64
    ) {
        let risk_manager = RiskManager::new();
        let win_rate_decimal = Decimal::from_f64(win_rate).unwrap();
        let avg_win_decimal = Decimal::from_f64(avg_win).unwrap();
        let avg_loss_decimal = Decimal::from_f64(avg_loss).unwrap();
        
        let kelly_fraction = risk_manager.calculate_kelly_fraction(
            win_rate_decimal,
            avg_win_decimal,
            avg_loss_decimal
        );
        
        // Kelly fraction should be bounded
        prop_assert!(kelly_fraction >= dec!(-1.0));
        prop_assert!(kelly_fraction <= dec!(1.0));
    }

    #[test]
    fn test_position_size_properties(
        capital in 1000.0..1000000.0_f64,
        kelly_fraction in 0.0..0.5_f64
    ) {
        let risk_manager = RiskManager::new();
        let capital_decimal = Decimal::from_f64(capital).unwrap();
        let kelly_decimal = Decimal::from_f64(kelly_fraction).unwrap();
        
        let position_size = risk_manager.calculate_position_size(
            capital_decimal,
            kelly_decimal,
            MarketRegime::CalmOrderly,
        );
        
        // Position size should never exceed capital
        prop_assert!(position_size <= capital_decimal);
        // Position size should be non-negative
        prop_assert!(position_size >= Decimal::ZERO);
        // Position size should scale with capital
        prop_assert!(position_size <= capital_decimal * kelly_decimal * dec!(2.0));
    }

    #[test]
    fn test_volatility_adjustment_properties(
        base_position in 100.0..10000.0_f64,
        volatility in 0.01..2.0_f64
    ) {
        let risk_manager = RiskManager::new();
        let base_decimal = Decimal::from_f64(base_position).unwrap();
        let vol_decimal = Decimal::from_f64(volatility).unwrap();
        
        let adjusted_position = risk_manager.adjust_for_volatility(base_decimal, vol_decimal);
        
        // Adjusted position should be positive
        prop_assert!(adjusted_position > Decimal::ZERO);
        // High volatility should reduce position size
        if vol_decimal > dec!(0.3) {
            prop_assert!(adjusted_position <= base_decimal);
        }
    }
}