// MISSION: Resonance Chamber - ARE Decision Visualization
// WHY: Show the Aetheric Resonance Engine's thought process in real-time
// HOW: Rich narrative display of opportunity analysis with educational context

use async_nats::Client as NatsClient;
use ratatui::{
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span, Text},
    widgets::{Block, Borders, Clear, List, ListItem, ListState, Paragraph, Wrap},
    Frame,
};
use std::collections::VecDeque;
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::{debug, error, info};

use crate::shared_types::NatsTopics;
use crate::shared_types::are_analysis::AREAnalysisReport;

/// Maximum number of analysis reports to keep in memory
const MAX_ANALYSIS_HISTORY: usize = 100;

/// The Resonance Chamber component - displays ARE analysis in narrative form
pub struct ResonanceChamber {
    /// Recent analysis reports for display
    analysis_history: VecDeque<AREAnalysisReport>,
    
    /// Currently selected analysis for detailed view
    selected_analysis: Option<usize>,
    
    /// List state for navigation
    list_state: ListState,
    
    /// Whether to show detailed view or summary list
    show_detailed_view: bool,
    
    /// NATS client for subscribing to analysis reports
    nats_client: Option<NatsClient>,
    
    /// Receiver for analysis reports from NATS subscription
    analysis_receiver: Option<mpsc::Receiver<AREAnalysisReport>>,
}

impl ResonanceChamber {
    /// Create a new Resonance Chamber component
    pub fn new() -> Self {
        Self {
            analysis_history: VecDeque::with_capacity(MAX_ANALYSIS_HISTORY),
            selected_analysis: None,
            list_state: ListState::default(),
            show_detailed_view: false,
            nats_client: None,
            analysis_receiver: None,
        }
    }
    
    /// Initialize the component with NATS client
    pub async fn init(&mut self, nats_client: NatsClient) -> Result<(), Box<dyn std::error::Error>> {
        info!("Initializing Resonance Chamber with NATS subscription");
        
        // Create channel for analysis reports
        let (tx, rx) = mpsc::channel(100);
        self.analysis_receiver = Some(rx);
        
        // Subscribe to ARE analysis topic
        let mut subscriber = nats_client
            .subscribe(NatsTopics::LIVING_CODEX_ARE_ANALYSIS)
            .await?;
        
        // Spawn task to handle incoming analysis reports
        tokio::spawn(async move {
            info!("Resonance Chamber: Listening for ARE analysis reports...");
            
            while let Some(msg) = subscriber.next().await {
                match serde_json::from_slice::<AREAnalysisReport>(&msg.payload) {
                    Ok(report) => {
                        debug!("Resonance Chamber: Received ARE analysis report: {}", report.analysis_id);
                        if let Err(e) = tx.send(report).await {
                            error!("Failed to send analysis report to Resonance Chamber: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Failed to deserialize ARE analysis report: {}", e);
                    }
                }
            }
            
            info!("Resonance Chamber: NATS subscription ended");
        });
        
        self.nats_client = Some(nats_client);
        Ok(())
    }
    
    /// Update the component state (call this regularly from the main loop)
    pub async fn update(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Process any new analysis reports
        let mut reports_to_process = Vec::new();
        if let Some(receiver) = &mut self.analysis_receiver {
            while let Ok(report) = receiver.try_recv() {
                reports_to_process.push(report);
            }
        }
        
        // Process all collected reports
        for report in reports_to_process {
            self.add_analysis_report(report);
        }
        
        Ok(())
    }
    
    /// Add a new analysis report to the history
    fn add_analysis_report(&mut self, report: AREAnalysisReport) {
        // Add to front of deque (most recent first)
        self.analysis_history.push_front(report);
        
        // Maintain maximum history size
        if self.analysis_history.len() > MAX_ANALYSIS_HISTORY {
            self.analysis_history.pop_back();
        }
        
        // If this is the first report, select it
        if self.analysis_history.len() == 1 {
            self.selected_analysis = Some(0);
            self.list_state.select(Some(0));
        }
    }
    
    /// Handle keyboard input
    pub fn handle_key(&mut self, key: crossterm::event::KeyCode) {
        match key {
            crossterm::event::KeyCode::Up => self.previous_analysis(),
            crossterm::event::KeyCode::Down => self.next_analysis(),
            crossterm::event::KeyCode::Enter | crossterm::event::KeyCode::Char(' ') => {
                self.toggle_detailed_view();
            }
            crossterm::event::KeyCode::Esc => {
                self.show_detailed_view = false;
            }
            _ => {}
        }
    }
    
    /// Move to previous analysis in the list
    fn previous_analysis(&mut self) {
        if self.analysis_history.is_empty() {
            return;
        }
        
        let current = self.selected_analysis.unwrap_or(0);
        let new_index = if current == 0 {
            self.analysis_history.len() - 1
        } else {
            current - 1
        };
        
        self.selected_analysis = Some(new_index);
        self.list_state.select(Some(new_index));
    }
    
    /// Move to next analysis in the list
    fn next_analysis(&mut self) {
        if self.analysis_history.is_empty() {
            return;
        }
        
        let current = self.selected_analysis.unwrap_or(0);
        let new_index = if current >= self.analysis_history.len() - 1 {
            0
        } else {
            current + 1
        };
        
        self.selected_analysis = Some(new_index);
        self.list_state.select(Some(new_index));
    }
    
    /// Toggle between summary list and detailed view
    fn toggle_detailed_view(&mut self) {
        if self.selected_analysis.is_some() {
            self.show_detailed_view = !self.show_detailed_view;
        }
    }
    
    /// Render the component
    pub fn render(&mut self, f: &mut Frame, area: Rect) {
        if self.show_detailed_view && self.selected_analysis.is_some() {
            self.render_detailed_view(f, area);
        } else {
            self.render_summary_list(f, area);
        }
    }
    
    /// Render the summary list of analysis reports
    fn render_summary_list(&mut self, f: &mut Frame, area: Rect) {
        let block = Block::default()
            .title("🔍 Resonance Chamber - ARE Analysis")
            .borders(Borders::ALL)
            .border_style(Style::default().fg(Color::Cyan));
        
        if self.analysis_history.is_empty() {
            let empty_text = Paragraph::new("Awaiting ARE analysis reports...\n\nThe Aetheric Resonance Engine will display its decision-making process here.")
                .block(block)
                .style(Style::default().fg(Color::Gray))
                .wrap(Wrap { trim: true });
            
            f.render_widget(empty_text, area);
            return;
        }
        
        // Create list items from analysis history
        let items: Vec<ListItem> = self
            .analysis_history
            .iter()
            .map(|report| {
                let style = match &report.decision {
                    crate::shared_types::are_analysis::AREDecision::Approved => {
                        Style::default().fg(Color::Green)
                    }
                    crate::shared_types::are_analysis::AREDecision::Rejected { .. } => {
                        Style::default().fg(Color::Red)
                    }
                };
                
                let summary = report.to_summary();
                ListItem::new(Line::from(Span::styled(summary, style)))
            })
            .collect();
        
        let list = List::new(items)
            .block(block)
            .highlight_style(Style::default().bg(Color::DarkGray).add_modifier(Modifier::BOLD))
            .highlight_symbol("► ");
        
        f.render_stateful_widget(list, area, &mut self.list_state);
        
        // Show help text at the bottom
        let help_area = Rect {
            x: area.x + 1,
            y: area.y + area.height - 2,
            width: area.width - 2,
            height: 1,
        };
        
        let help_text = Paragraph::new("↑↓: Navigate | Enter/Space: Details | ESC: Back")
            .style(Style::default().fg(Color::Gray));
        
        f.render_widget(help_text, help_area);
    }
    
    /// Render the detailed view of a selected analysis
    fn render_detailed_view(&mut self, f: &mut Frame, area: Rect) {
        if let Some(index) = self.selected_analysis {
            if let Some(report) = self.analysis_history.get(index) {
                // Clear the area first
                f.render_widget(Clear, area);
                
                let block = Block::default()
                    .title("🔍 ARE Analysis - Detailed View")
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Cyan));
                
                let narrative = report.to_narrative();
                let text = Text::from(narrative);
                
                let paragraph = Paragraph::new(text)
                    .block(block)
                    .wrap(Wrap { trim: true })
                    .style(Style::default().fg(Color::White));
                
                f.render_widget(paragraph, area);
                
                // Show help text at the bottom
                let help_area = Rect {
                    x: area.x + 1,
                    y: area.y + area.height - 2,
                    width: area.width - 2,
                    height: 1,
                };
                
                let help_text = Paragraph::new("ESC: Back to list | ↑↓: Navigate reports")
                    .style(Style::default().fg(Color::Gray));
                
                f.render_widget(help_text, help_area);
            }
        }
    }
}

impl Default for ResonanceChamber {
    fn default() -> Self {
        Self::new()
    }
}