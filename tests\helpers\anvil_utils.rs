// Anvil Testing Utilities
// Helper functions for blockchain simulation testing

use ethers::{
    providers::{Provider, Http},
    utils::Anvil,
    types::{Address, U256, TransactionRequest},
    signers::LocalWallet,
};
use std::sync::Arc;
use anyhow::Result;

/// Anvil test environment with common setup
pub struct AnvilTestEnv {
    pub anvil: Anvil,
    pub provider: Arc<Provider<Http>>,
    pub wallet: LocalWallet,
    pub chain_id: u64,
}

impl AnvilTestEnv {
    /// Create new Anvil environment with Base fork
    pub async fn new_base_fork() -> Result<Self> {
        let anvil = Anvil::new()
            .fork("https://mainnet.base.org")
            .fork_block_number(20000000u64)
            .chain_id(8453u64)
            .spawn();
        
        let provider = Arc::new(Provider::<Http>::try_from(anvil.endpoint())?);
        let wallet = anvil.keys()[0].clone().into();
        
        Ok(Self {
            anvil,
            provider,
            wallet,
            chain_id: 8453,
        })
    }

    /// Create new Anvil environment without fork (clean state)
    pub async fn new_clean() -> Result<Self> {
        let anvil = Anvil::new()
            .chain_id(31337u64)
            .spawn();
        
        let provider = Arc::new(Provider::<Http>::try_from(anvil.endpoint())?);
        let wallet = anvil.keys()[0].clone().into();
        
        Ok(Self {
            anvil,
            provider,
            wallet,
            chain_id: 31337,
        })
    }

    /// Fund an address with ETH
    pub async fn fund_address(&self, address: Address, amount_eth: u64) -> Result<()> {
        let tx = TransactionRequest::new()
            .to(address)
            .value(U256::from(amount_eth) * U256::exp10(18));
        
        // In Anvil, we can send from any address
        self.provider.send_transaction(tx, None).await?;
        Ok(())
    }

    /// Get current block number
    pub async fn current_block(&self) -> Result<U256> {
        Ok(self.provider.get_block_number().await?)
    }

    /// Mine blocks
    pub async fn mine_blocks(&self, count: u64) -> Result<()> {
        for _ in 0..count {
            self.provider.send("evm_mine", ()).await?;
        }
        Ok(())
    }

    /// Set next block timestamp
    pub async fn set_next_block_timestamp(&self, timestamp: u64) -> Result<()> {
        self.provider.send("evm_setNextBlockTimestamp", [timestamp]).await?;
        Ok(())
    }

    /// Take snapshot of current state
    pub async fn snapshot(&self) -> Result<U256> {
        let snapshot_id: U256 = self.provider.send("evm_snapshot", ()).await?;
        Ok(snapshot_id)
    }

    /// Revert to snapshot
    pub async fn revert_to_snapshot(&self, snapshot_id: U256) -> Result<()> {
        self.provider.send("evm_revert", [snapshot_id]).await?;
        Ok(())
    }
}

/// Common test token addresses for Anvil testing
pub mod test_tokens {
    use ethers::types::Address;
    use std::str::FromStr;

    // Base mainnet token addresses (for forked testing)
    pub fn weth() -> Address {
        Address::from_str("******************************************").unwrap()
    }

    pub fn usdc() -> Address {
        Address::from_str("******************************************").unwrap()
    }

    pub fn dai() -> Address {
        Address::from_str("******************************************").unwrap()
    }

    // Mock addresses for clean Anvil testing
    pub fn mock_weth() -> Address {
        Address::from([0x11; 20])
    }

    pub fn mock_usdc() -> Address {
        Address::from([0x22; 20])
    }

    pub fn mock_dai() -> Address {
        Address::from([0x33; 20])
    }
}

/// Utility functions for common Anvil operations
pub mod utils {
    use super::*;

    /// Wait for transaction confirmation
    pub async fn wait_for_tx(provider: &Provider<Http>, tx_hash: ethers::types::H256) -> Result<()> {
        provider.get_transaction_receipt(tx_hash).await?;
        Ok(())
    }

    /// Check if Anvil is available
    pub fn is_anvil_available() -> bool {
        std::env::var("ANVIL_RPC_URL").is_ok() || 
        std::process::Command::new("anvil").arg("--version").output().is_ok()
    }

    /// Skip test if Anvil not available
    pub fn skip_if_no_anvil(test_name: &str) {
        if !is_anvil_available() {
            println!("Skipping {} - Anvil not available", test_name);
            return;
        }
    }
}