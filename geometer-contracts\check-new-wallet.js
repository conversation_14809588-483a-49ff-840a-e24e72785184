async function main() {
  console.log("🔍 Checking wallet from private key...");
  
  // Get the signer from the configured private key
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  const balance = await ethers.provider.getBalance(address);
  
  console.log(`Wallet Address: ${address}`);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  console.log(`Private Key (first 10 chars): ${process.env.BASILISK_EXECUTION_PRIVATE_KEY?.substring(0, 10)}...`);
  
  if (balance > 0n) {
    console.log("✅ Wallet has ETH! Ready for deployment.");
    return true;
  } else {
    console.log("❌ Wallet still needs funding.");
    return false;
  }
}

main().then(hasBalance => {
  if (hasBalance) {
    console.log("🚀 Proceeding with deployment...");
  } else {
    console.log("💰 Please fund the wallet first.");
  }
}).catch(console.error);
