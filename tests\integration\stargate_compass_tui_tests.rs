// MISSION: StargateCompass TUI Integration Tests
// WHY: Verify TUI commands work correctly with StargateCompassV1 contract
// HOW: Execute TUI commands programmatically and validate contract interactions

use anyhow::Result;
use std::time::Duration;
use tokio::time::timeout;
use tracing::{info, warn};

mod stargate_compass;

use stargate_compass::{
    TuiIntegrationTestOrchestrator,
    TuiFunctionalityTester,
    TuiOutputParser,
};

/// Test TUI command identification and basic execution
#[tokio::test]
async fn test_tui_command_identification() -> Result<()> {
    // Initialize logging for test
    let _ = tracing_subscriber::fmt::try_init();

    info!("Testing TUI command identification framework");

    let tester = TuiFunctionalityTester::new(
        "http://localhost:8545".to_string(),
        "******************************************".to_string(),
    );

    // Test that all expected commands are available
    let available_commands = tester.get_available_commands();
    
    assert!(!available_commands.is_empty(), "No TUI commands found");
    assert!(available_commands.contains(&"emergency_stop".to_string()), "Emergency stop command not found");
    assert!(available_commands.contains(&"pause_bot".to_string()), "Pause bot command not found");
    assert!(available_commands.contains(&"restart_bot".to_string()), "Restart bot command not found");
    assert!(available_commands.contains(&"execute_opportunity".to_string()), "Execute opportunity command not found");
    assert!(available_commands.contains(&"query_balances".to_string()), "Query balances command not found");
    assert!(available_commands.contains(&"query_contract_status".to_string()), "Query contract status command not found");

    info!("✅ All expected TUI commands are available: {:?}", available_commands);

    // Test command definitions
    for command_name in &available_commands {
        let command_def = tester.get_command_definition(command_name);
        assert!(command_def.is_some(), "Command definition not found for: {}", command_name);
        
        let def = command_def.unwrap();
        assert!(!def.key_sequence.is_empty(), "Empty key sequence for command: {}", command_name);
        assert!(!def.expected_output_patterns.is_empty(), "No expected output patterns for command: {}", command_name);
        assert!(def.timeout_seconds > 0, "Invalid timeout for command: {}", command_name);
        
        info!("✅ Command '{}' has valid definition: {}", command_name, def.description);
    }

    Ok(())
}

/// Test TUI output parsing capabilities
#[tokio::test]
async fn test_tui_output_parsing() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    info!("Testing TUI output parsing framework");

    let parser = TuiOutputParser::new()?;

    // Test log entry parsing
    let sample_log_output = r#"
12:34:56.789 MASTER_CONTROL INFO Bot status changed to: RUNNING
12:34:57.123 EMERGENCY ERROR EMERGENCY STOP ACTIVATED - command sent to all services
12:34:58.456 EXECUTOR OK Trade executed successfully: $125.50 profit
"#;

    let parsed = parser.parse_output(sample_log_output)?;
    
    assert_eq!(parsed.log_entries.len(), 3, "Expected 3 log entries");
    assert!(parsed.log_entries.iter().any(|e| e.message.contains("EMERGENCY STOP")), "Emergency stop log not found");
    assert!(parsed.log_entries.iter().any(|e| e.message.contains("Trade executed")), "Trade execution log not found");

    info!("✅ Log entry parsing works correctly");

    // Test balance parsing
    let sample_balance_output = r#"
Total Balance: $1,234.56 USD
Available: $987.65 USD
Locked: $246.91 USD
ETH Balance: 5.67 ETH
USDC Balance: 1,000.00 USDC
"#;

    let parsed_balance = parser.parse_output(sample_balance_output)?;
    
    assert!(parsed_balance.balance_data.is_some(), "Balance data not parsed");
    let balance = parsed_balance.balance_data.unwrap();
    assert_eq!(balance.total_balance_usd, Some(1234.56), "Total balance not parsed correctly");
    assert_eq!(balance.available_balance_usd, Some(987.65), "Available balance not parsed correctly");
    assert_eq!(balance.token_balances.get("ETH"), Some(&5.67), "ETH balance not parsed correctly");

    info!("✅ Balance parsing works correctly");

    // Test contract interaction parsing
    let sample_contract_output = r#"
12:34:56 EMERGENCY STOP ACTIVATED - command sent to all services
Transaction sent to contract ****************************************** with gas 21000
Executing opportunity OPP_0001 - Est. Profit: $125.50
"#;

    let parsed_contract = parser.parse_output(sample_contract_output)?;
    
    assert!(!parsed_contract.contract_interactions.is_empty(), "Contract interactions not parsed");
    assert!(!parsed_contract.command_acknowledgments.is_empty(), "Command acknowledgments not parsed");

    let contract_data = parser.extract_contract_data(&parsed_contract);
    assert!(contract_data.emergency_stops > 0, "Emergency stop not detected");
    assert!(contract_data.opportunity_executions > 0, "Opportunity execution not detected");

    info!("✅ Contract interaction parsing works correctly");

    Ok(())
}

/// Test TUI command execution framework (mock test - doesn't require actual TUI)
#[tokio::test]
async fn test_tui_command_execution_framework() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    info!("Testing TUI command execution framework");

    // This test validates the framework structure without actually spawning TUI
    // In a real integration test environment, this would spawn the actual TUI process

    let mut tester = TuiFunctionalityTester::new(
        "http://localhost:8545".to_string(),
        "******************************************".to_string(),
    );

    // Test command validation
    let commands_to_test = vec![
        "emergency_stop",
        "pause_bot", 
        "restart_bot",
        "query_balances",
    ];

    for command_name in commands_to_test {
        let command_def = tester.get_command_definition(command_name);
        assert!(command_def.is_some(), "Command definition missing for: {}", command_name);
        
        let def = command_def.unwrap();
        
        // Validate command structure
        assert!(!def.key_sequence.is_empty(), "Empty key sequence for: {}", command_name);
        assert!(!def.expected_output_patterns.is_empty(), "No expected patterns for: {}", command_name);
        assert!(def.timeout_seconds > 0 && def.timeout_seconds <= 60, "Invalid timeout for: {}", command_name);
        assert!(def.contract_interaction, "Command should indicate contract interaction: {}", command_name);
        
        info!("✅ Command '{}' structure validated", command_name);
    }

    // Test timeout handling (simulate)
    let timeout_test = timeout(
        Duration::from_millis(100),
        async {
            // Simulate a long-running operation
            tokio::time::sleep(Duration::from_millis(200)).await;
            Ok::<(), anyhow::Error>(())
        }
    ).await;

    assert!(timeout_test.is_err(), "Timeout handling not working");
    info!("✅ Timeout handling works correctly");

    Ok(())
}

/// Test comprehensive TUI integration (requires running infrastructure)
#[tokio::test]
#[ignore] // Ignore by default since it requires full infrastructure
async fn test_comprehensive_tui_integration() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    info!("Running comprehensive TUI integration test");

    // This test requires:
    // 1. Anvil running on localhost:8545
    // 2. NATS server running on localhost:4222
    // 3. StargateCompassV1 contract deployed
    // 4. TUI harness binary available

    let anvil_url = std::env::var("ANVIL_URL").unwrap_or_else(|_| "http://localhost:8545".to_string());
    let contract_address = std::env::var("CONTRACT_ADDRESS")
        .unwrap_or_else(|_| "******************************************".to_string());

    let mut orchestrator = TuiIntegrationTestOrchestrator::new(anvil_url, contract_address)?;

    // Run comprehensive test suite
    let test_results = orchestrator.run_comprehensive_tests().await?;

    // Validate results
    assert!(test_results.total_tests > 0, "No tests were executed");
    
    let success_rate = test_results.success_rate();
    info!("Test suite success rate: {:.2}%", success_rate * 100.0);
    
    // We expect at least 50% success rate for a passing integration test
    assert!(success_rate >= 0.5, "Success rate too low: {:.2}%", success_rate * 100.0);
    
    // Validate that contract interactions were detected
    assert!(test_results.contract_interactions_detected > 0, "No contract interactions detected");
    
    info!("✅ Comprehensive TUI integration test completed successfully");
    info!("Results: {}/{} tests passed, {} contract interactions detected", 
          test_results.passed_tests, 
          test_results.total_tests,
          test_results.contract_interactions_detected);

    Ok(())
}

/// Test TUI stress testing capabilities
#[tokio::test]
#[ignore] // Ignore by default since it requires full infrastructure
async fn test_tui_stress_testing() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    info!("Running TUI stress test");

    let anvil_url = std::env::var("ANVIL_URL").unwrap_or_else(|_| "http://localhost:8545".to_string());
    let contract_address = std::env::var("CONTRACT_ADDRESS")
        .unwrap_or_else(|_| "******************************************".to_string());

    let mut orchestrator = TuiIntegrationTestOrchestrator::new(anvil_url, contract_address)?;

    // Run stress test with 10 iterations
    let stress_results = orchestrator.run_stress_test(10).await?;

    // Validate stress test results
    assert_eq!(stress_results.total_commands, 10, "Expected 10 commands to be executed");
    
    let success_rate = stress_results.success_rate();
    info!("Stress test success rate: {:.2}%", success_rate * 100.0);
    
    // We expect at least 70% success rate under stress
    assert!(success_rate >= 0.7, "Stress test success rate too low: {:.2}%", success_rate * 100.0);
    
    // Validate performance metrics
    assert!(stress_results.average_execution_time > 0, "Invalid average execution time");
    assert!(stress_results.average_execution_time < 30000, "Average execution time too high: {}ms", stress_results.average_execution_time);
    
    info!("✅ TUI stress test completed successfully");
    info!("Stress test results: {}/{} commands successful, avg execution time: {}ms", 
          stress_results.successful_commands, 
          stress_results.total_commands,
          stress_results.average_execution_time);

    if !stress_results.errors.is_empty() {
        warn!("Stress test errors encountered: {:?}", stress_results.errors);
    }

    Ok(())
}

/// Test custom command validation
#[tokio::test]
async fn test_custom_command_validation() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    info!("Testing custom command validation");

    let parser = TuiOutputParser::new()?;

    // Test validation with mock output
    let mock_output = r#"
12:34:56 EMERGENCY STOP ACTIVATED - command sent to all services
12:34:57 Contract interaction detected: 0x1234...
12:34:58 Transaction completed successfully
"#;

    let parsed = parser.parse_output(mock_output)?;
    
    let expected_patterns = vec![
        "EMERGENCY STOP".to_string(),
        "ACTIVATED".to_string(),
        "Contract interaction".to_string(),
        "successfully".to_string(),
    ];

    let validation_results = parser.validate_output(&parsed, &expected_patterns);
    
    assert_eq!(validation_results.len(), 4, "Expected 4 validation results");
    
    let all_found = validation_results.iter().all(|r| r.found);
    assert!(all_found, "Not all expected patterns were found");
    
    for result in &validation_results {
        assert!(result.found, "Pattern '{}' not found", result.pattern);
        assert!(!result.locations.is_empty(), "No locations found for pattern '{}'", result.pattern);
    }

    info!("✅ Custom command validation works correctly");

    Ok(())
}

/// Helper function to check if infrastructure is available
async fn check_infrastructure_available() -> bool {
    // Check if NATS is available
    let nats_available = async_nats::connect("nats://localhost:4222").await.is_ok();
    
    // Check if Anvil is available
    let anvil_available = reqwest::get("http://localhost:8545").await.is_ok();
    
    nats_available && anvil_available
}

/// Integration test that only runs if infrastructure is available
#[tokio::test]
async fn test_conditional_integration() -> Result<()> {
    let _ = tracing_subscriber::fmt::try_init();

    if !check_infrastructure_available().await {
        warn!("Infrastructure not available, skipping integration test");
        return Ok(());
    }

    info!("Infrastructure available, running conditional integration test");

    // Run a simple integration test
    let anvil_url = "http://localhost:8545".to_string();
    let contract_address = "******************************************".to_string();

    let orchestrator = TuiIntegrationTestOrchestrator::new(anvil_url, contract_address)?;
    
    // Just test that we can create the orchestrator successfully
    assert!(!orchestrator.tui_tester.get_available_commands().is_empty());
    
    info!("✅ Conditional integration test passed");

    Ok(())
}