// Standalone configuration validation test
// This test can run independently of the broader test suite

use std::path::PathBuf;
use ethers::types::Address;
use std::str::FromStr;
use tempfile::TempDir;
use std::fs;

// Import our modules directly
use crate::integration::stargate_compass::{ConfigurationManager, validate_ethereum_address, ConfigManager};

#[tokio::test]
async fn test_standalone_config_validation() {
    println!("🧪 Starting standalone configuration validation test");
    
    // Create a temporary test environment
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let config_dir = temp_dir.path().join("config");
    fs::create_dir(&config_dir).expect("Failed to create config directory");
    
    // Create a simple test configuration file
    let test_config = r#"
[contracts]
stargate_compass_v1 = "******************************************"

[network]
rpc_url = "https://mainnet.base.org"
chain_id = 8453

[execution]
max_gas_price = 50000000000
slippage_tolerance = 0.005
"#;
    
    let config_path = config_dir.join("test.toml");
    fs::write(&config_path, test_config).expect("Failed to write test config");
    
    // Change to temp directory
    let original_dir = std::env::current_dir().unwrap();
    std::env::set_current_dir(temp_dir.path()).unwrap();
    
    // Test 1: Configuration Manager Creation
    println!("  ✓ Test 1: Configuration Manager Creation");
    let config_manager = ConfigurationManager::new();
    assert!(config_manager.is_ok(), "Should create configuration manager");
    let config_manager = config_manager.unwrap();
    
    // Test 2: Configuration Validation
    println!("  ✓ Test 2: Configuration Validation");
    let validation_result = config_manager.validate_configuration().await;
    assert!(validation_result.is_ok(), "Should validate configuration");
    let validation = validation_result.unwrap();
    assert!(validation.valid, "Configuration should be valid");
    assert!(validation.contract_address_valid, "Contract address should be valid");
    
    // Test 3: Contract Address Extraction
    println!("  ✓ Test 3: Contract Address Extraction");
    let current_address = config_manager.get_current_contract_address().await;
    assert!(current_address.is_ok(), "Should extract contract address");
    let address = current_address.unwrap();
    let expected_address = Address::from_str("******************************************").unwrap();
    assert_eq!(address, expected_address, "Should extract correct address");
    
    // Test 4: Address Validation Utility
    println!("  ✓ Test 4: Address Validation Utility");
    let valid_addr = "******************************************";
    assert!(validate_ethereum_address(valid_addr).is_ok(), "Should validate correct address");
    
    let invalid_addr = "invalid_address";
    assert!(validate_ethereum_address(invalid_addr).is_err(), "Should reject invalid address");
    
    // Test 5: Configuration Update
    println!("  ✓ Test 5: Configuration Update");
    let new_address = Address::from_str("******************************************").unwrap();
    let update_result = config_manager.update_contract_address(new_address).await;
    assert!(update_result.is_ok(), "Should update contract address");
    let update = update_result.unwrap();
    assert!(update.success, "Update should succeed");
    assert!(update.backup_created, "Backup should be created");
    
    // Test 6: Verify Update
    println!("  ✓ Test 6: Verify Update");
    let updated_address = config_manager.get_current_contract_address().await.unwrap();
    assert_eq!(updated_address, new_address, "Address should be updated");
    
    // Test 7: Enhanced Validation
    println!("  ✓ Test 7: Enhanced Validation");
    let files_to_validate = vec![config_path];
    let validation_success = config_manager.validate_updates(&files_to_validate, new_address).await;
    assert!(validation_success.is_ok(), "Enhanced validation should work");
    let is_valid = validation_success.unwrap();
    assert!(is_valid, "Enhanced validation should pass");
    
    // Test 8: Backup and Restore
    println!("  ✓ Test 8: Backup and Restore");
    let backup_result = config_manager.backup_configuration().await;
    assert!(backup_result.is_ok(), "Should create additional backup");
    
    let restore_result = config_manager.restore_configuration().await;
    assert!(restore_result.is_ok(), "Should restore configuration");
    
    // Restore original directory
    std::env::set_current_dir(original_dir).unwrap();
    
    println!("🎉 All standalone configuration validation tests passed!");
}

#[test]
fn test_utility_functions() {
    println!("🔧 Testing utility functions");
    
    // Test address validation
    let valid_addresses = [
        "******************************************",
        "******************************************",
        "******************************************",
    ];
    
    for addr in &valid_addresses {
        assert!(validate_ethereum_address(addr).is_ok(), "Should validate: {}", addr);
    }
    
    let invalid_addresses = [
        "invalid",
        "0x123", // Too short
        "742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e", // Missing 0x
    ];
    
    for addr in &invalid_addresses {
        assert!(validate_ethereum_address(addr).is_err(), "Should reject: {}", addr);
    }
    
    println!("✅ Utility functions work correctly");
}