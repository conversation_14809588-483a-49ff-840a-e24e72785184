const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 - Debug Tests', function () {
  async function deployDebugFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with ETH
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('1'),
    });

    // Set up mock balance for the real USDC address
    const realUSDCAddress = '******************************************';
    await mockPool.setMockBalance(
      realUSDCAddress,
      ethers.parseUnits('100000', 6)
    );

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  describe('Flash Loan Debug', function () {
    it('Should debug flash loan execution step by step', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployDebugFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      const expectedProfit = ethers.parseUnits('10', 6);
      const minAmountOut = ethers.parseUnits('980', 6);

      console.log('=== Debug Flash Loan Execution ===');

      // Step 1: Check profitability validation
      console.log('1. Testing profitability validation...');
      const flashLoanCosts = (loanAmount * 5n) / 10000n;
      const minimumProfit = (loanAmount * 50n) / 10000n;
      const totalRequired = flashLoanCosts + minimumProfit;
      console.log(`   Flash loan costs: ${flashLoanCosts}`);
      console.log(`   Minimum profit: ${minimumProfit}`);
      console.log(`   Total required: ${totalRequired}`);
      console.log(`   Expected profit: ${expectedProfit}`);
      console.log(`   Should pass: ${expectedProfit >= totalRequired}`);

      // Step 2: Set up mocks
      console.log('2. Setting up mocks...');
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );
      await mockStargateRouter.setSwapSuccess(true);
      console.log('   Mocks configured');

      // Step 3: Check contract state
      console.log('3. Checking contract state...');
      console.log(
        `   Contract ETH balance: ${await ethers.provider.getBalance(
          stargateCompass.target
        )}`
      );
      console.log(
        `   Contract paused: ${await stargateCompass.emergencyPaused()}`
      );
      console.log(`   Owner: ${await stargateCompass.owner()}`);
      console.log(`   Caller: ${owner.address}`);

      // Step 4: Try the operation
      console.log('4. Attempting flash loan operation...');
      try {
        const tx = await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          minAmountOut,
          expectedProfit
        );
        console.log('   ✅ Transaction succeeded!');
        const receipt = await tx.wait();
        console.log(`   Gas used: ${receipt.gasUsed}`);
      } catch (error) {
        console.log('   ❌ Transaction failed:');
        console.log(`   Error: ${error.message}`);

        // Try to get more details
        if (error.data) {
          console.log(`   Error data: ${error.data}`);
        }
      }
    });

    it('Should test individual components', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployDebugFixture);

      console.log('=== Testing Individual Components ===');

      // Test 1: Mock pool flash loan
      console.log('1. Testing mock pool flash loan...');
      await mockPool.setFlashLoanSuccess(true);

      // Test 2: Mock router fee quote
      console.log('2. Testing mock router fee quote...');
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // Test 3: Mock router swap
      console.log('3. Testing mock router swap setup...');
      await mockStargateRouter.setSwapSuccess(true);

      console.log('   All components configured successfully');
    });

    it('Should test executeOperation directly', async function () {
      const { stargateCompass, owner, mockPool } = await loadFixture(
        deployDebugFixture
      );

      console.log('=== Testing executeOperation Directly ===');

      const loanAmount = ethers.parseUnits('1000', 6);
      const expectedProfit = ethers.parseUnits('10', 6);
      const minAmountOut = ethers.parseUnits('980', 6);
      const premium = (loanAmount * 5n) / 10000n;

      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        ['0x1234', owner.address, minAmountOut, expectedProfit]
      );

      console.log('Attempting to call executeOperation directly...');

      try {
        // This should fail because we're not the Aave pool
        await stargateCompass.executeOperation(
          '******************************************',
          loanAmount,
          premium,
          owner.address,
          params
        );
        console.log('   ❌ This should have failed!');
      } catch (error) {
        console.log('   ✅ Correctly rejected non-pool caller');
        console.log(`   Error: ${error.message}`);
      }
    });
  });
});
