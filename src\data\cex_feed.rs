use crate::error::{DataError, DataResult};
use crate::shared_types::{CexTrade, NatsTopics, OrderBookLevel, OrderBookUpdate, TradeSide};
use async_nats::Client as NatsClient;
use futures_util::{SinkExt, StreamExt};
use rand;
use rust_decimal::prelude::*;
use serde::Deserialize;
use std::time::{Duration, Instant};
use tokio::net::TcpStream;
use tokio::time;
use tokio_tungstenite::{
    connect_async, tungstenite::protocol::Message, MaybeTlsStream, WebSocketStream,
};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

pub struct CexFeed {
    name: String,
    ws_url: String,
    api_key: Option<String>,
    api_secret: Option<String>,
    nats_client: NatsClient,
}

impl CexFeed {
    pub fn new(
        name: String,
        ws_url: String,
        api_key: Option<String>,
        api_secret: Option<String>,
        nats_client: NatsClient,
    ) -> Self {
        Self {
            name,
            ws_url,
            api_key,
            api_secret,
            nats_client,
        }
    }

    pub async fn start(&self) -> DataResult<()> {
        info!("Starting CEX feed for {}", self.name);

        // Reconnection loop
        let mut retry_count = 0;
        let max_retries = 10;
        let mut backoff_duration = Duration::from_secs(1);

        loop {
            info!(
                "Connecting to {} WebSocket (attempt {})",
                self.name,
                retry_count + 1
            );

            match connect_async(&self.ws_url).await {
                Ok((ws_stream, _)) => {
                    info!("Connected to {} WebSocket", self.name);

                    // Reset retry count and backoff on successful connection
                    retry_count = 0;
                    backoff_duration = Duration::from_secs(1);

                    // Subscribe to relevant channels
                    if let Err(e) = self.subscribe(ws_stream).await {
                        error!("Error in {} WebSocket subscription: {}", self.name, e);
                    }

                    // If we reach here, the connection was closed, so we'll try to reconnect
                    info!("Reconnecting to {} WebSocket...", self.name);
                }
                Err(e) => {
                    error!("Failed to connect to {} WebSocket: {}", self.name, e);

                    // Increment retry count
                    retry_count += 1;

                    // Check if we've exceeded max retries
                    if retry_count >= max_retries {
                        error!("Max retries ({}) exceeded for {}", max_retries, self.name);
                        return Err(DataError::source_unavailable(format!(
                            "Failed to connect to {} after {} attempts",
                            self.name, max_retries
                        )));
                    }
                }
            }

            // Exponential backoff with jitter
            let jitter = rand::random::<u64>() % 1000;
            let backoff_ms = backoff_duration.as_millis() as u64 + jitter;
            info!(
                "Waiting {}ms before reconnecting to {}",
                backoff_ms, self.name
            );
            time::sleep(Duration::from_millis(backoff_ms)).await;

            // Increase backoff for next attempt (capped at 30 seconds)
            backoff_duration = std::cmp::min(backoff_duration * 2, Duration::from_secs(30));
        }
    }

    async fn subscribe(
        &self,
        mut ws_stream: WebSocketStream<MaybeTlsStream<TcpStream>>,
    ) -> DataResult<()> {
        // Send subscription message based on exchange
        match self.name.as_str() {
            "coinbase" => {
                let subscribe_msg = serde_json::json!({
                    "type": "subscribe",
                    "product_ids": ["ETH-USD", "BTC-USD", "ETH-BTC"],
                    "channels": ["ticker", "matches", "level2"]
                });

                ws_stream
                    .send(Message::Text(subscribe_msg.to_string()))
                    .await
                    .map_err(|e| {
                        DataError::source_unavailable(format!(
                            "Failed to send subscription to Coinbase: {}",
                            e
                        ))
                    })?;
                info!("Sent subscription request to Coinbase");
            }
            "binance" => {
                // Binance requires multiple subscription messages for different streams
                let streams = ["ethusdt@trade", "btcusdt@trade", "ethbtc@trade"];

                for stream in streams {
                    let subscribe_msg = serde_json::json!({
                        "method": "SUBSCRIBE",
                        "params": [stream],
                        "id": 1
                    });

                    ws_stream
                        .send(Message::Text(subscribe_msg.to_string()))
                        .await
                        .map_err(|e| {
                            DataError::source_unavailable(format!(
                                "Failed to send subscription to Binance for {}: {}",
                                stream, e
                            ))
                        })?;
                    info!("Sent subscription request to Binance for {}", stream);

                    // Small delay between subscription requests to avoid rate limiting
                    time::sleep(Duration::from_millis(100)).await;
                }
            }
            _ => {
                warn!("Unsupported exchange: {}", self.name);
                return Ok(());
            }
        }

        // Setup ping interval to keep connection alive
        let mut ping_interval = time::interval(Duration::from_secs(30));
        let mut last_received = Instant::now();
        let timeout_duration = Duration::from_secs(60); // 60 seconds timeout

        // Process incoming messages
        loop {
            tokio::select! {
                            // Handle WebSocket messages
                            msg = ws_stream.next() => {
                                match msg {
                                    Some(Ok(Message::Text(text))) => {
                                        debug!("Received message from {}", self.name);
                                        last_received = Instant::now();

                                        // Process and publish to NATS
                                        if let Err(e) = self.process_and_publish(text).await {
                                            error!("Error processing message from {}: {}", self.name, e);
                                        }
                                    },
                                    Some(Ok(Message::Binary(data))) => {
                                        debug!("Received binary message from {}", self.name);
                                        last_received = Instant::now();

                                        // Some exchanges like Binance may send binary messages
                                        if let Ok(text) = String::from_utf8(data) {
                                            if let Err(e) = self.process_and_publish(text).await {
                                                error!("Error processing binary message from {}: {}", self.name, e);
                                            }
                                        }
                                    },
                                    Some(Ok(Message::Ping(data))) => {
                                        debug!("Received ping from {}", self.name);
                                        last_received = Instant::now();
                                        ws_stream.send(Message::Pong(data)).await
                                            .map_err(|e| DataError::source_unavailable(format!("Failed to send pong to {}: {}", self.name, e)))?;
                                    },
                                    Some(Ok(Message::Pong(_))) => {
                                        debug!("Received pong from {}", self.name);
                                        last_received = Instant::now();
                                    },
                                    Some(Ok(Message::Close(frame))) => {
                                        info!("WebSocket closed by {}: {:?}", self.name, frame);
                                        break;
                                    },
            Some(Ok(tokio_tungstenite::tungstenite::Message::Frame(_))) => {
                                        debug!("Received frame message from {}", self.name);
                                        // TODO: Handle frame messages if necessary
                                    },
                                    Some(Err(e)) => {
                                        error!("Error receiving message from {}: {}", self.name, e);
                                        break;
                                    },
                                    None => {
                                        info!("WebSocket stream ended for {}", self.name);
                                        break;
                                    }
                                }

                                // Check for timeout
                                if last_received.elapsed() > timeout_duration {
                                    error!("Connection timeout for {}", self.name);
                                    break;
                                }
                            },

                            // Send periodic pings to keep connection alive
                            _ = ping_interval.tick() => {
                                debug!("Sending ping to {}", self.name);
                                if let Err(e) = ws_stream.send(Message::Ping(vec![])).await {
                                    error!("Failed to send ping to {}: {}", self.name, e);
                                    break;
                                }
                            }
                        }
        }

        info!("WebSocket connection closed for {}", self.name);
        Ok(())
    }

    async fn process_and_publish(&self, message: String) -> DataResult<()> {
        match self.name.as_str() {
            "coinbase" => self.process_coinbase_message(message).await,
            "binance" => self.process_binance_message(message).await,
            _ => {
                warn!("Unsupported exchange: {}", self.name);
                Ok(())
            }
        }
    }

    async fn process_coinbase_message(&self, message: String) -> DataResult<()> {
        #[derive(Debug, Deserialize)]
        struct CoinbaseTickerMessage {
            #[serde(rename = "type")]
            msg_type: String,
            product_id: String,
            price: Option<String>,
            side: Option<String>,
            size: Option<String>,
            time: Option<String>,
            #[serde(flatten)]
            _extra: serde_json::Value,
        }

        #[derive(Debug, Deserialize)]
        struct CoinbaseLevel2Message {
            #[serde(rename = "type")]
            msg_type: String,
            product_id: String,
            changes: Vec<Vec<String>>,
            #[serde(flatten)]
            _extra: serde_json::Value,
        }

        // Try to parse as ticker message (using from_slice for better performance)
        if let Ok(ticker) = serde_json::from_slice::<CoinbaseTickerMessage>(message.as_bytes()) {
            if ticker.msg_type == "match" || ticker.msg_type == "ticker" {
                if let (Some(price_str), Some(size_str), Some(side_str)) =
                    (&ticker.price, &ticker.size, &ticker.side)
                {
                    if let (Ok(price), Ok(size)) =
                        (price_str.parse::<f64>(), size_str.parse::<f64>())
                    {
                        let side = match side_str.as_str() {
                            "buy" => TradeSide::Buy,
                            "sell" => TradeSide::Sell,
                            _ => return Ok(()),
                        };

                        let trade = CexTrade::new(
                            self.name.clone(),
                            ticker.product_id,
                            rust_decimal::Decimal::from_f64(price).unwrap_or_default(),
                            rust_decimal::Decimal::from_f64(size).unwrap_or_default(),
                            side,
                        );

                        // Publish to NATS
                        let topic = NatsTopics::cex_trades_for(&self.name);
                        let payload = serde_json::to_vec(&trade).map_err(|e| {
                            DataError::parsing_failed(format!("Failed to serialize trade: {}", e))
                        })?;
                        self.nats_client
                            .publish(topic, payload.clone().into())
                            .await
                            .map_err(|e| {
                                DataError::source_unavailable(format!(
                                    "Failed to publish to NATS: {}",
                                    e
                                ))
                            })?;
                        debug!("Published trade to NATS");
                    }
                }
            }
        }

        // Try to parse as level2 message (using from_slice for better performance)
        if let Ok(l2_update) = serde_json::from_slice::<CoinbaseLevel2Message>(message.as_bytes()) {
            if l2_update.msg_type == "l2update" {
                let mut bids = Vec::new();
                let mut asks = Vec::new();

                for change in l2_update.changes {
                    if change.len() >= 3 {
                        let side = &change[0];
                        if let (Ok(price), Ok(size)) =
                            (change[1].parse::<f64>(), change[2].parse::<f64>())
                        {
                            let level = OrderBookLevel {
                                price: rust_decimal::Decimal::from_f64(price).unwrap_or_default(),
                                amount: rust_decimal::Decimal::from_f64(size).unwrap_or_default(),
                            };

                            match side.as_str() {
                                "buy" => bids.push(level),
                                "sell" => asks.push(level),
                                _ => {}
                            }
                        }
                    }
                }

                if !bids.is_empty() || !asks.is_empty() {
                    let orderbook =
                        OrderBookUpdate::new(self.name.clone(), l2_update.product_id, bids, asks);

                    // Publish to NATS
                    let topic =
                        format!("{}.{}", NatsTopics::CEX_ORDERBOOK, self.name.to_lowercase());
                    let payload = serde_json::to_vec(&orderbook).map_err(|e| {
                        DataError::parsing_failed(format!("Failed to serialize orderbook: {}", e))
                    })?;
                    self.nats_client
                        .publish(topic, payload.into())
                        .await
                        .map_err(|e| {
                            DataError::source_unavailable(format!(
                                "Failed to publish orderbook to NATS: {}",
                                e
                            ))
                        })?;
                    debug!("Published orderbook update to NATS");
                }
            }
        }

        Ok(())
    }

    async fn process_binance_message(&self, message: String) -> DataResult<()> {
        #[derive(Debug, Deserialize)]
        struct BinanceTradeMessage {
            e: String, // Event type
            s: String, // Symbol
            p: String, // Price
            q: String, // Quantity
            m: bool,   // Is the buyer the market maker?
            #[serde(flatten)]
            _extra: serde_json::Value,
        }

        // Try to parse as trade message (using from_slice for better performance)
        if let Ok(trade) = serde_json::from_slice::<BinanceTradeMessage>(message.as_bytes()) {
            if trade.e == "trade" {
                if let (Ok(price), Ok(amount)) = (trade.p.parse::<f64>(), trade.q.parse::<f64>()) {
                    // In Binance, 'm' is true if the buyer is the market maker (seller is taker)
                    let side = if trade.m {
                        TradeSide::Sell
                    } else {
                        TradeSide::Buy
                    };

                    let cex_trade = CexTrade::new(
                        self.name.clone(),
                        trade.s,
                        rust_decimal::Decimal::from_f64(price).unwrap_or_default(),
                        rust_decimal::Decimal::from_f64(amount).unwrap_or_default(),
                        side,
                    );

                    // Publish to NATS
                    let topic = NatsTopics::cex_trades_for(&self.name);
                    let payload = serde_json::to_vec(&cex_trade).map_err(|e| {
                        DataError::parsing_failed(format!("Failed to serialize trade: {}", e))
                    })?;
                    self.nats_client
                        .publish(topic, payload.clone().into())
                        .await
                        .map_err(|e| {
                            DataError::source_unavailable(format!(
                                "Failed to publish to NATS: {}",
                                e
                            ))
                        })?;
                    debug!("Published trade to NATS");
                }
            }
        }

        // Orderbook processing would be added here

        Ok(())
    }
}
