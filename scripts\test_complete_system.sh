#!/bin/bash

# Zen Geometer Complete System Test Script
# Tests all components of the production-ready trading bot

set -e

echo "Testing Zen Geometer Complete System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[SYSTEM TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}SUCCESS${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}WARNING${NC} $1"
}

print_error() {
    echo -e "${RED}ERROR${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Testing: $test_name"
    
    if eval "$test_command"; then
        print_success "$test_name"
        ((TESTS_PASSED++))
    else
        print_error "$test_name"
        ((TESTS_FAILED++))
    fi
}

# Start infrastructure if not running
print_status "Ensuring infrastructure is running..."
docker compose up -d
sleep 10

# Test 1: Build system
run_test "Cargo build" "cargo build"

# Test 2: Unit tests
run_test "Unit tests" "cargo test --lib"

# Test 3: Integration tests
run_test "Integration tests" "timeout 60 cargo test test_integration_anvil"

# Test 4: Configuration validation
run_test "Configuration validation" "timeout 30 cargo run -- validate"

# Test 5: Main binary help
run_test "Main binary help" "cargo run -- --help"

# Test 6: Feature exporter
run_test "Feature exporter" "timeout 30 cargo run --bin feature_exporter -- --help"

# Test 7: Graph analyzer
run_test "Graph analyzer" "timeout 30 cargo run --bin graph_analyzer -- --help"

# Test 8: TUI harness
run_test "TUI harness startup" "timeout 10 cargo run --bin tui_harness &>/dev/null || true"

# Test 9: Data ingestor
run_test "Data ingestor" "timeout 30 cargo run --bin data_ingestor -- --help"

# Test 10: Network observer
run_test "Network observer" "timeout 30 cargo run --bin network_observer -- --help"

# Test 11: Backtester
run_test "Backtester" "timeout 30 cargo run --bin backtester -- --help"

# Test 12: Demo data generator
run_test "Demo data generator" "timeout 30 cargo run --bin demo_data_generator -- --help"

# Test 13: Seismic analyzer
run_test "Seismic analyzer" "timeout 30 cargo run --bin seismic_analyzer -- --help"

# Test 14: Optimizer
run_test "Optimizer" "timeout 30 cargo run --bin optimizer -- --help"

# Test 15: Mempool backtester
run_test "Mempool backtester" "timeout 30 cargo run --bin mempool_backtester -- --help"

# Test 16: Docker build
run_test "Docker build" "docker build -t basilisk_bot:test ."

# Test 17: Infrastructure health
run_test "NATS health" "curl -s http://localhost:8222/healthz"
run_test "Redis health" "docker compose exec -T redis redis-cli ping"
run_test "PostgreSQL health" "docker compose exec -T timescaledb pg_isready -U basilisk_bot -d basilisk_bot"

# Test 18: Metrics endpoint (if bot is running)
print_status "Testing metrics endpoint..."
cargo run -- run --dry-run &
BOT_PID=$!
sleep 10
if curl -s http://localhost:9090/metrics > /dev/null; then
    print_success "Metrics endpoint responding"
    ((TESTS_PASSED++))
else
    print_warning "Metrics endpoint not responding (may be expected)"
    ((TESTS_FAILED++))
fi
kill $BOT_PID 2>/dev/null || true

# Test 19: CLI utilities
run_test "Config show command" "timeout 30 cargo run -- config show"
run_test "Utils balances command" "timeout 30 cargo run -- utils balances"

# Test 20: Zen Geometer dry run
print_status "Testing Zen Geometer dry run..."
timeout 30 cargo run -- run --dry-run &
ZEN_PID=$!
sleep 15
if ps -p $ZEN_PID > /dev/null; then
    print_success "Zen Geometer dry run"
    ((TESTS_PASSED++))
    kill $ZEN_PID 2>/dev/null || true
else
    print_error "Zen Geometer dry run failed"
    ((TESTS_FAILED++))
fi

# Summary
echo ""
echo "============================================"
echo "ZEN GEOMETER SYSTEM TEST SUMMARY"
echo "============================================"
echo "Tests Passed: $TESTS_PASSED"
echo "Tests Failed: $TESTS_FAILED"
echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    print_success "ALL TESTS PASSED - System is ready for deployment!"
    echo ""
    echo "Next steps:"
    echo "  1. Review configuration in config/default.toml"
    echo "  2. Set production environment variables"
    echo "  3. Run: ./scripts/mainnet_deploy.sh (with extreme caution)"
    exit 0
else
    print_warning "Some tests failed - review output above"
    echo ""
    echo "Common issues:"
    echo "  - Infrastructure services not fully started"
    echo "  - Missing environment variables"
    echo "  - Network connectivity issues"
    echo "  - Configuration errors"
    exit 1
fi