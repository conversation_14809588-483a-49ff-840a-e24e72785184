async function main() {
  console.log("🚀 Deploying StargateCompassV1 to Base mainnet...");
  
  // Use addresses without checksum validation
  const AAVE_PROVIDER = "******************************************";
  const STARGATE_ROUTER = "******************************************"; // lowercase
  
  console.log(`Aave Provider: ${AAVE_PROVIDER}`);
  console.log(`Stargate Router: ${STARGATE_ROUTER}`);
  
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  
  console.log("Deploying contract...");
  const contract = await StargateCompassV1.deploy(AAVE_PROVIDER, STARGATE_ROUTER);
  
  await contract.waitForDeployment();
  const address = await contract.getAddress();
  
  console.log(`✅ Contract deployed to: ${address}`);
  console.log(`🔗 BaseScan: https://basescan.org/address/${address}`);
  
  return address;
}

main().catch(console.error);
