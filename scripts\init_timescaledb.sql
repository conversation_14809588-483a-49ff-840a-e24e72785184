-- Basilisk Bot TimescaleDB Initialization
-- Creates the necessary tables and hypertables for time-series data

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Market data table for price feeds
CREATE TABLE IF NOT EXISTS market_data (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    price DECIMAL(20,8) NOT NULL,
    volume DECIMAL(20,8) NOT NULL,
    bid DECIMAL(20,8),
    ask DECIMAL(20,8),
    spread DECIMAL(20,8)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_time ON market_data (symbol, time DESC);
CREATE INDEX IF NOT EXISTS idx_market_data_exchange_time ON market_data (exchange, time DESC);

-- Fractal analysis results table
CREATE TABLE IF NOT EXISTS fractal_analysis (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    hurst_exponent DECIMAL(10,6),
    volatility DECIMAL(10,6),
    regime VARCHAR(50),
    character VARCHAR(50),
    volume_trend DECIMAL(10,6),
    price_momentum DECIMAL(10,6)
);

-- Convert to hypertable
SELECT create_hypertable('fractal_analysis', 'time', if_not_exists => TRUE);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_fractal_analysis_symbol_time ON fractal_analysis (symbol, time DESC);
CREATE INDEX IF NOT EXISTS idx_fractal_analysis_regime ON fractal_analysis (regime, time DESC);

-- Opportunities table for tracking detected opportunities
CREATE TABLE IF NOT EXISTS opportunities (
    time TIMESTAMPTZ NOT NULL,
    opportunity_id UUID NOT NULL,
    scanner_type VARCHAR(50) NOT NULL,
    opportunity_type VARCHAR(50) NOT NULL,
    estimated_profit_usd DECIMAL(20,8),
    intersection_value_usd DECIMAL(20,8),
    quality_ratio DECIMAL(10,6),
    execution_score DECIMAL(10,6),
    status VARCHAR(20) NOT NULL,
    chain_id INTEGER,
    asset_in VARCHAR(42),
    asset_out VARCHAR(42),
    path_data JSONB
);

-- Convert to hypertable
SELECT create_hypertable('opportunities', 'time', if_not_exists => TRUE);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_opportunities_scanner_time ON opportunities (scanner_type, time DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_status_time ON opportunities (status, time DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_chain_time ON opportunities (chain_id, time DESC);

-- Executions table for tracking trade executions
CREATE TABLE IF NOT EXISTS executions (
    time TIMESTAMPTZ NOT NULL,
    execution_id UUID NOT NULL,
    opportunity_id UUID NOT NULL,
    tx_hash VARCHAR(66),
    status VARCHAR(20) NOT NULL,
    gas_used BIGINT,
    gas_price BIGINT,
    actual_profit_usd DECIMAL(20,8),
    slippage DECIMAL(10,6),
    execution_time_ms INTEGER,
    chain_id INTEGER
);

-- Convert to hypertable
SELECT create_hypertable('executions', 'time', if_not_exists => TRUE);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_executions_opportunity_id ON executions (opportunity_id);
CREATE INDEX IF NOT EXISTS idx_executions_tx_hash ON executions (tx_hash);
CREATE INDEX IF NOT EXISTS idx_executions_status_time ON executions (status, time DESC);

-- SIGINT directives table for tracking intelligence officer commands
CREATE TABLE IF NOT EXISTS sigint_directives (
    time TIMESTAMPTZ NOT NULL,
    directive_id UUID NOT NULL,
    report_id VARCHAR(100) NOT NULL,
    directive_type VARCHAR(50) NOT NULL,
    directive_data JSONB NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_by VARCHAR(100),
    reason TEXT
);

-- Convert to hypertable
SELECT create_hypertable('sigint_directives', 'time', if_not_exists => TRUE);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_sigint_directives_report_id ON sigint_directives (report_id);
CREATE INDEX IF NOT EXISTS idx_sigint_directives_type_time ON sigint_directives (directive_type, time DESC);
CREATE INDEX IF NOT EXISTS idx_sigint_directives_status ON sigint_directives (status, expires_at);

-- System metrics table for monitoring
CREATE TABLE IF NOT EXISTS system_metrics (
    time TIMESTAMPTZ NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(20,8) NOT NULL,
    labels JSONB,
    component VARCHAR(50)
);

-- Convert to hypertable
SELECT create_hypertable('system_metrics', 'time', if_not_exists => TRUE);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time ON system_metrics (metric_name, time DESC);
CREATE INDEX IF NOT EXISTS idx_system_metrics_component_time ON system_metrics (component, time DESC);

-- Create retention policies for data management
-- Keep detailed data for 30 days, aggregated data for 1 year
SELECT add_retention_policy('market_data', INTERVAL '30 days', if_not_exists => TRUE);
SELECT add_retention_policy('fractal_analysis', INTERVAL '90 days', if_not_exists => TRUE);
SELECT add_retention_policy('opportunities', INTERVAL '90 days', if_not_exists => TRUE);
SELECT add_retention_policy('executions', INTERVAL '365 days', if_not_exists => TRUE);
SELECT add_retention_policy('sigint_directives', INTERVAL '365 days', if_not_exists => TRUE);
SELECT add_retention_policy('system_metrics', INTERVAL '30 days', if_not_exists => TRUE);

-- Create continuous aggregates for performance
CREATE MATERIALIZED VIEW IF NOT EXISTS market_data_1h
WITH (timescaledb.continuous) AS
SELECT time_bucket('1 hour', time) AS bucket,
       symbol,
       exchange,
       first(price, time) as open,
       max(price) as high,
       min(price) as low,
       last(price, time) as close,
       avg(price) as avg_price,
       sum(volume) as volume
FROM market_data
GROUP BY bucket, symbol, exchange;

-- Add refresh policy for continuous aggregates
SELECT add_continuous_aggregate_policy('market_data_1h',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

-- Create views for common queries
CREATE OR REPLACE VIEW current_market_state AS
SELECT DISTINCT ON (symbol)
    symbol,
    time,
    regime,
    character,
    hurst_exponent,
    volatility
FROM fractal_analysis
ORDER BY symbol, time DESC;

CREATE OR REPLACE VIEW active_opportunities AS
SELECT *
FROM opportunities
WHERE status IN ('detected', 'analyzing', 'queued')
AND time > NOW() - INTERVAL '5 minutes';

CREATE OR REPLACE VIEW active_sigint_directives AS
SELECT *
FROM sigint_directives
WHERE status = 'active'
AND expires_at > NOW();

-- Grant permissions to the application user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO basilisk_bot;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO basilisk_bot;

-- Insert initial system status
INSERT INTO system_metrics (time, metric_name, metric_value, component)
VALUES (NOW(), 'database_initialized', 1, 'timescaledb')
ON CONFLICT DO NOTHING;
