use ratatui::{
    backend::Backend,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, List, ListItem, ListState, Paragraph},
    Frame,
};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use crate::logging::{ErrorCode, AlertSeverity};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuiErrorEntry {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub error_code: ErrorCode,
    pub severity: AlertSeverity,
    pub message: String,
    pub count: u32,
}

#[derive(Debug)]
pub struct ErrorDashboard {
    errors: VecDeque<TuiErrorEntry>,
    list_state: ListState,
    show_details: bool,
    filter_severity: Option<AlertSeverity>,
}

impl ErrorDashboard {
    pub fn new() -> Self {
        Self {
            errors: VecDeque::new(),
            list_state: ListState::default(),
            show_details: false,
            filter_severity: None,
        }
    }

    pub fn add_error(&mut self, error: TuiErrorEntry) {
        self.errors.push_front(error);
        if self.errors.len() > 1000 {
            self.errors.pop_back();
        }
    }

    pub fn clear_errors(&mut self) {
        self.errors.clear();
        self.list_state.select(None);
    }

    pub fn next(&mut self) {
        let i = match self.list_state.selected() {
            Some(i) => {
                if i >= self.errors.len() - 1 {
                    0
                } else {
                    i + 1
                }
            }
            None => 0,
        };
        self.list_state.select(Some(i));
    }

    pub fn previous(&mut self) {
        let i = match self.list_state.selected() {
            Some(i) => {
                if i == 0 {
                    self.errors.len() - 1
                } else {
                    i - 1
                }
            }
            None => 0,
        };
        self.list_state.select(Some(i));
    }

    pub fn toggle_details(&mut self) {
        self.show_details = !self.show_details;
    }

    pub fn toggle_filter(&mut self) {
        self.filter_severity = match self.filter_severity {
            None => Some(AlertSeverity::Critical),
            Some(AlertSeverity::Critical) => Some(AlertSeverity::Error),
            Some(AlertSeverity::Error) => Some(AlertSeverity::Warning),
            Some(AlertSeverity::Warning) => None,
            Some(AlertSeverity::Info) => None,
        };
    }

    pub fn render(&mut self, f: &mut Frame, area: Rect) {
        let filtered_errors: Vec<&TuiErrorEntry> = self.errors
            .iter()
            .filter(|e| {
                if let Some(filter) = &self.filter_severity {
                    &e.severity == filter
                } else {
                    true
                }
            })
            .collect();

        let items: Vec<ListItem> = filtered_errors
            .iter()
            .map(|error| {
                let severity_color = match error.severity {
                    AlertSeverity::Critical => Color::Red,
                    AlertSeverity::Error => Color::LightRed,
                    AlertSeverity::Warning => Color::Yellow,
                    AlertSeverity::Info => Color::White,
                };

                ListItem::new(Line::from(vec![
                    Span::styled(
                        format!("[{}] ", error.timestamp.format("%H:%M:%S")),
                        Style::default().fg(Color::Gray),
                    ),
                    Span::styled(
                        format!("{:?} ", error.severity),
                        Style::default().fg(severity_color).add_modifier(Modifier::BOLD),
                    ),
                    Span::raw(error.message.clone()),
                ]))
            })
            .collect();

        let list = List::new(items)
            .block(
                Block::default()
                    .title("Error Dashboard")
                    .borders(Borders::ALL)
            )
            .highlight_style(Style::default().bg(Color::DarkGray));

        f.render_stateful_widget(list, area, &mut self.list_state);
    }
}