// TODO: N<PERSON> Scanner - Currently disabled for Zen Geometer strategy focus
// MISSION: The NFT Scanner - "Flicker of Light" Sense (Floor Price Sniper)
// WHY: Exploit data inefficiencies in NFT marketplaces
// HOW: Monitor NFT listings for significant underpricing vs floor price
//
// STATUS: Implementation exists but needs integration with new opportunity system
// PRIORITY: Low - implement after Zen Geometer is stable and profitable

use async_nats::Client as NatsClient;
use ethers::types::Address;
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;
use uuid::Uuid;

use crate::shared_types::{
    NFTFloorPrice, NFTListing, NatsTopics, NftArbitrageData, Opportunity, OpportunityBase,
};
use rust_decimal::prelude::*;
use rust_decimal::Decimal;
// use crate::math::vesica;

pub struct NFTScanner {
    nats_client: NatsClient,
    opportunity_tx: mpsc::Sender<Opportunity>,
    floor_prices: HashMap<Address, NFTFloorPrice>, // collection -> floor price
    snipe_threshold: Decimal, // Percentage below floor to trigger snipe (e.g., 0.1 = 10%)
}

impl NFTScanner {
    pub fn new(
        nats_client: NatsClient,
        opportunity_tx: mpsc::Sender<Opportunity>,
        snipe_threshold: Decimal,
    ) -> Self {
        Self {
            nats_client,
            opportunity_tx,
            floor_prices: HashMap::new(),
            snipe_threshold,
        }
    }

    pub async fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        info!("NFTScanner starting - The 'Flicker of Light' Sense is awakening...");

        // Subscribe to NFT listings
        let mut listings_subscriber = self
            .nats_client
            .subscribe(NatsTopics::DATA_NFT_LISTINGS_PROCESSED)
            .await?;

        // Subscribe to floor price updates
        let mut floor_subscriber = self
            .nats_client
            .subscribe(NatsTopics::DATA_NFT_FLOOR_PRICES)
            .await?;

        info!("NFTScanner subscribed to listings and floor prices - Watching for the glint of underpriced assets...");

        loop {
            tokio::select! {
                Some(msg) = listings_subscriber.next() => {
                    if let Err(e) = self.process_nft_listing(&msg.payload).await {
                        error!("NFTScanner failed to process listing: {}", e);
                    }
                }

                Some(msg) = floor_subscriber.next() => {
                    if let Err(e) = self.process_floor_price_update(&msg.payload).await {
                        error!("NFTScanner failed to process floor price update: {}", e);
                    }
                }

                _ = tokio::time::sleep(tokio::time::Duration::from_secs(30)) => {
                    debug!("NFTScanner: Flicker sense active, watching {} collections for mispriced listings...",
                           self.floor_prices.len());
                }
            }
        }
    }

    async fn process_nft_listing(&self, payload: &[u8]) -> Result<(), Box<dyn std::error::Error>> {
        let listing: NFTListing = serde_json::from_slice(payload)?;

        // Check if we have floor price data for this collection
        if let Some(floor_price) = self.floor_prices.get(&listing.collection) {
            // Calculate the threshold price (floor * (1 - threshold))
            let snipe_threshold_price =
                floor_price.floor_price_usd * (Decimal::from(1) - self.snipe_threshold);

            // BASILISK FOCUS: Look for the "glint" - listings significantly below floor
            if listing.price_usd < snipe_threshold_price && listing.price_usd > Decimal::ZERO {
                let discount_percentage = ((floor_price.floor_price_usd - listing.price_usd)
                    / floor_price.floor_price_usd)
                    * Decimal::from(100);

                debug!(
                    "NFTScanner: Detected underpriced NFT - {:.1}% below floor (${:.2} vs ${:.2})",
                    discount_percentage, listing.price_usd, floor_price.floor_price_usd
                );

                // Calculate potential profit
                let estimated_profit = calculate_snipe_profit(&listing, floor_price);

                if estimated_profit > Decimal::from(10) {
                    // Minimum threshold for NFT snipes

                    let opportunity =
                        create_nft_snipe_opportunity(&listing, floor_price, estimated_profit)?;

                    info!("NFTScanner: FLICKER DETECTED! NFT snipe opportunity ${:.2} profit - Collection: {:?}, Token: {}",
                          estimated_profit, listing.collection, listing.token_id);

                    // Send to the StrategyManager brain
                    if let Err(e) = self.opportunity_tx.send(opportunity).await {
                        error!("NFTScanner failed to send opportunity: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    async fn process_floor_price_update(
        &mut self,
        payload: &[u8],
    ) -> Result<(), Box<dyn std::error::Error>> {
        let floor_price: NFTFloorPrice = serde_json::from_slice(payload)?;

        // Update our floor price cache
        self.floor_prices
            .insert(floor_price.collection, floor_price.clone());

        debug!(
            "NFTScanner: Updated floor price for collection {:?} - ${:.2}",
            floor_price.collection, floor_price.floor_price_usd
        );

        Ok(())
    }
}

pub async fn scan(
    nats_client: NatsClient,
    opportunity_tx: mpsc::Sender<Opportunity>,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut scanner = NFTScanner::new(
        nats_client,
        opportunity_tx,
        Decimal::from_f64(0.1).unwrap_or_default(),
    ); // 10% below floor threshold
    scanner.run().await
}

fn calculate_snipe_profit(listing: &NFTListing, floor_price: &NFTFloorPrice) -> Decimal {
    // STRATEGY: Profit = (Floor Price - Listing Price) - Marketplace Fees

    let gross_profit = floor_price.floor_price_usd - listing.price_usd;

    // Account for marketplace fees (typically 2.5% on OpenSea, 0.5% on Blur)
    let marketplace_fee_rate = match listing.marketplace.as_str() {
        "opensea" => Decimal::from_f64(0.025).unwrap_or_default(),
        "blur" => Decimal::from_f64(0.005).unwrap_or_default(),
        "x2y2" => Decimal::from_f64(0.005).unwrap_or_default(),
        _ => Decimal::from_f64(0.025).unwrap_or_default(), // Default to OpenSea rates
    };

    // Account for gas costs (NFT transactions are expensive)
    let estimated_gas_cost = Decimal::from(50); // USD estimate for NFT purchase + transfer

    let marketplace_fees = floor_price.floor_price_usd * marketplace_fee_rate;
    let net_profit = gross_profit - marketplace_fees - estimated_gas_cost;

    net_profit.max(Decimal::ZERO) // Don't return negative profits
}

fn create_nft_snipe_opportunity(
    listing: &NFTListing,
    floor_price: &NFTFloorPrice,
    estimated_profit: Decimal,
) -> Result<Opportunity, Box<dyn std::error::Error>> {
    // MANDORLA GAUGE: Calculate intersection value for NFT arbitrage
    // For NFTs, we simulate liquidity based on collection volume and floor price
    let collection_daily_volume = floor_price
        .daily_volume_usd
        .unwrap_or(Decimal::from(100000)); // Default to $100k daily volume
    let price_deviation =
        (floor_price.floor_price_usd - listing.price_usd) / floor_price.floor_price_usd;

    let min_liquidity = (collection_daily_volume / Decimal::from(10))
        .min(collection_daily_volume / Decimal::from(12));
    let intersection_value = min_liquidity * price_deviation;

    // Create the NFT arbitrage opportunity
    let opportunity = Opportunity::NftArbitrage {
        base: OpportunityBase {
            id: Uuid::new_v4().to_string(),
            source_scanner: "NFTScanner".to_string(),
            estimated_gross_profit_usd: estimated_profit,
            associated_volatility: Decimal::from_f64(0.25).unwrap_or_default(), // NFTs are more volatile than DeFi
            requires_flash_liquidity: false, // NFT snipes typically don't need flash loans
            chain_id: 1,                     // Assume Ethereum mainnet for NFTs
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            intersection_value_usd: intersection_value,
            aetheric_resonance_score: None,
        },
        data: NftArbitrageData {
            collection: listing.collection,
            token_id: listing.token_id,
            buy_from: Address::zero(), // Would be the marketplace contract
            sell_to: Address::zero(),  // Would be another marketplace or private buyer
            floor_price_eth: floor_price.floor_price_usd / Decimal::from(2000), // Rough ETH conversion
            listing_price_eth: listing.price_usd / Decimal::from(2000), // Rough ETH conversion
            buy_price_usd: listing.price_usd,
            sell_price_usd: floor_price.floor_price_usd,
            source_marketplace: "OpenSea".to_string(), // Would be determined from listing source
            target_marketplace: "LooksRare".to_string(), // Would be determined from arbitrage target
        },
    };

    Ok(opportunity)
}
