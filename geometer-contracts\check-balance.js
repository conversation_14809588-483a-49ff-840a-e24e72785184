async function main() {
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  const balance = await ethers.provider.getBalance(address);
  
  console.log(`Wallet: ${address}`);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  
  if (balance === 0n) {
    console.log("❌ Wallet has no ETH for gas fees!");
    console.log("💡 Fund this wallet with ETH on Base network");
  } else {
    console.log("✅ Wallet has sufficient ETH for deployment");
  }
}

main().catch(console.error);
