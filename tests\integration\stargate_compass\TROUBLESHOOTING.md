# Stargate Compass Integration Tests - Troubleshooting Guide

## Overview

This guide provides detailed troubleshooting steps for common issues encountered when running the Stargate Compass Integration Test Suite. Each issue includes symptoms, root causes, and step-by-step solutions.

## Quick Diagnosis

### Test Status Indicators

- ✅ **PASS** - Test completed successfully
- ❌ **FAIL** - Test failed and requires attention
- ⚠️ **WARN** - Test passed with warnings
- ⏭️ **SKIP** - Test was skipped
- 🔄 **RETRY** - Test is being retried
- ⏱️ **TIMEOUT** - Test exceeded time limit

### Common Error Patterns

```bash
# Connection errors
Error: Failed to connect to Anvil at http://localhost:8545

# Contract errors
Error: Contract not found at address 0x1234...

# Configuration errors
Error: Failed to update configuration file config/local.toml

# TUI errors
Error: TUI command 'query_balances' timed out after 30s

# Backend errors
Error: ExecutionManager failed to process opportunity

# Validation errors
Error: Balance mismatch - TUI: 1000.00 USDC, On-chain: 999.99 USDC
```

## Environment Setup Issues

### Issue 1: Anvil Not Running

**Symptoms**:

```
❌ Failed to connect to Anvil testnet
Connection refused at http://localhost:8545
Test Phase: Environment Setup - FAILED
```

**Root Cause**: Anvil testnet is not running or not accessible

**Solutions**:

1. **Check if Anvil is running**:

   ```bash
   # Test connection
   curl -X POST -H "Content-Type: application/json" \
     --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
     http://localhost:8545

   # Should return: {"jsonrpc":"2.0","id":1,"result":"0x..."}
   ```

2. **Start Anvil with correct parameters**:

   ```bash
   # Basic setup
   anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545

   # With specific block for consistency
   anvil --fork-url https://mainnet.base.org \
     --fork-block-number ******** \
     --chain-id 8453 \
     --port 8545 \
     --accounts 10 \
     --balance 10000
   ```

3. **Check port availability**:

   ```bash
   # Check if port 8545 is in use
   netstat -an | grep 8545
   lsof -i :8545

   # Kill process using port if needed
   kill -9 $(lsof -t -i:8545)
   ```

4. **Verify firewall settings**:

   ```bash
   # Allow port 8545 (Ubuntu/Debian)
   sudo ufw allow 8545

   # Check iptables rules
   sudo iptables -L | grep 8545
   ```

### Issue 2: Contract Deployment Failures

**Symptoms**:

```
❌ Contract not found at address 0x********90********90********90********90
Contract verification failed
Test Phase: Configuration Management - FAILED
```

**Root Cause**: StargateCompassV1 contract not deployed or deployed to wrong address

**Solutions**:

1. **Verify contract deployment**:

   ```bash
   # Check if contract exists
   cast code --rpc-url http://localhost:8545 0x********90********90********90********90

   # Should return bytecode, not "0x"
   ```

2. **Deploy contract to Anvil**:

   ```bash
   cd geometer-contracts

   # Install dependencies
   npm install

   # Deploy to Anvil
   npm run deploy:anvil

   # Note the deployed address from output
   ```

3. **Update configuration with correct address**:

   ```toml
   # config/local.toml
   [contracts]
   stargate_compass_v1 = "0xACTUAL_DEPLOYED_ADDRESS"
   ```

4. **Verify contract functionality**:
   ```bash
   # Test basic contract call
   cast call --rpc-url http://localhost:8545 \
     0xACTUAL_DEPLOYED_ADDRESS \
     "paused()(bool)"
   ```

### Issue 3: Insufficient Account Balances

**Symptoms**:

```
❌ Transaction failed: insufficient funds for gas
Backend Integration Test - ExecutionManager - FAILED
Gas estimation failed
```

**Root Cause**: Test accounts don't have sufficient ETH or tokens

**Solutions**:

1. **Fund accounts with ETH**:

   ```bash
   # Get Anvil default private key
   PRIVATE_KEY="0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"

   # Fund test account
   cast send --rpc-url http://localhost:8545 \
     --private-key $PRIVATE_KEY \
     0xYourTestAddress \
     --value 10ether
   ```

2. **Fund accounts with USDC**:

   ```bash
   # Get USDC contract address on Base
   USDC_ADDRESS="******************************************"

   # Transfer USDC to test account
   cast send --rpc-url http://localhost:8545 \
     --private-key $PRIVATE_KEY \
     $USDC_ADDRESS \
     "transfer(address,uint256)" \
     0xYourTestAddress \
     **********  # 1000 USDC (6 decimals)
   ```

3. **Verify balances**:

   ```bash
   # Check ETH balance
   cast balance --rpc-url http://localhost:8545 0xYourTestAddress

   # Check USDC balance
   cast call --rpc-url http://localhost:8545 \
     $USDC_ADDRESS \
     "balanceOf(address)(uint256)" \
     0xYourTestAddress
   ```

## Configuration Issues

### Issue 4: Configuration File Not Found

**Symptoms**:

```
❌ Configuration file not found: config/local.toml
Configuration Management - File Discovery - FAILED
```

**Root Cause**: Configuration files missing or in wrong location

**Solutions**:

1. **Check file existence**:

   ```bash
   # List configuration files
   ls -la config/

   # Should show local.toml, testnet.toml, etc.
   ```

2. **Create missing configuration file**:

   ```bash
   # Create config directory if needed
   mkdir -p config

   # Create local.toml from template
   cp config/default.toml config/local.toml
   ```

3. **Verify file permissions**:

   ```bash
   # Check permissions
   ls -la config/local.toml

   # Fix permissions if needed
   chmod 644 config/local.toml
   ```

4. **Validate TOML syntax**:

   ```bash
   # Install toml-cli if needed
   cargo install toml-cli

   # Validate syntax
   toml get config/local.toml
   ```

### Issue 5: Configuration Update Failures

**Symptoms**:

```
❌ Failed to update configuration file config/local.toml
Permission denied or file locked
Configuration Management - Update - FAILED
```

**Root Cause**: File permissions, locks, or concurrent access

**Solutions**:

1. **Check file permissions**:

   ```bash
   # Check current permissions
   ls -la config/local.toml

   # Make writable
   chmod 644 config/local.toml
   ```

2. **Check for file locks**:

   ```bash
   # Check if file is locked (Linux)
   lsof config/local.toml

   # Kill processes using the file if needed
   fuser -k config/local.toml
   ```

3. **Verify backup directory**:

   ```bash
   # Create backup directory
   mkdir -p config/backups
   chmod 755 config/backups
   ```

4. **Manual configuration update**:

   ```bash
   # Backup original
   cp config/local.toml config/backups/local.toml.backup

   # Edit manually
   nano config/local.toml

   # Update contract address
   [contracts]
   stargate_compass_v1 = "0xNEW_CONTRACT_ADDRESS"
   ```

## Backend Integration Issues

### Issue 6: ExecutionManager Test Failures

**Symptoms**:

```
❌ ExecutionManager integration test failed
Failed to process trading opportunity
Backend Integration Test - ExecutionManager - FAILED
```

**Root Cause**: ExecutionManager cannot interact with contract properly

**Solutions**:

1. **Check contract ABI compatibility**:

   ```bash
   # Verify contract ABI matches expectations
   cd geometer-contracts
   cat artifacts/contracts/StargateCompassV1.sol/StargateCompassV1.json | jq '.abi'
   ```

2. **Test contract interaction directly**:

   ```bash
   # Test basic contract call
   cast call --rpc-url http://localhost:8545 \
     0xCONTRACT_ADDRESS \
     "getVersion()(string)"
   ```

3. **Check gas estimation**:

   ```bash
   # Test gas estimation for contract call
   cast estimate --rpc-url http://localhost:8545 \
     0xCONTRACT_ADDRESS \
     "someFunction()"
   ```

4. **Review ExecutionManager logs**:
   ```bash
   # Run with debug logging
   RUST_LOG=execution_manager=debug cargo test --test stargate_compass_integration
   ```

### Issue 7: Transaction Broadcasting Failures

**Symptoms**:

```
❌ Transaction broadcasting failed
Nonce too low or transaction underpriced
Backend Integration Test - ExecutionDispatcher - FAILED
```

**Root Cause**: Transaction nonce or gas price issues

**Solutions**:

1. **Check account nonce**:

   ```bash
   # Get current nonce
   cast nonce --rpc-url http://localhost:8545 0xYourAddress
   ```

2. **Reset account nonce** (if needed):

   ```bash
   # Send a transaction to reset nonce
   cast send --rpc-url http://localhost:8545 \
     --private-key $PRIVATE_KEY \
     0xYourAddress \
     --value 0
   ```

3. **Adjust gas price**:

   ```bash
   # Check current gas price
   cast gas-price --rpc-url http://localhost:8545

   # Use higher gas price in tests
   cast send --rpc-url http://localhost:8545 \
     --gas-price ********** \
     --private-key $PRIVATE_KEY \
     0xCONTRACT_ADDRESS \
     "someFunction()"
   ```

## TUI Integration Issues

### Issue 8: TUI Command Execution Timeouts

**Symptoms**:

```
❌ TUI command 'query_balances' timed out after 30s
TUI Functionality Test - Command Execution - FAILED
```

**Root Cause**: TUI commands taking too long or hanging

**Solutions**:

1. **Increase timeout**:

   ```bash
   # Run with longer timeout
   cargo test --test stargate_compass_integration -- --timeout 600
   ```

2. **Test TUI independently**:

   ```bash
   # Build TUI binary
   cargo build --bin tui_harness

   # Test TUI manually
   ./target/debug/tui_harness
   ```

3. **Check TUI dependencies**:

   ```bash
   # Verify TUI can connect to Anvil
   ANVIL_URL=http://localhost:8545 ./target/debug/tui_harness
   ```

4. **Review TUI logs**:
   ```bash
   # Run TUI with debug logging
   RUST_LOG=tui_harness=debug ./target/debug/tui_harness
   ```

### Issue 9: TUI Data Validation Failures

**Symptoms**:

```
❌ Balance mismatch - TUI: 1000.00 USDC, On-chain: 999.99 USDC
TUI Functionality Test - Data Validation - FAILED
```

**Root Cause**: Minor discrepancies in displayed vs on-chain data

**Solutions**:

1. **Adjust validation tolerance**:

   ```rust
   // In test configuration
   let mut data_validator = TuiDataValidator::new(anvil_client, contract_address);
   data_validator.set_balance_tolerance(Decimal::new(1, 2)); // 0.01 tolerance
   ```

2. **Check for pending transactions**:

   ```bash
   # Check pending transactions
   cast pending --rpc-url http://localhost:8545
   ```

3. **Verify token decimals**:

   ```bash
   # Check USDC decimals
   cast call --rpc-url http://localhost:8545 \
     ****************************************** \
     "decimals()(uint8)"
   ```

4. **Review validation logic**:
   ```bash
   # Run with detailed validation logging
   RUST_LOG=data_validator=trace cargo test --test stargate_compass_integration
   ```

## End-to-End Workflow Issues

### Issue 10: Workflow Coherence Failures

**Symptoms**:

```
❌ End-to-end workflow validation failed
Data pipeline coherence check failed
End-to-End Workflow Test - System Coherence - FAILED
```

**Root Cause**: Data inconsistency between components

**Solutions**:

1. **Check component integration**:

   ```bash
   # Test individual components first
   cargo test --test stargate_compass_integration -- --skip-workflow
   ```

2. **Verify data flow**:

   ```bash
   # Run with comprehensive logging
   RUST_LOG=debug cargo test --test stargate_compass_integration
   ```

3. **Test opportunity simulation**:

   ```bash
   # Test opportunity generation
   RUST_LOG=strategy_manager=debug,execution_manager=debug \
     cargo test --test stargate_compass_integration
   ```

4. **Check timing issues**:
   ```bash
   # Add delays between workflow steps
   cargo test --test stargate_compass_integration -- --timeout 900
   ```

## Performance Issues

### Issue 11: Slow Test Execution

**Symptoms**:

```
⚠️ Test execution took longer than expected: 15m30s
Performance warning: Consider optimizing test execution
```

**Root Cause**: Tests running slower than expected

**Solutions**:

1. **Enable parallel execution**:

   ```bash
   cargo test --test stargate_compass_integration -- --parallel
   ```

2. **Skip non-critical tests**:

   ```bash
   # Skip TUI tests for faster execution
   cargo test --test stargate_compass_integration -- --skip-tui
   ```

3. **Optimize Anvil settings**:

   ```bash
   # Use faster block time
   anvil --fork-url https://mainnet.base.org \
     --chain-id 8453 \
     --port 8545 \
     --block-time 1  # 1 second blocks
   ```

4. **Monitor system resources**:
   ```bash
   # Check CPU and memory usage
   top -p $(pgrep -f anvil)
   htop
   ```

### Issue 12: Memory Usage Issues

**Symptoms**:

```
❌ Test failed due to out of memory error
System resource exhaustion
```

**Root Cause**: High memory usage during test execution

**Solutions**:

1. **Monitor memory usage**:

   ```bash
   # Check memory usage
   free -h
   ps aux | grep -E "(anvil|cargo|test)"
   ```

2. **Reduce test parallelism**:

   ```bash
   # Run tests sequentially
   cargo test --test stargate_compass_integration -- --test-threads=1
   ```

3. **Restart Anvil between test runs**:
   ```bash
   # Kill and restart Anvil
   pkill anvil
   anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545
   ```

## Debugging Techniques

### Enable Comprehensive Logging

```bash
# Set detailed logging
export RUST_LOG=debug

# Component-specific logging
export RUST_LOG=stargate_compass_integration=debug,tui_functionality_tester=trace

# Run tests with logging
cargo test --test stargate_compass_integration -- --verbose 2>&1 | tee test_debug.log
```

### Analyze Test Reports

```bash
# Check latest test report
cat test_reports/latest_report.json | jq '.'

# Look for specific errors
cat test_reports/latest_report.json | jq '.errors[]'

# Check warnings
cat test_reports/latest_report.json | jq '.warnings[]'
```

### Manual Component Testing

```bash
# Test configuration manager
cargo test config_manager --test stargate_compass_integration

# Test backend integration
cargo test backend_integration --test stargate_compass_integration

# Test TUI functionality
cargo test tui_functionality --test stargate_compass_integration
```

### Network Debugging

```bash
# Monitor network traffic
sudo tcpdump -i lo port 8545

# Check RPC call logs
RUST_LOG=reqwest=debug cargo test --test stargate_compass_integration
```

## Recovery Procedures

### Reset Test Environment

```bash
# Stop all processes
pkill anvil
pkill tui_harness

# Clean up test artifacts
rm -rf test_reports/*
rm -rf config/backups/*

# Restart Anvil
anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545

# Redeploy contracts
cd geometer-contracts && npm run deploy:anvil
```

### Restore Configuration

```bash
# Restore from backup
cp config/backups/local.toml.backup config/local.toml

# Or restore from default
cp config/default.toml config/local.toml
```

### Clean Rebuild

```bash
# Clean build artifacts
cargo clean

# Rebuild everything
cargo build --release

# Run tests fresh
cargo test --test stargate_compass_integration
```

## Getting Additional Help

### Log Analysis

When reporting issues, include:

1. **Full error messages** from test output
2. **Test report JSON** from `test_reports/`
3. **System information** (OS, Rust version, etc.)
4. **Configuration files** (sanitized)
5. **Anvil logs** if available

### Debug Information Collection

```bash
# Collect system info
uname -a
rustc --version
cargo --version
anvil --version

# Collect test environment info
ls -la config/
ps aux | grep anvil
netstat -an | grep 8545

# Run comprehensive debug test
RUST_LOG=debug cargo test --test stargate_compass_integration -- --verbose > debug_output.log 2>&1
```

### Contact Information

- **GitHub Issues**: Create detailed issue with logs and reproduction steps
- **Team Chat**: Contact Basilisk Bot development team
- **Documentation**: Check README.md and component-specific documentation

## Prevention

### Regular Maintenance

1. **Update dependencies** regularly
2. **Monitor test performance** trends
3. **Review and update test data** monthly
4. **Validate test environment** before critical runs

### Best Practices

1. **Run tests in clean environment**
2. **Use consistent test data**
3. **Monitor system resources**
4. **Keep detailed logs**
5. **Document any custom configurations**

This troubleshooting guide should help resolve most common issues encountered during integration testing. For persistent problems, collect debug information and contact the development team.
