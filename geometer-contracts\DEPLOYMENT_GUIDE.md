# StargateCompassV1 Deployment Guide

## 🔧 Prerequisites

### 1. Node.js Setup
You need Node.js v18 or v20. Fix the current version issue:

```bash
# Option 1: Install Node.js v20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Option 2: Use NVM (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 20
nvm use 20

# Option 3: Use Docker
docker run -v $(pwd):/workspace -w /workspace node:20 bash
```

### 2. Environment Setup
Create your `.env` file in the `geometer-contracts/` directory:

```bash
cd geometer-contracts
cp .env.example .env
```

## 📝 Required Environment Variables

Your `.env` file should contain:

```bash
# CRITICAL: Private key for contract deployment (without 0x prefix)
# ⚠️  SECURITY: Use a dedicated deployment wallet, not your main wallet
BASILISK_EXECUTION_PRIVATE_KEY=your_64_character_private_key_here

# RPC URLs for deployment
BASE_RPC_URL=https://mainnet.base.org
DEGEN_RPC_URL=https://rpc.degen.tips

# API Keys for contract verification (get from https://basescan.org/apis)
BASESCAN_API_KEY=your_basescan_api_key_here

# Verified contract addresses on Base mainnet (DO NOT CHANGE)
AAVE_POOL_ADDRESSES_PROVIDER=******************************************
STARGATE_ROUTER=******************************************

# Gas settings for deployment
GAS_PRICE_GWEI=1
GAS_LIMIT=5000000
```

## 🔑 Private Key Setup

### Option 1: Generate New Deployment Wallet (Recommended)
```bash
# Generate a new wallet specifically for deployment
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### Option 2: Export from MetaMask
1. Open MetaMask
2. Click account menu → Account Details → Export Private Key
3. Copy the private key (remove the 0x prefix)

### Option 3: Use Anvil Test Key (TESTNET ONLY)
```bash
# Default Anvil private key (has test ETH on forks)
BASILISK_EXECUTION_PRIVATE_KEY=ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
```

## 💰 Funding Your Deployment Wallet

Your deployment wallet needs ETH on Base for gas fees:

1. **Get the wallet address:**
   ```bash
   # If using the Anvil key above, the address is:
   # ******************************************
   ```

2. **Fund with ETH:**
   - Send 0.01-0.05 ETH to your deployment address
   - Use Base bridge: https://bridge.base.org/
   - Or buy directly on Base via exchanges

## 🚀 Deployment Steps

### 1. Install Dependencies
```bash
cd geometer-contracts
npm install dotenv @types/node
```

### 2. Compile Contracts
```bash
# Method 1: Direct compilation
npx hardhat compile

# Method 2: Use our script
./compile-contracts.sh
```

### 3. Deploy to Base Mainnet
```bash
# Deploy using our production script
./deploy-production.sh

# Or deploy manually
npx hardhat ignition deploy ignition/modules/StargateCompassV1Production.ts --network base
```

### 4. Verify Deployment
After successful deployment, you'll see:
```
✅ Deployment successful!
📋 Contract addresses saved in: ignition/deployments/chain-8453/
```

## 📋 Contract Verification

The contract will be automatically verified if you have `BASESCAN_API_KEY` set. Get your API key from:
https://basescan.org/apis

## 🔍 Post-Deployment

### 1. Update Bot Configuration
Copy the deployed contract address to your bot's config:

```toml
# In config/production.toml
[chains.8453.contracts]
stargate_compass_v1 = "0xYourDeployedContractAddress"
```

### 2. Test the Contract
```bash
# Test with the bot's validation
cd ..
cargo run -- -c config/production.toml validate --reason
```

## 🛡️ Security Checklist

- [ ] Used a dedicated deployment wallet
- [ ] Private key is secure and not shared
- [ ] Contract addresses match production values
- [ ] Deployment wallet has sufficient ETH
- [ ] Contract verified on BaseScan
- [ ] Bot configuration updated with new address

## 📊 Contract Details

**StargateCompassV1** enables cross-chain arbitrage between Base and Degen Chain:

- **Aave Flash Loans**: Borrows USDC on Base without collateral
- **Stargate Bridge**: Bridges USDC to Degen Chain for execution
- **Remote Execution**: Executes swaps on Degen Chain DEXes
- **Profit Return**: Returns borrowed amount + premium + profit

## 🔧 Troubleshooting

### Node.js Issues
```bash
# Check Node version
node --version

# Should be v18.x.x or v20.x.x
# If not, reinstall Node.js
```

### Compilation Errors
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npx hardhat clean
npx hardhat compile
```

### Deployment Failures
- Check wallet has sufficient ETH
- Verify RPC URL is accessible
- Ensure private key is correct (64 characters, no 0x prefix)
- Check gas price settings

### Gas Estimation Errors
```bash
# Increase gas limit in hardhat.config.ts
gas: 5000000,
gasPrice: 1000000000, // 1 gwei
```

## 📞 Support

If you encounter issues:
1. Check the error messages carefully
2. Verify all environment variables are set
3. Ensure wallet is funded
4. Try deploying to Base Sepolia testnet first for testing