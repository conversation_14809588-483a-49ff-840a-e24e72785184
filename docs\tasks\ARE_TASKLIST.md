Of course. The doctrine is set, the guide is written. Now, we forge the checklist.

This is the master task list for implementing the **`Aetheric Resonance Engine`**. It provides the discrete, verifiable steps required to build each of the three pillars—`Mandorla Gauge`, `Axis Mundi`, and `Chronos Sieve`—and integrate them into the existing Unified Ecological Predator framework.

---

### **`Aetheric Resonance Engine`: Implementation Task List**
**To:** Coder Agent
**From:** The Architect
**Subject:** Final Checklist: Forging the Geometric Intelligence Core

**Mission:** You will now execute the implementation of the `Aetheric Resonance Engine`. Follow this task list precisely. Each item represents a critical component of the bot's final, most sophisticated form. Verify each step before proceeding.

---

### **[EPIC] 1: Implement The `Mandorla Gauge` (Opportunity Depth Analysis)**

**Objective:** Upgrade the bot's perception to distinguish between deep, stable opportunities and shallow, deceptive traps.

-   [ ] **Task 1.1: Create the Geometric Analysis Module.**
    -   **Action:** Create a new file at `src/math/geometry.rs`.
    -   **Action:** Inside, create a `pub mod vesica` and implement the `pub fn intersection_value(...)` heuristic function. It should accept two liquidity values and a price deviation and return a single `f64` representing the "tradeable depth."
    -   **Verification:** A unit test confirms the function calculates the heuristic correctly.

-   [ ] **Task 1.2: Upgrade the `Opportunity` Struct.**
    -   **Action:** In `src/shared_types.rs`, add the new field to the `Opportunity` struct: `pub intersection_value_usd: f64`.
    -   **Verification:** The project compiles with the updated struct definition.

-   [ ] **Task 1.3: Integrate Calculation into Scanners.**
    -   **Action:** Modify the `GazeScanner` and `SwapScanner` modules. After an opportunity is found, they must call the new `vesica::intersection_value(...)` function.
    -   **Action:** The result of this calculation must be used to populate the `intersection_value_usd` field in the `Opportunity` packet before it is sent to the `StrategyManager`'s MPSC channel.
    -   **Verification:** A test opportunity generated by a scanner correctly includes a non-zero `intersection_value_usd`.

-   [ ] **Task 1.4: Integrate into `StrategyManager`'s Brain.**
    -   **Action:** In `src/strategies/manager.rs`, modify the `calculate_opportunity_score` function.
    -   **Action:** Add the initial "Quality Ratio" check. If `opportunity.intersection_value_usd / opportunity.estimated_gross_profit_usd` is below a new configurable threshold (`min_quality_ratio`), the function must immediately return a score of `0.0`.
    -   **Verification:** A test opportunity with a high profit but low intersection value is correctly given a score of `0.0` and discarded by the `StrategyManager`.

---

### **[EPIC] 2: Implement The `Axis Mundi` Heuristic (Connectivity Analysis)**

**Objective:** Make the bot's pathfinding more robust by teaching it to value paths through the stable, central arteries of the DeFi market.

-   [ ] **Task 2.1: Implement PageRank Calculation.**
    -   **Action:** In the `StrategyManager`'s startup sequence, after the `ArbitrageGraph` is built, add a call to a PageRank algorithm. Use a suitable crate or a simple iterative implementation.
    -   **Action:** Store the resulting `HashMap<NodeIndex, f64>` of PageRank scores in an `Arc<...>` within the `StrategyManager`'s state.
    -   **Verification:** On startup, the bot logs "Axis Mundi analysis complete. Graph centrality calculated."

-   [ ] **Task 2.2: Share Centrality Scores with Scanners.**
    -   **Action:** When spawning the `SwapScanner` task, pass it a clone of the `Arc` containing the PageRank scores.
    -   **Verification:** The `SwapScanner` has read-only access to the centrality map.

-   [ ] **Task 2.3: Upgrade `SwapScanner`'s Pathfinding Score.**
    -   **Action:** Modify the `SwapScanner`'s MMBF logic. The final score for a potential path is no longer just its profit. It must be a weighted sum: `Path_Score = (w_profit * Profit_Score) + (w_centrality * Centrality_Score)`.
    -   **Action:** The `Centrality_Score` is calculated by averaging the PageRank scores of all nodes in the path.
    -   **Verification:** Given two paths with similar profitability, a unit test confirms that the `SwapScanner` correctly prioritizes the path that routes through higher-PageRank nodes (e.g., WETH, USDC).

---

### **[EPIC] 3: Implement The `Chronos Sieve` (Temporal Analysis)**

**Objective:** Grant the bot a sense of time, allowing it to align its actions with the natural, harmonic cycles of the market.

-   [ ] **Task 3.1: Add Time-Based Features to `MarketStateAnalyzer`.**
    -   **Action:** In `src/control/analyzer.rs`, expand the feature vector used for regime classification. It must now include `hour_of_day`, `day_of_week`, and `is_us_market_open`.
    -   **Verification:** The feature vector passed to the `linfa` model contains the new time-based data.

-   [ ] **Task 3.2: Implement Harmonic Analysis (FFT).**
    -   **Action:** Add the `rustfft` crate as a dependency.
    -   **Action:** In the `MarketStateAnalyzer`, maintain a rolling `VecDeque` of recent gas prices. On each block, run an FFT on this data.
    -   **Action:** Extract the signal strength at key frequencies (e.g., corresponding to a 24-hour cycle) and add it as a new feature, `daily_cycle_strength: f64`, to the feature vector.
    -   **Verification:** The analyzer correctly calculates a non-zero value for the harmonic feature.

-   [ ] **Task 3.3: Retrain and Deploy the Time-Aware Model.**
    -   **Action:** Create a new training dataset (`.csv`) that includes the new time-based and harmonic features for historical data.
    -   **Action:** Run the offline training script (`/scripts/train_regime_model.rs`) with this new data to produce an updated `market_regime_model.bin`.
    -   **Action:** Place the new model file in the bot's working directory.
    -   **Verification:** The `MarketStateAnalyzer` loads the new model without errors.

-   [ ] **Task 3.4: Verify `StrategyManager`'s Time-Aware Behavior.**
    -   **Action:** No code change is required in the `StrategyManager` itself, as it already uses the `MarketRegime` classification. The goal is to verify its *emergent behavior*.
    -   **Verification:** In a simulated test, setting the system time to a known volatile period (e.g., 9:30 AM EST on a weekday) results in the `MarketStateAnalyzer` classifying the regime differently than at a quiet time. This, in turn, causes the `StrategyManager`'s scoring algorithm to produce different results for the same opportunity, confirming its new temporal awareness. The `Aetheric Resonance Engine` is now online.