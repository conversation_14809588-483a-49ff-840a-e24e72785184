const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 - Production Readiness Validation', function () {
  // Test fixture for production readiness testing
  async function deployProductionReadinessFixture() {
    const [owner, operator, monitor] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with substantial ETH for production testing
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('10'), // 10 ETH for extensive testing
    });

    // Set up large mock USDC balance for production-scale testing
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('10000000', 6)); // 10M USDC

    return {
      stargateCompass,
      owner,
      operator,
      monitor,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  // Production test constants
  const PRODUCTION_LOAN_AMOUNTS = [
    ethers.parseUnits('1000', 6), // 1K USDC - Small
    ethers.parseUnits('10000', 6), // 10K USDC - Medium
    ethers.parseUnits('100000', 6), // 100K USDC - Large
    ethers.parseUnits('1000000', 6), // 1M USDC - Very Large
  ];

  const LAYERZERO_FEES = [
    ethers.parseEther('0.001'), // Very low
    ethers.parseEther('0.01'), // Standard
    ethers.parseEther('0.05'), // High
    ethers.parseEther('0.1'), // Maximum
  ];

  describe('High-Load Performance Tests', function () {
    it('Should handle multiple sequential operations without degradation', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      const operationCount = 10;
      const gasUsages = [];

      for (let i = 0; i < operationCount; i++) {
        const loanAmount = ethers.parseUnits('1000', 6);
        const expectedProfit = ethers.parseUnits('10', 6);
        const minAmountOut = ethers.parseUnits('980', 6);

        const tx = await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          minAmountOut,
          expectedProfit
        );

        const receipt = await tx.wait();
        gasUsages.push(receipt.gasUsed);
      }

      // Verify gas usage remains consistent (no significant degradation)
      const avgGas =
        gasUsages.reduce((a, b) => a + b, 0n) / BigInt(gasUsages.length);
      const maxDeviation = avgGas / 10n; // 10% deviation allowed

      for (const gasUsed of gasUsages) {
        expect(gasUsed).to.be.within(
          avgGas - maxDeviation,
          avgGas + maxDeviation
        );
      }
    });

    it('Should handle various loan amounts efficiently', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      const gasUsages = [];

      for (const loanAmount of PRODUCTION_LOAN_AMOUNTS) {
        const expectedProfit = (loanAmount * 100n) / 10000n; // 1% profit
        const minAmountOut = (loanAmount * 9800n) / 10000n; // 2% slippage

        const tx = await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          minAmountOut,
          expectedProfit
        );

        const receipt = await tx.wait();
        gasUsages.push({ amount: loanAmount, gas: receipt.gasUsed });
      }

      // Verify gas usage scales reasonably with loan amount
      console.log('Gas usage by loan amount:');
      gasUsages.forEach(({ amount, gas }) => {
        console.log(
          `  ${ethers.formatUnits(amount, 6)} USDC: ${gas.toString()} gas`
        );
      });

      // Gas usage should not increase dramatically with loan amount
      const baseGas = gasUsages[0].gas;
      const maxGas = gasUsages[gasUsages.length - 1].gas;
      expect(maxGas).to.be.lt(baseGas * 2n); // Should not double
    });

    it('Should handle varying LayerZero fees efficiently', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);

      const loanAmount = ethers.parseUnits('10000', 6);
      const expectedProfit = ethers.parseUnits('100', 6);
      const minAmountOut = ethers.parseUnits('9800', 6);

      for (const fee of LAYERZERO_FEES) {
        await mockStargateRouter.setQuoteLayerZeroFee(fee, 0);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            '0x1234',
            owner.address,
            minAmountOut,
            expectedProfit
          )
        ).to.not.be.reverted;
      }
    });

    it('Should maintain performance under stress conditions', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // Stress test with rapid configuration changes
      const stressOperations = 5;
      const slippageValues = [100, 200, 300, 400, 500]; // 1% to 5%

      for (let i = 0; i < stressOperations; i++) {
        // Change configuration
        await stargateCompass.setMaxSlippage(slippageValues[i]);

        // Execute operation
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            ethers.parseUnits('1000', 6),
            '0x1234',
            owner.address,
            ethers.parseUnits('980', 6),
            ethers.parseUnits('10', 6)
          )
        ).to.not.be.reverted;
      }
    });
  });

  describe('Monitoring and Alerting Validation', function () {
    it('Should emit comprehensive monitoring events', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      const loanAmount = ethers.parseUnits('10000', 6);
      const expectedProfit = ethers.parseUnits('100', 6);
      const minAmountOut = ethers.parseUnits('9800', 6);

      // Test profitability validation event
      const flashLoanCosts = (loanAmount * 5n) / 10000n;
      const minimumProfit = (loanAmount * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          minAmountOut,
          expectedProfit
        )
      )
        .to.emit(stargateCompass, 'ProfitabilityValidated')
        .withArgs(loanAmount, expectedProfit, totalRequiredProfit);
    });

    it('Should emit configuration change events for monitoring', async function () {
      const { stargateCompass } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Test slippage configuration monitoring
      await expect(stargateCompass.setMaxSlippage(300))
        .to.emit(stargateCompass, 'SlippageConfigured')
        .withArgs(200, 300);

      await expect(stargateCompass.setMaxSlippage(500))
        .to.emit(stargateCompass, 'SlippageConfigured')
        .withArgs(300, 500);
    });

    it('Should emit ETH management events for monitoring', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployProductionReadinessFixture
      );

      const depositAmount = ethers.parseEther('1');

      // Test ETH deposit monitoring
      await expect(stargateCompass.depositETH({ value: depositAmount }))
        .to.emit(stargateCompass, 'ETHDeposited')
        .withArgs(depositAmount, owner.address);

      // Test ETH withdrawal monitoring
      const withdrawAmount = ethers.parseEther('0.5');
      await expect(stargateCompass.withdrawETH(withdrawAmount))
        .to.emit(stargateCompass, 'ETHWithdrawn')
        .withArgs(withdrawAmount, owner.address);
    });

    it('Should emit emergency events for critical monitoring', async function () {
      const { stargateCompass, owner, mockUSDC } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Test emergency pause monitoring
      await expect(stargateCompass.emergencyPause())
        .to.emit(stargateCompass, 'EmergencyPaused')
        .withArgs(owner.address);

      await expect(stargateCompass.emergencyUnpause())
        .to.emit(stargateCompass, 'EmergencyUnpaused')
        .withArgs(owner.address);

      // Test emergency withdrawal monitoring
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('1000', 6));

      await expect(stargateCompass.emergencyWithdraw(mockUSDC.target))
        .to.emit(stargateCompass, 'EmergencyWithdraw')
        .withArgs(mockUSDC.target, ethers.parseUnits('1000', 6), owner.address);
    });

    it('Should provide comprehensive state visibility for monitoring', async function () {
      const { stargateCompass } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Verify all monitoring-relevant state is accessible
      expect(await stargateCompass.maxSlippageBps()).to.be.a('bigint');
      expect(await stargateCompass.emergencyPaused()).to.be.a('boolean');
      expect(await stargateCompass.getETHBalance()).to.be.a('bigint');

      // Verify constants are accessible for monitoring thresholds
      expect(await stargateCompass.MAX_SLIPPAGE_BPS()).to.equal(1000);
      expect(await stargateCompass.MIN_PROFIT_BPS()).to.equal(50);
      expect(await stargateCompass.MAX_NATIVE_FEE()).to.equal(
        ethers.parseEther('0.1')
      );
      expect(await stargateCompass.MIN_ETH_BALANCE()).to.equal(
        ethers.parseEther('0.05')
      );

      // Verify asset recovery visibility
      const [tokens, balances] = await stargateCompass.getRecoverableAssets();
      expect(tokens).to.be.an('array');
      expect(balances).to.be.an('array');
      expect(tokens.length).to.equal(balances.length);
    });
  });

  describe('Production Configuration Validation', function () {
    it('Should validate production-ready configuration limits', async function () {
      const { stargateCompass } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Verify production-safe defaults
      expect(await stargateCompass.maxSlippageBps()).to.equal(200); // 2% - reasonable default
      expect(await stargateCompass.MAX_SLIPPAGE_BPS()).to.equal(1000); // 10% - safe maximum
      expect(await stargateCompass.MIN_PROFIT_BPS()).to.equal(50); // 0.5% - ensures profitability
      expect(await stargateCompass.MAX_NATIVE_FEE()).to.equal(
        ethers.parseEther('0.1')
      ); // 0.1 ETH - prevents drainage
      expect(await stargateCompass.MIN_ETH_BALANCE()).to.equal(
        ethers.parseEther('0.05')
      ); // 0.05 ETH - operational reserve
    });

    it('Should validate production deployment parameters', async function () {
      const { stargateCompass, mockAaveProvider, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      // Verify immutable addresses are set correctly
      expect(await stargateCompass.aaveProvider()).to.equal(
        mockAaveProvider.target
      );
      expect(await stargateCompass.stargateRouter()).to.equal(
        mockStargateRouter.target
      );
      expect(await stargateCompass.owner()).to.not.equal(ethers.ZeroAddress);

      // Verify initial state
      expect(await stargateCompass.emergencyPaused()).to.equal(false);
    });

    it('Should validate production-scale operation parameters', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // Test production-scale parameters
      const productionTests = [
        {
          amount: ethers.parseUnits('100000', 6), // 100K USDC
          profit: ethers.parseUnits('1000', 6), // 1K USDC profit (1%)
          slippage: ethers.parseUnits('98000', 6), // 2% slippage
          description: 'Large production operation',
        },
        {
          amount: ethers.parseUnits('1000000', 6), // 1M USDC
          profit: ethers.parseUnits('10000', 6), // 10K USDC profit (1%)
          slippage: ethers.parseUnits('980000', 6), // 2% slippage
          description: 'Very large production operation',
        },
      ];

      for (const test of productionTests) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            test.amount,
            '0x1234',
            owner.address,
            test.slippage,
            test.profit
          )
        ).to.not.be.reverted;
      }
    });
  });

  describe('Production Error Handling Validation', function () {
    it('Should handle production error scenarios gracefully', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      // Test various production error scenarios
      const errorScenarios = [
        {
          setup: () =>
            mockStargateRouter.setQuoteLayerZeroFee(
              ethers.parseEther('0.2'),
              0
            ),
          expectedError: 'ExcessiveFee',
          description: 'Excessive LayerZero fee',
        },
        {
          setup: () =>
            mockStargateRouter.setQuoteLayerZeroFee(
              ethers.parseEther('0.01'),
              ethers.parseUnits('100', 18)
            ),
          expectedError: 'ZROFeesNotSupported',
          description: 'Unsupported ZRO fees',
        },
        {
          setup: () => mockPool.setFlashLoanSuccess(false),
          expectedError: null, // Will revert from mock pool
          description: 'Flash loan failure',
        },
      ];

      for (const scenario of errorScenarios) {
        await scenario.setup();

        if (scenario.expectedError) {
          await expect(
            stargateCompass.executeRemoteDegenSwap(
              ethers.parseUnits('10000', 6),
              '0x1234',
              owner.address,
              ethers.parseUnits('9800', 6),
              ethers.parseUnits('100', 6)
            )
          ).to.be.revertedWithCustomError(
            stargateCompass,
            scenario.expectedError
          );
        } else {
          await expect(
            stargateCompass.executeRemoteDegenSwap(
              ethers.parseUnits('10000', 6),
              '0x1234',
              owner.address,
              ethers.parseUnits('9800', 6),
              ethers.parseUnits('100', 6)
            )
          ).to.be.reverted;
        }

        // Reset for next test
        await mockPool.setFlashLoanSuccess(true);
        await mockStargateRouter.setQuoteLayerZeroFee(
          ethers.parseEther('0.01'),
          0
        );
      }
    });

    it('Should provide detailed error information for production debugging', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Test detailed error information
      const insufficientProfit = ethers.parseUnits('1', 6);
      const loanAmount = ethers.parseUnits('10000', 6);
      const flashLoanCosts = (loanAmount * 5n) / 10000n;
      const minimumProfit = (loanAmount * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          ethers.parseUnits('9800', 6),
          insufficientProfit
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit')
        .withArgs(insufficientProfit, totalRequiredProfit, flashLoanCosts);
    });
  });

  describe('Production Security Validation', function () {
    it('Should maintain security under production load', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      // Execute multiple large operations to test security under load
      const largeOperations = 5;
      const loanAmount = ethers.parseUnits('100000', 6); // 100K USDC each

      for (let i = 0; i < largeOperations; i++) {
        const expectedProfit = ethers.parseUnits('1000', 6); // 1K USDC profit
        const minAmountOut = ethers.parseUnits('98000', 6); // 2% slippage

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            loanAmount,
            '0x1234',
            owner.address,
            minAmountOut,
            expectedProfit
          )
        ).to.not.be.reverted;
      }

      // Verify contract state remains secure
      expect(await stargateCompass.emergencyPaused()).to.equal(false);
      expect(await stargateCompass.maxSlippageBps()).to.equal(200);
    });

    it('Should handle production emergency scenarios', async function () {
      const { stargateCompass, owner, mockUSDC } = await loadFixture(
        deployProductionReadinessFixture
      );

      // Simulate production emergency with assets in contract
      await mockUSDC.mint(
        stargateCompass.target,
        ethers.parseUnits('100000', 6)
      ); // 100K USDC

      // Emergency pause should work immediately
      await expect(stargateCompass.emergencyPause()).to.not.be.reverted;

      // All operations should be blocked
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          ethers.parseUnits('1000', 6),
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          ethers.parseUnits('10', 6)
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'ContractPaused');

      // Emergency recovery should work
      await expect(stargateCompass.emergencyRecoverAll()).to.not.be.reverted;

      // Unpause should restore functionality
      await expect(stargateCompass.emergencyUnpause()).to.not.be.reverted;
    });
  });

  describe('Production Deployment Checklist Validation', function () {
    it('Should validate all production deployment requirements', async function () {
      const { stargateCompass, mockAaveProvider, mockStargateRouter } =
        await loadFixture(deployProductionReadinessFixture);

      // ✓ Contract deployed with correct constructor parameters
      expect(await stargateCompass.aaveProvider()).to.equal(
        mockAaveProvider.target
      );
      expect(await stargateCompass.stargateRouter()).to.equal(
        mockStargateRouter.target
      );

      // ✓ Owner is set correctly
      expect(await stargateCompass.owner()).to.not.equal(ethers.ZeroAddress);

      // ✓ Initial configuration is production-safe
      expect(await stargateCompass.maxSlippageBps()).to.equal(200); // 2%
      expect(await stargateCompass.emergencyPaused()).to.equal(false);

      // ✓ All constants are set to production values
      expect(await stargateCompass.MAX_SLIPPAGE_BPS()).to.equal(1000); // 10%
      expect(await stargateCompass.MIN_PROFIT_BPS()).to.equal(50); // 0.5%
      expect(await stargateCompass.MAX_NATIVE_FEE()).to.equal(
        ethers.parseEther('0.1')
      );
      expect(await stargateCompass.MIN_ETH_BALANCE()).to.equal(
        ethers.parseEther('0.05')
      );

      // ✓ Emergency functions are accessible
      await expect(stargateCompass.emergencyPause()).to.not.be.reverted;
      await expect(stargateCompass.emergencyUnpause()).to.not.be.reverted;

      // ✓ Asset recovery functions work
      const [tokens, balances] = await stargateCompass.getRecoverableAssets();
      expect(tokens).to.be.an('array');
      expect(balances).to.be.an('array');

      // ✓ ETH management functions work
      expect(await stargateCompass.getETHBalance()).to.be.gt(0);
    });

    it('Should validate production monitoring capabilities', async function () {
      const { stargateCompass } = await loadFixture(
        deployProductionReadinessFixture
      );

      // ✓ All monitoring events are defined and accessible
      const events = [
        'SlippageConfigured',
        'ProfitabilityValidated',
        'ETHDeposited',
        'ETHWithdrawn',
        'ETHRecovered',
        'EmergencyPaused',
        'EmergencyUnpaused',
        'EmergencyWithdraw',
        'EmergencyRecoverAll',
      ];

      // Events are tested in other test cases, here we verify the contract interface
      expect(stargateCompass.interface.getEvent('SlippageConfigured')).to.not.be
        .null;
      expect(stargateCompass.interface.getEvent('ProfitabilityValidated')).to
        .not.be.null;
      expect(stargateCompass.interface.getEvent('ETHDeposited')).to.not.be.null;

      // ✓ All monitoring state is accessible
      expect(await stargateCompass.maxSlippageBps()).to.be.a('bigint');
      expect(await stargateCompass.emergencyPaused()).to.be.a('boolean');
      expect(await stargateCompass.getETHBalance()).to.be.a('bigint');
    });

    it('Should validate production security measures are active', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployProductionReadinessFixture
      );

      // ✓ Access control is enforced
      expect(await stargateCompass.owner()).to.equal(owner.address);

      // ✓ Parameter validation is active
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          0, // Invalid amount
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          ethers.parseUnits('10', 6)
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount');

      // ✓ Slippage protection is active
      await expect(
        stargateCompass.setMaxSlippage(1500) // Above maximum
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'SlippageExceedsMaximum'
      );

      // ✓ Profitability validation is active
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          ethers.parseUnits('1000', 6),
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          0 // Zero profit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount');

      // ✓ Emergency controls are functional
      await expect(stargateCompass.emergencyPause()).to.not.be.reverted;
      await expect(stargateCompass.emergencyUnpause()).to.not.be.reverted;
    });
  });
});
