use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use crate::shared_types::MarketRegime;
use tracing::{debug, info};
use crate::error::BasiliskError;

// Placeholder for pre-trained HMM probabilities
// In a real scenario, these would be loaded from a file after an offline training process.
// For demonstration, we use simplified, hardcoded values.
struct HMMProbabilities {
    // Transition probabilities: P(state_t | state_{t-1})
    // Rows: previous state, Columns: current state
    // Order: CalmOrderly, RetailFomoSpike, BotGasWar, HighVolatilityCorrection, Unknown
    transitions: Vec<Vec<Decimal>>,
    // Emission probabilities: P(observation | state)
    // Rows: state, Columns: observation (simplified: 0=low_vol, 1=high_vol, 2=trending, 3=mean_reverting)
    emissions: Vec<Vec<Decimal>>,
    // Initial probabilities: P(state_0)
    initial_states: Vec<Decimal>,
}

impl Default for HMMProbabilities {
    fn default() -> Self {
        Self {
            transitions: vec![
                // CalmOrderly
                vec![dec!(0.7), dec!(0.1), dec!(0.05), dec!(0.05), dec!(0.1)],
                // RetailFomoSpike
                vec![dec!(0.1), dec!(0.6), dec!(0.1), dec!(0.1), dec!(0.1)],
                // BotGasWar
                vec![dec!(0.05), dec!(0.15), dec!(0.6), dec!(0.1), dec!(0.1)],
                // HighVolatilityCorrection
                vec![dec!(0.1), dec!(0.1), dec!(0.15), dec!(0.6), dec!(0.05)],
                // Unknown
                vec![dec!(0.2), dec!(0.2), dec!(0.2), dec!(0.2), dec!(0.2)],
            ],
            emissions: vec![
                // CalmOrderly: low_vol, trending
                vec![dec!(0.4), dec!(0.1), dec!(0.3), dec!(0.2)],
                // RetailFomoSpike: high_vol, trending
                vec![dec!(0.1), dec!(0.4), dec!(0.4), dec!(0.1)],
                // BotGasWar: high_vol, erratic
                vec![dec!(0.1), dec!(0.5), dec!(0.2), dec!(0.2)],
                // HighVolatilityCorrection: high_vol, mean_reverting
                vec![dec!(0.1), dec!(0.4), dec!(0.1), dec!(0.4)],
                // Unknown
                vec![dec!(0.25), dec!(0.25), dec!(0.25), dec!(0.25)],
            ],
            initial_states: vec![dec!(0.4), dec!(0.2), dec!(0.1), dec!(0.1), dec!(0.2)],
        }
    }
}

pub struct MarketHMM {
    probabilities: HMMProbabilities,
}

impl MarketHMM {
    pub fn new() -> Self {
        Self {
            probabilities: Self::load_probabilities().unwrap_or_default(),
        }
    }

    fn load_probabilities() -> Result<HMMProbabilities, anyhow::Error> {
        // In a real scenario, this would load probabilities from a file (e.g., JSON, TOML)
        // or a database after an offline training process.
        // For now, we'll just return the default hardcoded values.
        info!("HMM: Loading probabilities (using hardcoded defaults for now).");
        Ok(HMMProbabilities::default())
    }

    /// Predicts the most likely market regime given a sequence of observations.
    /// Implements the Viterbi algorithm.
    /// Observations are simplified for this example: 0=low_vol, 1=high_vol, 2=trending, 3=mean_reverting
    pub fn predict_regime(&self, observations: &[usize]) -> MarketRegime {
        let num_states = self.probabilities.transitions.len();
        let num_observations = observations.len();

        if num_observations == 0 {
            return MarketRegime::Unknown;
        }

        // dp[i][j] = max probability of being in state i at time j
        let mut dp = vec![vec![Decimal::ZERO; num_observations]; num_states];
        // path[i][j] = previous state that led to state i at time j
        let mut path = vec![vec![0; num_observations]; num_states];

        // Initialization step (t=0)
        for i in 0..num_states {
            dp[i][0] = self.probabilities.initial_states[i] * self.probabilities.emissions[i][observations[0]];
        }

        // Recursion step (t=1 to num_observations-1)
        for t in 1..num_observations {
            for j in 0..num_states { // current state
                let mut max_prob = dec!(-1.0);
                let mut best_prev_state = 0;

                for i in 0..num_states { // previous state
                    let prob = dp[i][t-1] * self.probabilities.transitions[i][j];
                    if prob > max_prob {
                        max_prob = prob;
                        best_prev_state = i;
                    }
                }
                dp[j][t] = max_prob * self.probabilities.emissions[j][observations[t]];
                path[j][t] = best_prev_state;
            }
        }

        // Termination step: Find the most likely last state
        let mut max_prob = dec!(-1.0);
        let mut last_state = 0;
        for i in 0..num_states {
            if dp[i][num_observations - 1] > max_prob {
                max_prob = dp[i][num_observations - 1];
                last_state = i;
            }
        }

        // Path backtracking (not strictly needed for just the last state, but good for full path)
        let mut best_path = vec![0; num_observations];
        best_path[num_observations - 1] = last_state;
        for t in (0..num_observations - 1).rev() {
            best_path[t] = path[best_path[t+1]][t+1];
        }

        debug!("HMM: Most likely state sequence: {:?}", best_path);

        match last_state {
            0 => MarketRegime::CalmOrderly,
            1 => MarketRegime::RetailFomoSpike,
            2 => MarketRegime::BotGasWar,
            3 => MarketRegime::HighVolatilityCorrection,
            _ => MarketRegime::Unknown,
        }
    }
}
