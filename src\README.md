# Source Code Overview

This directory contains the core source code for the Basilisk Bot. The architecture is designed to be modular, scalable, and maintainable.

## Table of Contents

- [Directory Structure](#directory-structure)
- [Core Modules](#core-modules)
- [Further Documentation](#further-documentation)

## Directory Structure

```
src/
├── abi/              # ABI definitions for smart contracts
├── alerting/         # Alerting system
├── bin/              # Additional binary crates
├── cli.rs            # Command-line interface
├── cli_handlers.rs   # Handlers for CLI commands
├── config/           # Configuration management
├── constants.rs      # Project-wide constants
├── contracts.rs      # Smart contract interactions
├── control/          # System monitoring and control
├── data/             # Data ingestion and processing
├── error.rs          # Error handling
├── execution/        # Transaction execution
├── lib.rs            # Library root
├── main.rs           # Main application entry point
├── math/             # Mathematical foundations
├── mev/              # MEV (Maximal Extractable Value) strategies
├── metrics.rs        # Prometheus metrics
├── multicall.rs      # Multicall contract interactions
├── prelude.rs        # Common imports
├── risk/             # Risk management
├── shared_types.rs   # Shared data structures
├── strategies/       # Core trading intelligence
└── tui/              # Terminal User Interface
```

## Core Modules

-   **`main.rs`**: The main entry point for the application.
-   **`lib.rs`**: The root of the `basilisk_bot` library.
-   **`cli.rs` / `cli_handlers.rs`**: Defines the command-line interface and its handlers.
-   **`config/`**: Manages the bot's configuration.
-   **`data/`**: Handles the ingestion and processing of market data.
-   **`strategies/`**: Contains the core trading logic and strategy management.
-   **`execution/`**: Manages the execution of trades, including gas estimation and nonce management.
-   **`risk/`**: Implements the bot's risk management framework.
-   **`math/`**: Provides the mathematical foundations for the bot's analytical models.
-   **`tui/`**: Implements the terminal user interface for monitoring and control.

## Further Documentation

For more detailed information about the project's architecture, design principles, and operational procedures, please refer to the documents in the [`docs/`](../docs/) directory.
