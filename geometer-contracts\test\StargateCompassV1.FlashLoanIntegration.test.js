const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 - Flash Loan Integration Tests', function () {
  // Test fixture for contract deployment and setup
  async function deployFlashLoanIntegrationFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with ETH for LayerZero fees
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('1'),
    });

    // Set up mock balance for the real USDC address that the contract uses
    const realUSDCAddress = '******************************************';
    await mockPool.setMockBalance(
      realUSDCAddress,
      ethers.parseUnits('100000', 6)
    ); // 100k USDC

    // Set up mock USDC balance for flash loan simulation
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('100000', 6)); // 100k USDC

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  // Test constants
  const LOAN_AMOUNT = ethers.parseUnits('1000', 6); // 1000 USDC
  const EXPECTED_PROFIT = ethers.parseUnits('10', 6); // 10 USDC profit
  const MIN_AMOUNT_OUT = ethers.parseUnits('980', 6); // 2% slippage tolerance
  const REMOTE_CALLDATA = '0x1234567890abcdef';
  const LAYERZERO_FEE = ethers.parseEther('0.01'); // 0.01 ETH

  describe('Complete Flash Loan Cycle Tests', function () {
    it('Should execute complete profitable flash loan operation successfully', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Set up successful flash loan scenario
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      // Execute the operation
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;

      // Verify flash loan was called with correct parameters
      expect(await mockPool.lastFlashLoanAsset()).to.equal(
        '******************************************'
      );
      expect(await mockPool.lastFlashLoanAmount()).to.equal(LOAN_AMOUNT);
    });

    it('Should handle flash loan with exact minimum profit requirements', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Calculate exact minimum profit required
      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n; // 0.05% Aave premium
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n; // 0.5% minimum profit
      const exactMinimumProfit = flashLoanCosts + minimumProfit;

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      // Should succeed with exact minimum profit
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          exactMinimumProfit
        )
      ).to.not.be.reverted;
    });

    it('Should emit ProfitabilityValidated event during flash loan execution', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n;
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      )
        .to.emit(stargateCompass, 'ProfitabilityValidated')
        .withArgs(LOAN_AMOUNT, EXPECTED_PROFIT, totalRequiredProfit);
    });

    it('Should handle Stargate swap with calculated minimum amounts', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await stargateCompass.executeRemoteDegenSwap(
        LOAN_AMOUNT,
        REMOTE_CALLDATA,
        owner.address,
        MIN_AMOUNT_OUT,
        EXPECTED_PROFIT
      );

      // Verify Stargate swap was called with correct parameters
      const swapParams = await mockStargateRouter.getLastSwapParams();
      expect(swapParams.dstChainId).to.equal(204); // Degen Chain ID
      expect(swapParams.srcPoolId).to.equal(1); // Base USDC pool
      expect(swapParams.dstPoolId).to.equal(13); // Degen USDC pool
      expect(swapParams.amount).to.equal(LOAN_AMOUNT);

      // Verify minimum amount calculation (should use more restrictive of user-specified or calculated)
      const expectedCalculatedMin = (LOAN_AMOUNT * 9800n) / 10000n; // 2% default slippage
      const expectedFinalMin =
        MIN_AMOUNT_OUT > expectedCalculatedMin
          ? MIN_AMOUNT_OUT
          : expectedCalculatedMin;
      expect(swapParams.minAmountOut).to.equal(expectedFinalMin);
    });
  });

  describe('Profitable Operation Scenarios', function () {
    it('Should execute high-profit operations successfully', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      const highProfit = ethers.parseUnits('50', 6); // 50 USDC profit (5%)

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          highProfit
        )
      ).to.not.be.reverted;
    });

    it('Should handle large loan amounts with proportional profit requirements', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      const largeLoanAmount = ethers.parseUnits('10000', 6); // 10k USDC
      const proportionalProfit = ethers.parseUnits('100', 6); // 100 USDC profit (1%)

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          largeLoanAmount,
          REMOTE_CALLDATA,
          owner.address,
          ethers.parseUnits('9800', 6), // 2% slippage on large amount
          proportionalProfit
        )
      ).to.not.be.reverted;
    });
  });

  describe('Unprofitable Operation Scenarios', function () {
    it('Should reject operations with insufficient profit before flash loan initiation', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      const insufficientProfit = ethers.parseUnits('1', 6); // 1 USDC - too low
      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n;
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          insufficientProfit
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit')
        .withArgs(insufficientProfit, totalRequiredProfit, flashLoanCosts);
    });

    it('Should reject zero profit operations', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      const zeroProfit = 0;
      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n;
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          zeroProfit
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit')
        .withArgs(zeroProfit, totalRequiredProfit, flashLoanCosts);
    });

    it('Should reject operations that only cover flash loan costs without profit margin', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n; // Only covers flash loan premium
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          flashLoanCosts // Only flash loan costs, no profit margin
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit')
        .withArgs(flashLoanCosts, totalRequiredProfit, flashLoanCosts);
    });
  });

  describe('Flash Loan Failure Recovery', function () {
    it('Should handle flash loan initiation failures gracefully', async function () {
      const { stargateCompass, owner, mockPool } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      // Set flash loan to fail
      await mockPool.setFlashLoanSuccess(false);

      // The flash loan failure should be handled by the mock pool
      // In a real scenario, this would revert from the Aave pool
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.reverted; // Mock pool will revert on failure
    });

    it('Should handle executeOperation failures during flash loan execution', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Set up scenario where flash loan starts but Stargate swap fails
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(false); // This will cause failure in executeOperation

      // The operation should fail during executeOperation
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.reverted; // Mock router will revert on swap failure
    });

    it('Should validate caller is Aave pool in executeOperation', async function () {
      const { stargateCompass, owner, otherAccount } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      // Try to call executeOperation directly (not from Aave pool)
      const params = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'address', 'uint256', 'uint256'],
        [REMOTE_CALLDATA, owner.address, MIN_AMOUNT_OUT, EXPECTED_PROFIT]
      );

      await expect(
        stargateCompass.connect(otherAccount).executeOperation(
          '******************************************', // USDC address (correct checksum)
          LOAN_AMOUNT,
          ethers.parseUnits('0.5', 6), // Premium
          owner.address, // Initiator
          params
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'NotAavePool');
    });
  });

  describe('Security Measures During Flash Loan', function () {
    it('Should validate profitability both pre-execution and during executeOperation', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      // This should pass both pre-execution and runtime validation
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('Should apply slippage protection during flash loan execution', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Set custom slippage tolerance
      await stargateCompass.setMaxSlippage(500); // 5%

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await stargateCompass.executeRemoteDegenSwap(
        LOAN_AMOUNT,
        REMOTE_CALLDATA,
        owner.address,
        MIN_AMOUNT_OUT,
        EXPECTED_PROFIT
      );

      // Verify slippage protection was applied
      const swapParams = await mockStargateRouter.getLastSwapParams();
      const expectedCalculatedMin = (LOAN_AMOUNT * 9500n) / 10000n; // 5% slippage
      const expectedFinalMin =
        MIN_AMOUNT_OUT > expectedCalculatedMin
          ? MIN_AMOUNT_OUT
          : expectedCalculatedMin;
      expect(swapParams.minAmountOut).to.equal(expectedFinalMin);
    });

    it('Should validate and apply fee limits during flash loan execution', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      const maxAllowedFee = ethers.parseEther('0.1'); // Maximum allowed fee

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(maxAllowedFee, 0);
      await mockStargateRouter.setSwapSuccess(true);

      // Should succeed with fee at maximum limit
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('Should validate ETH balance is sufficient for fees during flash loan', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Drain most ETH from contract
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const withdrawAmount = currentBalance - ethers.parseEther('0.01'); // Leave minimal ETH
      await stargateCompass.withdrawETH(withdrawAmount);

      const highFee = ethers.parseEther('0.05'); // More than remaining balance
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(highFee, 0);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });
  });

  describe('Flash Loan Repayment Validation', function () {
    it('Should properly calculate and repay flash loan with premium', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter, mockUSDC } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Ensure contract has USDC for repayment (simulate successful arbitrage)
      const premium = (LOAN_AMOUNT * 5n) / 10000n; // 0.05% premium
      const totalRepayment = LOAN_AMOUNT + premium;
      await mockUSDC.mint(stargateCompass.target, totalRepayment);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;

      // Verify the contract attempted to repay the correct amount
      // (This would be validated by the mock pool's behavior)
    });

    it('Should handle flash loan repayment with exact required amounts', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter, mockUSDC } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Calculate exact repayment amount
      const premium = (LOAN_AMOUNT * 5n) / 10000n;
      const exactRepayment = LOAN_AMOUNT + premium;

      // Provide exact amount needed for repayment
      await mockUSDC.mint(stargateCompass.target, exactRepayment);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });

  describe('Emergency Controls During Flash Loan', function () {
    it('Should prevent flash loan execution when contract is paused', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployFlashLoanIntegrationFixture
      );

      // Pause the contract
      await stargateCompass.emergencyPause();

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'ContractPaused');
    });

    it('Should allow flash loan execution after unpausing', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployFlashLoanIntegrationFixture);

      // Pause and then unpause
      await stargateCompass.emergencyPause();
      await stargateCompass.emergencyUnpause();

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);
      await mockStargateRouter.setSwapSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });
});
