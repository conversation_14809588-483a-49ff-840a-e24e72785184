// MISSION: Services Management Component - Process Control Center
// WHY: Provide centralized control and monitoring of all bot services
// HOW: Table-based interface with process management and log aggregation

use ratatui::{
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, List, ListItem, Paragraph, Row, Table, Wrap},
    Frame,
};
use std::collections::HashMap;

use crate::tui::app::{App, LogEvent, ManagedProcess, ProcessStatus};

impl App {
    pub fn render_services(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(40), Constraint::Percentage(60)].as_ref())
            .split(area);

        self.render_services_table(f, chunks[0]);
        self.render_service_logs(f, chunks[1]);
    }

    fn render_services_table(&self, f: &mut Frame, area: Rect) {
        let header = Row::new(vec!["Service", "Status", "PID", "Uptime"])
            .style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD))
            .height(1);

        let rows: Vec<Row> = self.managed_processes
            .iter()
            .map(|(name, process)| {
                let status_color = match &process.status {
                    ProcessStatus::Running => Color::Green,
                    ProcessStatus::Stopped => Color::Gray,
                    ProcessStatus::Starting => Color::Yellow,
                    ProcessStatus::Stopping => Color::Magenta,
                    ProcessStatus::Error(_) => Color::Red,
                };

                let status_text = match &process.status {
                    ProcessStatus::Running => "Running",
                    ProcessStatus::Stopped => "Stopped",
                    ProcessStatus::Starting => "Starting",
                    ProcessStatus::Stopping => "Stopping",
                    ProcessStatus::Error(e) => "Error",
                };

                let pid_text = process.pid
                    .map(|p| p.to_string())
                    .unwrap_or_else(|| "-".to_string());

                let uptime_text = process.started_at
                    .map(|start| {
                        let elapsed = start.elapsed().as_secs();
                        if elapsed < 60 {
                            format!("{}s", elapsed)
                        } else if elapsed < 3600 {
                            format!("{}m", elapsed / 60)
                        } else {
                            format!("{}h", elapsed / 3600)
                        }
                    })
                    .unwrap_or_else(|| "-".to_string());

                let short_name = if name.len() > 11 { 
                    format!("{}...", &name[..8]) 
                } else { 
                    name.clone() 
                };

                Row::new(vec![
                    short_name,
                    status_text.to_string(),
                    pid_text,
                    uptime_text,
                ])
                .style(if Some(name) == self.selected_service.as_ref() {
                    Style::default().bg(Color::DarkGray)
                } else {
                    Style::default()
                })
            })
            .collect();

        let table = Table::new(rows)
        .header(header)
        .block(Block::default()
            .borders(Borders::ALL)
            .title("Services [↑/↓] [S]tart [K]ill [R]estart"))
        .widths(&[
            Constraint::Length(12),
            Constraint::Length(8),
            Constraint::Length(6),
            Constraint::Length(6),
        ])
        .highlight_style(Style::default().bg(Color::DarkGray));

        f.render_widget(table, area);
    }

    fn render_service_logs(&self, f: &mut Frame, area: Rect) {
        let title = if let Some(service) = &self.selected_service {
            if let Some(process) = self.managed_processes.get(service) {
                format!("Service Logs: {} ({} lines)", service, process.log_buffer.len())
            } else {
                "Service Logs: Not found".to_string()
            }
        } else {
            "Service Logs: Select service above".to_string()
        };

        let logs: Vec<ListItem> = if let Some(service) = &self.selected_service {
            if let Some(process) = self.managed_processes.get(service) {
                process.log_buffer
                    .iter()
                    .rev() // Show newest first
                    .take(20) // Limit for better visibility
                    .map(|log| {
                        let short_message = if log.message.len() > 60 {
                            format!("{}...", &log.message[..57])
                        } else {
                            log.message.clone()
                        };
                        
                        ListItem::new(Line::from(vec![
                            Span::styled(format!("{} ", &log.timestamp[..8]), Style::default().fg(Color::Gray)), // Just time, no date
                            Span::styled(format!("[{}] ", log.severity.prefix()), Style::default().fg(log.severity.color())),
                            Span::styled(short_message, Style::default().fg(Color::White)),
                        ]))
                    })
                    .collect()
            } else {
                vec![ListItem::new("Service not found")]
            }
        } else {
            vec![ListItem::new("← Select a service from the table to view its logs")]
        };

        let list = List::new(logs)
            .block(Block::default().borders(Borders::ALL).title(title))
            .style(Style::default().fg(Color::White));

        f.render_widget(list, area);
    }

    pub fn render_aggregated_logs(&self, f: &mut Frame, area: Rect) {
        let title = if let Some(filter) = &self.log_filter {
            format!("All Logs (Filter: {}) [F]ilter [C]lear", filter)
        } else {
            "All Service Logs [F]ilter [C]lear".to_string()
        };

        let logs: Vec<ListItem> = self.aggregated_logs
            .iter()
            .rev() // Show newest first
            .filter(|log| {
                if let Some(filter) = &self.log_filter {
                    log.source.to_lowercase().contains(&filter.to_lowercase())
                } else {
                    true
                }
            })
            .take(30) // Show more logs for better visibility
            .map(|log| {
                let short_source = if log.source.len() > 8 {
                    format!("{}...", &log.source[..5])
                } else {
                    log.source.clone()
                };
                
                let short_message = if log.message.len() > 80 {
                    format!("{}...", &log.message[..77])
                } else {
                    log.message.clone()
                };
                
                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} ", &log.timestamp[..8]), Style::default().fg(Color::Gray)), // Just time
                    Span::styled(format!("[{:8}] ", short_source), Style::default().fg(get_service_color(&log.source))),
                    Span::styled(format!("[{}] ", log.severity.prefix()), Style::default().fg(log.severity.color())),
                    Span::styled(short_message, Style::default().fg(Color::White)),
                ]))
            })
            .collect();

        let list = List::new(logs)
            .block(Block::default().borders(Borders::ALL).title(title))
            .style(Style::default().fg(Color::White));

        f.render_widget(list, area);
    }

    pub fn initialize_services(&mut self) {
        // Initialize managed processes for all available services
        let services = vec![
            ("data_ingestor", "cargo run --bin data_ingestor"),
            ("listener", "cargo run --bin listener"),
            ("feature_exporter", "cargo run --bin feature_exporter"),
            ("graph_analyzer", "cargo run --bin graph_analyzer"),
            ("network_observer", "cargo run --bin network_observer"),
            ("seismic_analyzer", "cargo run --bin seismic_analyzer"),
        ];

        for (name, binary_path) in services {
            let process = ManagedProcess::new(name.to_string(), binary_path.to_string());
            self.managed_processes.insert(name.to_string(), process);
        }

        // Select the first service by default
        if !self.managed_processes.is_empty() {
            self.selected_service = self.managed_processes.keys().next().cloned();
        }
    }

    pub fn handle_services_key(&mut self, key: crossterm::event::KeyCode) {
        match key {
            crossterm::event::KeyCode::Up => {
                self.select_previous_service();
            }
            crossterm::event::KeyCode::Down => {
                self.select_next_service();
            }
            crossterm::event::KeyCode::Char('s') | crossterm::event::KeyCode::Char('S') => {
                if let Some(service) = &self.selected_service {
                    self.start_service(service.clone());
                }
            }
            crossterm::event::KeyCode::Char('k') | crossterm::event::KeyCode::Char('K') => {
                if let Some(service) = &self.selected_service {
                    self.stop_service(service.clone());
                }
            }
            crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                if let Some(service) = &self.selected_service {
                    self.restart_service(service.clone());
                }
            }
            _ => {}
        }
    }

    fn select_previous_service(&mut self) {
        if let Some(current) = &self.selected_service {
            let services: Vec<String> = self.managed_processes.keys().cloned().collect();
            if let Some(current_index) = services.iter().position(|s| s == current) {
                let new_index = if current_index == 0 {
                    services.len() - 1
                } else {
                    current_index - 1
                };
                self.selected_service = Some(services[new_index].clone());
            }
        }
    }

    fn select_next_service(&mut self) {
        if let Some(current) = &self.selected_service {
            let services: Vec<String> = self.managed_processes.keys().cloned().collect();
            if let Some(current_index) = services.iter().position(|s| s == current) {
                let new_index = (current_index + 1) % services.len();
                self.selected_service = Some(services[new_index].clone());
            }
        }
    }

    fn start_service(&mut self, service_name: String) {
        if let Some(tx) = &self.process_command_tx {
            let _ = tx.send(crate::tui::app::ProcessCommand::Start(service_name));
        }
    }

    fn stop_service(&mut self, service_name: String) {
        if let Some(tx) = &self.process_command_tx {
            let _ = tx.send(crate::tui::app::ProcessCommand::Stop(service_name));
        }
    }

    fn restart_service(&mut self, service_name: String) {
        if let Some(tx) = &self.process_command_tx {
            let _ = tx.send(crate::tui::app::ProcessCommand::Restart(service_name));
        }
    }

    pub fn add_service_log(&mut self, service_name: String, log: LogEvent) {
        // Add to specific service log buffer
        if let Some(process) = self.managed_processes.get_mut(&service_name) {
            process.add_log(log.clone());
        }

        // Add to aggregated logs
        self.aggregated_logs.push_back(log);
        if self.aggregated_logs.len() > 1000 {
            self.aggregated_logs.pop_front();
        }
    }
}

fn get_service_color(service_name: &str) -> Color {
    match service_name {
        "data_ingestor" => Color::Cyan,
        "listener" => Color::Green,
        "feature_exporter" => Color::Yellow,
        "graph_analyzer" => Color::Magenta,
        "network_observer" => Color::Blue,
        "seismic_analyzer" => Color::Red,
        _ => Color::White,
    }
}