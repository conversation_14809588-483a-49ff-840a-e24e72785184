# Environment Variables Guide - Zen Geometer

## Required Environment Variables

### Core Trading Configuration
```bash
# Private Key (REQUIRED for live trading)
BASILISK_EXECUTION_PRIVATE_KEY=your_64_character_private_key_here

# RPC Endpoints (REQUIRED)
BASE_RPC_URL=https://your-base-rpc-endpoint
DEGEN_RPC_URL=https://rpc.degen.tips
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# MEV Protection (OPTIONAL)
MEV_RELAY_AUTH_KEY=your_mev_relay_key
```

### Infrastructure (OPTIONAL - defaults provided)
```bash
# Database
DATABASE_URL=postgres://basilisk:password@localhost:5432/basilisk_db

# Redis
REDIS_URL=redis://localhost:6379

# NATS
NATS_URL=nats://localhost:4222
```

## Security Best Practices

### 1. Private Key Management
- **NEVER** commit private keys to version control
- Use secure key management systems in production
- Consider hardware wallets for high-value operations
- Rotate keys regularly

### 2. Environment Variable Setup

#### Development (.env file):
```bash
# Copy from .env.example
cp .env.example .env

# Edit with your values
BASILISK_EXECUTION_PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
BASE_RPC_URL=https://mainnet.base.org
```

#### Production (System Environment):
```bash
# Set via system environment
export BASILISK_EXECUTION_PRIVATE_KEY=your_production_key
export BASE_RPC_URL=https://your-production-rpc

# Or via Docker secrets
docker run -e BASILISK_EXECUTION_PRIVATE_KEY=your_key basilisk_bot
```

## Configuration Validation

### Check Environment Setup:
```bash
# Verify all required variables are set
cargo run -- config check

# Show current configuration (private key masked)
cargo run -- config show
```

### Common Issues:

#### Missing Private Key:
```
❌ Error: BASILISK_EXECUTION_PRIVATE_KEY not set
✅ Fix: export BASILISK_EXECUTION_PRIVATE_KEY=your_key
```

#### Invalid Private Key Format:
```
❌ Error: Invalid private key format
✅ Fix: Ensure 64-character hex string without 0x prefix
```

#### RPC Connection Failed:
```
❌ Error: Failed to connect to RPC
✅ Fix: Verify RPC_URL is accessible and valid
```

## Environment-Specific Configurations

### Development Environment
- Uses local Anvil for testing
- Default configuration in `config/default.toml`
- Private key can be test key from Anvil

### Production Environment
- Uses production RPC endpoints
- Configuration in `config/production.toml`
- **MUST** use secure private key management

### Testing Environment
- Uses testnet endpoints
- Configuration in `config/testnet.toml`
- Can use testnet private keys

## Integration with Configuration Files

The system loads environment variables in this order:
1. System environment variables
2. `.env` file in project root
3. Default values from configuration files

Example configuration loading:
```toml
[execution]
private_key = "${BASILISK_EXECUTION_PRIVATE_KEY}"
chain_id = 8453
stargate_compass_address = "******************************************"
```

## Deployment Checklist

### Before Production Deployment:
- [ ] Set `BASILISK_EXECUTION_PRIVATE_KEY` securely
- [ ] Configure production RPC endpoints
- [ ] Verify wallet has sufficient funds
- [ ] Test configuration with `cargo run -- config check`
- [ ] Run preflight checks with `./scripts/preflight.sh`

### Security Verification:
- [ ] Private key never appears in logs
- [ ] Environment variables properly masked in output
- [ ] No sensitive data in configuration files
- [ ] Proper access controls on deployment environment

## Troubleshooting

### Debug Environment Loading:
```bash
# Check if variable is set
echo $BASILISK_EXECUTION_PRIVATE_KEY

# Verify configuration loading
cargo run -- config show --verbose

# Test wallet connectivity
cargo run -- wallet check
```

### Common Solutions:
1. **Variable not found**: Check spelling and export
2. **Permission denied**: Verify file permissions on .env
3. **Invalid format**: Ensure proper hex format without 0x prefix
4. **Network issues**: Test RPC endpoints manually

---

**Security Note**: Always treat private keys as highly sensitive data. Use secure key management practices and never expose them in logs, configuration files, or version control.