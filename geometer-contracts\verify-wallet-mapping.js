async function main() {
  console.log("🔍 Verifying wallet mapping...");
  
  const privateKey = process.env.BASILISK_EXECUTION_PRIVATE_KEY;
  console.log(`Private Key (first 10): ${privateKey?.substring(0, 10)}...`);
  
  // Create wallet from private key
  const wallet = new ethers.Wallet(privateKey);
  const derivedAddress = wallet.address;
  
  console.log(`Derived Address: ${derivedAddress}`);
  console.log(`Expected Address: ******************************************`);
  console.log(`Match: ${derivedAddress.toLowerCase() === '******************************************'.toLowerCase()}`);
  
  // Check if we need to update the private key
  if (derivedAddress.toLowerCase() !== '******************************************'.toLowerCase()) {
    console.log("❌ Private key doesn't match expected address!");
    console.log("💡 You need to update BASILISK_EXECUTION_PRIVATE_KEY in .env");
    console.log("💡 Or use the address that matches your current private key");
  } else {
    console.log("✅ Private key matches expected address!");
  }
}

main().catch(console.error);
