// MISSION: TUI Components - Living Codex Educational Interface
// WHY: Modular components for the educational feedback system
// HOW: Specialized widgets for different aspects of the trading system

pub mod resonance_chamber;
pub mod trade_lifecycle;
pub mod services;
pub mod strategy_control;
pub mod resonance_diagnostics;
pub mod configuration_tuning;
pub mod risk_analysis;
pub mod network_management;
pub mod operational_control;
pub mod error_dashboard;
pub mod notification_system;

// Re-export components
pub use resonance_chamber::ResonanceChamber;
pub use trade_lifecycle::TradeLifecycle;
pub use error_dashboard::{ErrorDashboard, TuiErrorEntry};
pub use operational_control::OperationalControl;
pub use notification_system::{NotificationSystem, Notification, NotificationType};