use ethers::providers::Middleware;
// HoneypotChecker: A multi-layered defense system against honeypot tokens
//
// This module implements a fast, multi-layered approach to identifying potentially
// malicious tokens before they're added to the arbitrage graph. It uses three layers:
// 1. Fast bytecode heuristics to quickly identify suspicious patterns
// 2. Sellability simulation to verify tokens can actually be sold
// 3. External API verification as a final check
//
// The results are cached to avoid repeated checks for the same tokens.
// Uses RwLock for better read performance since cache lookups are more frequent than updates.

use anyhow::Result;
use dashmap::DashMap;
use ethers::providers::{Http, Provider};
use ethers::types::{Address, U256};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

// Result of a token security check
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SecurityStatus {
    Safe,
    Unsafe,
    Unknown,
}

pub struct HoneypotChecker {
    provider: Arc<Provider<Http>>,
    token_cache: DashMap<Address, (SecurityStatus, u64)>, // Token address -> (Status, timestamp)
    cache_ttl: Duration,            // How long to keep results in cache
    anvil_fork_url: Option<String>, // URL for Anvil fork for simulation
}

impl HoneypotChecker {
    pub fn new(
        provider: Arc<Provider<Http>>,
        cache_ttl_seconds: u64,
        anvil_fork_url: Option<String>,
    ) -> Self {
        Self {
            provider,
            token_cache: DashMap::new(),
            cache_ttl: Duration::from_secs(cache_ttl_seconds),
            anvil_fork_url,
        }
    }

    // Main entry point for checking token security
    pub async fn check_token(&self, token_address: Address) -> Result<SecurityStatus> {
        // Check cache first
        if let Some(entry) = self.token_cache.get(&token_address) {
            let (status, timestamp) = entry.value();
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .map_err(|e| anyhow::anyhow!("System time error: {}", e))?
                .as_secs();

            if now - *timestamp < self.cache_ttl.as_secs() {
                debug!(
                    "Using cached security status for token {}: {:?}",
                    token_address, status
                );
                return Ok(*status);
            }
        }

        // Layer 1: Fast bytecode heuristics
        let status = match self.check_bytecode_heuristics(token_address).await {
            Ok(true) => {
                debug!("Token {} passed bytecode heuristics check", token_address);
                // Layer 2: Sellability simulation
                if let Some(anvil_url) = &self.anvil_fork_url {
                    match self.simulate_sellability(token_address, anvil_url).await {
                        Ok(true) => {
                            debug!("Token {} passed sellability simulation", token_address);
                            // Layer 3: External API check (optional)
                            match self.check_external_api(token_address).await {
                                Ok(true) => {
                                    info!("Token {} passed all security checks", token_address);
                                    SecurityStatus::Safe
                                }
                                Ok(false) => {
                                    warn!("Token {} failed external API security check", token_address);
                                    SecurityStatus::Unsafe
                                }
                                Err(e) => {
                                    warn!(
                                        "Failed to check external API for token {}: {}",
                                        token_address, e
                                    );
                                    SecurityStatus::Safe // Previous checks passed
                                }
                            }
                        }
                        Ok(false) => {
                            warn!("Token {} failed sellability simulation", token_address);
                            SecurityStatus::Unsafe
                        }
                        Err(e) => {
                            error!(
                                "Failed to simulate sellability for token {}: {}",
                                token_address, e
                            );
                            SecurityStatus::Unknown
                        }
                    }
                } else {
                    // No simulation, proceed to external API
                    match self.check_external_api(token_address).await {
                        Ok(true) => SecurityStatus::Safe,
                        Ok(false) => SecurityStatus::Unsafe,
                        Err(_) => SecurityStatus::Safe, // Heuristics passed
                    }
                }
            }
            Ok(false) => {
                warn!("Token {} failed bytecode heuristics check", token_address);
                SecurityStatus::Unsafe
            }
            Err(e) => {
                error!(
                    "Failed to check bytecode for token {}: {}",
                    token_address, e
                );
                SecurityStatus::Unknown
            }
        };

        self.update_cache(token_address, status).await;
        Ok(status)
    }

    // Layer 1: Check bytecode for suspicious patterns
    async fn check_bytecode_heuristics(&self, token_address: Address) -> Result<bool> {
        let bytecode = self
            .provider
            .get_code(token_address, None)
            .await
            .map_err(|e| {
                anyhow::anyhow!(format!(
                    "Failed to get bytecode for {}: {}",
                    token_address, e
                ))
            })?;

        if bytecode.0.is_empty() {
            return Err(anyhow::anyhow!("Empty bytecode"));
        }

        let bytecode_hex = hex::encode(&bytecode.0);

        let malicious_patterns = ["tx.origin", "block.timestamp", "selfdestruct"];

        for pattern in malicious_patterns {
            if bytecode_hex.contains(pattern) {
                debug!(
                    "Malicious bytecode pattern '{}' found in token {}",
                    pattern, token_address
                );
                return Ok(false);
            }
        }

        Ok(true)
    }

    // Layer 2: Simulate buying and selling the token on Degen Chain
    async fn simulate_sellability(&self, token_address: Address, anvil_url: &str) -> Result<bool> {
        use ethers::signers::{LocalWallet, Signer};
        use ethers::middleware::SignerMiddleware;
        use ethers::contract::Contract;
        use ethers::abi::{Abi, Token};
        use std::process::{Command, Stdio};
        use std::thread;
        use std::time::Duration;

        // Start Anvil fork of Degen Chain
        let mut anvil_cmd = Command::new("anvil")
            .arg("--fork-url")
            .arg("https://rpc.degen.tips")
            .arg("--port")
            .arg("8545")
            .arg("--silent")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()?;
        
        // Give anvil time to start
        thread::sleep(Duration::from_secs(3));

        let result = async {
            // Connect to local Anvil fork
            let anvil_provider = Provider::<Http>::try_from("http://127.0.0.1:8545")?;
            let anvil_wallet: LocalWallet = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80".parse()?;
            let client = Arc::new(SignerMiddleware::new(
                anvil_provider, 
                anvil_wallet.with_chain_id(666666666u64)
            ));
            
            // DegenSwap router address on Degen Chain
            let dex_router_address: Address = "******************************************".parse()?;
            let weth_address: Address = "******************************************".parse()?;
            
            // Minimal router ABI for testing
            let router_abi: Abi = serde_json::from_str(r#"[
                {
                    "inputs": [
                        {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                        {"internalType": "address[]", "name": "path", "type": "address[]"},
                        {"internalType": "address", "name": "to", "type": "address"},
                        {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                    ],
                    "name": "swapExactETHForTokens",
                    "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                    "stateMutability": "payable",
                    "type": "function"
                },
                {
                    "inputs": [
                        {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                        {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                        {"internalType": "address[]", "name": "path", "type": "address[]"},
                        {"internalType": "address", "name": "to", "type": "address"},
                        {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                    ],
                    "name": "swapExactTokensForETH",
                    "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                    "stateMutability": "nonpayable",
                    "type": "function"
                }
            ]"#)?;
            
            let router_contract = Contract::new(dex_router_address, router_abi, client.clone());
            let test_amount = ethers::utils::parse_ether("0.01")?; // 0.01 ETH for testing
            let deadline = U256::from((client.get_block_number().await? + 100).as_u64());
            let wallet_address = client.address();
            
            // Step 1: Try to buy the token with ETH
            let buy_path = vec![weth_address, token_address];
            let buy_call = router_contract
                .method::<_, Vec<U256>>("swapExactETHForTokens", (
                    U256::zero(), // amountOutMin = 0 (accept any amount)
                    buy_path,
                    wallet_address,
                    deadline,
                ))?
                .value(test_amount);
            
            let buy_receipt = match buy_call.send().await?.await? {
                Some(receipt) if receipt.status == Some(1.into()) => receipt,
                _ => {
                    debug!("Buy transaction failed for token {}", token_address);
                    return Ok(false);
                }
            };
            
            debug!("Successfully bought token {} in simulation", token_address);
            
            // Step 2: Try to sell the token back to ETH
            // First, we need to approve the router to spend our tokens
            let token_abi: Abi = serde_json::from_str(r#"[
                {
                    "inputs": [
                        {"internalType": "address", "name": "spender", "type": "address"},
                        {"internalType": "uint256", "name": "amount", "type": "uint256"}
                    ],
                    "name": "approve",
                    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
                    "stateMutability": "nonpayable",
                    "type": "function"
                },
                {
                    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                    "stateMutability": "view",
                    "type": "function"
                }
            ]"#)?;
            
            let token_contract = Contract::new(token_address, token_abi, client.clone());
            let token_balance: U256 = token_contract
                .method("balanceOf", wallet_address)?
                .call()
                .await?;
            
            if token_balance.is_zero() {
                debug!("No token balance after buy for token {}", token_address);
                return Ok(false);
            }
            
            // Approve router to spend tokens
            let approve_receipt = token_contract
                .method::<_, bool>("approve", (dex_router_address, token_balance))?
                .send()
                .await?
                .await?;
            
            if !matches!(approve_receipt.and_then(|r| r.status), Some(s) if s == 1.into()) {
                debug!("Approve transaction failed for token {}", token_address);
                return Ok(false);
            }
            
            // Now try to sell
            let sell_path = vec![token_address, weth_address];
            let sell_call = router_contract
                .method::<_, Vec<U256>>("swapExactTokensForETH", (
                    token_balance,
                    U256::zero(), // amountOutMin = 0
                    sell_path,
                    wallet_address,
                    deadline,
                ))?;
            
            let sell_receipt = sell_call.send().await?.await?;
            
            match sell_receipt {
                Some(receipt) if receipt.status == Some(1.into()) => {
                    debug!("Successfully sold token {} in simulation", token_address);
                    Ok(true)
                }
                _ => {
                    debug!("Sell transaction failed for token {} - likely honeypot", token_address);
                    Ok(false)
                }
            }
        }.await;

        // Ensure anvil process is terminated
        let _ = anvil_cmd.kill();
        
        result
    }

    // Layer 3: Check with external security API
    async fn check_external_api(&self, token_address: Address) -> Result<bool> {
        let client = reqwest::Client::new();
        let url = format!(
            "https://api.gopluslabs.io/api/v1/token_security/1?contract_addresses={:?}",
            token_address
        );

        let response = client.get(&url).send().await?;

        if response.status().is_success() {
            let body: serde_json::Value = response.json().await?;
            if let Some(is_honeypot) = body["result"][token_address.to_string()]["is_honeypot"].as_bool() {
                return Ok(!is_honeypot);
            }
        }

        Ok(false)
    }

    // Update the token cache with a new status
    async fn update_cache(&self, token_address: Address, status: SecurityStatus) {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        self.token_cache.insert(token_address, (status, timestamp));
        debug!(
            "Updated cache for token {} with status {:?}",
            token_address, status
        );
    }
}
