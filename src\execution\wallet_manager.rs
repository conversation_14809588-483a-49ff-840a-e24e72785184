// MISSION: WalletManager - Secure Wallet Management for Cross-Chain Operations
// WHY: Centralized, secure handling of wallet operations with proper error handling
// HOW: Arc-wrapped LocalWallet with chain-aware initialization

use anyhow::Result;
use ethers::{
    signers::{LocalWallet, Signer},
    types::Address,
};
use std::sync::Arc;
use tracing::{debug, info, warn};
use crate::error::BasiliskError;

/// Secure wallet manager for signing transactions across chains
#[derive(Debug, Clone)]
pub struct WalletManager {
    pub signer: Arc<LocalWallet>,
}

impl WalletManager {
    /// Create a new WalletManager from a private key string and chain ID
    pub fn new(private_key: &str, chain_id: u64) -> Result<Self> {
        debug!("Initializing WalletManager for chain ID: {}", chain_id);
        
        // MEDIUM PRIORITY FIX #5: Implement Chain ID Validation
        Self::validate_chain_id(chain_id)?;
        
        // Parse the private key into a LocalWallet
        let wallet: LocalWallet = private_key.parse()?;
        
        // Set the chain ID for the wallet
        let wallet_with_chain = wallet.with_chain_id(chain_id);
        
        info!(
            "WalletManager initialized - Address: {:?}, Chain ID: {}", 
            wallet_with_chain.address(), 
            chain_id
        );
        
        Ok(Self {
            signer: Arc::new(wallet_with_chain),
        })
    }
    
    /// Get the wallet's address
    pub fn address(&self) -> Address {
        self.signer.address()
    }
    
    /// Get the chain ID for this wallet
    pub fn chain_id(&self) -> u64 {
        self.signer.chain_id()
    }
    
    /// Create a clone of the signer for use in middleware
    pub fn signer(&self) -> Arc<LocalWallet> {
        self.signer.clone()
    }
    
    /// MEDIUM PRIORITY FIX #5: Validate chain ID to prevent replay attacks
    fn validate_chain_id(chain_id: u64) -> Result<()> {
        // List of supported chain IDs
        const SUPPORTED_CHAINS: &[u64] = &[
            1,          // Ethereum Mainnet
            8453,       // Base
            42161,      // Arbitrum One
            137,        // Polygon
            10,         // Optimism
            666666666,  // Degen Chain
            // Test networks
            11155111,   // Sepolia
            84532,      // Base Sepolia
            421614,     // Arbitrum Sepolia
        ];
        
        if !SUPPORTED_CHAINS.contains(&chain_id) {
            return Err(anyhow::anyhow!(
                "Unsupported chain ID: {}. Supported chains: {:?}",
                chain_id,
                SUPPORTED_CHAINS
            ));
        }
        
        // Additional validation for production chains
        match chain_id {
            1 => {
                warn!("Using Ethereum Mainnet - ensure this is intentional (high gas costs)");
            },
            8453 | 42161 | 137 | 10 => {
                info!("Using production L2 chain: {}", chain_id);
            },
            666666666 => {
                info!("Using Degen Chain (L3) - experimental network");
            },
            11155111 | 84532 | 421614 => {
                warn!("Using testnet chain: {} - not for production", chain_id);
            },
            _ => {
                warn!("Unknown chain ID: {} - proceed with caution", chain_id);
            }
        }
        
        debug!("Chain ID {} validation passed", chain_id);
        Ok(())
    }
    
    /// Validate that a transaction is intended for the correct chain
    pub fn validate_transaction_chain(&self, target_chain_id: u64) -> Result<()> {
        let wallet_chain_id = self.chain_id();
        
        if wallet_chain_id != target_chain_id {
            return Err(anyhow::anyhow!(
                "Chain ID mismatch: wallet configured for chain {} but transaction targets chain {}",
                wallet_chain_id,
                target_chain_id
            ));
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_wallet_manager_creation() {
        // Test private key (from Anvil default accounts)
        let private_key = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80";
        let chain_id = 8453; // Base chain ID
        
        let wallet_manager = WalletManager::new(private_key, chain_id).unwrap();
        
        // Verify the address matches expected
        let expected_address: Address = "******************************************".parse().unwrap();
        assert_eq!(wallet_manager.address(), expected_address);
        assert_eq!(wallet_manager.chain_id(), chain_id);
    }
    
    #[test]
    fn test_invalid_private_key() {
        let invalid_key = "invalid_key";
        let chain_id = 8453;
        
        let result = WalletManager::new(invalid_key, chain_id);
        assert!(result.is_err());
    }
}