# Development Workflow Documentation

## Mission Brief: Zen Geometer Development Environment and Procedures

This comprehensive tactical manual provides complete guidance for developers working on the **Zen Geometer** autonomous trading system, covering Task Master integration, Mission Control Protocol (MCP) usage, and Anvil blockchain simulation testing procedures.

**Mission Objective**: Establish a world-class development environment for autonomous trading systems  
**Tactical Approach**: Integrated development workflow with AI assistance and blockchain simulation  
**Production Status**: ✅ **DEVELOPMENT READY** - All tools and procedures validated and operational

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Task Master System Integration](#task-master-system-integration)
- [Mission Control Protocol (MCP) Usage](#mission-control-protocol-mcp-usage)
- [Anvil Blockchain Simulation Testing](#anvil-blockchain-simulation-testing)
- [Development Workflow Procedures](#development-workflow-procedures)
- [Testing Procedures](#testing-procedures)
- [Code Quality and Standards](#code-quality-and-standards)
- [Troubleshooting](#troubleshooting)

## 🛠️ Development Environment Setup

### Prerequisites

Before beginning development, ensure you have the following tools installed:

```bash
# Rust toolchain
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update stable

# Foundry (for Anvil blockchain simulation)
curl -L https://foundry.paradigm.xyz | bash
foundryup

# Docker and Docker Compose
# Follow platform-specific installation instructions

# Node.js (for Task Master MCP integration)
# Install via your preferred method (nvm, package manager, etc.)
```

### Initial Setup

1. **Clone and Setup Repository**:

```bash
git clone <repository-url>
cd zen-geometer
./scripts/local_dev_setup.sh
```

2. **Verify Installation**:

```bash
# Verify Rust
cargo --version
rustc --version

# Verify Foundry
forge --version
anvil --version
cast --version

# Verify Docker
docker --version
docker compose version
```

3. **Environment Configuration**:

```bash
# Copy environment template
cp .env.example .env

# Edit configuration as needed
# See docs/ENVIRONMENT_VARIABLES_GUIDE.md for details
```

## 🤖 Task Master System Integration

The **Zen Geometer** project integrates with Task Master AI for autonomous development workflow management. This system provides AI-driven task execution and code generation capabilities designed to maintain philosophical alignment with sacred geometry principles.

### Task Master Configuration

The Task Master system is configured through multiple files:

#### 1. Core Configuration (`.taskmaster/config.json`)

```json
{
  "models": {
    "main": {
      "provider": "anthropic",
      "modelId": "claude-3-7-sonnet-20250219",
      "maxTokens": 120000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity",
      "modelId": "sonar-pro",
      "maxTokens": 8700,
      "temperature": 0.1
    }
  },
  "global": {
    "logLevel": "info",
    "debug": false,
    "defaultSubtasks": 5,
    "defaultPriority": "medium",
    "projectName": "Taskmaster"
  }
}
```

#### 2. Project-Specific Configuration (`taskmaster_config.toml`)

This file defines the AI's behavior and standards for the Zen Geometer project:

```toml
[persona]
name = "MCP-Harmonist"
role = "AI Master Control Program for the Zen Geometer Project"
communication_style = "Technical Synthesis"

[directives]
primary_objective = "Translate abstract strategies and detailed task lists into high-quality, production-ready, and philosophically-aligned Rust code."
conceptual_anchor_file = "GEMINI.md"

priorities = [
    "PhilosophicalAlignment", # Adherence to GEMINI.md doctrines
    "CodeCorrectness",       # Code must work as intended
    "Security",              # No vulnerabilities
    "Performance",           # Optimized hot-paths
    "Clarity",               # Readable and maintainable
    "Modularity"             # Service-oriented architecture
]

[code_generation]
language = "Rust"
rust_edition = "2021"
style_linter = "rustfmt"
quality_linter = "clippy -- -D warnings"
documentation_style = "Doc comments (`///`) for all public functions"
error_handling_pattern = "Use anyhow::Result for application-level errors"
```

### Using Task Master

#### 1. Creating Task Lists

Task Master expects structured markdown files with implementation guides:

```markdown
# Feature Implementation Guide

## Overview

Brief description of the feature to implement.

## Requirements

- Requirement 1: Specific functionality needed
- Requirement 2: Performance constraints
- Requirement 3: Integration requirements

## Implementation Tasks

- [ ] Task 1: Setup core structures
- [ ] Task 2: Implement business logic
- [ ] Task 3: Add error handling
- [ ] Task 4: Write tests
- [ ] Task 5: Integration testing

## Verification Checklist

- [ ] Code compiles without warnings
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Performance benchmarks met
```

#### 2. Task Execution Workflow

1. **Prepare Task Document**: Create detailed implementation guide
2. **Review GEMINI.md**: Ensure philosophical alignment
3. **Execute Tasks**: Use Task Master to implement features
4. **Verification**: Run automated checks and tests
5. **Documentation**: Update relevant documentation

#### 3. Quality Standards

Task Master enforces strict quality standards:

- **Zero Warnings**: Code must compile with `clippy -- -D warnings`
- **Complete Documentation**: All public APIs documented
- **Test Coverage**: New functionality must include tests
- **Philosophical Alignment**: Code must align with GEMINI.md principles

## 🎛️ Mission Control Protocol (MCP) Usage

The **Mission Control Protocol** provides integration with external AI services and tools for enhanced development capabilities, maintaining tactical coordination between human operators and AI systems.

### MCP Configuration

MCP is configured in `.roo/mcp.json`:

```json
{
  "mcpServers": {
    "task-master-ai": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],
      "env": {
        "ANTHROPIC_API_KEY": "your-key-here",
        "PERPLEXITY_API_KEY": "your-key-here",
        "OPENAI_API_KEY": "your-key-here"
      }
    }
  }
}
```

### MCP Server Management

#### Starting MCP Services

```bash
# MCP servers start automatically when configured
# Check status in your IDE's MCP panel

# Manual server management (if needed)
npx -y --package=task-master-ai task-master-ai
```

#### Available MCP Tools

The Task Master MCP server provides:

- **Code Generation**: AI-driven code implementation
- **Documentation**: Automated documentation generation
- **Testing**: Test case generation and validation
- **Refactoring**: Code improvement suggestions
- **Architecture**: System design recommendations

### MCP Development Workflow

1. **Configure API Keys**: Set up required API keys in MCP configuration
2. **Enable Auto-Approval**: Add trusted tools to `autoApprove` list
3. **Use MCP Tools**: Access tools through IDE integration
4. **Monitor Usage**: Check MCP server logs for issues

## ⚔️ Anvil Blockchain Simulation Testing

**Anvil** provides local blockchain simulation for testing smart contract interactions and transaction execution without using real networks, enabling comprehensive testing of cross-chain execution strategies.

### Anvil Setup and Usage

#### 1. Basic Anvil Operations

```bash
# Start Anvil with Base mainnet fork
anvil --fork-url https://mainnet.base.org --chain-id 8453 --port 8545

# Start with specific block number
anvil --fork-url https://mainnet.base.org --fork-block-number ********

# Start with custom accounts
anvil --accounts 10 --balance 1000
```

#### 2. Anvil Integration in Tests

The project includes comprehensive Anvil integration for testing:

```rust
// Example from tests/integration/stargate_compass/anvil_client.rs
use ethers::utils::Anvil;

pub struct AnvilClient {
    pub anvil: Option<Anvil>,
    pub provider: Arc<Provider<Http>>,
    pub wallet: LocalWallet,
    pub chain_id: u64,
    pub url: String,
}

impl AnvilClient {
    /// Create new Anvil client with Base fork
    pub async fn new_base_fork(contract_address: Option<Address>) -> Result<Self> {
        let anvil = Anvil::new()
            .fork("https://mainnet.base.org")
            .fork_block_number(********u64)
            .chain_id(8453u64)
            .port(8545u16)
            .spawn();

        let url = anvil.endpoint();
        let provider = Arc::new(Provider::<Http>::try_from(&url)?);
        let wallet = anvil.keys()[0].clone().into();

        Ok(Self {
            anvil: Some(anvil),
            provider,
            wallet,
            chain_id: 8453,
            url,
            contract_address,
        })
    }
}
```

#### 3. Anvil Testing Procedures

**Contract Deployment Testing**:

```rust
// Verify contract deployment
let result = client.verify_contract_deployment(contract_address).await?;
assert!(result.deployed);
assert!(result.functions_callable);
```

**Transaction Testing**:

```rust
// Execute and monitor transactions
let tx_result = client.execute_transaction(tx_request).await?;
assert!(tx_result.success);
assert!(!tx_result.reverted);
```

**Blockchain State Management**:

```rust
// Take snapshots for test isolation
let snapshot_id = client.snapshot().await?;

// Run test operations
// ...

// Revert to clean state
client.revert_to_snapshot(snapshot_id).await?;
```

### Anvil Testing Best Practices

1. **Use Forked Networks**: Test against real network state
2. **Snapshot Management**: Isolate tests with snapshots
3. **Gas Estimation**: Test gas usage and optimization
4. **Error Handling**: Test transaction failures and reverts
5. **Network Conditions**: Simulate various network states

## 🎯 Development Workflow Procedures

### Daily Development Workflow

#### 1. Environment Startup

```bash
# Start development environment
./scripts/dev_environment.sh start

# Verify services are running
./scripts/dev_environment.sh status

# Check logs if needed
./scripts/dev_environment.sh logs
```

#### 2. Code Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes following GEMINI.md principles
# Ensure code aligns with philosophical foundations

# Run continuous checks
cargo check
cargo clippy -- -D warnings
cargo fmt
```

#### 3. Testing During Development

```bash
# Run unit tests
cargo test --lib

# Run integration tests with Anvil
cargo test --test integration

# Run specific test suites
cargo test test_anvil_integration
```

#### 4. Pre-Commit Validation

```bash
# Complete system test
./scripts/test_complete_system.sh

# Validate configuration
cargo run -- validate

# Check documentation
./scripts/verify_documentation.sh
```

### Feature Development Process

#### 1. Planning Phase

1. **Review GEMINI.md**: Understand philosophical alignment requirements
2. **Create Task List**: Use Task Master template format
3. **Architecture Review**: Ensure alignment with Hub and Spoke model
4. **Define Tests**: Plan testing strategy including Anvil scenarios

#### 2. Implementation Phase

1. **Setup Branch**: Create feature branch with descriptive name
2. **Implement Core**: Focus on core functionality first
3. **Add Tests**: Include unit and integration tests
4. **Documentation**: Document public APIs and complex logic
5. **Validation**: Run quality checks continuously

#### 3. Integration Phase

1. **System Testing**: Run complete test suite
2. **Performance Testing**: Benchmark critical paths
3. **Security Review**: Check for vulnerabilities
4. **Documentation Update**: Update relevant documentation
5. **Code Review**: Peer review before merge

## 🧪 Testing Procedures

### Test Categories

#### 1. Unit Tests

```bash
# Run all unit tests
cargo test --lib

# Run specific module tests
cargo test --lib math::geometry

# Run with output
cargo test --lib -- --nocapture
```

#### 2. Integration Tests

```bash
# Run integration tests (requires Anvil)
cargo test --test integration

# Run specific integration test
cargo test test_stargate_compass_integration

# Run with Anvil debugging
RUST_LOG=debug cargo test test_anvil_client
```

#### 3. End-to-End Tests

```bash
# Complete system test
./scripts/test_complete_system.sh

# Live data integration test
./scripts/test_live_data_integration.sh

# Zen Geometer strategy test
./scripts/test_zen_geometer.sh
```

### Anvil-Specific Testing

#### 1. Setup Anvil Environment

```bash
# Start Anvil for testing
anvil --fork-url https://mainnet.base.org --chain-id 8453 &

# Set environment variables
export ANVIL_RPC_URL="http://localhost:8545"
export TEST_RPC_URL="http://localhost:8545"
```

#### 2. Run Anvil Tests

```bash
# Integration tests with Anvil
cargo test test_integration_anvil

# Stargate Compass contract tests
cargo test stargate_compass_integration

# Backend integration tests
cargo test test_backend_integration
```

#### 3. Anvil Test Utilities

The project provides comprehensive Anvil utilities:

- **AnvilClient**: Blockchain interaction and testing
- **Contract Verification**: Deployment and functionality testing
- **Transaction Monitoring**: Execution and confirmation tracking
- **State Management**: Snapshots and rollbacks
- **Network Simulation**: Various network conditions

## 📋 Code Quality and Standards

### Rust Code Standards

#### 1. Formatting and Linting

```bash
# Format code
cargo fmt

# Check linting (zero warnings required)
cargo clippy -- -D warnings

# Check all targets
cargo clippy --all-targets -- -D warnings
```

#### 2. Documentation Standards

All public APIs must include documentation:

````rust
/// Calculates the Aetheric Resonance Score for a trading opportunity.
///
/// This function synthesizes inputs from the three analytical pillars:
/// - Temporal Harmonics (Chronos Sieve)
/// - Geometric Score (Mandorla Gauge)
/// - Network Resonance State (Network Seismology)
///
/// # Arguments
/// * `temporal_harmonics` - Market rhythm and cycle analysis
/// * `geometric_score` - Opportunity shape and asset centrality
/// * `network_state` - Network propagation and coherence metrics
///
/// # Returns
/// * `AethericResonanceScore` - Final synthesized opportunity score
///
/// # Example
/// ```rust
/// let score = calculate_aetheric_resonance(
///     temporal_harmonics,
///     geometric_score,
///     network_state
/// )?;
/// ```
pub fn calculate_aetheric_resonance(
    temporal_harmonics: &TemporalHarmonics,
    geometric_score: f64,
    network_state: &NetworkResonanceState,
) -> Result<AethericResonanceScore> {
    // Implementation...
}
````

#### 3. Error Handling Patterns

Follow established error handling patterns:

```rust
// Application-level errors
use anyhow::{Result, Context};

pub fn process_market_data(data: &MarketData) -> Result<ProcessedData> {
    let validated = validate_data(data)
        .context("Failed to validate market data")?;

    let processed = transform_data(validated)
        .context("Failed to transform market data")?;

    Ok(processed)
}

// Library-level specific errors
use thiserror::Error;

#[derive(Error, Debug)]
pub enum GeometryError {
    #[error("Invalid convexity ratio: {ratio}")]
    InvalidConvexityRatio { ratio: f64 },

    #[error("Insufficient data points: expected {expected}, got {actual}")]
    InsufficientDataPoints { expected: usize, actual: usize },
}
```

### Philosophical Alignment

All code must align with GEMINI.md principles:

#### 1. Resonance over Correlation

```rust
// Good: Focus on resonance and quality
let resonance_score = calculate_market_resonance(&opportunity)?;
if resonance_score > MIN_RESONANCE_THRESHOLD {
    execute_opportunity(opportunity).await?;
}

// Avoid: Simple correlation-based decisions
// if price_correlation > 0.8 { ... }
```

#### 2. Layered Analysis

```rust
// Good: Progressive refinement through analytical layers
let temporal_score = chronos_sieve.analyze(&market_data)?;
let geometric_score = mandorla_gauge.evaluate(&opportunity)?;
let network_score = network_seismology.assess(&network_state)?;

let final_score = synthesize_scores(temporal_score, geometric_score, network_score)?;
```

#### 3. Contextual Execution

```rust
// Good: Consider network conditions for timing
let optimal_timing = harmonic_timing_oracle.calculate_optimal_broadcast_time(
    &network_state,
    &opportunity
)?;

schedule_execution(opportunity, optimal_timing).await?;
```

## 🛠️ Troubleshooting

### Common Development Issues

#### 1. Anvil Connection Issues

```bash
# Check if Anvil is running
curl -s http://localhost:8545 -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'

# Restart Anvil if needed
pkill anvil
anvil --fork-url https://mainnet.base.org --chain-id 8453 &
```

#### 2. Task Master Integration Issues

```bash
# Check MCP server status
# Look for MCP panel in your IDE

# Restart MCP server
# Use IDE MCP management tools

# Check API keys
grep -r "API_KEY" .roo/mcp.json
```

#### 3. Build and Test Issues

```bash
# Clean build artifacts
cargo clean

# Update dependencies
cargo update

# Check for conflicting processes
lsof -i :8545  # Check Anvil port
lsof -i :8222  # Check NATS port
```

#### 4. Infrastructure Services

```bash
# Check Docker services
docker compose ps

# Restart infrastructure
./scripts/dev_environment.sh restart

# Check service logs
./scripts/dev_environment.sh logs
```

### Performance Optimization

#### 1. Build Optimization

```bash
# Release build for performance testing
cargo build --release

# Profile-guided optimization
cargo build --profile release-with-debug
```

#### 2. Test Optimization

```bash
# Parallel test execution
cargo test -- --test-threads=4

# Skip slow tests during development
cargo test --lib  # Skip integration tests
```

#### 3. Anvil Optimization

```bash
# Use specific block number to avoid sync delays
anvil --fork-url https://mainnet.base.org --fork-block-number ********

# Increase block time for testing
anvil --block-time 1
```

## Development Best Practices

### 1. Code Organization

- Follow the established module structure
- Keep functions focused and single-purpose
- Use descriptive names that reflect GEMINI.md concepts
- Maintain clear separation between analytical pillars

### 2. Testing Strategy

- Write tests before implementation (TDD)
- Use Anvil for realistic blockchain testing
- Include edge cases and error conditions
- Test philosophical alignment, not just functionality

### 3. Documentation

- Document the "why" not just the "what"
- Explain how code contributes to the ARE system
- Include examples for complex functionality
- Keep documentation current with code changes

### 4. Performance Considerations

- Profile hot paths regularly
- Use appropriate data structures
- Consider memory allocation patterns
- Optimize for the three analytical pillars

### 5. Security Practices

- Validate all external inputs
- Use secure random number generation
- Implement proper error handling
- Regular security audits of critical paths

---

## 🎯 Development Best Practices

### 1. Code Organization

- Follow the established module structure
- Keep functions focused and single-purpose
- Use descriptive names that reflect GEMINI.md concepts
- Maintain clear separation between analytical pillars

### 2. Testing Strategy

- Write tests before implementation (TDD)
- Use Anvil for realistic blockchain testing
- Include edge cases and error conditions
- Test philosophical alignment, not just functionality

### 3. Documentation

- Document the "why" not just the "what"
- Explain how code contributes to the ARE system
- Include examples for complex functionality
- Keep documentation current with code changes

### 4. Performance Considerations

- Profile hot paths regularly
- Use appropriate data structures
- Consider memory allocation patterns
- Optimize for the three analytical pillars

### 5. Security Practices

- Validate all external inputs
- Use secure random number generation
- Implement proper error handling
- Regular security audits of critical paths

---

This comprehensive development workflow documentation provides the foundation for effective development on the **Zen Geometer** project, ensuring philosophical alignment, code quality, and comprehensive testing through Task Master integration, MCP usage, and Anvil blockchain simulation.

_"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."_ - **The Zen Geometer**
