const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 Fee Monitoring and Optimization Tests', function () {
  // Test fixture for contract deployment
  async function deployStargateCompassFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockAaveProvider.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory('MockPool');
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockStargateRouter.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USDC', 'USDC', 6);

    // Set up mock provider to return mock pool
    await mockAaveProvider.setPool(mockPool.target);

    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  describe('Fee Monitoring Constants', function () {
    it('Should have correct FEE_HISTORY_SIZE constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.FEE_HISTORY_SIZE()).to.equal(10);
    });

    it('Should have correct FEE_EFFICIENCY_THRESHOLD_BPS constant', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );
      expect(await stargateCompass.FEE_EFFICIENCY_THRESHOLD_BPS()).to.equal(
        2000
      ); // 20%
    });
  });

  describe('Fee Statistics Functions', function () {
    it('Should return zero statistics when no operations have been tracked', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      const stats = await stargateCompass.getFeeStatistics();
      expect(stats.totalOperations).to.equal(0);
      expect(stats.averageFee).to.equal(0);
      expect(stats.minFee).to.equal(0);
      expect(stats.maxFee).to.equal(0);
      expect(stats.cumulativeFeesSpent).to.equal(0);
    });

    it('Should return correct average fee', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      // Initially should return 0
      expect(await stargateCompass.getAverageFee()).to.equal(0);
    });

    it('Should return empty recent fees when no operations tracked', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      const [fees, count] = await stargateCompass.getRecentFees();
      expect(fees.length).to.equal(0);
      expect(count).to.equal(0);
    });
  });

  describe('Fee Optimization Recommendations', function () {
    it('Should return no data message when no operations tracked', async function () {
      const { stargateCompass } = await loadFixture(
        deployStargateCompassFixture
      );

      const [recommendation, averageFee, totalOperations] =
        await stargateCompass.getFeeOptimizationRecommendation();
      expect(recommendation).to.equal('No operations tracked yet');
      expect(averageFee).to.equal(0);
      expect(totalOperations).to.equal(0);
    });
  });

  describe('Fee Tracking Integration', function () {
    beforeEach(async function () {
      // This setup will be used for integration tests
      // We'll need to set up the full flow to test fee tracking
    });

    it('Should track fees during successful operations', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      // Set up mock router with specific fee
      const testFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(testFee, 0);

      // Fund contract with sufficient ETH
      const bufferedFee = testFee + (testFee * 200n) / 10000n; // 2% buffer
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee + ethers.parseEther('0.01'),
      });

      // Mint USDC to mock pool
      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount);

      // Execute operation to trigger fee tracking
      const remoteCalldata = '0x1234';
      const remoteSwapRouter = ethers.Wallet.createRandom().address;
      const minAmountOut = ethers.parseUnits('990', 6);
      const expectedProfit = ethers.parseUnits('10', 6);

      const tx = await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        remoteCalldata,
        remoteSwapRouter,
        minAmountOut,
        expectedProfit
      );

      // Check for FeeTracked event
      await expect(tx)
        .to.emit(stargateCompass, 'FeeTracked')
        .withArgs(
          1,
          testFee,
          testFee,
          await ethers.provider.getBlock('latest').then((b) => b.timestamp)
        );
    });

    it('Should update fee statistics after operations', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      // Set up mock router with specific fee
      const testFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(testFee, 0);

      // Fund contract with sufficient ETH
      const bufferedFee = testFee + (testFee * 200n) / 10000n;
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee + ethers.parseEther('0.01'),
      });

      // Mint USDC to mock pool
      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 2n); // Enough for multiple operations

      // Execute first operation
      await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        '0x1234',
        ethers.Wallet.createRandom().address,
        ethers.parseUnits('990', 6),
        ethers.parseUnits('10', 6)
      );

      // Check statistics after first operation
      const stats1 = await stargateCompass.getFeeStatistics();
      expect(stats1.totalOperations).to.equal(1);
      expect(stats1.averageFee).to.equal(testFee);
      expect(stats1.minFee).to.equal(testFee);
      expect(stats1.maxFee).to.equal(testFee);
      expect(stats1.cumulativeFeesSpent).to.equal(testFee);

      // Execute second operation with different fee
      const testFee2 = ethers.parseEther('0.02');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(testFee2, 0);

      // Fund for second operation
      const bufferedFee2 = testFee2 + (testFee2 * 200n) / 10000n;
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: bufferedFee2,
      });

      await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        '0x5678',
        ethers.Wallet.createRandom().address,
        ethers.parseUnits('990', 6),
        ethers.parseUnits('10', 6)
      );

      // Check statistics after second operation
      const stats2 = await stargateCompass.getFeeStatistics();
      expect(stats2.totalOperations).to.equal(2);
      expect(stats2.averageFee).to.equal((testFee + testFee2) / 2n);
      expect(stats2.minFee).to.equal(testFee);
      expect(stats2.maxFee).to.equal(testFee2);
      expect(stats2.cumulativeFeesSpent).to.equal(testFee + testFee2);
    });
  });

  describe('Fee Efficiency Monitoring', function () {
    it('Should emit efficiency alerts for high fees', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      // Execute multiple operations to build history
      const baseFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(baseFee, 0);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n); // Enough for many operations

      // Fund contract generously
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('1'),
      });

      // Execute 10 operations with base fee to establish history
      for (let i = 0; i < 10; i++) {
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      // Now execute operation with significantly higher fee (should trigger alert)
      const highFee = ethers.parseEther('0.025'); // 2.5x higher than base fee
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(highFee, 0);

      const tx = await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        '0x5678',
        ethers.Wallet.createRandom().address,
        ethers.parseUnits('990', 6),
        ethers.parseUnits('10', 6)
      );

      // Should emit FeeEfficiencyAlert for high fee
      await expect(tx)
        .to.emit(stargateCompass, 'FeeEfficiencyAlert')
        .withArgs(
          baseFee,
          highFee,
          15000,
          'High fee alert - consider optimization'
        ); // 150% increase
    });

    it('Should emit efficiency alerts for low fees', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      // Execute multiple operations to build history with high fees
      const highFee = ethers.parseEther('0.02');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(highFee, 0);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n);

      // Fund contract generously
      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('2'),
      });

      // Execute 10 operations with high fee to establish history
      for (let i = 0; i < 10; i++) {
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      // Now execute operation with significantly lower fee (should trigger positive alert)
      const lowFee = ethers.parseEther('0.008'); // 60% lower than high fee
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(lowFee, 0);

      const tx = await stargateCompass.executeRemoteDegenSwap(
        loanAmount,
        '0x5678',
        ethers.Wallet.createRandom().address,
        ethers.parseUnits('990', 6),
        ethers.parseUnits('10', 6)
      );

      // Should emit FeeEfficiencyAlert for low fee (good efficiency)
      await expect(tx)
        .to.emit(stargateCompass, 'FeeEfficiencyAlert')
        .withArgs(highFee, lowFee, 6000, 'Low fee alert - good efficiency'); // 60% decrease
    });
  });

  describe('Fee Optimization Recommendations', function () {
    it('Should provide trending higher recommendation', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('2'),
      });

      // Execute operations with increasing fees to simulate trending higher
      const fees = [
        ethers.parseEther('0.01'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.015'),
        ethers.parseEther('0.015'),
        ethers.parseEther('0.02'),
        ethers.parseEther('0.02'),
        ethers.parseEther('0.025'),
      ];

      for (let i = 0; i < fees.length; i++) {
        await mockStargateRouter.setQuoteLayerZeroFeeResponse(fees[i], 0);
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      const [recommendation, averageFee, totalOperations] =
        await stargateCompass.getFeeOptimizationRecommendation();
      expect(totalOperations).to.equal(10);
      expect(recommendation).to.include('trending higher');
    });

    it('Should provide trending lower recommendation', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('2'),
      });

      // Execute operations with decreasing fees to simulate trending lower
      const fees = [
        ethers.parseEther('0.025'),
        ethers.parseEther('0.02'),
        ethers.parseEther('0.02'),
        ethers.parseEther('0.015'),
        ethers.parseEther('0.015'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.01'),
        ethers.parseEther('0.008'),
        ethers.parseEther('0.008'),
        ethers.parseEther('0.005'),
      ];

      for (let i = 0; i < fees.length; i++) {
        await mockStargateRouter.setQuoteLayerZeroFeeResponse(fees[i], 0);
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      const [recommendation, averageFee, totalOperations] =
        await stargateCompass.getFeeOptimizationRecommendation();
      expect(totalOperations).to.equal(10);
      expect(recommendation).to.include('trending lower');
    });

    it('Should provide stable fees recommendation', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('1'),
      });

      // Execute operations with stable fees
      const stableFee = ethers.parseEther('0.01');
      await mockStargateRouter.setQuoteLayerZeroFeeResponse(stableFee, 0);

      for (let i = 0; i < 10; i++) {
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      const [recommendation, averageFee, totalOperations] =
        await stargateCompass.getFeeOptimizationRecommendation();
      expect(totalOperations).to.equal(10);
      expect(recommendation).to.include('stable');
    });
  });

  describe('Recent Fees Tracking', function () {
    it('Should track recent fees correctly', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 5n);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('1'),
      });

      // Execute 3 operations with different fees
      const fees = [
        ethers.parseEther('0.01'),
        ethers.parseEther('0.015'),
        ethers.parseEther('0.02'),
      ];

      for (let i = 0; i < fees.length; i++) {
        await mockStargateRouter.setQuoteLayerZeroFeeResponse(fees[i], 0);
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      const [recentFees, count] = await stargateCompass.getRecentFees();
      expect(count).to.equal(3);
      expect(recentFees.length).to.equal(3);
      expect(recentFees[0]).to.equal(fees[0]);
      expect(recentFees[1]).to.equal(fees[1]);
      expect(recentFees[2]).to.equal(fees[2]);
    });

    it('Should handle circular buffer correctly when exceeding history size', async function () {
      const { stargateCompass, mockStargateRouter, mockPool, mockUSDC, owner } =
        await loadFixture(deployStargateCompassFixture);

      const loanAmount = ethers.parseUnits('1000', 6);
      await mockUSDC.mint(mockPool.target, loanAmount * 15n);

      await owner.sendTransaction({
        to: stargateCompass.target,
        value: ethers.parseEther('2'),
      });

      // Execute 12 operations (more than FEE_HISTORY_SIZE of 10)
      const baseFee = ethers.parseEther('0.01');
      for (let i = 0; i < 12; i++) {
        const fee = baseFee + ethers.parseEther((i * 0.001).toString());
        await mockStargateRouter.setQuoteLayerZeroFeeResponse(fee, 0);
        await stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          ethers.Wallet.createRandom().address,
          ethers.parseUnits('990', 6),
          ethers.parseUnits('10', 6)
        );
      }

      const [recentFees, count] = await stargateCompass.getRecentFees();
      expect(count).to.equal(10); // Should be limited to FEE_HISTORY_SIZE
      expect(recentFees.length).to.equal(10);

      // Should contain the last 10 fees (operations 2-11, 0-indexed)
      for (let i = 0; i < 10; i++) {
        const expectedFee =
          baseFee + ethers.parseEther(((i + 2) * 0.001).toString());
        expect(recentFees[i]).to.equal(expectedFee);
      }
    });
  });
});
