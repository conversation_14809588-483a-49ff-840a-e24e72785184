// Script to deploy StargateCompassV1 to Base mainnet
async function main() {
  console.log("🚀 Deploying StargateCompassV1 to Base mainnet...");
  
  // Production addresses for Base Mainnet
  const AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
  const STARGATE_ROUTER = "******************************************";
  
  console.log(`Using Aave Provider: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
  console.log(`Using Stargate Router: ${STARGATE_ROUTER}`);
  
  // Get the contract factory
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  
  // Deploy the contract
  console.log("Deploying contract...");
  const stargateCompass = await StargateCompassV1.deploy(
    AAVE_POOL_ADDRESSES_PROVIDER,
    STARGATE_ROUTER
  );
  
  // Wait for deployment to complete
  await stargateCompass.waitForDeployment();
  
  // Get the deployed contract address
  const deployedAddress = await stargateCompass.getAddress();
  console.log(`✅ StargateCompassV1 deployed to: ${deployedAddress}`);
  
  console.log("Deployment complete!");
  
  // Return the deployed contract for testing
  return { stargateCompass };
}

// Execute the deployment
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Deployment failed:", error);
    process.exit(1);
  });
