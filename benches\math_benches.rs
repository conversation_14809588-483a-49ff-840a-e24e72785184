use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rust_decimal_macros::dec;

fn sqrt_benchmark(c: &mut Criterion) {
    let analyzer = FractalAnalyzer::new();
    c.bench_function("sqrt", |b| {
        b.iter(|| analyzer.sqrt(black_box(dec!(12345.6789))))
    });
}

fn natural_log_benchmark(c: &mut Criterion) {
    let analyzer = FractalAnalyzer::new();
    c.bench_function("natural_log", |b| {
        b.iter(|| analyzer.natural_log(black_box(dec!(1.2345))))
    });
}

criterion_group!(benches, sqrt_benchmark, natural_log_benchmark);
criterion_main!(benches);
