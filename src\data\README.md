# Data Module

This module is responsible for ingesting, processing, and analyzing market data. It provides the real-time data feeds and advanced mathematical analysis that power the bot's decision-making process.

## Table of Contents

- [Core Components](#core-components)
- [Data Flow](#data-flow)

## Core Components

-   **`block_ingestor.rs`**: Ingests new blocks from the blockchain and extracts relevant data, such as transactions and gas prices.
-   **`chain_monitor.rs`**: Monitors the blockchain for new blocks and passes them to the `BlockIngestor`.
-   **`fractal_analyzer.rs`**: Implements the Chronos Sieve, which analyzes time-series data to identify market cycles and temporal patterns.
-   **`price_oracle.rs`**: Provides on-demand price data for assets.

## Data Flow

1.  The `ChainMonitor` detects a new block.
2.  The new block is passed to the `BlockIngestor` for processing.
3.  The `FractalAnalyzer` consumes the processed data to perform time-series analysis.
4.  The `PriceOracle` provides up-to-date price information to other modules.
5.  The `StrategyManager` (in `src/strategies/`) uses the data from this module to make trading decisions.