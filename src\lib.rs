// Code quality enforcement - no suppressed warnings allowed

pub mod alerting;
pub mod cli;
pub mod cli_handlers;
pub mod config;
pub mod constants;
pub mod contracts;
pub mod error;
pub mod logging;
// pub mod mev; // MEV Protection and Detection - temporarily disabled
// pub mod multicall; // temporarily disabled
pub mod network;
pub mod prelude;

// Test modules are in the tests/ directory
// pub mod control; // NOMADIC HUNTER: Control systems - temporarily disabled
pub mod data;
pub mod execution;
pub mod math;
pub use math::decimal_ext::DecimalExt;
// pub mod metrics;
// AUDIT-FIX: Add performance monitoring for Task 5.2
pub mod performance_monitor {
    pub use crate::metrics::performance_monitor::*;
} // temporarily disabled
pub mod risk;
pub mod shared_types;
pub mod strategies;
pub mod token_registry;
pub mod tui;
pub mod inspector;
pub mod operational_modes;
