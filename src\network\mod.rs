// MISSION: Network Layer - Resilient RPC and Network Management
// WHY: Provide bulletproof network connectivity with intelligent failover
// HOW: Dynamic RPC health monitoring, latency-aware routing, and graceful degradation

pub mod rpc_manager;
pub mod sequencer_monitor;
// AUDIT-FIX: Add censorship detection module for Task 4.2
pub mod censorship_detector;

pub use rpc_manager::{RpcManager, RpcHealth, RpcEndpoint};