// Unit Tests for Geometric Analysis
// Tests geometric scoring, convex hull, and sacred geometry calculations

use basilisk_bot::math::geometry::{calculate_geometric_score, Point, convex_hull};
use basilisk_bot::shared_types::Pool;
use ethers::types::Address;
use proptest::prelude::*;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::str::FromStr;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_empty_pools_geometric_score() {
        let pools = vec![];
        let central_assets = vec![];
        let score = calculate_geometric_score(&pools, &central_assets);
        
        // Empty pools should return zero scores
        assert_eq!(score.convexity_ratio, Decimal::ZERO);
        assert_eq!(score.liquidity_centroid_bias, Decimal::ZERO);
    }

    #[test]
    fn test_single_pool_geometric_score() {
        let pool = Pool {
            address: Address::from_str("******************************************").unwrap(),
            token0: Address::from_str("******************************************").unwrap(),
            token1: Address::from_str("******************************************").unwrap(),
            liquidity: Decimal::from(1000),
            protocol: "test".to_string(),
        };
        
        let pools = vec![pool];
        let central_assets = vec![];
        let score = calculate_geometric_score(&pools, &central_assets);
        
        // Single pool should have specific geometric properties
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
    }

    #[test]
    fn test_point_distance_properties() {
        let test_cases = vec![
            (Point::new(0.0, 0.0), Point::new(3.0, 4.0), 5.0),
            (Point::new(1.0, 1.0), Point::new(1.0, 1.0), 0.0),
            (Point::new(-1.0, -1.0), Point::new(1.0, 1.0), 2.828427124746190),
        ];

        for (p1, p2, expected_distance) in test_cases {
            let distance = p1.distance(&p2);
            assert!(
                (distance - expected_distance).abs() < 1e-10,
                "Distance between {:?} and {:?} should be {}, got {}",
                p1, p2, expected_distance, distance
            );
            
            // Test symmetry
            assert_eq!(distance, p2.distance(&p1));
        }
    }

    #[test]
    fn test_convex_hull_basic() {
        // Test with a simple square
        let points = vec![
            Point::new(0.0, 0.0),
            Point::new(1.0, 0.0),
            Point::new(1.0, 1.0),
            Point::new(0.0, 1.0),
            Point::new(0.5, 0.5), // Interior point
        ];
        
        let hull = convex_hull(&points);
        
        // Hull should have 4 points (the square corners)
        assert_eq!(hull.len(), 4);
        
        // Interior point should not be in hull
        assert!(!hull.iter().any(|p| (p.x - 0.5).abs() < 1e-10 && (p.y - 0.5).abs() < 1e-10));
    }

    #[test]
    fn test_convex_hull_collinear() {
        // Test with collinear points
        let points = vec![
            Point::new(0.0, 0.0),
            Point::new(1.0, 0.0),
            Point::new(2.0, 0.0),
            Point::new(3.0, 0.0),
        ];
        
        let hull = convex_hull(&points);
        
        // Hull should have 2 points (endpoints)
        assert_eq!(hull.len(), 2);
    }

    #[test]
    fn test_geometric_score_bounds() {
        // Create test pools with known properties
        let pools = vec![
            Pool {
                address: Address::random(),
                token0: Address::random(),
                token1: Address::random(),
                liquidity: dec!(1000),
                protocol: "uniswap".to_string(),
            },
            Pool {
                address: Address::random(),
                token0: Address::random(),
                token1: Address::random(),
                liquidity: dec!(2000),
                protocol: "sushiswap".to_string(),
            },
        ];
        
        let central_assets = vec!["WETH".to_string(), "USDC".to_string()];
        let score = calculate_geometric_score(&pools, &central_assets);
        
        // Scores should be bounded between 0 and 1
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.convexity_ratio <= Decimal::ONE);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias <= Decimal::ONE);
    }
}

// Property-based tests
proptest! {
    #[test]
    fn test_geometric_score_properties(
        pool_count in 1..10usize,
        liquidity_values in prop::collection::vec(1.0..1000000.0_f64, 1..10)
    ) {
        let pools: Vec<Pool> = (0..pool_count).zip(liquidity_values.iter()).map(|(i, &liquidity)| {
            Pool {
                address: Address::random(),
                token0: Address::random(),
                token1: Address::random(),
                liquidity: Decimal::from_f64(liquidity).unwrap_or_default(),
                protocol: format!("protocol_{}", i),
            }
        }).collect();
        
        let central_assets = vec!["WETH".to_string(), "USDC".to_string()];
        let score = calculate_geometric_score(&pools, &central_assets);
        
        // Geometric score components should be between 0 and 1
        prop_assert!(score.convexity_ratio >= Decimal::ZERO);
        prop_assert!(score.convexity_ratio <= Decimal::ONE);
        prop_assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        prop_assert!(score.liquidity_centroid_bias <= Decimal::ONE);
    }

    #[test]
    fn test_point_distance_properties(
        x1 in -1000.0..1000.0_f64,
        y1 in -1000.0..1000.0_f64,
        x2 in -1000.0..1000.0_f64,
        y2 in -1000.0..1000.0_f64
    ) {
        let p1 = Point::new(x1, y1);
        let p2 = Point::new(x2, y2);
        
        let distance = p1.distance(&p2);
        
        // Distance should be non-negative
        prop_assert!(distance >= 0.0);
        
        // Distance to self should be zero
        prop_assert_eq!(p1.distance(&p1), 0.0);
        
        // Distance should be symmetric
        prop_assert!((distance - p2.distance(&p1)).abs() < 1e-10);
    }
}