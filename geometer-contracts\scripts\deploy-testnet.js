const { ethers } = require('hardhat');

async function main() {
  console.log('🚀 Starting StargateCompassV1 Testnet Deployment...');

  const [deployer] = await ethers.getSigners();
  console.log(`Deploying with account: ${deployer.address}`);
  console.log(
    `Account balance: ${ethers.formatEther(
      await ethers.provider.getBalance(deployer.address)
    )} ETH`
  );

  // Base Sepolia addresses
  const BASE_SEPOLIA_ADDRESSES = {
    AAVE_POOL_ADDRESSES_PROVIDER: '******************************************', // Aave V3 on Base Sepolia
    STARGATE_ROUTER: '******************************************', // Placeholder - need real address
  };

  console.log('📋 Deployment Configuration:');
  console.log(`  Network: ${hre.network.name}`);
  console.log(
    `  Aave Provider: ${BASE_SEPOLIA_ADDRESSES.AAVE_POOL_ADDRESSES_PROVIDER}`
  );
  console.log(`  Stargate Router: ${BASE_SEPOLIA_ADDRESSES.STARGATE_ROUTER}`);

  // Deploy StargateCompassV1
  console.log('\n📦 Deploying StargateCompassV1...');
  const StargateCompassV1 = await ethers.getContractFactory(
    'StargateCompassV1'
  );

  const stargateCompass = await StargateCompassV1.deploy(
    BASE_SEPOLIA_ADDRESSES.AAVE_POOL_ADDRESSES_PROVIDER,
    BASE_SEPOLIA_ADDRESSES.STARGATE_ROUTER
  );

  await stargateCompass.waitForDeployment();
  const contractAddress = await stargateCompass.getAddress();

  console.log('✅ StargateCompassV1 deployed successfully!');
  console.log(`📍 Contract Address: ${contractAddress}`);

  // Verify deployment
  console.log('\n🔍 Verifying deployment...');
  const owner = await stargateCompass.owner();
  const aaveProvider = await stargateCompass.aaveProvider();
  const stargateRouter = await stargateCompass.stargateRouter();
  const maxSlippage = await stargateCompass.maxSlippageBps();
  const emergencyPaused = await stargateCompass.emergencyPaused();

  console.log('📊 Contract State:');
  console.log(`  Owner: ${owner}`);
  console.log(`  Aave Provider: ${aaveProvider}`);
  console.log(`  Stargate Router: ${stargateRouter}`);
  console.log(`  Max Slippage: ${maxSlippage} BPS (${maxSlippage / 100}%)`);
  console.log(`  Emergency Paused: ${emergencyPaused}`);

  // Fund contract with initial ETH
  console.log('\n💰 Funding contract with initial ETH...');
  const initialETH = ethers.parseEther('0.1'); // 0.1 ETH for testing
  const fundTx = await deployer.sendTransaction({
    to: contractAddress,
    value: initialETH,
  });
  await fundTx.wait();

  const contractBalance = await ethers.provider.getBalance(contractAddress);
  console.log(
    `✅ Contract funded with ${ethers.formatEther(contractBalance)} ETH`
  );

  // Security validation
  console.log('\n🛡️  Security Validation:');
  console.log(
    `  MAX_SLIPPAGE_BPS: ${await stargateCompass.MAX_SLIPPAGE_BPS()} (10%)`
  );
  console.log(
    `  MIN_PROFIT_BPS: ${await stargateCompass.MIN_PROFIT_BPS()} (0.5%)`
  );
  console.log(
    `  MAX_NATIVE_FEE: ${ethers.formatEther(
      await stargateCompass.MAX_NATIVE_FEE()
    )} ETH`
  );
  console.log(
    `  MIN_ETH_BALANCE: ${ethers.formatEther(
      await stargateCompass.MIN_ETH_BALANCE()
    )} ETH`
  );

  // Save deployment info
  const deploymentInfo = {
    network: hre.network.name,
    contractAddress: contractAddress,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    aaveProvider: aaveProvider,
    stargateRouter: stargateRouter,
    initialETHBalance: ethers.formatEther(contractBalance),
    securityConfig: {
      maxSlippageBps: maxSlippage.toString(),
      maxSlippageBpsLimit: (
        await stargateCompass.MAX_SLIPPAGE_BPS()
      ).toString(),
      minProfitBps: (await stargateCompass.MIN_PROFIT_BPS()).toString(),
      maxNativeFee: ethers.formatEther(await stargateCompass.MAX_NATIVE_FEE()),
      minEthBalance: ethers.formatEther(
        await stargateCompass.MIN_ETH_BALANCE()
      ),
    },
  };

  console.log('\n📄 Deployment Summary:');
  console.log(JSON.stringify(deploymentInfo, null, 2));

  // Verification command
  console.log('\n🔗 Verification Command:');
  console.log(
    `npx hardhat verify --network ${hre.network.name} ${contractAddress} "${BASE_SEPOLIA_ADDRESSES.AAVE_POOL_ADDRESSES_PROVIDER}" "${BASE_SEPOLIA_ADDRESSES.STARGATE_ROUTER}"`
  );

  console.log('\n🎉 Deployment Complete!');
  console.log('Next steps:');
  console.log('1. Verify the contract on Basescan');
  console.log('2. Test basic functionality with small amounts');
  console.log('3. Set up monitoring and alerting');
  console.log('4. Prepare for mainnet deployment');

  return {
    contractAddress,
    deploymentInfo,
  };
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  });
