#!/bin/bash

# Test Live Data Integration Script
# This script tests the complete NATS -> data_ingestor -> TUI pipeline

set -e

echo "🚀 Testing Live Data Integration Pipeline"
echo "========================================"

# Check if NATS server is running
echo "1. Checking NATS server status..."
if ! curl -s http://localhost:8222/healthz > /dev/null; then
    echo "❌ NATS server not running. Starting infrastructure..."
    docker-compose -f docker-compose.infrastructure.yml up -d nats
    echo "⏳ Waiting for NATS to be ready..."
    sleep 5
fi

# Verify NATS is accessible
if curl -s http://localhost:8222/healthz > /dev/null; then
    echo "✅ NATS server is running and healthy"
else
    echo "❌ NATS server failed to start"
    exit 1
fi

# Install nats CLI if not available (for testing)
if ! command -v nats &> /dev/null; then
    echo "📦 Installing NATS CLI for testing..."
    if command -v brew &> /dev/null; then
        brew install nats-io/nats-tools/nats
    elif command -v go &> /dev/null; then
        go install github.com/nats-io/natscli/nats@latest
    else
        echo "⚠️  Please install NATS CLI manually: https://github.com/nats-io/natscli"
        echo "   Or use docker: docker run --rm -it --network host natsio/nats-box:latest"
    fi
fi

# Test message publishing
echo ""
echo "2. Testing NATS message publishing..."

# Test ARE component data
echo "📡 Publishing test ARE component data..."

# Temporal Harmonics (Chronos Sieve)
nats pub are.temporal_harmonics '{
    "market_rhythm_stability": 0.85,
    "dominant_cycles_minutes": [[12.5, 0.8], [5.2, 0.6]],
    "timestamp": '$(date +%s)'
}' || echo "⚠️  Failed to publish temporal harmonics"

# Network Seismology
nats pub are.network_seismology '{
    "tti_stats": {
        "avg_tti_ms": 145.5,
        "std_dev_ms": 23.2
    },
    "coherence": {
        "coherence_score": 0.92
    },
    "timestamp": '$(date +%s)'
}' || echo "⚠️  Failed to publish network seismology"

# Market data
echo "💹 Publishing test market data..."
nats pub market.trades '{
    "pair": "ETH/USDC",
    "price": 2456.78,
    "volume": 1.25,
    "timestamp": '$(date +%s)'
}' || echo "⚠️  Failed to publish market data"

# Network block data
echo "🔗 Publishing test network data..."
nats pub network.blocks '{
    "number": 19234567,
    "timestamp": '$(date +%s)',
    "gasUsed": 15234567
}' || echo "⚠️  Failed to publish network data"

# Gas price data
echo "⛽ Publishing test gas price data..."
nats pub gas.prices '{
    "price_gwei": 25.5,
    "timestamp": '$(date +%s)'
}' || echo "⚠️  Failed to publish gas price data"

echo ""
echo "3. Verification steps:"
echo "   ✅ Start the data_ingestor: cargo run --bin data_ingestor"
echo "   ✅ Start the TUI: cargo run --bin tui_harness"
echo "   ✅ Check Operations tab for live activity log entries"
echo "   ✅ Check Dashboard tab for real data indicators (🟢 ● LIVE)"
echo "   ✅ Check Systems tab for Data Ingestor status (should be RUNNING)"

echo ""
echo "4. Expected results in TUI:"
echo "   📊 Dashboard: Real data indicators should show 🟢 ● LIVE"
echo "   🎛️  Operations: Activity log should show MARKET_DATA, NETWORK, GAS_TRACKER entries"
echo "   🔧 Systems: Data Ingestor component should show RUNNING status"
echo "   📈 Real-time updates: Sparklines and charts should update with live data"

echo ""
echo "🎯 Live Data Integration Test Complete!"
echo "   Run the verification steps above to confirm the pipeline is working."