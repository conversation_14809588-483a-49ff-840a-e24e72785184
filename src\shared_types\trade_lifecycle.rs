// MISSION: Trade Lifecycle Types - Living Codex Educational System
// WHY: Enable detailed tracking and educational context for trade execution
// HOW: Comprehensive status tracking with narrative explanations

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use ethers::types::H256;

/// A single event in the lifecycle of a trade execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeLifecycleEvent {
    /// Unique identifier for the trade this event belongs to
    pub trade_id: String,
    
    /// The type of opportunity being executed
    pub opportunity_type: String,
    
    /// Human-readable description of the trading path
    pub path_description: String,
    
    /// Current status of the trade
    pub status: TradeStatus,
    
    /// Educational context explaining what's happening
    pub educational_context: String,
    
    /// Timestamp when this event occurred
    pub timestamp: u64,
    
    /// Chain ID where this trade is executing
    pub chain_id: u64,
}

impl TradeLifecycleEvent {
    /// Create a new trade lifecycle event
    pub fn new(
        trade_id: String,
        opportunity_type: String,
        path_description: String,
        status: TradeStatus,
        educational_context: String,
        chain_id: u64,
    ) -> Self {
        Self {
            trade_id,
            opportunity_type,
            path_description,
            status,
            educational_context,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            chain_id,
        }
    }
    
    /// Get a formatted timestamp for display
    pub fn formatted_timestamp(&self) -> String {
        let datetime = chrono::DateTime::from_timestamp(self.timestamp as i64, 0)
            .unwrap_or_else(|| chrono::Utc::now());
        datetime.format("%H:%M:%S").to_string()
    }
}

/// Detailed status of a trade execution with educational context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeStatus {
    /// Trade has been approved by the ARE and is ready for execution
    Approved,
    
    /// Waiting for optimal network conditions before broadcasting
    AwaitingHarmonicWindow,
    
    /// Constructing the raw transaction payload
    BuildingTransaction,
    
    /// Signing the transaction with the bot's private key
    SigningTransaction,
    
    /// Broadcasting transaction to a specific relay or mempool
    BroadcastingToRelay {
        relay: String,
        tx_hash: H256,
    },
    
    /// Transaction is pending in the mempool
    PendingInMempool {
        tx_hash: H256,
    },
    
    /// Transaction has been confirmed on-chain
    ConfirmedOnChain {
        block_number: u64,
        gas_used: u64,
        tx_hash: H256,
    },
    
    /// Trade completed successfully
    Success {
        net_profit: Decimal,
        gas_cost: Decimal,
        tx_hash: H256,
    },
    
    /// Trade failed for some reason
    Failed {
        reason: String,
        tx_hash: Option<H256>,
    },
    
    /// Transaction was reverted on-chain
    Reverted {
        reason: String,
        gas_used: u64,
        tx_hash: H256,
    },
}

impl TradeStatus {
    /// Check if this status represents a terminal state (trade is finished)
    pub fn is_terminal(&self) -> bool {
        matches!(
            self,
            TradeStatus::Success { .. } | TradeStatus::Failed { .. } | TradeStatus::Reverted { .. }
        )
    }
    
    /// Get a status indicator icon and text for display
    pub fn status_indicator(&self) -> (&'static str, &'static str) {
        match self {
            TradeStatus::Approved => ("✅", "Approved"),
            TradeStatus::AwaitingHarmonicWindow => ("⏳", "Awaiting Window"),
            TradeStatus::BuildingTransaction => ("🔧", "Building Tx"),
            TradeStatus::SigningTransaction => ("✍️", "Signing"),
            TradeStatus::BroadcastingToRelay { .. } => ("📡", "Broadcasting"),
            TradeStatus::PendingInMempool { .. } => ("⏰", "Pending"),
            TradeStatus::ConfirmedOnChain { .. } => ("⛓️", "Confirmed"),
            TradeStatus::Success { .. } => ("🎉", "Success"),
            TradeStatus::Failed { .. } => ("❌", "Failed"),
            TradeStatus::Reverted { .. } => ("🔄", "Reverted"),
        }
    }
    
    /// Get the transaction hash if available
    pub fn tx_hash(&self) -> Option<H256> {
        match self {
            TradeStatus::BroadcastingToRelay { tx_hash, .. } => Some(*tx_hash),
            TradeStatus::PendingInMempool { tx_hash } => Some(*tx_hash),
            TradeStatus::ConfirmedOnChain { tx_hash, .. } => Some(*tx_hash),
            TradeStatus::Success { tx_hash, .. } => Some(*tx_hash),
            TradeStatus::Failed { tx_hash, .. } => *tx_hash,
            TradeStatus::Reverted { tx_hash, .. } => Some(*tx_hash),
            _ => None,
        }
    }
}

/// Builder for creating educational trade lifecycle events
pub struct TradeLifecycleEventBuilder {
    trade_id: String,
    opportunity_type: String,
    path_description: String,
    chain_id: u64,
}

impl TradeLifecycleEventBuilder {
    pub fn new(
        trade_id: String,
        opportunity_type: String,
        path_description: String,
        chain_id: u64,
    ) -> Self {
        Self {
            trade_id,
            opportunity_type,
            path_description,
            chain_id,
        }
    }
    
    /// Create an "Approved" event
    pub fn approved(&self) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::Approved,
            "The opportunity has been approved by the Aetheric Resonance Engine. All three pillars (Chronos Sieve, Mandorla Gauge, and Network Seismology) have given their blessing. The trade is now queued for execution.".to_string(),
            self.chain_id,
        )
    }
    
    /// Create an "AwaitingHarmonicWindow" event
    pub fn awaiting_harmonic_window(&self) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::AwaitingHarmonicWindow,
            "The Harmonic Timing Oracle is monitoring network conditions. We wait for a moment of low network jitter and optimal gas prices before broadcasting. This patience often means the difference between profit and loss.".to_string(),
            self.chain_id,
        )
    }
    
    /// Create a "BuildingTransaction" event
    pub fn building_transaction(&self) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::BuildingTransaction,
            "Network conditions are optimal. Now constructing the raw transaction payload. This involves ABI-encoding the function call and its parameters, calculating gas limits, and setting the optimal gas price.".to_string(),
            self.chain_id,
        )
    }
    
    /// Create a "SigningTransaction" event
    pub fn signing_transaction(&self) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::SigningTransaction,
            "The transaction payload is complete. Now signing it with the bot's private key using ECDSA cryptography. This creates a digital signature that proves we authorized this transaction.".to_string(),
            self.chain_id,
        )
    }
    
    /// Create a "BroadcastingToRelay" event
    pub fn broadcasting_to_relay(&self, relay: String, tx_hash: H256) -> TradeLifecycleEvent {
        let context = if relay.contains("flashbots") || relay.contains("mev") {
            format!("Transaction signed. Sending to a private MEV relay ({}) to protect against front-running. This shields our transaction from public view until it's included in a block. Tx Hash: {:#x}", relay, tx_hash)
        } else {
            format!("Transaction signed. Broadcasting to the public mempool via {}. This is visible to all network participants. Tx Hash: {:#x}", relay, tx_hash)
        };
        
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::BroadcastingToRelay { relay, tx_hash },
            context,
            self.chain_id,
        )
    }
    
    /// Create a "PendingInMempool" event
    pub fn pending_in_mempool(&self, tx_hash: H256) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::PendingInMempool { tx_hash },
            format!("Transaction is now in the mempool waiting to be included in a block. Miners/validators will see our gas price and decide whether to include it. Tx Hash: {:#x}", tx_hash),
            self.chain_id,
        )
    }
    
    /// Create a "ConfirmedOnChain" event
    pub fn confirmed_on_chain(&self, block_number: u64, gas_used: u64, tx_hash: H256) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::ConfirmedOnChain { block_number, gas_used, tx_hash },
            format!("Success! The transaction was included in block #{}. It consumed {} gas units. The state of the blockchain has been permanently updated. Tx Hash: {:#x}", block_number, gas_used, tx_hash),
            self.chain_id,
        )
    }
    
    /// Create a "Success" event
    pub fn success(&self, net_profit: Decimal, gas_cost: Decimal, tx_hash: H256) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::Success { net_profit, gas_cost, tx_hash },
            format!("🎉 The hunt was successful! Net profit: ${:.2} (after ${:.2} in gas costs). The geometric patterns aligned perfectly, and the Basilisk has fed. Tx Hash: {:#x}", net_profit, gas_cost, tx_hash),
            self.chain_id,
        )
    }
    
    /// Create a "Failed" event
    pub fn failed(&self, reason: String, tx_hash: Option<H256>) -> TradeLifecycleEvent {
        let context = if let Some(hash) = tx_hash {
            format!("The trade failed: {}. This can happen due to network congestion, slippage, or changing market conditions. Learning from failure makes us stronger. Tx Hash: {:#x}", reason, hash)
        } else {
            format!("The trade failed before broadcasting: {}. This is often due to simulation detecting unfavorable conditions or network issues.", reason)
        };
        
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::Failed { reason, tx_hash },
            context,
            self.chain_id,
        )
    }
    
    /// Create a "Reverted" event
    pub fn reverted(&self, reason: String, gas_used: u64, tx_hash: H256) -> TradeLifecycleEvent {
        TradeLifecycleEvent::new(
            self.trade_id.clone(),
            self.opportunity_type.clone(),
            self.path_description.clone(),
            TradeStatus::Reverted { reason: reason.clone(), gas_used, tx_hash },
            format!("The transaction was included in a block but reverted during execution: {}. We still paid {} gas for the failed attempt. This teaches us about the harsh realities of on-chain execution. Tx Hash: {:#x}", reason, gas_used, tx_hash),
            self.chain_id,
        )
    }
}