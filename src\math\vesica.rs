// MISSION: Vesica Piscis <PERSON>le - The Sacred Geometry of Opportunity
// WHY: Implement the geometric principle that reveals true opportunity depth
// HOW: Mathematical functions based on the intersection of two circles (pools)

use rust_decimal::Decimal;
use tracing::debug;

/// The Vesica Piscis represents the intersection of two circles
/// In our context, it represents the intersection of two liquidity pools
/// This module provides the core geometric analysis for the Mandorla Gauge

/// Calculate the intersection value using the Vesica Piscis principle
/// This is the primary function of the Mandorla Gauge
/// UPGRADED FORMULA: Amount_To_Equalize = Pool_Reserves_X * (sqrt(Price_B / Price_A) - 1)
/// This is derived directly from the AMM's x*y=k invariant for precise depth calculation
pub fn calculate_mandorla_depth(
    pool_a_reserves_x: Decimal,
    pool_b_reserves_x: Decimal,
    price_deviation_percent: Decimal,
) -> Decimal {
    // Direct call to calculate_amount_to_equalize as per audit fix
    calculate_amount_to_equalize(
        pool_a_reserves_x,
        pool_b_reserves_x,
        price_deviation_percent,
    )
}

/// AUDIT-FIX: Calculate the exact amount needed to equalize prices between two pools
/// Formula: Amount_To_Equalize = Pool_Reserves_X * |sqrt(Price_Ratio) - 1|
/// This is the geometrically pure measure of opportunity depth
pub fn calculate_amount_to_equalize(
    pool_a_reserves_x: Decimal,
    pool_b_reserves_x: Decimal,
    price_deviation_percent: Decimal,
) -> Decimal {
    if pool_a_reserves_x <= Decimal::ZERO || pool_b_reserves_x <= Decimal::ZERO {
        return Decimal::ZERO;
    }

    // AUDIT-FIX: Handle both positive and negative deviations correctly
    // For positive deviation: Pool A is cheaper, use Price_B/Price_A ratio
    // For negative deviation: Pool B is cheaper, use Price_A/Price_B ratio

    let (price_ratio, cheaper_pool_reserves) = if price_deviation_percent > Decimal::ZERO {
        // Pool A is cheaper by price_deviation_percent
        // Price_B / Price_A = 1 + deviation
        let ratio = Decimal::ONE + price_deviation_percent;
        (ratio, pool_a_reserves_x)
    } else if price_deviation_percent < Decimal::ZERO {
        // Pool B is cheaper by |price_deviation_percent|
        // Price_A / Price_B = 1 + |deviation|
        let ratio = Decimal::ONE + price_deviation_percent.abs();
        (ratio, pool_b_reserves_x)
    } else {
        // No price deviation, no arbitrage opportunity
        return Decimal::ZERO;
    };

    // Calculate sqrt(price_ratio)
    let sqrt_price_ratio = optimized_sqrt(price_ratio);

    // STRATEGIC-FIX: Amount = Pool_Reserves * |sqrt(ratio) - 1|
    // AUDIT-FIX: Use absolute value to handle both positive and negative deviations correctly
    // This fixes the critical mathematical error that was zeroing out 50% of arbitrage opportunities
    let amount_to_equalize = cheaper_pool_reserves * (sqrt_price_ratio - Decimal::ONE).abs();

    amount_to_equalize
}

/// Optimized square root using Newton's method with adaptive convergence
/// This ensures the upgraded Mandorla Gauge is both fast and precise
/// AUDIT-FIX: Improved precision and convergence for sacred geometry calculations
pub fn optimized_sqrt(value: Decimal) -> Decimal {
    if value <= Decimal::ZERO {
        return Decimal::ZERO;
    }

    if value == Decimal::ONE {
        return Decimal::ONE;
    }

    // AUDIT-FIX: Better initial guess for faster convergence
    let mut x = if value > Decimal::ONE {
        value / Decimal::TWO
    } else {
        (value + Decimal::ONE) / Decimal::TWO
    };
    
    // AUDIT-FIX: More precise tolerance for mathematical accuracy
    let tolerance = Decimal::new(1, 15); // 0.000000000000001 - higher precision
    let max_iterations = 50; // More iterations for precision

    for _ in 0..max_iterations {
        let x_new = (x + value / x) / Decimal::TWO;
        let diff = (x - x_new).abs();
        if diff < tolerance {
            return x_new;
        }
        x = x_new;
    }

    x
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_mandorla_depth_calculation() {
        let depth = calculate_mandorla_depth(
            Decimal::from(50000),
            Decimal::from(30000),
            Decimal::new(2, 2), // 0.02
        );
        // Expected value for 50000 * (sqrt(1.02) - 1)
        // sqrt(1.02) is approximately 1.0099504938362077
        // 50000 * (1.0099504938362077 - 1) = 50000 * 0.0099504938362077 = 497.52469181038535
        assert!((depth - dec!(497.52469)).abs() < dec!(0.00001));
    }

    #[test]
    fn test_optimized_sqrt_perfect_squares() {
        // AUDIT-FIX: Use tolerance for floating point precision in Newton's method
        let sqrt_4 = optimized_sqrt(dec!(4));
        assert!((sqrt_4 - dec!(2)).abs() < dec!(0.000001), "sqrt(4) = {}, expected 2", sqrt_4);
        
        let sqrt_9 = optimized_sqrt(dec!(9));
        assert!((sqrt_9 - dec!(3)).abs() < dec!(0.000001), "sqrt(9) = {}, expected 3", sqrt_9);
        
        let sqrt_100 = optimized_sqrt(dec!(100));
        assert!((sqrt_100 - dec!(10)).abs() < dec!(0.000001), "sqrt(100) = {}, expected 10", sqrt_100);
    }

    #[test]
    fn test_optimized_sqrt_non_perfect_squares() {
        // AUDIT-FIX: Adjust tolerance for Newton's method precision
        let sqrt_2 = optimized_sqrt(dec!(2));
        assert!((sqrt_2 - dec!(1.41421356237)).abs() < dec!(0.00001), "sqrt(2) = {}", sqrt_2);

        let sqrt_3 = optimized_sqrt(dec!(3));
        assert!((sqrt_3 - dec!(1.73205080757)).abs() < dec!(0.00001), "sqrt(3) = {}", sqrt_3);

        let sqrt_5 = optimized_sqrt(dec!(5));
        assert!((sqrt_5 - dec!(2.2360679775)).abs() < dec!(0.00001), "sqrt(5) = {}", sqrt_5);
    }

    #[test]
    fn test_optimized_sqrt_small_values() {
        let sqrt_0_25 = optimized_sqrt(dec!(0.25));
        assert!((sqrt_0_25 - dec!(0.5)).abs() < dec!(0.0000000001));

        let sqrt_0_01 = optimized_sqrt(dec!(0.01));
        assert!((sqrt_0_01 - dec!(0.1)).abs() < dec!(0.0000000001));
    }

    #[test]
    fn test_optimized_sqrt_large_values() {
        let sqrt_1_000_000 = optimized_sqrt(dec!(1_000_000));
        assert!((sqrt_1_000_000 - dec!(1000)).abs() < dec!(0.0000000001));
    }

    #[test]
    fn test_optimized_sqrt_zero_and_one() {
        assert_eq!(optimized_sqrt(dec!(0)), dec!(0));
        assert_eq!(optimized_sqrt(dec!(1)), dec!(1));
    }

    #[test]
    fn test_amount_to_equalize_negative_deviation() {
        // AUDIT-FIX: Test that negative deviations work correctly
        let amount = calculate_amount_to_equalize(
            dec!(50000), // Pool A reserves
            dec!(30000), // Pool B reserves
            dec!(-0.02), // Pool B is 2% cheaper than Pool A
        );

        // For negative deviation, Pool B is cheaper
        // Price_A / Price_B = 1 + 0.02 = 1.02
        // sqrt(1.02) ≈ 1.0099504938
        // Amount = 30000 * |1.0099504938 - 1| = 30000 * 0.0099504938 ≈ 298.51
        assert!((amount - dec!(298.51)).abs() < dec!(1.0), "Expected ~298.51, got {}", amount);
        assert!(amount > Decimal::ZERO, "Amount should be positive for negative deviation");
    }

    #[test]
    fn test_amount_to_equalize_positive_deviation() {
        // Test that positive deviations still work correctly
        let amount = calculate_amount_to_equalize(
            dec!(50000), // Pool A reserves
            dec!(30000), // Pool B reserves
            dec!(0.02),  // Pool A is 2% cheaper than Pool B
        );

        // For positive deviation, Pool A is cheaper
        // Price_B / Price_A = 1 + 0.02 = 1.02
        // sqrt(1.02) ≈ 1.0099504938
        // Amount = 50000 * |1.0099504938 - 1| = 50000 * 0.0099504938 ≈ 497.52
        assert!((amount - dec!(497.52)).abs() < dec!(1.0), "Expected ~497.52, got {}", amount);
        assert!(amount > Decimal::ZERO, "Amount should be positive for positive deviation");
    }
}
