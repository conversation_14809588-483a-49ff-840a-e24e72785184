// Script to deploy StargateCompassV1 to Base mainnet with correct checksums
async function main() {
  console.log("🚀 Deploying StargateCompassV1 to Base mainnet...");
  
  // Production addresses for Base Mainnet (with correct checksums)
  const AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
  const STARGATE_ROUTER = "******************************************";
  
  // Convert to proper checksum format
  const aaveProvider = ethers.getAddress(AAVE_POOL_ADDRESSES_PROVIDER);
  const stargateRouter = ethers.getAddress(STARGATE_ROUTER);
  
  console.log(`Using Aave Provider: ${aaveProvider}`);
  console.log(`Using Stargate Router: ${stargateRouter}`);
  
  // Get the contract factory
  const StargateCompassV1 = await ethers.getContractFactory("StargateCompassV1");
  
  // Deploy the contract
  console.log("Deploying contract...");
  const stargateCompass = await StargateCompassV1.deploy(
    aaveProvider,
    stargateRouter
  );
  
  // Wait for deployment to complete
  await stargateCompass.waitForDeployment();
  
  // Get the deployed contract address
  const deployedAddress = await stargateCompass.getAddress();
  console.log(`✅ StargateCompassV1 deployed to: ${deployedAddress}`);
  
  // Save deployment info
  console.log("📋 Deployment Summary:");
  console.log(`Contract Address: ${deployedAddress}`);
  console.log(`Transaction Hash: ${stargateCompass.deploymentTransaction().hash}`);
  console.log(`Block Number: ${stargateCompass.deploymentTransaction().blockNumber}`);
  
  return { address: deployedAddress, contract: stargateCompass };
}

main()
  .then((result) => {
    console.log("🎉 Deployment successful!");
    console.log(`Update your config with: ${result.address}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
