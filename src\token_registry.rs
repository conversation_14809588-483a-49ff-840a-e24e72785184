// src/token_registry.rs

use ethers::types::Address;
use std::collections::HashMap;
use std::str::FromStr;

/// Token registry for mapping symbols to addresses
/// AUDIT-FIX: Replaces Address::zero() placeholders with real token addresses
pub struct TokenRegistry {
    address_map: HashMap<String, Address>,
}

impl TokenRegistry {
    pub fn new() -> Self {
        let mut address_map = HashMap::new();
        
        // Ethereum Mainnet tokens - AUDIT-FIX: Real token addresses instead of placeholders
        address_map.insert("WETH".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("USDC".to_string(), 
            Address::from_str("******************************************").unwrap()); // Real USDC address
        address_map.insert("USDT".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("DAI".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("WBTC".to_string(), 
            Address::from_str("******************************************").unwrap());
        
        // Base network tokens (if different)
        address_map.insert("BASE_USDC".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("BASE_WETH".to_string(), 
            Address::from_str("******************************************").unwrap());
        
        // Additional DeFi tokens
        address_map.insert("UNI".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("LINK".to_string(), 
            Address::from_str("******************************************").unwrap());
        address_map.insert("AAVE".to_string(), 
            Address::from_str("******************************************").unwrap());
        
        Self { address_map }
    }
    
    /// Get token address by symbol
    pub fn get_address(&self, symbol: &str) -> Option<Address> {
        self.address_map.get(symbol).copied()
    }
    
    /// Add or update a token address
    pub fn add_token(&mut self, symbol: String, address: Address) {
        self.address_map.insert(symbol, address);
    }
    
    /// Get all registered tokens
    pub fn get_all_tokens(&self) -> &HashMap<String, Address> {
        &self.address_map
    }
    
    /// Check if a token is registered
    pub fn has_token(&self, symbol: &str) -> bool {
        self.address_map.contains_key(symbol)
    }
}

impl Default for TokenRegistry {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_registry_basic_functionality() {
        let registry = TokenRegistry::new();
        
        // Test major tokens are registered
        assert!(registry.has_token("WETH"));
        assert!(registry.has_token("USDC"));
        assert!(registry.has_token("USDT"));
        assert!(registry.has_token("DAI"));
        assert!(registry.has_token("WBTC"));
        
        // Test addresses are not zero
        let weth_address = registry.get_address("WETH").unwrap();
        assert_ne!(weth_address, Address::zero());
        
        let usdc_address = registry.get_address("USDC").unwrap();
        assert_ne!(usdc_address, Address::zero());
    }
    
    #[test]
    fn test_token_registry_unknown_token() {
        let registry = TokenRegistry::new();
        
        assert!(!registry.has_token("UNKNOWN"));
        assert_eq!(registry.get_address("UNKNOWN"), None);
    }
    
    #[test]
    fn test_token_registry_add_token() {
        let mut registry = TokenRegistry::new();
        
        let test_address = Address::from_str("******************************************").unwrap();
        registry.add_token("TEST".to_string(), test_address);
        
        assert!(registry.has_token("TEST"));
        assert_eq!(registry.get_address("TEST"), Some(test_address));
    }
}