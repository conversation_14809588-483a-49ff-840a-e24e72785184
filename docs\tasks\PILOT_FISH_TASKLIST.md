### **`/docs/tasks/implement_pilot_fish.md`**

# **Task List: Forging the "Pilot Fish" Strategy**

**To:** Coder Agent
**From:** The Architect
**Subject:** Technical Checklist for Flash Loan Arbitrage

**Mission:** You will now execute the implementation of the **"Pilot Fish"** strategy. Follow this task list precisely. Each item represents a discrete, verifiable unit of work required to build this new emergent behavior.

### **[EPIC] 1: Upgrade the `MempoolScanner`'s Senses**

**Objective:** The scanner must be upgraded to identify and precisely quantify "whale" opportunities.

-   [ ] **Task 1.1: Enhance the Transaction Classifier.**
    -   **Action:** In your offline data labeling workflow, add a new classification label: **`Whale_Trade`**.
    -   **Action:** Run the training script (`/scripts/train_models.rs`) with the new labeled data to produce an updated `transaction_classifier.bin`.
    -   **Verification:** The `MempoolIngestor` service, when reloaded with the new model, correctly classifies a test transaction with a very high value as `Whale_Trade`.

-   [ ] **Task 1.2: Implement "Impact Analysis" Logic.**
    -   **Action:** In `strategies/scanners/mempool.rs`, add a new private `async fn analyze_whale_impact(...)`.
    -   **Action:** This function must be called when a `Whale_Trade` is detected. It must use the `Simulator` to fork the chain, simulate the whale's trade, and calculate both the potential profit from a back-run and the `capital_requirement_usd` for that back-run.
    -   **Verification:** A unit test confirms this function correctly calculates both profit and required capital for a known whale trade.

-   [ ] **Task 1.3: Create the Specialized Opportunity Packet.**
    -   **Action:** In `src/shared_types.rs`, add the new field to the `Opportunity` struct: `pub capital_requirement_usd: Option<f64>`.
    -   **Action:** The `MempoolScanner`, after a successful impact analysis, must construct an `Opportunity` with `source_scanner: "MempoolScanner_PilotFish"`, `requires_flash_liquidity: true`, and the calculated `capital_requirement_usd`.
    -   **Verification:** The `StrategyManager` correctly receives this enriched opportunity packet from the MPSC channel.

### **[EPIC] 2: Upgrade the `StrategyManager`'s Brain**

**Objective:** The brain must recognize and prioritize Pilot Fish opportunities.

-   [ ] **Task 2.1: Enhance the Scoring Algorithm.**
    -   **Action:** In `src/strategies/manager.rs`, add a new `match` arm to the `get_regime_multiplier` function (or equivalent logic).
    -   **Action:** This arm must match on `source_scanner: "MempoolScanner_PilotFish"` and apply a very high multiplier (e.g., `2.2` during `High_Volatility_Correction`, `1.9` otherwise).
    -   **Verification:** A unit test confirms that a Pilot Fish opportunity receives a higher final score than any other opportunity type with the same gross profit.

### **[EPIC] 3: Upgrade the Execution & On-Chain Contract**

**Objective:** Implement the final execution path for performing the flash loan arbitrage.

-   [ ] **Task 3.1: Implement the `Executor.sol` Flash Loan Function.**
    -   **Action:** In `Executor.sol`, add the new `executeFlashLoanArbitrage` function and its required `executeOperation` callback function, as detailed in the implementation guide. The function must interface with a lending protocol like Aave V3.
    -   **Verification:** A Foundry test successfully executes a full flash loan arbitrage on a forked testnet, borrowing from a mocked Aave pool, performing a swap, and repaying the loan.

-   [ ] **Task 3.2: Implement the Pilot Fish Execution Path in `ExecutionManager`.**
    -   **Action:** In `src/execution/manager.rs`, inside the main `match` statement on `OpportunityType::DexArbitrage`, add the `if opportunity.requires_flash_liquidity && opportunity.capital_requirement_usd.is_some()` check.
    -   **Action:** This block must call a new, dedicated dispatcher function, e.g., `self.dispatcher.dispatch_pilot_fish_trade(...)`.
    -   **Verification:** A test `Opportunity` with the correct flags is correctly routed to the new dispatcher function.

-   [ ] **Task 3.3: Implement the `Dispatcher` Function.**
    -   **Action:** In `src/execution/dispatcher.rs`, create the `async fn dispatch_pilot_fish_trade(...)`.
    -   **Action:** This function must correctly encode the transaction calldata for the `executeFlashLoanArbitrage` function in your `Executor.sol` contract, including the Aave pool address, the required capital, and the encoded swap path.
    -   **Action:** It signs and dispatches this single, complex transaction with the gas bribe calculated by the Gorgon's Gaze.
    -   **Verification:** A successful Pilot Fish opportunity results in a single, valid transaction being sent to the private relay, correctly formatted to trigger the flash loan arbitrage on-chain. The "Pilot Fish" is now fully operational.