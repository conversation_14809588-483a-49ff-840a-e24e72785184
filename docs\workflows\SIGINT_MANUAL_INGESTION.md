### **`/docs/workflows/SIGINT_MANUAL_INGESTION.md`**

# **Guide: The SIGINT Workflow for the Zen Geometer**

**To:** You, The Intelligence Officer
**From:** The Architect
**Subject:** Manual Intelligence Override & Strategic Guidance Protocol

**Mission:** This document outlines the standard operating procedure for the **SIGINT (Signals Intelligence) Workflow**. The Zen Geometer bot is a master of real-time, micro-level analysis. Your role as the Intelligence Officer is to provide the crucial **macro-level strategic context** that it cannot derive on its own. This workflow allows you to manually override the bot's autonomous sensors and apply a strategic bias, guiding its focus without altering its core logic.

### **Doctrine: The Spotter and the Sniper**

The relationship between you and the bot is that of a **spotter and a sniper**.

- **The Bot (Sniper):** The Basilisk is a world-class sniper. It can perfectly calculate the immediate variables for a shot—wind, humidity, distance. This is its autonomous `MarketStateAnalyzer`.
- **You (Spotter):** You have the high-powered binoculars and a map of the entire battlefield. You can see things the sniper cannot—a convoy approaching from over the hill, a structural weakness in a bridge, a change in the weather pattern for the entire region.

Your job is not to tell the sniper _how_ to shoot. Your job is to tell it **where to aim its attention.** The `sigint_report.json` file is your command.

### **The Workflow: From Observation to Directive**

This workflow is performed whenever you, the operator, identify a macro-level opportunity or risk that the bot's short-term sensors might misinterpret.

#### **Part 1: Market Character Assessment (The "Weather Report")**

**Objective:** To provide a high-level, longer-term assessment of the market's "character," overriding the bot's block-by-block analysis when necessary.

1.  **Run the Feature Exporter Script:**

    - **Action:** From your terminal, execute the standalone analysis script: `cargo run --bin feature_exporter`.
    - **Purpose:** This script provides you with a quantitative, objective snapshot of the market over a longer timeframe (e.g., the last 4-6 hours), calculating key metrics like `volatility_4h`, `hurst_exponent_6h`, etc. This prevents you from being fooled by short-term noise.

2.  **Classify the Macro-Regime:**

    - **Action:** Use the output from the script and the **Classification Matrix** to determine the true, overarching market character.
    - **Example:** The bot's real-time sensor might see a 5-minute period of calm (`Calm / Mean-Reverting`). However, your `feature_exporter` shows that the 6-hour Hurst exponent is `0.75` (strongly trending) and volatility is rising. You, the spotter, can see that this is not true calm; it is the "eye of the storm."

3.  **Formulate the `SET_MARKET_CONTEXT` Directive:**
    - **Action:** You will create the first entry in your `sigint_report.json` file.
    - **Code:**
      ```json
      {
        "directive_type": "SET_MARKET_CONTEXT",
        "regime": "High_Volatility_Correction",
        "character": "Trending",
        "reason": "Manual override: Long-term Hurst exponent indicates a strong trend despite short-term calm.",
        "duration_hours": 4
      }
      ```
    - **Effect:** When the bot ingests this, it will ignore its own `MarketStateAnalyzer`'s output for the next 4 hours and operate under _your_ strategic assessment. It will now correctly prioritize `MempoolScanner` opportunities, preparing for the trend to continue.

#### **Part 2: Structural Anomaly Identification (The "Geometer's Insight")**

**Objective:** To identify and flag structural opportunities or risks on the battlefield that are not immediately apparent from price data alone.

1.  **Conduct Reconnaissance:**

    - **Action:** Use external tools like **DefiLlama** or **Dune Analytics** to survey the broader DeFi landscape on Base.
    - **Look For:**
      - **Anomalous Liquidity:** A major, core pool (like the Aerodrome WETH/USDC pool) has suddenly lost a huge percentage of its liquidity.
      - **Peg Deviations:** Normally correlated assets (like different liquid staking tokens for ETH) have significantly de-pegged.
      - **New Structures:** A brand new DEX has launched and attracted a massive, surprising amount of liquidity in its first 24 hours.

2.  **Formulate the `APPLY_STRATEGIC_BIAS` Directive:**
    - **Action:** Translate your structural insight into a direct command for the bot's brain.
    - **Scenario: You notice the `wstETH`/`WETH` peg is broken.** The bot's scanners might see this, but you want to focus _all_ of the bot's attention on this massive, structural opportunity.
    - **Code:**
      ```json
      {
        "directive_type": "APPLY_STRATEGIC_BIAS",
        "bias_target": "asset:0x...", // The on-chain address of wstETH
        "multiplier_adjustment": 2.5, // Apply a massive bonus to any path involving this asset
        "reason": "Structural opportunity: Significant wstETH/WETH de-peg observed.",
        "duration_hours": 2
      }
      ```
    - **Effect:** The `StrategyManager` will ingest this. For the next 2 hours, its scoring algorithm will apply a `* 2.5` multiplier to the final score of _any_ opportunity from _any_ scanner that involves `wstETH`. The bot will now aggressively hunt this specific inefficiency above all others.

#### **Part 3: Deployment of Intelligence**

1.  **Construct the Final Report:**

    - **Action:** Combine your directives into a single `sigint_report.json` file. The file must have a unique `report_id` and an `expires_at` timestamp.

    ```json
    {
      "report_id": "sigint-2024-10-28-01",
      "expires_at": "2024-10-29T00:00:00Z",
      "directives": [
        {
          "directive_type": "SET_MARKET_CONTEXT",
          "regime": "High_Volatility_Correction",
          "character": "Trending",
          "reason": "Manual override: Long-term Hurst exponent indicates a strong trend.",
          "duration_hours": 4
        },
        {
          "directive_type": "APPLY_STRATEGIC_BIAS",
          "bias_target": "asset:******************************************",
          "multiplier_adjustment": 2.5,
          "reason": "Structural opportunity: Significant wstETH/WETH de-peg observed.",
          "duration_hours": 2
        }
      ]
    }
    ```

2.  **Deploy the Report:**
    - **Action:** Place this single file in the bot's pre-configured `sigint/` directory.
    - **Action:** The bot's `StrategyManager` is programmed to periodically check this directory. Upon finding a new file (with a new `report_id`), it will ingest it, log that it's now operating under a manual directive, and apply the overrides.

This workflow provides the perfect symbiotic relationship. The bot handles the lightning-fast, real-time calculations, while you provide the overarching strategic wisdom and structural insight it needs to truly dominate the ecosystem.
