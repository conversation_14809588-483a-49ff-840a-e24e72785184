// MISSION: Enhanced Heartbeat System with System Health Metrics
// WHY: Provide comprehensive system health monitoring for production trading
// HOW: Periodic health reports with actionable metrics and performance indicators

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use tracing::{info, warn, error};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::logging::{TradingContext, ErrorCode};
use crate::{log_info, log_warning, log_error};

/// Comprehensive system health metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMetrics {
    // Trading Performance
    pub opportunities_scanned_last_minute: u64,
    pub opportunities_executed_last_minute: u64,
    pub trades_executed_last_hour: u64,
    pub total_profit_usd_last_hour: Decimal,
    pub success_rate_percent: f64,
    
    // Strategy Status
    pub active_strategy: String,
    pub current_market_regime: String,
    pub strategy_uptime_seconds: u64,
    
    // Data Stream Health
    pub data_stream_status: HashMap<String, DataStreamHealth>,
    pub last_price_update_age_ms: u64,
    pub missing_data_sources: Vec<String>,
    
    // Network Performance
    pub rpc_latency_ms: HashMap<String, u64>,
    pub rpc_success_rate: HashMap<String, f64>,
    pub network_congestion_level: String,
    pub gas_price_gwei: Decimal,
    
    // System Resources
    pub memory_usage_mb: u64,
    pub memory_usage_percent: f64,
    pub cpu_usage_percent: f64,
    pub disk_usage_percent: f64,
    pub open_file_descriptors: u64,
    
    // Risk Metrics
    pub current_exposure_usd: Decimal,
    pub daily_pnl_usd: Decimal,
    pub max_drawdown_percent: f64,
    pub risk_score: f64,
    
    // Operational Status
    pub uptime_seconds: u64,
    pub last_restart_reason: Option<String>,
    pub circuit_breakers_active: Vec<String>,
    pub pending_transactions: u64,
    pub nonce_health: NonceHealthStatus,
}

/// Data stream health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataStreamHealth {
    pub status: String,  // "healthy", "degraded", "failed"
    pub last_update_ms: u64,
    pub error_rate_percent: f64,
    pub latency_ms: u64,
}

/// Nonce management health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NonceHealthStatus {
    pub current_nonce: u64,
    pub pending_nonces: Vec<u64>,
    pub stuck_nonces: Vec<u64>,
    pub last_successful_tx_age_ms: u64,
}

/// Enhanced heartbeat logger with comprehensive health monitoring
pub struct EnhancedHeartbeatLogger {
    metrics: Arc<RwLock<SystemHealthMetrics>>,
    last_heartbeat: Arc<RwLock<DateTime<Utc>>>,
    start_time: DateTime<Utc>,
    heartbeat_interval_seconds: u64,
    alert_thresholds: HealthAlertThresholds,
}

/// Configurable alert thresholds for health monitoring
#[derive(Debug, Clone)]
pub struct HealthAlertThresholds {
    pub max_rpc_latency_ms: u64,
    pub min_success_rate_percent: f64,
    pub max_memory_usage_percent: f64,
    pub max_cpu_usage_percent: f64,
    pub max_data_age_ms: u64,
    pub max_pending_transactions: u64,
    pub max_stuck_nonces: usize,
}

impl Default for HealthAlertThresholds {
    fn default() -> Self {
        Self {
            max_rpc_latency_ms: 5000,
            min_success_rate_percent: 95.0,
            max_memory_usage_percent: 80.0,
            max_cpu_usage_percent: 90.0,
            max_data_age_ms: 30000,
            max_pending_transactions: 10,
            max_stuck_nonces: 3,
        }
    }
}

impl EnhancedHeartbeatLogger {
    pub fn new(heartbeat_interval_seconds: u64) -> Self {
        let now = Utc::now();
        Self {
            metrics: Arc::new(RwLock::new(SystemHealthMetrics::default())),
            last_heartbeat: Arc::new(RwLock::new(now)),
            start_time: now,
            heartbeat_interval_seconds,
            alert_thresholds: HealthAlertThresholds::default(),
        }
    }
    
    pub fn with_thresholds(mut self, thresholds: HealthAlertThresholds) -> Self {
        self.alert_thresholds = thresholds;
        self
    }
    
    /// Update system health metrics
    pub async fn update_metrics(&self, metrics: SystemHealthMetrics) {
        *self.metrics.write().await = metrics;
    }
    
    /// Log comprehensive heartbeat with health analysis
    pub async fn log_heartbeat(&self) {
        let metrics = self.metrics.read().await.clone();
        let mut last_heartbeat = self.last_heartbeat.write().await;
        let now = Utc::now();
        
        let heartbeat_context = TradingContext::new("HeartbeatLogger", "log_heartbeat")
            .with_strategy(&metrics.active_strategy)
            .with_regime(&metrics.current_market_regime);
        
        // Calculate uptime
        let uptime_seconds = (now - self.start_time).num_seconds() as u64;
        
        // Log main heartbeat
        log_info!(heartbeat_context,
            opportunities_scanned = metrics.opportunities_scanned_last_minute,
            opportunities_executed = metrics.opportunities_executed_last_minute,
            trades_executed = metrics.trades_executed_last_hour,
            total_profit_usd = %metrics.total_profit_usd_last_hour,
            success_rate_percent = metrics.success_rate_percent,
            active_strategy = %metrics.active_strategy,
            market_regime = %metrics.current_market_regime,
            uptime_seconds = uptime_seconds,
            memory_mb = metrics.memory_usage_mb,
            cpu_percent = metrics.cpu_usage_percent,
            current_exposure_usd = %metrics.current_exposure_usd,
            daily_pnl_usd = %metrics.daily_pnl_usd,
            pending_transactions = metrics.pending_transactions,
            "System heartbeat - Zen Geometer operational"
        );
        
        // Perform health checks and generate alerts
        self.check_system_health(&metrics, &heartbeat_context).await;
        
        *last_heartbeat = now;
    }
    
    /// Comprehensive system health analysis with targeted alerts
    async fn check_system_health(&self, metrics: &SystemHealthMetrics, context: &TradingContext) {
        // Check RPC health
        for (endpoint, latency) in &metrics.rpc_latency_ms {
            if *latency > self.alert_thresholds.max_rpc_latency_ms {
                log_warning!(context.clone(), ErrorCode::ENetworkLatencyHigh,
                    "High RPC latency detected: {} = {}ms > {}ms threshold",
                    endpoint, latency, self.alert_thresholds.max_rpc_latency_ms
                );
            }
        }
        
        // Check success rates
        for (endpoint, rate) in &metrics.rpc_success_rate {
            if *rate < self.alert_thresholds.min_success_rate_percent {
                log_warning!(context.clone(), ErrorCode::ERpcConnectionFailed,
                    "Low RPC success rate: {} = {:.1}% < {:.1}% threshold",
                    endpoint, rate, self.alert_thresholds.min_success_rate_percent
                );
            }
        }
        
        // Check resource usage
        if metrics.memory_usage_percent > self.alert_thresholds.max_memory_usage_percent {
            log_warning!(context.clone(), ErrorCode::ESystemOverloaded,
                "High memory usage: {:.1}% > {:.1}% threshold",
                metrics.memory_usage_percent, self.alert_thresholds.max_memory_usage_percent
            );
        }
        
        if metrics.cpu_usage_percent > self.alert_thresholds.max_cpu_usage_percent {
            log_warning!(context.clone(), ErrorCode::ESystemOverloaded,
                "High CPU usage: {:.1}% > {:.1}% threshold",
                metrics.cpu_usage_percent, self.alert_thresholds.max_cpu_usage_percent
            );
        }
        
        // Check data freshness
        if metrics.last_price_update_age_ms > self.alert_thresholds.max_data_age_ms {
            log_warning!(context.clone(), ErrorCode::EDataStale,
                "Stale price data: {}ms old > {}ms threshold",
                metrics.last_price_update_age_ms, self.alert_thresholds.max_data_age_ms
            );
        }
        
        // Check transaction health
        if metrics.pending_transactions > self.alert_thresholds.max_pending_transactions {
            log_warning!(context.clone(), ErrorCode::ETransactionTimeout,
                "High pending transaction count: {} > {} threshold",
                metrics.pending_transactions, self.alert_thresholds.max_pending_transactions
            );
        }
        
        // Check nonce health
        if metrics.nonce_health.stuck_nonces.len() > self.alert_thresholds.max_stuck_nonces {
            log_error!(context.clone(), ErrorCode::ENonceConflict,
                "Multiple stuck nonces detected: {:?}",
                metrics.nonce_health.stuck_nonces
            );
        }
        
        // Check for missing data sources
        if !metrics.missing_data_sources.is_empty() {
            log_warning!(context.clone(), ErrorCode::EDataSourceUnavailable,
                "Missing data sources: {:?}",
                metrics.missing_data_sources
            );
        }
        
        // Check circuit breakers
        if !metrics.circuit_breakers_active.is_empty() {
            log_warning!(context.clone(), ErrorCode::ECircuitBreakerTripped,
                "Active circuit breakers: {:?}",
                metrics.circuit_breakers_active
            );
        }
        
        // Performance analysis
        if metrics.success_rate_percent < 90.0 {
            log_warning!(context.clone(), ErrorCode::EInsufficientProfit,
                "Low trading success rate: {:.1}%",
                metrics.success_rate_percent
            );
        }
        
        // Risk monitoring
        if metrics.risk_score > 0.8 {
            log_warning!(context.clone(), ErrorCode::EVolatilityTooHigh,
                "High risk score detected: {:.2}",
                metrics.risk_score
            );
        }
    }
    
    /// Check if it's time to log a heartbeat
    pub async fn should_log_heartbeat(&self) -> bool {
        let last = *self.last_heartbeat.read().await;
        let now = Utc::now();
        (now - last).num_seconds() >= self.heartbeat_interval_seconds as i64
    }
    
    /// Get current system metrics
    pub async fn get_metrics(&self) -> SystemHealthMetrics {
        self.metrics.read().await.clone()
    }
    
    /// Update specific metric categories
    pub async fn update_trading_metrics(&self, 
        opportunities_scanned: u64,
        opportunities_executed: u64,
        trades_executed: u64,
        total_profit: Decimal,
        success_rate: f64
    ) {
        let mut metrics = self.metrics.write().await;
        metrics.opportunities_scanned_last_minute = opportunities_scanned;
        metrics.opportunities_executed_last_minute = opportunities_executed;
        metrics.trades_executed_last_hour = trades_executed;
        metrics.total_profit_usd_last_hour = total_profit;
        metrics.success_rate_percent = success_rate;
    }
    
    pub async fn update_network_metrics(&self, rpc_latencies: HashMap<String, u64>, success_rates: HashMap<String, f64>) {
        let mut metrics = self.metrics.write().await;
        metrics.rpc_latency_ms = rpc_latencies;
        metrics.rpc_success_rate = success_rates;
    }
    
    pub async fn update_system_resources(&self, memory_mb: u64, memory_percent: f64, cpu_percent: f64) {
        let mut metrics = self.metrics.write().await;
        metrics.memory_usage_mb = memory_mb;
        metrics.memory_usage_percent = memory_percent;
        metrics.cpu_usage_percent = cpu_percent;
    }
}

impl Default for SystemHealthMetrics {
    fn default() -> Self {
        Self {
            opportunities_scanned_last_minute: 0,
            opportunities_executed_last_minute: 0,
            trades_executed_last_hour: 0,
            total_profit_usd_last_hour: Decimal::ZERO,
            success_rate_percent: 0.0,
            active_strategy: "zen_geometer".to_string(),
            current_market_regime: "unknown".to_string(),
            strategy_uptime_seconds: 0,
            data_stream_status: HashMap::new(),
            last_price_update_age_ms: 0,
            missing_data_sources: Vec::new(),
            rpc_latency_ms: HashMap::new(),
            rpc_success_rate: HashMap::new(),
            network_congestion_level: "normal".to_string(),
            gas_price_gwei: Decimal::ZERO,
            memory_usage_mb: 0,
            memory_usage_percent: 0.0,
            cpu_usage_percent: 0.0,
            disk_usage_percent: 0.0,
            open_file_descriptors: 0,
            current_exposure_usd: Decimal::ZERO,
            daily_pnl_usd: Decimal::ZERO,
            max_drawdown_percent: 0.0,
            risk_score: 0.0,
            uptime_seconds: 0,
            last_restart_reason: None,
            circuit_breakers_active: Vec::new(),
            pending_transactions: 0,
            nonce_health: NonceHealthStatus {
                current_nonce: 0,
                pending_nonces: Vec::new(),
                stuck_nonces: Vec::new(),
                last_successful_tx_age_ms: 0,
            },
        }
    }
}