# Utility Scripts

This directory contains a collection of scripts for managing the development, testing, and deployment of the Basilisk Bot.

## Table of Contents

- [Development Scripts](#development-scripts)
- [Testing Scripts](#testing-scripts)
- [Deployment Scripts](#deployment-scripts)
- [Other Scripts](#other-scripts)
- [Common Workflows](#common-workflows)

## Development Scripts

### `local_dev_setup.sh`

This script sets up a complete local development environment. It checks for prerequisites, creates necessary directories, sets up configuration files, and starts the required infrastructure services.

**Usage:**

```bash
./scripts/local_dev_setup.sh
```

### `dev_environment.sh`

This is the primary tool for managing the development environment. It provides a set of commands for common development tasks.

**Usage:**

```bash
./scripts/dev_environment.sh [COMMAND]
```

**Commands:**

- `setup`: Sets up the development environment.
- `start`: Starts the infrastructure services.
- `stop`: Stops the infrastructure services.
- `restart`: Restarts the infrastructure services.
- `status`: Shows the status of all services.
- `logs`: Shows the logs from all services.
- `clean`: Cleans up Docker volumes and containers.
- `build`: Builds the Rust project.
- `test`: Runs all tests.
- `run`: Runs the bot in dry-run mode.
- `tui`: Starts the TUI harness.
- `validate`: Validates the configuration.
- `help`: Shows the help message.

## Testing Scripts

### `test_complete_system.sh`

This script runs a comprehensive test suite that covers all components of the system, including unit tests, integration tests, and binary tool tests.

**Usage:**

```bash
./scripts/test_complete_system.sh
```

### `test_zen_geometer.sh`

This script tests the core Zen Geometer trading strategy.

**Usage:**

```bash
./scripts/test_zen_geometer.sh
```

## Deployment Scripts

### `mainnet_deploy.sh`

This script deploys the bot to a production mainnet environment. **Use with extreme caution.**

**Usage:**

```bash
./scripts/mainnet_deploy.sh
```

### `cloud_backtest_deploy.sh`

This script deploys a backtesting environment to a cloud infrastructure.

**Usage:**

```bash
./scripts/cloud_backtest_deploy.sh
```

### `cloud_test_deploy.sh`

This script deploys a testing environment to a cloud infrastructure.

**Usage:**

```bash
./scripts/cloud_test_deploy.sh
```

## Cross-Reference Validation Scripts

### `validate_cross_references.ps1`

Comprehensive cross-reference validation system that checks all internal links, file references, and anchor links in documentation files.

**Usage:**

```powershell
powershell -ExecutionPolicy Bypass -File scripts/validate_cross_references.ps1 [-Verbose]
```

### `broken_link_detector.ps1`

Specialized broken link detection and reporting system with detailed analysis and reporting capabilities.

**Usage:**

```powershell
powershell -ExecutionPolicy Bypass -File scripts/broken_link_detector.ps1 [-OutputFile "report.md"] [-JsonOutput] [-Verbose]
```

### `cross_reference_validation_system.ps1`

Master controller for all cross-reference validation functionality with unified reporting.

**Usage:**

```powershell
powershell -ExecutionPolicy Bypass -File scripts/cross_reference_validation_system.ps1 [-Mode validate|detect|report|all] [-OutputDir "validation_reports"] [-JsonOutput] [-Verbose]
```

### `run_cross_reference_validation.ps1`

Quick runner script for common validation scenarios.

**Usage:**

```powershell
powershell -ExecutionPolicy Bypass -File scripts/run_cross_reference_validation.ps1 [-Quick] [-Detailed] [-JsonOutput]
```

## Other Scripts

- **`backup.sh`**: Creates backups of critical data.
- **`local_backtest.sh`**: Runs a backtest on the local machine.
- **`verify_documentation.sh`**: Verifies that the documentation is up-to-date.
- **`validate_documentation.sh`**: Validates code examples, configuration references, and CLI commands.

## Common Workflows

### First-Time Setup

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/your-org/basilisk_bot.git
    cd basilisk_bot
    ```

2.  **Run the initial setup:**
    ```bash
    ./scripts/local_dev_setup.sh
    ```

### Daily Development

1.  **Start the services:**

    ```bash
    ./scripts/dev_environment.sh start
    ```

2.  **Run the bot in dry-run mode:**

    ```bash
    ./scripts/dev_environment.sh run
    ```

3.  **View the logs:**

    ```bash
    ./scripts/dev_environment.sh logs
    ```

4.  **Stop the services:**
    ```bash
    ./scripts/dev_environment.sh stop
    ```

### Cross-Reference Validation

1.  **Quick validation check:**

    ```powershell
    powershell -ExecutionPolicy Bypass -File scripts/run_cross_reference_validation.ps1 -Quick
    ```

2.  **Detailed validation with full reporting:**

    ```powershell
    powershell -ExecutionPolicy Bypass -File scripts/run_cross_reference_validation.ps1 -Detailed
    ```

3.  **Generate broken link report:**

    ```powershell
    powershell -ExecutionPolicy Bypass -File scripts/broken_link_detector.ps1 -OutputFile "validation_report.md"
    ```

4.  **Comprehensive validation (all modes):**
    ```powershell
    powershell -ExecutionPolicy Bypass -File scripts/cross_reference_validation_system.ps1 -Mode all -JsonOutput
    ```
