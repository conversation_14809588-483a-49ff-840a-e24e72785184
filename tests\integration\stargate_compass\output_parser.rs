// MISSION: Output Parser - TUI Output Analysis and Data Extraction
// WHY: Parse and validate TUI output to verify contract interactions and data accuracy
// HOW: Regex patterns, JSON parsing, and structured data validation

use anyhow::{Context, Result};
use regex::Regex;
use serde_json::Value;
use std::collections::HashMap;
use tracing::{debug, warn};

/// Parsed TUI output with structured data
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ParsedTuiOutput {
    pub raw_output: String,
    pub log_entries: Vec<LogEntry>,
    pub balance_data: Option<BalanceData>,
    pub contract_interactions: Vec<ContractInteraction>,
    pub system_status: Option<SystemStatus>,
    pub error_messages: Vec<String>,
    pub command_acknowledgments: Vec<CommandAck>,
}

/// Log entry from TUI output
#[derive(Debug, <PERSON>lone)]
pub struct LogEntry {
    pub timestamp: String,
    pub source: String,
    pub severity: LogSeverity,
    pub message: String,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum LogSeverity {
    Info,
    War<PERSON>,
    <PERSON><PERSON><PERSON>,
    Ok,
    Debug,
}

/// Balance information extracted from TUI
#[derive(Debug, Clone)]
pub struct BalanceData {
    pub total_balance_usd: Option<f64>,
    pub available_balance_usd: Option<f64>,
    pub locked_balance_usd: Option<f64>,
    pub token_balances: HashMap<String, f64>,
    pub chain_balances: HashMap<String, f64>,
}

/// Contract interaction detected in output
#[derive(Debug, Clone)]
pub struct ContractInteraction {
    pub interaction_type: ContractInteractionType,
    pub contract_address: Option<String>,
    pub transaction_hash: Option<String>,
    pub gas_used: Option<u64>,
    pub gas_price: Option<f64>,
    pub status: String,
    pub timestamp: String,
}

#[derive(Debug, Clone)]
pub enum ContractInteractionType {
    EmergencyStop,
    PauseTrading,
    ResumeTrading,
    ExecuteOpportunity,
    BalanceQuery,
    StatusQuery,
    Unknown(String),
}

/// System status information
#[derive(Debug, Clone)]
pub struct SystemStatus {
    pub overall_status: String,
    pub active_strategies: Option<u32>,
    pub total_strategies: Option<u32>,
    pub uptime_seconds: Option<u64>,
    pub service_statuses: HashMap<String, String>,
}

/// Command acknowledgment
#[derive(Debug, Clone)]
pub struct CommandAck {
    pub command_type: String,
    pub success: bool,
    pub message: String,
    pub timestamp: String,
}

/// TUI output parser implementation
pub struct TuiOutputParser {
    log_regex: Regex,
    balance_regex: Regex,
    contract_regex: Regex,
    command_ack_regex: Regex,
    error_regex: Regex,
}

impl TuiOutputParser {
    pub fn new() -> Result<Self> {
        Ok(Self {
            log_regex: Regex::new(r"(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)\s+(\w+)\s+(\w+)\s+(.+)")?,
            balance_regex: Regex::new(r"(?:Balance|Available|Total):\s*\$?([\d,]+\.?\d*)\s*(\w{3,4})?")?,
            contract_regex: Regex::new(r"(?:Transaction|Contract|0x[a-fA-F0-9]{40}|gas|wei|gwei)")?,
            command_ack_regex: Regex::new(r"(EMERGENCY STOP|PAUSED|RESTARTED|command sent)")?,
            error_regex: Regex::new(r"(?i)(error|failed|exception|panic)")?,
        })
    }

    /// Parse complete TUI output into structured data
    pub fn parse_output(&self, raw_output: &str) -> Result<ParsedTuiOutput> {
        debug!("Parsing TUI output ({} chars)", raw_output.len());

        let log_entries = self.parse_log_entries(raw_output)?;
        let balance_data = self.parse_balance_data(raw_output)?;
        let contract_interactions = self.parse_contract_interactions(raw_output)?;
        let system_status = self.parse_system_status(raw_output)?;
        let error_messages = self.parse_error_messages(raw_output);
        let command_acknowledgments = self.parse_command_acknowledgments(raw_output)?;

        Ok(ParsedTuiOutput {
            raw_output: raw_output.to_string(),
            log_entries,
            balance_data,
            contract_interactions,
            system_status,
            error_messages,
            command_acknowledgments,
        })
    }

    /// Parse log entries from TUI output
    fn parse_log_entries(&self, output: &str) -> Result<Vec<LogEntry>> {
        let mut log_entries = Vec::new();

        for line in output.lines() {
            if let Some(captures) = self.log_regex.captures(line) {
                let timestamp = captures.get(1).map_or("", |m| m.as_str()).to_string();
                let source = captures.get(2).map_or("", |m| m.as_str()).to_string();
                let severity_str = captures.get(3).map_or("", |m| m.as_str());
                let message = captures.get(4).map_or("", |m| m.as_str()).to_string();

                let severity = match severity_str.to_uppercase().as_str() {
                    "INFO" => LogSeverity::Info,
                    "WARN" | "WARNING" => LogSeverity::Warn,
                    "ERROR" => LogSeverity::Error,
                    "OK" | "SUCCESS" => LogSeverity::Ok,
                    "DEBUG" => LogSeverity::Debug,
                    _ => LogSeverity::Info,
                };

                log_entries.push(LogEntry {
                    timestamp,
                    source,
                    severity,
                    message,
                });
            }
        }

        debug!("Parsed {} log entries", log_entries.len());
        Ok(log_entries)
    }

    /// Parse balance data from TUI output
    fn parse_balance_data(&self, output: &str) -> Result<Option<BalanceData>> {
        let mut balance_data = BalanceData {
            total_balance_usd: None,
            available_balance_usd: None,
            locked_balance_usd: None,
            token_balances: HashMap::new(),
            chain_balances: HashMap::new(),
        };

        let mut found_balance = false;

        for line in output.lines() {
            if let Some(captures) = self.balance_regex.captures(line) {
                let amount_str = captures.get(1).map_or("", |m| m.as_str());
                let currency = captures.get(2).map_or("USD", |m| m.as_str());

                // Remove commas and parse as float
                let amount = amount_str.replace(",", "").parse::<f64>().unwrap_or(0.0);

                if line.to_lowercase().contains("total") {
                    balance_data.total_balance_usd = Some(amount);
                    found_balance = true;
                } else if line.to_lowercase().contains("available") {
                    balance_data.available_balance_usd = Some(amount);
                    found_balance = true;
                } else if line.to_lowercase().contains("locked") {
                    balance_data.locked_balance_usd = Some(amount);
                    found_balance = true;
                } else if currency != "USD" {
                    balance_data.token_balances.insert(currency.to_string(), amount);
                    found_balance = true;
                }
            }
        }

        if found_balance {
            debug!("Parsed balance data: {:?}", balance_data);
            Ok(Some(balance_data))
        } else {
            Ok(None)
        }
    }

    /// Parse contract interactions from TUI output
    fn parse_contract_interactions(&self, output: &str) -> Result<Vec<ContractInteraction>> {
        let mut interactions = Vec::new();

        for line in output.lines() {
            if self.contract_regex.is_match(line) {
                let interaction = self.parse_contract_interaction_line(line)?;
                if let Some(interaction) = interaction {
                    interactions.push(interaction);
                }
            }
        }

        debug!("Parsed {} contract interactions", interactions.len());
        Ok(interactions)
    }

    /// Parse a single contract interaction line
    fn parse_contract_interaction_line(&self, line: &str) -> Result<Option<ContractInteraction>> {
        let line_lower = line.to_lowercase();

        let interaction_type = if line_lower.contains("emergency stop") {
            ContractInteractionType::EmergencyStop
        } else if line_lower.contains("pause") {
            ContractInteractionType::PauseTrading
        } else if line_lower.contains("resume") || line_lower.contains("restart") {
            ContractInteractionType::ResumeTrading
        } else if line_lower.contains("executing opportunity") {
            ContractInteractionType::ExecuteOpportunity
        } else if line_lower.contains("balance") {
            ContractInteractionType::BalanceQuery
        } else if line_lower.contains("status") {
            ContractInteractionType::StatusQuery
        } else if line_lower.contains("transaction") || line_lower.contains("contract") {
            ContractInteractionType::Unknown(line.to_string())
        } else {
            return Ok(None);
        };

        // Extract contract address if present
        let address_regex = Regex::new(r"0x[a-fA-F0-9]{40}").unwrap();
        let contract_address = address_regex.find(line).map(|m| m.as_str().to_string());

        // Extract transaction hash if present
        let tx_hash_regex = Regex::new(r"0x[a-fA-F0-9]{64}").unwrap();
        let transaction_hash = tx_hash_regex.find(line).map(|m| m.as_str().to_string());

        // Extract gas information
        let gas_regex = Regex::new(r"gas[:\s]+(\d+)").unwrap();
        let gas_used = gas_regex.captures(line)
            .and_then(|cap| cap.get(1))
            .and_then(|m| m.as_str().parse::<u64>().ok());

        let gas_price_regex = Regex::new(r"(\d+\.?\d*)\s*gwei").unwrap();
        let gas_price = gas_price_regex.captures(line)
            .and_then(|cap| cap.get(1))
            .and_then(|m| m.as_str().parse::<f64>().ok());

        // Determine status
        let status = if line_lower.contains("success") || line_lower.contains("completed") {
            "success".to_string()
        } else if line_lower.contains("failed") || line_lower.contains("error") {
            "failed".to_string()
        } else if line_lower.contains("pending") {
            "pending".to_string()
        } else {
            "unknown".to_string()
        };

        // Extract timestamp (simple heuristic)
        let timestamp_regex = Regex::new(r"(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)").unwrap();
        let timestamp = timestamp_regex.find(line)
            .map(|m| m.as_str().to_string())
            .unwrap_or_else(|| "unknown".to_string());

        Ok(Some(ContractInteraction {
            interaction_type,
            contract_address,
            transaction_hash,
            gas_used,
            gas_price,
            status,
            timestamp,
        }))
    }

    /// Parse system status information
    fn parse_system_status(&self, output: &str) -> Result<Option<SystemStatus>> {
        let mut system_status = SystemStatus {
            overall_status: "unknown".to_string(),
            active_strategies: None,
            total_strategies: None,
            uptime_seconds: None,
            service_statuses: HashMap::new(),
        };

        let mut found_status = false;

        for line in output.lines() {
            let line_lower = line.to_lowercase();

            // Overall status
            if line_lower.contains("status") {
                if line_lower.contains("running") || line_lower.contains("active") {
                    system_status.overall_status = "running".to_string();
                    found_status = true;
                } else if line_lower.contains("stopped") {
                    system_status.overall_status = "stopped".to_string();
                    found_status = true;
                } else if line_lower.contains("paused") {
                    system_status.overall_status = "paused".to_string();
                    found_status = true;
                }
            }

            // Strategy counts
            let strategy_regex = Regex::new(r"(\d+)\s*(?:active|running)\s*strategies?").unwrap();
            if let Some(captures) = strategy_regex.captures(line) {
                if let Some(count_str) = captures.get(1) {
                    if let Ok(count) = count_str.as_str().parse::<u32>() {
                        system_status.active_strategies = Some(count);
                        found_status = true;
                    }
                }
            }

            let total_strategy_regex = Regex::new(r"(\d+)\s*total\s*strategies?").unwrap();
            if let Some(captures) = total_strategy_regex.captures(line) {
                if let Some(count_str) = captures.get(1) {
                    if let Ok(count) = count_str.as_str().parse::<u32>() {
                        system_status.total_strategies = Some(count);
                        found_status = true;
                    }
                }
            }

            // Uptime
            let uptime_regex = Regex::new(r"uptime[:\s]+(\d+)\s*seconds?").unwrap();
            if let Some(captures) = uptime_regex.captures(line) {
                if let Some(uptime_str) = captures.get(1) {
                    if let Ok(uptime) = uptime_str.as_str().parse::<u64>() {
                        system_status.uptime_seconds = Some(uptime);
                        found_status = true;
                    }
                }
            }

            // Service statuses
            let service_regex = Regex::new(r"(\w+):\s*(running|stopped|error|connected|disconnected)").unwrap();
            if let Some(captures) = service_regex.captures(line) {
                if let (Some(service), Some(status)) = (captures.get(1), captures.get(2)) {
                    system_status.service_statuses.insert(
                        service.as_str().to_string(),
                        status.as_str().to_string(),
                    );
                    found_status = true;
                }
            }
        }

        if found_status {
            debug!("Parsed system status: {:?}", system_status);
            Ok(Some(system_status))
        } else {
            Ok(None)
        }
    }

    /// Parse error messages from output
    fn parse_error_messages(&self, output: &str) -> Vec<String> {
        let mut errors = Vec::new();

        for line in output.lines() {
            if self.error_regex.is_match(line) {
                errors.push(line.to_string());
            }
        }

        if !errors.is_empty() {
            debug!("Found {} error messages", errors.len());
        }

        errors
    }

    /// Parse command acknowledgments
    fn parse_command_acknowledgments(&self, output: &str) -> Result<Vec<CommandAck>> {
        let mut acknowledgments = Vec::new();

        for line in output.lines() {
            if let Some(captures) = self.command_ack_regex.captures(line) {
                let command_indicator = captures.get(1).map_or("", |m| m.as_str());
                
                let command_type = match command_indicator.to_uppercase().as_str() {
                    "EMERGENCY STOP" => "emergency_stop".to_string(),
                    "PAUSED" => "pause_bot".to_string(),
                    "RESTARTED" => "restart_bot".to_string(),
                    _ if line.contains("command sent") => "generic_command".to_string(),
                    _ => "unknown".to_string(),
                };

                let success = !line.to_lowercase().contains("failed") && 
                             !line.to_lowercase().contains("error");

                // Extract timestamp
                let timestamp_regex = Regex::new(r"(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)").unwrap();
                let timestamp = timestamp_regex.find(line)
                    .map(|m| m.as_str().to_string())
                    .unwrap_or_else(|| "unknown".to_string());

                acknowledgments.push(CommandAck {
                    command_type,
                    success,
                    message: line.to_string(),
                    timestamp,
                });
            }
        }

        debug!("Parsed {} command acknowledgments", acknowledgments.len());
        Ok(acknowledgments)
    }

    /// Validate parsed output against expected patterns
    pub fn validate_output(&self, parsed: &ParsedTuiOutput, expected_patterns: &[String]) -> Vec<ValidationResult> {
        let mut results = Vec::new();

        for pattern in expected_patterns {
            let found = parsed.raw_output.contains(pattern) ||
                       parsed.log_entries.iter().any(|entry| entry.message.contains(pattern)) ||
                       parsed.command_acknowledgments.iter().any(|ack| ack.message.contains(pattern));

            results.push(ValidationResult {
                pattern: pattern.clone(),
                found,
                locations: if found {
                    self.find_pattern_locations(&parsed.raw_output, pattern)
                } else {
                    Vec::new()
                },
            });
        }

        results
    }

    /// Find locations where a pattern appears in the output
    fn find_pattern_locations(&self, output: &str, pattern: &str) -> Vec<usize> {
        let mut locations = Vec::new();
        let mut start = 0;

        while let Some(pos) = output[start..].find(pattern) {
            locations.push(start + pos);
            start += pos + pattern.len();
        }

        locations
    }

    /// Extract contract-specific data from parsed output
    pub fn extract_contract_data(&self, parsed: &ParsedTuiOutput) -> ContractData {
        ContractData {
            emergency_stops: parsed.contract_interactions.iter()
                .filter(|i| matches!(i.interaction_type, ContractInteractionType::EmergencyStop))
                .count(),
            pause_commands: parsed.contract_interactions.iter()
                .filter(|i| matches!(i.interaction_type, ContractInteractionType::PauseTrading))
                .count(),
            resume_commands: parsed.contract_interactions.iter()
                .filter(|i| matches!(i.interaction_type, ContractInteractionType::ResumeTrading))
                .count(),
            opportunity_executions: parsed.contract_interactions.iter()
                .filter(|i| matches!(i.interaction_type, ContractInteractionType::ExecuteOpportunity))
                .count(),
            balance_queries: parsed.contract_interactions.iter()
                .filter(|i| matches!(i.interaction_type, ContractInteractionType::BalanceQuery))
                .count(),
            total_gas_used: parsed.contract_interactions.iter()
                .filter_map(|i| i.gas_used)
                .sum(),
            successful_interactions: parsed.contract_interactions.iter()
                .filter(|i| i.status == "success")
                .count(),
            failed_interactions: parsed.contract_interactions.iter()
                .filter(|i| i.status == "failed")
                .count(),
        }
    }
}

/// Validation result for expected patterns
#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub pattern: String,
    pub found: bool,
    pub locations: Vec<usize>,
}

/// Contract-specific data extracted from TUI output
#[derive(Debug, Clone)]
pub struct ContractData {
    pub emergency_stops: usize,
    pub pause_commands: usize,
    pub resume_commands: usize,
    pub opportunity_executions: usize,
    pub balance_queries: usize,
    pub total_gas_used: u64,
    pub successful_interactions: usize,
    pub failed_interactions: usize,
}

impl Default for TuiOutputParser {
    fn default() -> Self {
        Self::new().expect("Failed to create TUI output parser")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parser_creation() {
        let parser = TuiOutputParser::new();
        assert!(parser.is_ok());
    }

    #[test]
    fn test_log_entry_parsing() {
        let parser = TuiOutputParser::new().unwrap();
        let output = "12:34:56.789 MASTER_CONTROL INFO Bot status changed to: RUNNING";
        
        let parsed = parser.parse_output(output).unwrap();
        assert_eq!(parsed.log_entries.len(), 1);
        
        let entry = &parsed.log_entries[0];
        assert_eq!(entry.timestamp, "12:34:56.789");
        assert_eq!(entry.source, "MASTER_CONTROL");
        assert_eq!(entry.severity, LogSeverity::Info);
        assert!(entry.message.contains("Bot status changed"));
    }

    #[test]
    fn test_balance_parsing() {
        let parser = TuiOutputParser::new().unwrap();
        let output = "Total Balance: $1,234.56 USD\nAvailable: 5.67 ETH";
        
        let parsed = parser.parse_output(output).unwrap();
        assert!(parsed.balance_data.is_some());
        
        let balance = parsed.balance_data.unwrap();
        assert_eq!(balance.total_balance_usd, Some(1234.56));
        assert_eq!(balance.token_balances.get("ETH"), Some(&5.67));
    }

    #[test]
    fn test_contract_interaction_parsing() {
        let parser = TuiOutputParser::new().unwrap();
        let output = "12:34:56 EMERGENCY STOP ACTIVATED - command sent to all services\nTransaction 0x123...abc with gas 21000";
        
        let parsed = parser.parse_output(output).unwrap();
        assert!(!parsed.contract_interactions.is_empty());
        
        let interaction = &parsed.contract_interactions[0];
        assert!(matches!(interaction.interaction_type, ContractInteractionType::EmergencyStop));
    }

    #[test]
    fn test_command_acknowledgment_parsing() {
        let parser = TuiOutputParser::new().unwrap();
        let output = "12:34:56 EMERGENCY STOP ACTIVATED - command sent to all services";
        
        let parsed = parser.parse_output(output).unwrap();
        assert!(!parsed.command_acknowledgments.is_empty());
        
        let ack = &parsed.command_acknowledgments[0];
        assert_eq!(ack.command_type, "emergency_stop");
        assert!(ack.success);
    }

    #[test]
    fn test_pattern_validation() {
        let parser = TuiOutputParser::new().unwrap();
        let output = "EMERGENCY STOP ACTIVATED";
        let parsed = parser.parse_output(output).unwrap();
        
        let patterns = vec!["EMERGENCY STOP".to_string(), "ACTIVATED".to_string()];
        let results = parser.validate_output(&parsed, &patterns);
        
        assert_eq!(results.len(), 2);
        assert!(results.iter().all(|r| r.found));
    }
}