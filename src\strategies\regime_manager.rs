// src/strategies/regime_manager.rs

use async_nats::Client as NatsClient;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use tracing::{info, warn, error};
use serde::{Deserialize, Serialize};

use crate::shared_types::{MarketRegime, NatsTopics, MarketRegimeState};

pub struct RegimeManager {
    nats_client: NatsClient,
    last_regime: Arc<Mutex<MarketRegime>>,
}

impl RegimeManager {
    pub fn new(nats_client: NatsClient) -> Self {
        Self {
            nats_client,
            last_regime: Arc::new(Mutex::new(MarketRegime::Unknown)),
        }
    }

    pub async fn run(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("REGIME MANAGER: Starting market regime analysis.");

        let mut market_regime_hmm_subscriber = self.nats_client
            .subscribe(NatsTopics::STATE_MARKET_REGIME_HMM)
            .await?;

        while let Some(msg) = market_regime_hmm_subscriber.next().await {
            match serde_json::from_slice::<crate::shared_types::MarketRegimeState>(&msg.payload) {
                Ok(report) => {
                    let new_regime = report.regime;
                    let mut last_regime_guard = self.last_regime.lock().await;

                    if new_regime != *last_regime_guard {
                        info!("REGIME MANAGER: Market regime changed from {:?} to {:?}", *last_regime_guard, new_regime);
                        *last_regime_guard = new_regime.clone();

                        // Publish the new market regime
                        let payload = serde_json::to_vec(&new_regime)?;
                        self.nats_client
                            .publish(NatsTopics::STATE_MARKET_REGIME, payload.into())
                            .await?;
                    }
                },
                Err(e) => {
                    error!("REGIME MANAGER: Failed to deserialize MarketRegimeState: {}", e);
                }
            }
        }

        Ok(())
    }

    
}
