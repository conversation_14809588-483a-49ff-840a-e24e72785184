// FFT Implementation Validation Tests
// Tests for the FFT usage in FractalAnalyzer to verify correctness

use basilisk_bot::data::fractal_analyzer::{FractalAnalyzer, PricePoint};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use rustfft::{num_complex::Complex, FftPlanner};
use std::collections::VecDeque;

#[cfg(test)]
mod fft_tests {
    use super::*;

    /// Test FFT with a known sine wave to validate frequency detection
    #[test]
    fn test_fft_sine_wave_detection() {
        // Create a sine wave with known frequency
        let sample_rate = 1.0; // 1 sample per minute
        let frequency = 1.0 / 60.0; // 1 cycle per 60 minutes
        let duration_minutes = 240; // 4 hours of data
        
        let mut test_data = Vec::new();
        for i in 0..duration_minutes {
            let t = i as f64;
            let amplitude = 100.0; // Base price
            let sine_component = 10.0 * (2.0 * std::f64::consts::PI * frequency * t).sin();
            let price = amplitude + sine_component;
            test_data.push(price);
        }
        
        // Test the FFT implementation directly
        let result = test_fft_implementation(&test_data, sample_rate);
        
        // The dominant frequency should be close to our input frequency
        assert!(!result.is_empty(), "FFT should detect frequencies");
        
        // Check if the detected period is close to 60 minutes
        let detected_period = result[0].0;
        let expected_period = 60.0;
        let tolerance = 5.0; // 5 minute tolerance
        
        assert!(
            (detected_period - expected_period).abs() < tolerance,
            "Expected period ~{}, got {}",
            expected_period,
            detected_period
        );
    }

    /// Test FFT buffer size mismatch issue
    #[test]
    #[should_panic(expected = "FFT buffer size mismatch")]
    fn test_fft_buffer_size_mismatch() {
        // This test demonstrates the critical bug in the current implementation
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0]; // 5 elements
        let n = data.len();
        
        // This is what the current implementation does (incorrectly)
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(n); // Plan for 5 elements
        
        let mut buffer: Vec<Complex<f64>> = data
            .into_iter()
            .map(|val| Complex::new(val, 0.0))
            .collect();
        
        // Resize to next power of 2 (8 elements)
        let target_len = buffer.len().next_power_of_two();
        buffer.resize(target_len, Complex::new(0.0, 0.0));
        
        // This should panic because FFT was planned for 5 but buffer has 8 elements
        fft.process(&mut buffer);
    }

    /// Test edge case: empty data
    #[test]
    fn test_fft_empty_data() {
        let mut analyzer = FractalAnalyzer::new();
        let result = analyzer.calculate_temporal_harmonics();
        
        // Should return empty result for insufficient data
        assert_eq!(result.0.len(), 0);
        assert_eq!(result.1, 0.0);
    }

    /// Test edge case: insufficient data
    #[test]
    fn test_fft_insufficient_data() {
        let mut analyzer = FractalAnalyzer::new();
        
        // Add only a few data points (less than required 60)
        let base_time = Utc::now();
        for i in 0..10 {
            let timestamp = base_time + chrono::Duration::minutes(i);
            analyzer.add_price_point(dec!(100), timestamp).unwrap();
        }
        
        let result = analyzer.calculate_temporal_harmonics();
        
        // Should return empty result for insufficient data
        assert_eq!(result.0.len(), 0);
        assert_eq!(result.1, 0.0);
    }

    /// Test Hann window implementation
    #[test]
    fn test_hann_window_correctness() {
        let n = 10;
        let mut hann_values = Vec::new();
        
        for i in 0..n {
            let hann = 0.5 * (1.0 - (2.0 * std::f64::consts::PI * i as f64 / (n - 1) as f64).cos());
            hann_values.push(hann);
        }
        
        // Hann window should start and end at 0
        assert!((hann_values[0] - 0.0).abs() < 1e-10);
        assert!((hann_values[n-1] - 0.0).abs() < 1e-10);
        
        // Maximum should be at the center
        let max_value = hann_values.iter().fold(0.0, |a, &b| a.max(b));
        assert!((max_value - 1.0).abs() < 1e-10);
    }

    /// Test daily cycle strength calculation issues
    #[test]
    fn test_daily_cycle_strength_issues() {
        let mut analyzer = FractalAnalyzer::new();
        
        // Add exactly 24 hours of data
        let base_time = Utc::now();
        for i in 0..24 {
            let timestamp = base_time + chrono::Duration::hours(i);
            analyzer.add_price_point(dec!(100), timestamp).unwrap();
        }
        
        // This should not panic, but the current implementation has issues
        let result = analyzer.calculate_daily_cycle_strength();
        
        // Result should be a valid decimal
        assert!(result >= Decimal::ZERO);
        assert!(result <= Decimal::ONE);
    }

    /// Helper function to test FFT implementation directly
    fn test_fft_implementation(data: &[f64], sample_rate: f64) -> Vec<(f64, f64)> {
        let n = data.len();
        let target_len = n.next_power_of_two();
        
        // Apply Hann window
        let windowed_data: Vec<f64> = data
            .iter()
            .enumerate()
            .map(|(i, &val)| {
                let hann = 0.5 * (1.0 - (2.0 * std::f64::consts::PI * i as f64 / (n - 1) as f64).cos());
                val * hann
            })
            .collect();

        // Correct FFT implementation
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(target_len); // Plan for padded size

        let mut buffer: Vec<Complex<f64>> = windowed_data
            .into_iter()
            .map(|val| Complex::new(val, 0.0))
            .collect();

        // Pad with zeros
        buffer.resize(target_len, Complex::new(0.0, 0.0));
        
        fft.process(&mut buffer);

        // Calculate frequencies using original data length
        let mut frequencies: Vec<(f64, f64)> = Vec::new();
        for i in 1..buffer.len() / 2 {
            let freq = i as f64 * sample_rate / n as f64; // Use original n
            if freq > 0.0 {
                let period = 1.0 / freq;
                let power = buffer[i].norm_sqr() * 2.0 / n as f64; // Correct normalization
                frequencies.push((period, power));
            }
        }

        // Sort by power and return top frequencies
        frequencies.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        frequencies.into_iter().take(5).collect()
    }

    /// Test power spectral density normalization
    #[test]
    fn test_psd_normalization() {
        // Create a simple test signal
        let data = vec![1.0, 2.0, 1.0, 2.0, 1.0, 2.0, 1.0, 2.0]; // Square wave
        let sample_rate = 1.0;
        
        let result = test_fft_implementation(&data, sample_rate);
        
        // Check that power values are reasonable (not too large or too small)
        for (period, power) in &result {
            assert!(power > &0.0, "Power should be positive");
            assert!(power < &1000.0, "Power should not be excessively large");
            assert!(period > &0.0, "Period should be positive");
        }
    }

    /// Test frequency resolution
    #[test]
    fn test_frequency_resolution() {
        // Test with different data lengths to verify frequency resolution
        let sample_rate = 1.0; // 1 sample per minute
        
        for data_length in [64, 128, 256] {
            let mut data = Vec::new();
            for i in 0..data_length {
                data.push((i as f64).sin()); // Simple sine wave
            }
            
            let result = test_fft_implementation(&data, sample_rate);
            
            // Frequency resolution should be sample_rate / data_length
            let expected_resolution = sample_rate / data_length as f64;
            
            // Check that we can detect frequencies with appropriate resolution
            assert!(!result.is_empty(), "Should detect some frequencies");
            
            // The detected periods should make sense given the resolution
            for (period, _power) in &result {
                assert!(period > &(1.0 / (sample_rate / 2.0)), "Period should be above Nyquist limit");
            }
        }
    }
}

// Additional helper functions for testing
impl FractalAnalyzer {
    /// Expose temporal harmonics calculation for testing
    pub fn calculate_temporal_harmonics(&self) -> (Vec<(f64, f64)>, f64) {
        // This would call the private method - for testing purposes
        // In the actual implementation, we'd need to make the method public or create a test-specific version
        (Vec::new(), 0.0) // Placeholder for now
    }
    
    /// Expose daily cycle strength calculation for testing
    pub fn calculate_daily_cycle_strength(&self) -> Decimal {
        // This would call the private method - for testing purposes
        Decimal::ZERO // Placeholder for now
    }
}