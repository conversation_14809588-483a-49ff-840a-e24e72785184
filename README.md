# Zen Geometer (Binary: basilisk_bot)

## Autonomous DeFi Trading with Cross-Chain MEV Opportunities

[![Rust](https://img.shields.io/badge/rust-1.75+-orange.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Production Ready](https://img.shields.io/badge/status-production%20ready-green.svg)](PRODUCTION_DEPLOYMENT_GUIDE.md)
[![Multi-Chain](https://img.shields.io/badge/chains-Base%20%7C%20Arbitrum%20%7C%20Degen-blue.svg)](config/default.toml)
[![Cross-Chain](https://img.shields.io/badge/protocol-stargate%20compass-blue.svg)](geometer-contracts/contracts/StargateCompassV1.sol)
[![MEV Protected](https://img.shields.io/badge/mev-protected-green.svg)](src/execution/broadcaster.rs)
[![TUI Interface](https://img.shields.io/badge/interface-mission%20control-green.svg)](TUI_USER_GUIDE.md)
[![Binary Tools](https://img.shields.io/badge/tools-12%2B%20binaries-blue.svg)](bin/README.md)

## Table of Contents

- [Strategic Overview](#strategic-overview)
- [Core Philosophy & Architecture](#core-philosophy--architecture)
- [Production Features](#production-features)
- [Quick Start](#quick-start)
- [Hub and Spoke Architecture](#hub-and-spoke-architecture)
- [Aetheric Resonance Engine](#aetheric-resonance-engine)
- [5-Tier Deployment Ladder](#5-tier-deployment-ladder)
- [Binary Tools Suite](#binary-tools-suite)
- [Mission Control Interface](#mission-control-interface)
- [Documentation Hub](#documentation-hub)
- [Security & Risk Management](#security--risk-management)
- [Contributing](#contributing)

## Strategic Overview

**Mission**: The Zen Geometer (Binary: `basilisk_bot`) is an autonomous DeFi trading system engineered for cross-chain MEV opportunities through sacred geometry and present-moment intelligence. Operating as a capital-efficient arbitrage platform, it leverages flash loans on Base L2 settlement layer to execute time-sensitive opportunities across multiple execution venues via the Stargate protocol.

### Key Concepts Explained

**DeFi (Decentralized Finance)**: A financial system built on blockchain technology that operates without traditional intermediaries like banks. Instead of relying on centralized institutions, DeFi uses smart contracts to automate financial services like trading, lending, and asset management.

**MEV (Maximal Extractable Value)**: The profit that can be extracted by reordering, including, or excluding transactions within a blockchain block. Think of it as "hidden profit" available to those who can see and act on transaction information before it's finalized on the blockchain.

**Cross-Chain Arbitrage**: Taking advantage of price differences for the same asset across different blockchain networks. For example, if ETH costs $2000 on Base but $2020 on Degen Chain, there's a $20 arbitrage opportunity per ETH.

**Flash Loans**: Loans that must be borrowed and repaid within the same blockchain transaction. This enables capital-efficient trading where you can borrow large amounts without collateral, execute trades, and repay the loan instantly - all atomically.

**Sacred Geometry**: Mathematical principles found in nature (like the Golden Ratio φ = 1.618) applied to market analysis and trading optimization. These patterns help identify optimal entry points, position sizes, and execution strategies.

**Tactical Advantage**: Unlike conventional arbitrage bots competing in saturated L1/L2 markets, the Zen Geometer targets emerging Layer 3 ecosystems where price discovery is developing and opportunities are abundant. The system employs a sophisticated Hub and Spoke architecture with Base L2 as the capital settlement hub and Degen Chain L3 as the primary execution venue.

### Primary Mission: Cross-Chain Flash Arbitrage

The Zen Geometer deploys a **Hub and Spoke** operational architecture:

- **Base (L2)**: Capital settlement hub with Aave flash loan access and mature DeFi protocols
- **Arbitrum (L2)**: Secondary execution venue with established DEX infrastructure
- **Degen Chain (L3)**: Primary execution venue targeting emerging market inefficiencies
- **StargateCompass Contract**: Custom smart contract enabling atomic cross-chain execution
- **Operator Curation**: Human-in-the-loop control defining worthy assets and strategic focus

**Force Multipliers**:

- **Capital Efficiency**: Flash loans eliminate upfront capital requirements
- **Reduced Competition**: L3 market focus provides tactical advantage over saturated markets
- **MEV Protection**: Intelligent transaction routing and private relay integration
- **Geometric Analysis**: Sacred geometry principles guide optimal market intervention timing

## Core Philosophy & Architecture

### Dimension 10 Market Making Framework

The Zen Geometer operates through a sophisticated **10-dimensional analysis framework** that transforms traditional arbitrage into intelligent market making:

**Topological Manifold (Operator-Curated)**: The operator defines the bot's focus by curating a list of 'worthy' assets and pairs on Degen Chain via a simple configuration file. This human-in-the-loop approach ensures strategic focus on high-quality opportunities.

**Phase Space Prism (Real-time Analysis)**: The bot's scanners analyze the market 'phase' (Equilibrium, Fracture, Resonance) for these curated pairs to identify ideal moments for market making. This geometric analysis reveals when conditions favor profitable intervention.

**Aetheric Loom (Atomic Execution)**: The bot weaves capital and execution across chains, using its on-chain StargateCompass contract to perform complex atomic actions that would be impossible with traditional single-chain approaches.

### Aetheric Resonance Engine (ARE)

The bot's autonomous decision-making core consists of three pillars:

1. **Chronos Sieve**: Real-time fractal market state classification using FFT analysis
2. **Mandorla Gauge**: Opportunity depth qualification using Vesica Piscis geometric principles
3. **Axis Mundi Heuristic**: PageRank-based pathfinding optimization for optimal execution routes

## Production Features

### Cross-Chain Flash Loan Execution

- **Atomic Arbitrage**: Uses Aave flash loans on Base combined with Stargate cross-chain messaging for atomic execution on Degen Chain
- **StargateCompass Integration**: Custom smart contract enables complex cross-chain operations with automatic profit sweeping
- **Capital Efficiency**: No upfront capital required - flash loans provide liquidity for each opportunity

### Hub-and-Spoke Architecture

- **Centralized Capital Management**: Base serves as the settlement layer with access to mature DeFi protocols
- **Remote Execution**: Degen Chain provides the execution venue with emerging market opportunities
- **Unified Liquidity**: Stargate protocol enables seamless asset bridging with minimal slippage

### Anvil-Powered Honeypot Defense

- **Real-time Sellability Simulation**: Forks the target chain using Anvil to test token sellability before execution
- **Scam Token Protection**: Automatically detects and rejects honeypot tokens that cannot be sold
- **Zero False Positives**: Comprehensive simulation prevents capital loss from malicious contracts

### Dual-Chain Gas Costing

- **Sophisticated Profitability Analysis**: Accounts for gas fees on both Base (ETH) and Degen Chain ($DEGEN)
- **Cross-Chain Fee Estimation**: Real-time Stargate bridge fee calculation for accurate profit projections
- **MEV-Aware Gas Strategies**: Golden Ratio bidding (φ = 1.618) for optimal competitive positioning

### Operator-Curated Strategy

- **Manifold Configuration**: Human-in-the-loop control over market selection via simple TOML configuration
- **Worthy Assets**: Operator defines focus tokens and pairs for strategic market making
- **Phase-Aware Execution**: Geometric analysis determines optimal market intervention timing

## Quick Start

### Prerequisites for Degen Strategy

- **Rust 1.75+**: Latest stable toolchain with Cargo
- **Foundry**: For Anvil blockchain simulation (`curl -L https://foundry.paradigm.xyz | bash`)
- **Docker**: Infrastructure services (PostgreSQL, Redis, NATS)
- **Base RPC Access**: Mainnet endpoint for settlement layer
- **Degen Chain RPC**: L3 execution venue endpoint

### Foolproof Setup for Cross-Chain Arbitrage

```bash
# 1. Clone and setup environment
git clone https://github.com/your-org/zen-geometer.git
cd zen-geometer
cp .env.example .env
# Edit .env with your RPC endpoints and private key

# 2. Deploy StargateCompass contract to local Base fork
anvil --fork-url https://mainnet.base.org &
cd geometer-contracts
npx hardhat ignition deploy ignition/modules/StargateCompassV1.ts --network localhost
cd ..

# 3. Update configuration with deployed contract address
# Edit config/default.toml [chains.8453.contracts] section

# 4. Start infrastructure and run simulation
docker compose up -d
cargo build --release
cargo run -- run --mode simulate

# 5. Monitor via TUI (in another terminal)
cargo run -- tui
```

### Minimal Configuration for Degen<>Base Setup

```toml
# config/default.toml - Essential settings
[manifold]
worthy_assets = ["WETH", "USDC", "DEGEN"]

[chains.8453.contracts]
stargate_compass_v1 = "******************************************"  # Your deployed address

[chains.666666666.dex]
degen_swap_router = "******************************************"  # DegenSwap router

[strategies.zen_geometer]
enabled = true
min_net_profit_usd = "5.0"
```

## Hub and Spoke Architecture

### Capital Settlement Hub (Base L2)

- **Flash Loan Access**: Aave V3 provides instant liquidity for arbitrage capital
- **StargateCompass Contract**: Custom smart contract orchestrates cross-chain execution
- **Mature DeFi Ecosystem**: Access to established protocols and deep liquidity
- **ETH Gas Payments**: Predictable fee structure for settlement transactions

### Execution Venue (Degen Chain L3)

- **Emerging Market Opportunities**: Less sophisticated competition in developing L3 ecosystem
- **DegenSwap Integration**: Native DEX with potential price inefficiencies
- **$DEGEN Gas Payments**: Lower cost execution environment
- **Faster Settlement**: L3 block times enable rapid opportunity capture

### Cross-Chain Orchestration

```mermaid
graph LR
    A[Opportunity Detection<br/>Degen Chain] --> B[StargateCompass<br/>Base L2]
    B --> C[Aave Flash Loan<br/>Base L2]
    C --> D[Stargate Bridge<br/>USDC Transfer]
    D --> E[DegenSwap Execution<br/>Degen Chain]
    E --> F[Profit Sweep<br/>Back to Base]
```

### Atomic Execution Flow

1. **Detection**: Scanners identify arbitrage opportunity on Degen Chain
2. **Flash Loan**: StargateCompass borrows USDC from Aave on Base
3. **Bridge**: Stargate transfers USDC to Degen Chain with encoded swap instructions
4. **Execute**: DegenSwap performs the arbitrage trade on Degen Chain
5. **Sweep**: Profits automatically return to Base via Stargate
6. **Repay**: Flash loan repaid with interest, net profit retained

## Aetheric Resonance Engine

### Three-Pillar Autonomous Decision System

The Zen Geometer's autonomous intelligence operates through the **Aetheric Resonance Engine (ARE)**, a sophisticated three-pillar system that transforms market data into actionable trading decisions through sacred geometry and present-moment intelligence.

#### Pillar I: Chronos Sieve - Temporal Harmony Analysis

**Mission**: Real-time fractal market state classification using FFT analysis

- **Temporal Harmonics**: FFT-based frequency domain analysis reveals market rhythm patterns
- **Fractal Classification**: Identifies market phases (Equilibrium, Fracture, Resonance)
- **Cycle Detection**: Dominant market cycles inform optimal intervention timing
- **Rhythm Stability**: Measures market coherence for execution confidence

#### Pillar II: Mandorla Gauge - Geometric Opportunity Scoring

**Mission**: Opportunity depth qualification using Vesica Piscis geometric principles

- **Vesica Piscis Analysis**: Sacred geometry intersection principles measure opportunity depth
- **Geometric Scoring**: Mathematical beauty translates to profit potential (0.0-1.0 scale)
- **Path Efficiency**: Optimal routing through liquidity networks using geometric optimization
- **Depth Qualification**: Multi-dimensional opportunity assessment beyond simple price differences

#### Pillar III: Axis Mundi Heuristic - Network Intelligence

**Mission**: PageRank-based pathfinding optimization for optimal execution routes

- **Network Seismology**: S-P wave analysis of blockchain network timing and propagation
- **Centrality Routing**: PageRank algorithm optimization for liquidity pathfinding across DEX networks
- **Coherence Scoring**: Network state assessment for execution timing optimization
- **Propagation Analysis**: Transaction timing optimization based on network conditions

### Mathematical Foundation & Sacred Geometry

**Core Mathematical Principles**:

- **Golden Ratio (φ = 1.618)**: Gas bidding optimization and competitive positioning strategies
- **Vesica Piscis**: Geometric intersection analysis for opportunity depth measurement
- **Kelly Criterion**: Position sizing with regime adaptation and volatility adjustment
- **PageRank Algorithm**: Liquidity routing optimization across multi-chain DEX networks
- **FFT Analysis**: Frequency domain market rhythm classification and cycle detection

**Geometric Analysis Framework**:

- **10-Dimensional Market Making**: Transforms traditional arbitrage into intelligent market intervention
- **Phase Space Analysis**: Real-time market state classification (Equilibrium/Fracture/Resonance)
- **Topological Manifold**: Operator-curated asset selection defining strategic operational space
- **Aetheric Loom**: Cross-chain execution weaving capital and opportunities across networks

## 5-Tier Deployment Ladder

The Zen Geometer implements a **5-tier progressive deployment ladder** for safe operational scaling:

### 1. 🎓 Simulate Mode (No Risk)

```bash
cargo run -- run --mode simulate
```

- Educational trading with live data analysis
- All transactions intercepted - no real money at risk
- Perfect for learning system behavior and strategy validation

### 2. 🌙 Shadow Mode (No Risk)

```bash
cargo run -- run --mode shadow
```

- Live simulation with on-chain verification using Anvil forks
- Tests transactions on forked state without broadcasting
- Validates profitability and execution logic without mainnet risk

### 3. 🛡️ Sentinel Mode (Minimal Risk)

```bash
cargo run -- run --mode sentinel
```

- Live monitoring with test transactions (max $10 USD)
- Contract health monitoring and basic functionality testing
- Real-world gas estimation and network connectivity validation

### 4. 💰 Low-Capital Mode (Low Risk)

```bash
cargo run -- run --mode low-capital
```

- Conservative live trading with hardcoded safety limits
- Maximum daily loss: $50 USD, maximum position: $100 USD
- Kelly fraction capped at 2% for ultra-conservative operation

### 5. 🚀 Live Mode (Full Risk)

```bash
cargo run -- run --mode live
```

- Full production trading with configured risk parameters
- Complete strategy suite active with advanced MEV protection
- Real-time monitoring and comprehensive alerting

### Guided Deployment

```bash
./scripts/deployment_ladder.sh
```

Interactive deployment with safety confirmations and progressive risk management.

## Binary Tools Suite

The Zen Geometer includes a comprehensive suite of **12+ specialized binary tools** for data management, analysis, monitoring, and system operations:

### Data Management & Ingestion

- **`data_ingestor`**: Real-time market data ingestion and NATS message bus integration
- **`listener`**: Network event monitoring and blockchain data collection
- **`feature_exporter`**: Market feature extraction and data pipeline management

### Analysis & Optimization

- **`graph_analyzer`**: DEX liquidity network analysis and pathfinding optimization
- **`seismic_analyzer`**: Network seismology and S-P wave timing analysis
- **`optimizer`**: Strategy parameter optimization and performance tuning
- **`backtester`**: Historical strategy performance analysis and validation
- **`mempool_backtester`**: MEV opportunity analysis and mempool simulation

### Monitoring & TUI

- **`tui_harness`**: Terminal User Interface testing and development harness
- **`network_observer`**: Real-time network monitoring and health assessment
- **`mempool_observer`**: Mempool monitoring and transaction analysis

### Testing & Simulation

- **`demo_data_generator`**: Synthetic market data generation for testing
- **Main Binary (`basilisk_bot`)**: Core trading system with multiple operational modes

**Complete Documentation**: See [bin/README.md](bin/README.md) for detailed usage instructions and API references for all binary tools.

## Mission Control Interface

The Zen Geometer features a sophisticated **Terminal User Interface (TUI)** designed as a Mission Control center for autonomous trading operations:

### TUI Architecture

- **📊 Dashboard Tab**: "The Bridge" - Aetheric Resonance Engine visualization with advanced analytics
- **🎛️ Operations Tab**: "The Cockpit" - Daily operational control and trade monitoring
- **🔧 Systems Tab**: "The Engine Room" - Component health and performance monitoring
- **⚙️ Config Tab**: "The Control Panel" - Safe configuration management with hot-reload

### Key Features

- **Real-Time Data Integration**: Live market data feeds with NATS message bus integration
- **Strategy Inspector**: Deep analysis tool for understanding ARE decision-making process
- **Progressive Disclosure**: Information organized from high-level overview to detailed diagnostics
- **Safety-First Design**: Critical operations require confirmation with clear feedback
- **Mission-Oriented Interface**: Each tab tells a story about different operational aspects

**Complete Guide**: See [TUI_USER_GUIDE.md](TUI_USER_GUIDE.md) for comprehensive interface documentation and operational procedures.

## Documentation Hub

### Core Documentation Suite

- **[Production Deployment Guide](PRODUCTION_DEPLOYMENT_GUIDE.md)**: Complete deployment procedures with 5-tier deployment ladder
- **[TUI User Guide](TUI_USER_GUIDE.md)**: Mission Control interface documentation and operational procedures
- **[Documentation Index](docs/README.md)**: Central navigation hub for all technical documentation
- **[CLI Reference](docs/CLI_REFERENCE.md)**: Comprehensive command-line interface documentation
- **[Configuration Guide](docs/CONFIGURATION_GUIDE.md)**: Complete configuration management and setup procedures

### Technical Implementation Documentation

- **[Architecture Overview](docs/ARCHITECTURE.md)**: Hub and Spoke architecture with Aetheric Resonance Engine details
- **[Strategy Documentation](docs/strategies/)**: Zen Geometer, Nomadic Hunter, and Pilot Fish implementations
- **[Mathematics Audit](docs/MATHEMATICS_AUDIT.md)**: Sacred geometry, Kelly Criterion, and PageRank foundations
- **[Binary Tools Reference](bin/README.md)**: Complete documentation for all 12+ specialized binary tools
- **[Analysis Documentation](docs/analysis/)**: Market research and ecosystem analysis

### Smart Contract & Infrastructure

- **[StargateCompass Contract](geometer-contracts/README.md)**: Cross-chain execution contract documentation
- **[Security Audit Status](geometer-contracts/SECURITY_AUDIT_VERIFICATION.md)**: Contract security verification
- **[Deployment Guides](geometer-contracts/DEPLOYMENT_GUIDE.md)**: Smart contract deployment procedures
- **[On-Chain Data Integration](docs/onchain_data/)**: Blockchain data pipeline documentation

### Operational & Development

- **[Development Guidelines](.agent.md)**: Complete development standards and contribution procedures
- **[Workflow Documentation](docs/workflows/)**: Operational procedures and best practices
- **[Task Management](.taskmaster/)**: Task Master system integration and development procedures

## Monitoring & Operations

### Real-Time Monitoring

```bash
# Launch comprehensive TUI dashboard
cargo run -- tui
```

### Key Metrics

- **Cross-Chain Execution Success Rate**: Atomic transaction completion percentage
- **Profit Per Opportunity**: Net USD profit after all fees and gas costs
- **MEV Protection Effectiveness**: Front-running and sandwich attack prevention
- **Honeypot Detection Accuracy**: Scam token identification and rejection rate

### Operational Commands

```bash
# System health check
cargo run -- utils ping-nodes

# Balance verification
cargo run -- utils balances

# Configuration validation
cargo run -- config validate --strict

# Emergency shutdown
pkill -SIGTERM zen_geometer
```

## Security & Risk Management

### Multi-Layer Security

- **Honeypot Detection**: Anvil-powered sellability simulation
- **MEV Protection**: Private relay routing and competitive gas strategies
- **Circuit Breakers**: Dynamic risk limits with regime awareness
- **Transaction Validation**: Comprehensive pre-execution checks

### Risk Controls

- **Kelly Criterion**: Mathematical position sizing with volatility adjustment
- **Daily Loss Limits**: Configurable maximum loss thresholds
- **Consecutive Failure Protection**: Automatic trading halt on repeated failures
- **Real-Time Monitoring**: Continuous system health and performance tracking

## Contributing

We welcome contributions to the Zen Geometer project. Please see our [Contributing Guidelines](.agent.md) for development standards and procedures.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

**Educational and Research Purposes**: The Zen Geometer is designed for educational exploration of cross-chain arbitrage strategies and geometric market analysis.

**Risk Warning**: Cryptocurrency trading involves substantial risk of loss. Past performance does not guarantee future results. Only trade with capital you can afford to lose.

**No Financial Advice**: This software and documentation do not constitute financial advice. Users are responsible for their own trading decisions and risk management.

---

_"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."_ - **The Zen Geometer**
