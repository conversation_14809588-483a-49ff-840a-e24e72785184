// Mock Price Oracle for Testing
// Provides deterministic price data for test scenarios

use async_trait::async_trait;
use ethers::types::Address;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::collections::HashMap;
use anyhow::Result;

use basilisk_bot::data::oracle::PriceOracle;

pub struct MockPriceOracle {
    prices: HashMap<Address, Decimal>,
    symbols: HashMap<Address, String>,
}

impl MockPriceOracle {
    pub fn new() -> Self {
        Self {
            prices: HashMap::new(),
            symbols: HashMap::new(),
        }
    }

    /// Add a price for a specific token
    pub fn add_price(&mut self, token: Address, price: Decimal, symbol: &str) {
        self.prices.insert(token, price);
        self.symbols.insert(token, symbol.to_string());
    }

    /// Create a mock oracle with common test tokens
    pub fn with_test_tokens() -> Self {
        let mut oracle = Self::new();
        
        // Add common test token prices
        oracle.add_price(
            Address::from([0x11; 20]), // Mock WETH
            dec!(3000.0),
            "WETH"
        );
        oracle.add_price(
            Address::from([0x22; 20]), // Mock USDC
            dec!(1.0),
            "USDC"
        );
        oracle.add_price(
            Address::from([0x33; 20]), // Mock USDT
            dec!(1.0),
            "USDT"
        );
        oracle.add_price(
            Address::from([0x44; 20]), // Mock DAI
            dec!(1.0),
            "DAI"
        );
        
        oracle
    }

    /// Update price for existing token
    pub fn update_price(&mut self, token: Address, new_price: Decimal) -> Result<()> {
        if self.prices.contains_key(&token) {
            self.prices.insert(token, new_price);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Token not found in mock oracle"))
        }
    }

    /// Simulate price volatility
    pub fn add_volatility(&mut self, token: Address, volatility_percent: Decimal) -> Result<()> {
        if let Some(&current_price) = self.prices.get(&token) {
            // Simple volatility simulation: ±volatility_percent
            let volatility_factor = dec!(1.0) + (volatility_percent / dec!(100.0));
            let new_price = current_price * volatility_factor;
            self.prices.insert(token, new_price);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Token not found for volatility simulation"))
        }
    }
}

#[async_trait]
impl PriceOracle for MockPriceOracle {
    async fn get_price(&self, token: Address) -> Result<Decimal> {
        Ok(*self.prices.get(&token).unwrap_or(&Decimal::ZERO))
    }

    async fn get_prices(&self, tokens: &[Address]) -> Result<HashMap<Address, Decimal>> {
        let mut result = HashMap::new();
        for token in tokens {
            if let Some(price) = self.prices.get(token) {
                result.insert(*token, *price);
            }
        }
        Ok(result)
    }

    async fn get_token_symbols(&self, token_a: Address, token_b: Address) -> Result<(String, String)> {
        let symbol_a = self.symbols.get(&token_a).cloned().unwrap_or_else(|| "UNKNOWN".to_string());
        let symbol_b = self.symbols.get(&token_b).cloned().unwrap_or_else(|| "UNKNOWN".to_string());
        Ok((symbol_a, symbol_b))
    }
}

impl Default for MockPriceOracle {
    fn default() -> Self {
        Self::new()
    }
}