#!/bin/bash

# Contract verification script
echo "🔍 Verifying StargateCompassV1 contract on BaseScan..."

# Set your API key
API_KEY="78WD6QFJ1W7RD5WWQJBRVSNS9ZDEFAQHP6"

# Update .env file with API key
if grep -q "BASESCAN_API_KEY" .env; then
    sed -i "s/BASESCAN_API_KEY=.*/BASESCAN_API_KEY=$API_KEY/" .env
else
    echo "BASESCAN_API_KEY=$API_KEY" >> .env
fi

echo "✅ API key set in .env file"

# Verify the contract
echo "🔧 Running verification with Hardhat..."
npx hardhat verify --network base 0x10fb5800FA746C592f013c51941F28b2D8Fb2c6B \
    "0xe20fCBdBfFC4Dd138cE8b2E6FBb6CB49777ad64D" \
    "0x53Bf833A5d6c4ddA888F69c22C88E1f34A4451a5"

# Check verification status
echo "🔍 Checking verification status..."
curl -s "https://api.basescan.org/api?module=contract&action=getabi&address=0x10fb5800FA746C592f013c51941F28b2D8Fb2c6B&apikey=$API_KEY"

echo ""
echo "📋 Contract verification complete!"
echo "🔗 View on BaseScan: https://basescan.org/address/0x10fb5800FA746C592f013c51941F28b2D8Fb2c6B"