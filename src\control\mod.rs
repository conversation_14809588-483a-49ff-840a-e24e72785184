// NOMADIC HUNTER: Control Systems Module
// WHY: Centralized control systems for territory management and resilience
// HOW: EcologicalSurveyor, SystemMonitor (OROBOROS), and MigrationController

pub mod balance_manager;
pub mod directive_manager;
pub mod ecological_surveyor;
pub mod migration_controller;
pub mod service_control_handler;
pub mod system_monitor;
pub mod treasury_manager;

pub use balance_manager::BalanceManager;
pub use ecological_surveyor::{EcologicalSurveyor, TerritoryHealth, TerritoryStatus};
pub use migration_controller::MigrationController;
pub use system_monitor::SystemMonitor;
pub use directive_manager::DirectiveManager;
pub use crate::shared_types::{Directive, DirectiveCommand};
pub use treasury_manager::TreasuryManager;
