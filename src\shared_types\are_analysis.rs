// MISSION: ARE Analysis Report Types - Living Codex Foundation
// WHY: Enable the TUI to display the Aetheric Resonance Engine's decision-making process
// HOW: Comprehensive data structures for narrative-driven opportunity analysis

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;

use super::{GeometricScore, NetworkResonanceState, TemporalHarmonics};

/// Comprehensive report of the ARE's analysis of an opportunity
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AREAnalysisReport {
    /// Unique identifier for this analysis
    pub analysis_id: String,
    
    /// The opportunity being analyzed
    pub opportunity_id: String,
    pub opportunity_type: String,
    pub path_description: String,
    pub potential_profit_usd: Decimal,
    
    /// Raw analysis results from each pillar
    pub temporal_harmonics: TemporalHarmonics,
    pub geometric_score: GeometricScore,
    pub network_resonance: NetworkResonanceState,
    
    /// Weighted calculations applied
    pub temporal_weight: Decimal,
    pub geometric_weight: Decimal,
    pub network_weight: Decimal,
    
    /// Final decision metrics
    pub final_resonance_score: Decimal,
    pub min_threshold: Decimal,
    pub decision: AREDecision,
    pub decision_reasoning: String,
    
    /// Metadata
    pub timestamp: u64,
    pub chain_id: u64,
}

impl AREAnalysisReport {
    /// Create a new ARE analysis report
    pub fn new(
        opportunity_id: String,
        opportunity_type: String,
        path_description: String,
        potential_profit_usd: Decimal,
        temporal_harmonics: TemporalHarmonics,
        geometric_score: GeometricScore,
        network_resonance: NetworkResonanceState,
        temporal_weight: Decimal,
        geometric_weight: Decimal,
        network_weight: Decimal,
        final_resonance_score: Decimal,
        min_threshold: Decimal,
        decision: AREDecision,
        decision_reasoning: String,
        chain_id: u64,
    ) -> Self {
        Self {
            analysis_id: Uuid::new_v4().to_string(),
            opportunity_id,
            opportunity_type,
            path_description,
            potential_profit_usd,
            temporal_harmonics,
            geometric_score,
            network_resonance,
            temporal_weight,
            geometric_weight,
            network_weight,
            final_resonance_score,
            min_threshold,
            decision,
            decision_reasoning,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            chain_id,
        }
    }
    
    /// Convert the analysis report into a narrative story for the Living Codex
    pub fn to_narrative(&self) -> String {
        let mut narrative = String::new();
        
        // Header with opportunity details
        narrative.push_str(&format!(
            "🔍 [Evaluating Opportunity: {}]\n",
            &self.opportunity_id[..8] // Show first 8 chars of ID
        ));
        
        narrative.push_str(&format!(
            "📈 Type: {} | Path: {}\n",
            self.opportunity_type,
            self.path_description
        ));
        
        narrative.push_str(&format!(
            "💰 Potential Profit: ${:.2}\n",
            self.potential_profit_usd
        ));
        
        narrative.push_str("═══════════════════════════════════════\n");
        
        // Pillar Analysis - The Three Pillars of Wisdom
        narrative.push_str(&format!(
            "⏰ [Chronos Sieve Analysis] (Weight: {:.1}x):\n   {}\n\n",
            self.temporal_weight,
            self.temporal_harmonics.to_narrative()
        ));
        
        narrative.push_str(&format!(
            "🔺 [Mandorla Gauge Analysis] (Weight: {:.1}x):\n   {}\n\n",
            self.geometric_weight,
            self.geometric_score.to_narrative()
        ));
        
        narrative.push_str(&format!(
            "🌊 [Network Seismology Analysis] (Weight: {:.1}x):\n   {}\n",
            self.network_weight,
            self.network_resonance.to_narrative()
        ));
        
        narrative.push_str("═══════════════════════════════════════\n");
        
        // Final Decision with dramatic flair
        narrative.push_str(&format!(
            "🎯 Final Resonance Score: {:.1} / 10.0 (Threshold: {:.1})\n",
            self.final_resonance_score,
            self.min_threshold
        ));
        
        match &self.decision {
            AREDecision::Approved => {
                narrative.push_str("✅ Verdict: APPROVED for Execution\n");
                narrative.push_str("🚀 The stars align! This opportunity resonates with harmonic perfection.\n");
            }
            AREDecision::Rejected { reason } => {
                narrative.push_str("❌ Verdict: REJECTED\n");
                narrative.push_str(&format!("🔍 Reason: {}\n", reason));
                narrative.push_str("⚠️  The geometric patterns warn against this path.\n");
            }
        }
        
        if !self.decision_reasoning.is_empty() {
            narrative.push_str(&format!("💭 Analysis: {}\n", self.decision_reasoning));
        }
        
        narrative
    }
    
    /// Get a compact summary for list views
    pub fn to_summary(&self) -> String {
        let status_icon = match &self.decision {
            AREDecision::Approved => "✅",
            AREDecision::Rejected { .. } => "❌",
        };
        
        format!(
            "{} {} | ${:.2} | Score: {:.1}/10",
            status_icon,
            &self.opportunity_id[..8],
            self.potential_profit_usd,
            self.final_resonance_score
        )
    }
}

/// The final decision made by the Aetheric Resonance Engine
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AREDecision {
    /// Opportunity approved for execution
    Approved,
    /// Opportunity rejected with specific reason
    Rejected { reason: String },
}

impl AREDecision {
    pub fn is_approved(&self) -> bool {
        matches!(self, AREDecision::Approved)
    }
    
    pub fn rejection_reason(&self) -> Option<&str> {
        match self {
            AREDecision::Rejected { reason } => Some(reason),
            _ => None,
        }
    }
}

/// Builder pattern for constructing ARE analysis reports
pub struct AREAnalysisReportBuilder {
    opportunity_id: Option<String>,
    opportunity_type: Option<String>,
    path_description: Option<String>,
    potential_profit_usd: Option<Decimal>,
    temporal_harmonics: Option<TemporalHarmonics>,
    geometric_score: Option<GeometricScore>,
    network_resonance: Option<NetworkResonanceState>,
    temporal_weight: Option<Decimal>,
    geometric_weight: Option<Decimal>,
    network_weight: Option<Decimal>,
    final_resonance_score: Option<Decimal>,
    min_threshold: Option<Decimal>,
    decision: Option<AREDecision>,
    decision_reasoning: Option<String>,
    chain_id: Option<u64>,
}

impl AREAnalysisReportBuilder {
    pub fn new() -> Self {
        Self {
            opportunity_id: None,
            opportunity_type: None,
            path_description: None,
            potential_profit_usd: None,
            temporal_harmonics: None,
            geometric_score: None,
            network_resonance: None,
            temporal_weight: None,
            geometric_weight: None,
            network_weight: None,
            final_resonance_score: None,
            min_threshold: None,
            decision: None,
            decision_reasoning: None,
            chain_id: None,
        }
    }
    
    pub fn opportunity_id(mut self, id: String) -> Self {
        self.opportunity_id = Some(id);
        self
    }
    
    pub fn opportunity_type(mut self, type_name: String) -> Self {
        self.opportunity_type = Some(type_name);
        self
    }
    
    pub fn path_description(mut self, description: String) -> Self {
        self.path_description = Some(description);
        self
    }
    
    pub fn potential_profit_usd(mut self, profit: Decimal) -> Self {
        self.potential_profit_usd = Some(profit);
        self
    }
    
    pub fn temporal_harmonics(mut self, harmonics: TemporalHarmonics) -> Self {
        self.temporal_harmonics = Some(harmonics);
        self
    }
    
    pub fn geometric_score(mut self, score: GeometricScore) -> Self {
        self.geometric_score = Some(score);
        self
    }
    
    pub fn network_resonance(mut self, resonance: NetworkResonanceState) -> Self {
        self.network_resonance = Some(resonance);
        self
    }
    
    pub fn weights(mut self, temporal: Decimal, geometric: Decimal, network: Decimal) -> Self {
        self.temporal_weight = Some(temporal);
        self.geometric_weight = Some(geometric);
        self.network_weight = Some(network);
        self
    }
    
    pub fn final_decision(
        mut self,
        score: Decimal,
        threshold: Decimal,
        decision: AREDecision,
        reasoning: String,
    ) -> Self {
        self.final_resonance_score = Some(score);
        self.min_threshold = Some(threshold);
        self.decision = Some(decision);
        self.decision_reasoning = Some(reasoning);
        self
    }
    
    pub fn chain_id(mut self, id: u64) -> Self {
        self.chain_id = Some(id);
        self
    }
    
    pub fn build(self) -> Result<AREAnalysisReport, String> {
        Ok(AREAnalysisReport::new(
            self.opportunity_id.ok_or("Missing opportunity_id")?,
            self.opportunity_type.ok_or("Missing opportunity_type")?,
            self.path_description.ok_or("Missing path_description")?,
            self.potential_profit_usd.ok_or("Missing potential_profit_usd")?,
            self.temporal_harmonics.ok_or("Missing temporal_harmonics")?,
            self.geometric_score.ok_or("Missing geometric_score")?,
            self.network_resonance.ok_or("Missing network_resonance")?,
            self.temporal_weight.ok_or("Missing temporal_weight")?,
            self.geometric_weight.ok_or("Missing geometric_weight")?,
            self.network_weight.ok_or("Missing network_weight")?,
            self.final_resonance_score.ok_or("Missing final_resonance_score")?,
            self.min_threshold.ok_or("Missing min_threshold")?,
            self.decision.ok_or("Missing decision")?,
            self.decision_reasoning.ok_or("Missing decision_reasoning")?,
            self.chain_id.ok_or("Missing chain_id")?,
        ))
    }
}

impl Default for AREAnalysisReportBuilder {
    fn default() -> Self {
        Self::new()
    }
}