// Simple test to verify our configuration update logic works
use std::fs;
use std::path::PathBuf;

fn main() {
    println!("Testing configuration manager contract address update functionality...");
    
    // Test 1: Basic TOML parsing and address update
    test_toml_address_update();
    
    // Test 2: Atomic file operations
    test_atomic_file_operations();
    
    // Test 3: Backup and restore functionality
    test_backup_restore();
    
    println!("🎉 All configuration manager tests passed!");
}

fn test_toml_address_update() {
    println!("Test 1: TOML address update...");
    
    let config_content = r#"
[network]
rpc_url = "http://localhost:8545"
chain_id = 8453

[contracts]
stargate_compass_v1 = "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e"

[execution]
max_gas_price = 50000000000
"#;
    
    // Parse the TOML
    let mut config: toml::Value = toml::from_str(config_content).expect("Failed to parse TOML");
    
    // Update the address
    let new_address = "0x1234567890123456789012345678901234567890";
    let updated = update_contract_address_in_toml(&mut config, new_address);
    
    assert!(updated, "Should have found and updated the address");
    
    // Verify the update
    let contracts = config.get("contracts").expect("Should have contracts section");
    let contracts_table = contracts.as_table().expect("Contracts should be a table");
    let address_value = contracts_table.get("stargate_compass_v1").expect("Should have stargate_compass_v1");
    let address_str = address_value.as_str().expect("Address should be a string");
    
    assert_eq!(address_str, new_address, "Address should be updated correctly");
    
    println!("✅ TOML address update test passed!");
}

fn test_atomic_file_operations() {
    println!("Test 2: Atomic file operations...");
    
    let temp_dir = std::env::temp_dir();
    let test_file = temp_dir.join("test_config.toml");
    
    let original_content = r#"
[contracts]
stargate_compass_v1 = "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e"
"#;
    
    // Write original file
    fs::write(&test_file, original_content).expect("Failed to write test file");
    
    // Test atomic update
    let new_address = "0x1234567890123456789012345678901234567890";
    let result = atomic_update_config_file(&test_file, new_address);
    
    assert!(result.is_ok(), "Atomic update should succeed: {:?}", result);
    
    // Verify the file was updated
    let updated_content = fs::read_to_string(&test_file).expect("Failed to read updated file");
    assert!(updated_content.contains(new_address), "File should contain new address");
    
    // Clean up
    let _ = fs::remove_file(&test_file);
    
    println!("✅ Atomic file operations test passed!");
}

fn test_backup_restore() {
    println!("Test 3: Backup and restore functionality...");
    
    let temp_dir = std::env::temp_dir();
    let test_file = temp_dir.join("test_backup.toml");
    
    let original_content = r#"
[contracts]
stargate_compass_v1 = "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e"
"#;
    
    // Write original file
    fs::write(&test_file, original_content).expect("Failed to write test file");
    
    // Create backup
    let backup_path = create_backup(&test_file).expect("Failed to create backup");
    
    // Modify original file
    fs::write(&test_file, "modified content").expect("Failed to modify file");
    
    // Restore from backup
    restore_from_backup(&test_file, &backup_path).expect("Failed to restore from backup");
    
    // Verify restoration
    let restored_content = fs::read_to_string(&test_file).expect("Failed to read restored file");
    assert_eq!(restored_content, original_content, "Content should be restored");
    
    // Clean up
    let _ = fs::remove_file(&test_file);
    let _ = fs::remove_file(&backup_path);
    
    println!("✅ Backup and restore test passed!");
}

// Core implementation functions

fn update_contract_address_in_toml(config: &mut toml::Value, new_address: &str) -> bool {
    let mut updated = false;
    
    // Look for contracts.stargate_compass_v1
    if let Some(contracts) = config.get_mut("contracts") {
        if let Some(contracts_table) = contracts.as_table_mut() {
            // Try different possible keys
            let possible_keys = [
                "stargate_compass_v1",
                "stargate_compass",
                "compass_v1",
                "compass"
            ];
            
            for key in &possible_keys {
                if let Some(address_value) = contracts_table.get_mut(*key) {
                    *address_value = toml::Value::String(new_address.to_string());
                    updated = true;
                }
            }
            
            // Also search for any key containing "stargate" or "compass"
            if !updated {
                let keys_to_update: Vec<String> = contracts_table
                    .keys()
                    .filter(|key| {
                        let key_lower = key.to_lowercase();
                        key_lower.contains("stargate") || key_lower.contains("compass")
                    })
                    .cloned()
                    .collect();
                
                for key in keys_to_update {
                    if let Some(value) = contracts_table.get_mut(&key) {
                        *value = toml::Value::String(new_address.to_string());
                        updated = true;
                    }
                }
            }
        }
    }
    
    updated
}

fn atomic_update_config_file(file_path: &std::path::Path, new_address: &str) -> Result<(), Box<dyn std::error::Error>> {
    // Read and parse current config
    let content = fs::read_to_string(file_path)?;
    let mut config: toml::Value = toml::from_str(&content)?;
    
    // Update the address
    let updated = update_contract_address_in_toml(&mut config, new_address);
    if !updated {
        return Err("No contract address found to update".into());
    }
    
    // Create backup
    let backup_path = create_backup(file_path)?;
    
    // Write to temporary file first
    let temp_path = file_path.with_extension("tmp");
    let updated_content = toml::to_string_pretty(&config)?;
    
    match fs::write(&temp_path, updated_content) {
        Ok(_) => {
            // Atomic move from temp to final location
            match fs::rename(&temp_path, file_path) {
                Ok(_) => {
                    // Success - clean up backup
                    let _ = fs::remove_file(&backup_path);
                    Ok(())
                }
                Err(e) => {
                    // Failed to move - restore from backup
                    let _ = fs::remove_file(&temp_path);
                    let _ = restore_from_backup(file_path, &backup_path);
                    Err(format!("Failed to atomically update file: {}", e).into())
                }
            }
        }
        Err(e) => {
            // Failed to write temp file - restore from backup
            let _ = fs::remove_file(&temp_path);
            let _ = restore_from_backup(file_path, &backup_path);
            Err(format!("Failed to write temporary file: {}", e).into())
        }
    }
}

fn create_backup(file_path: &std::path::Path) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)?
        .as_secs();
    
    let backup_name = format!(
        "{}.backup.{}",
        file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown"),
        timestamp
    );
    
    let backup_path = file_path.parent()
        .unwrap_or_else(|| std::path::Path::new("."))
        .join(backup_name);
    
    fs::copy(file_path, &backup_path)?;
    Ok(backup_path)
}

fn restore_from_backup(original_path: &std::path::Path, backup_path: &std::path::Path) -> Result<(), Box<dyn std::error::Error>> {
    fs::copy(backup_path, original_path)?;
    fs::remove_file(backup_path)?;
    Ok(())
}