// MISSION: Comprehensive Alerting System - The Neural Network of the Zen Geometer
// WHY: Provide actionable insights into Performance, Risk, and Operational Health (PRO Health)
// HOW: Multi-layered notification system with configurable channels and tactical intelligence

use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error, debug};
use crate::error::Result;
use crate::shared_types::{<PERSON>ert, AlertLevel, AlertCategory, NatsTopics};
use async_nats::Client as NatsClient;
use tokio_stream::StreamExt;

use crate::alerting::notifiers::Notifier;
use crate::alerting::notifiers::console::ConsoleNotifier;

pub struct AlertingConfig {
    pub enabled: bool,
    pub min_level: AlertLevel,
    pub opportunity_drought_secs: u64,
    pub max_revert_rate_pct: f64,
    pub significant_profit_usd: f64,
    pub telegram_bot_token: Option<String>,
    pub telegram_chat_id: Option<String>,
    pub discord_webhook_url: Option<String>,
    pub pagerduty_integration_key: Option<String>,
}

impl Default for AlertingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            min_level: AlertLevel::INFO,
            opportunity_drought_secs: 3600, // 1 hour
            max_revert_rate_pct: 0.25, // 25%
            significant_profit_usd: 1000.0,
            telegram_bot_token: None,
            telegram_chat_id: None,
            discord_webhook_url: None,
            pagerduty_integration_key: None,
        }
    }
}

pub struct Alerter {
    config: AlertingConfig,
    nats_client: NatsClient,
    notifiers: Vec<Arc<dyn Notifier>>,
    // Performance tracking
    last_opportunity_time: std::sync::Mutex<Option<std::time::Instant>>,
    recent_reverts: std::sync::Mutex<Vec<std::time::Instant>>,
}

impl Alerter {
    pub fn new(config: AlertingConfig, nats_client: NatsClient) -> Self {
        let mut notifiers: Vec<Arc<dyn Notifier>> = Vec::new();

        // Always add console notifier for development
        notifiers.push(Arc::new(ConsoleNotifier::new(config.min_level.clone())));

        // Add Discord notifier if configured
        if let Some(webhook_url) = &config.discord_webhook_url {
            if !webhook_url.is_empty() {
                info!("Discord webhook configured but notifier not implemented yet");
                // TODO: Implement DiscordNotifier
                // notifiers.push(Arc::new(DiscordNotifier::new(webhook_url.clone(), config.min_level.clone())));
            }
        }

        // Add Telegram notifier if configured
        if let (Some(bot_token), Some(chat_id)) = (&config.telegram_bot_token, &config.telegram_chat_id) {
            if !bot_token.is_empty() && !chat_id.is_empty() {
                info!("Telegram bot configured but notifier not implemented yet");
                // TODO: Implement TelegramNotifier
                // notifiers.push(Arc::new(TelegramNotifier::new(bot_token.clone(), chat_id.clone(), config.min_level.clone())));
            }
        }

        // Add PagerDuty notifier if configured
        if let Some(integration_key) = &config.pagerduty_integration_key {
            if !integration_key.is_empty() {
                info!("PagerDuty integration configured but notifier not implemented yet");
                // TODO: Implement PagerDutyNotifier
                // notifiers.push(Arc::new(PagerDutyNotifier::new(integration_key.clone(), AlertLevel::SEVERE)));
            }
        }

        info!("Alerting system initialized with {} notifiers", notifiers.len());

        Self {
            config,
            nats_client,
            notifiers,
            last_opportunity_time: std::sync::Mutex::new(None),
            recent_reverts: std::sync::Mutex::new(Vec::new()),
        }
    }

    /// Main service loop - subscribes to alerts.> wildcard topic
    pub async fn run(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Alerting system disabled by configuration");
            return Ok(());
        }

        info!("Starting Comprehensive Alerting System - The Neural Network of Zen Geometer");
        info!("Listening on topic: {}", NatsTopics::ALERTS_WILDCARD);

        // Subscribe to the wildcard alerts topic
        let mut alerts_subscriber = self.nats_client
            .subscribe(NatsTopics::ALERTS_WILDCARD)
            .await
            .map_err(|e| crate::error::BasiliskError::NetworkError(format!("Failed to subscribe to alerts: {}", e)))?;

        // Start performance monitoring tasks
        self.start_performance_monitoring().await?;

        // Main alert processing loop
        while let Some(msg) = alerts_subscriber.next().await {
            match serde_json::from_slice::<Alert>(&msg.payload) {
                Ok(alert) => {
                    if let Err(e) = self.dispatch_alert(&alert).await {
                        error!("Failed to dispatch alert: {}", e);
                    }
                },
                Err(e) => {
                    error!("Failed to parse alert message: {}", e);
                }
            }
        }

        Ok(())
    }

    /// Dispatch alert to all configured notifiers
    async fn dispatch_alert(&self, alert: &Alert) -> Result<()> {
        debug!("Dispatching alert: {} - {}", alert.level_str(), alert.title);

        // Check if alert level meets minimum threshold
        if !self.should_process_alert(alert) {
            debug!("Alert filtered out by minimum level threshold");
            return Ok(());
        }

        // Send to all notifiers concurrently
        let mut handles = Vec::new();
        
        for notifier in &self.notifiers {
            let notifier = notifier.clone();
            let alert = alert.clone();
            
            let handle = tokio::spawn(async move {
                if let Err(e) = notifier.send(&alert).await {
                    error!("Failed to send alert via {}: {}", notifier.name(), e);
                } else {
                    debug!("Alert sent via {}", notifier.name());
                }
            });
            
            handles.push(handle);
        }

        // Wait for all notifications to complete
        for handle in handles {
            let _ = handle.await;
        }

        info!("Alert dispatched: {} - {}", alert.level_str(), alert.title);
        Ok(())
    }

    fn should_process_alert(&self, alert: &Alert) -> bool {
        match (&self.config.min_level, &alert.level) {
            (AlertLevel::INFO, _) => true,
            (AlertLevel::WARNING, AlertLevel::WARNING | AlertLevel::SEVERE | AlertLevel::CRITICAL) => true,
            (AlertLevel::SEVERE, AlertLevel::SEVERE | AlertLevel::CRITICAL) => true,
            (AlertLevel::CRITICAL, AlertLevel::CRITICAL) => true,
            _ => false,
        }
    }

    /// Start background tasks for performance monitoring
    async fn start_performance_monitoring(&self) -> Result<()> {
        // Opportunity drought monitoring
        let nats_client = self.nats_client.clone();
        let drought_threshold = self.config.opportunity_drought_secs;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(300)); // Check every 5 minutes
            
            loop {
                interval.tick().await;
                
                // This would be implemented to check for opportunity drought
                // For now, it's a placeholder for the monitoring logic
                debug!("Checking for opportunity drought (threshold: {}s)", drought_threshold);
            }
        });

        info!("Performance monitoring tasks started");
        Ok(())
    }

    /// Publish an alert to the NATS system
    pub async fn publish_alert(&self, alert: Alert) -> Result<()> {
        let topic = match alert.category {
            AlertCategory::Risk => NatsTopics::ALERTS_RISK,
            AlertCategory::Performance => NatsTopics::ALERTS_PERFORMANCE,
            AlertCategory::Operational => NatsTopics::ALERTS_OPERATIONAL,
            AlertCategory::Profitability => NatsTopics::ALERTS_PROFITABILITY,
        };

        let payload = serde_json::to_vec(&alert)
            .map_err(|e| crate::error::BasiliskError::SerializationError(format!("Failed to serialize alert: {}", e)))?;

        self.nats_client
            .publish(topic, payload.into())
            .await
            .map_err(|e| crate::error::BasiliskError::NetworkError(format!("Failed to publish alert: {}", e)))?;

        debug!("Alert published to topic: {}", topic);
        Ok(())
    }

    /// Helper method to create and publish alerts easily
    pub async fn alert(
        &self,
        level: AlertLevel,
        category: AlertCategory,
        title: String,
        message: String,
        source: String,
    ) -> Result<()> {
        let alert = Alert::new(level, category, title, message, source);
        self.publish_alert(alert).await
    }

    /// Helper method to create alerts with context
    pub async fn alert_with_context(
        &self,
        level: AlertLevel,
        category: AlertCategory,
        title: String,
        message: String,
        source: String,
        context: HashMap<String, String>,
    ) -> Result<()> {
        let alert = Alert::new(level, category, title, message, source)
            .with_contexts(context);
        self.publish_alert(alert).await
    }
}