// Validation test for centrality score initialization
// This test verifies that task 3.1 requirements are met

#[cfg(test)]
mod centrality_validation_tests {
    use super::super::centrality_manager::CentralityScoreManager;
    use rust_decimal_macros::dec;

    #[test]
    fn test_task_3_1_requirements_validation() {
        // Initialize the centrality manager (simulating what happens in StrategyManager)
        let centrality_manager = CentralityScoreManager::new();
        let centrality_scores = centrality_manager.get_all_scores();

        // Requirement 1: Replace empty HashMap with populated centrality scores
        assert!(!centrality_scores.is_empty(), "Centrality scores should not be empty");
        assert!(centrality_scores.len() >= 5, "Should have at least 5 major tokens");

        // Requirement 2: Add major token centrality values (WETH: 0.95, USDC: 0.90, etc.)
        assert_eq!(centrality_scores.get("WETH"), Some(&dec!(0.95)), "WETH should have centrality 0.95");
        assert_eq!(centrality_scores.get("USDC"), Some(&dec!(0.90)), "USDC should have centrality 0.90");
        assert_eq!(centrality_scores.get("USDT"), Some(&dec!(0.85)), "USDT should have centrality 0.85");
        assert_eq!(centrality_scores.get("DAI"), Some(&dec!(0.80)), "DAI should have centrality 0.80");
        assert_eq!(centrality_scores.get("WBTC"), Some(&dec!(0.75)), "WBTC should have centrality 0.75");

        // Requirement 3: Implement CentralityScoreManager for score management
        // Test that the manager provides proper score management functionality
        assert_eq!(centrality_manager.get_centrality_score("WETH"), dec!(0.95));
        assert_eq!(centrality_manager.get_centrality_score("USDC"), dec!(0.90));

        // Requirement 4: Add default fallback scores for unknown tokens
        assert_eq!(centrality_manager.get_centrality_score("UNKNOWN_TOKEN"), dec!(0.05));
        assert_eq!(centrality_manager.get_default_fallback_score(), dec!(0.05));
        assert_eq!(centrality_scores.get("UNKNOWN"), Some(&dec!(0.05)), "UNKNOWN token should have fallback score");

        println!("✅ All task 3.1 requirements validated successfully");
        println!("   - Centrality scores are populated (not empty)");
        println!("   - Major tokens have correct centrality values");
        println!("   - CentralityScoreManager provides proper management");
        println!("   - Default fallback scores work for unknown tokens");
    }

    #[test]
    fn test_centrality_score_usage_in_path_calculation() {
        let centrality_manager = CentralityScoreManager::new();
        let centrality_scores = centrality_manager.get_all_scores();

        // Simulate path centrality calculation (like in swap scanner)
        let test_tokens = vec!["WETH", "USDC", "UNKNOWN_TOKEN"];
        let mut total_centrality = dec!(0.0);
        let mut valid_tokens = 0;

        for token_symbol in &test_tokens {
            let centrality = centrality_scores.get(*token_symbol)
                .copied()
                .or_else(|| centrality_scores.get("UNKNOWN").copied())
                .unwrap_or(dec!(0.05));
            
            total_centrality += centrality;
            valid_tokens += 1;
        }

        let average_centrality = total_centrality / rust_decimal::Decimal::from(valid_tokens);
        
        // Expected: (0.95 + 0.90 + 0.05) / 3 = 1.90 / 3 = 0.633...
        let expected = (dec!(0.95) + dec!(0.90) + dec!(0.05)) / dec!(3.0);
        assert_eq!(average_centrality, expected, "Path centrality calculation should work correctly");

        println!("✅ Centrality score usage in path calculation validated");
        println!("   - Average centrality for [WETH, USDC, UNKNOWN_TOKEN]: {}", average_centrality);
    }

    #[test]
    fn test_no_empty_hashmap_initialization() {
        // This test specifically validates that we're not using empty HashMap initialization
        // as mentioned in the audit findings
        let centrality_manager = CentralityScoreManager::new();
        let centrality_scores = centrality_manager.get_all_scores();

        // Verify this is NOT an empty HashMap (the original issue)
        assert!(!centrality_scores.is_empty(), "Centrality scores must not be empty HashMap");
        
        // Verify major tokens are present (proving it's populated)
        assert!(centrality_scores.contains_key("WETH"), "WETH must be present");
        assert!(centrality_scores.contains_key("USDC"), "USDC must be present");
        assert!(centrality_scores.contains_key("UNKNOWN"), "UNKNOWN fallback must be present");

        println!("✅ Confirmed: No empty HashMap initialization (audit issue resolved)");
    }
}