async function main() {
  console.log("🔍 Checking Base Sepolia testnet balance...");
  
  const [signer] = await ethers.getSigners();
  const address = await signer.getAddress();
  const balance = await ethers.provider.getBalance(address);
  
  console.log(`Wallet: ${address}`);
  console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
  console.log(`Network: ${hre.network.name} (Chain ID: ${hre.network.config.chainId})`);
  
  if (balance === 0n) {
    console.log("❌ No testnet ETH! Get free ETH from:");
    console.log("🚰 Base Sepolia Faucet: https://www.coinbase.com/faucets/base-ethereum-goerli-faucet");
    console.log("🚰 Alternative: https://faucet.quicknode.com/base/sepolia");
  } else {
    console.log("✅ Ready for testnet deployment!");
  }
}

main().catch(console.error);
