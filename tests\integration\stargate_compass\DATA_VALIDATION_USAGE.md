# TUI Data Validation System Usage Guide

## Overview

The TUI Data Validation System provides comprehensive validation of TUI outputs against on-chain blockchain data. This system ensures that the TUI displays accurate information by comparing displayed data with direct blockchain queries.

## Key Components

### 1. TuiDataValidator

The main validator that performs on-chain queries and data comparisons.

```rust
use super::data_validator::{TuiDataValidator, ValidationConfig};
use super::anvil_client::AnvilClient;
use ethers::types::Address;
use std::sync::Arc;

// Create Anvil client
let anvil_client = Arc::new(
    AnvilClient::connect_existing("http://localhost:8545".to_string(), Some(contract_address)).await?
);

// Create data validator
let data_validator = Arc::new(TuiDataValidator::new(anvil_client, contract_address));
```

### 2. Validation Functions

#### Balance Validation

```rust
// Validate balance display against on-chain data
let validation_result = data_validator
    .validate_balance_display(tui_output, wallet_address)
    .await?;

if validation_result.matches {
    println!("✅ Balance display is accurate");
} else {
    println!("❌ Balance mismatch: {}", validation_result.error_message.unwrap_or_default());
}
```

#### Contract Status Validation

```rust
// Validate contract status display
let validation_result = data_validator
    .validate_contract_status_display(tui_output)
    .await?;

if validation_result.matches {
    println!("✅ Contract status is accurate");
} else {
    println!("❌ Contract status mismatch: {}", validation_result.error_message.unwrap_or_default());
}
```

#### Transaction Status Validation

```rust
// Validate transaction status display
let validation_result = data_validator
    .validate_transaction_status_display(tui_output, tx_hash)
    .await?;

if validation_result.matches {
    println!("✅ Transaction status is accurate");
} else {
    println!("❌ Transaction status mismatch: {}", validation_result.error_message.unwrap_or_default());
}
```

#### Transaction History Validation

```rust
// Validate transaction history display
let validation_result = data_validator
    .validate_transaction_history_display(tui_output, expected_count)
    .await?;

if validation_result.matches {
    println!("✅ Transaction history is accurate");
} else {
    println!("❌ Transaction history mismatch: {}", validation_result.error_message.unwrap_or_default());
}
```

### 3. Comprehensive Validation

```rust
// Run comprehensive validation on TUI output
let validation_config = ValidationConfig {
    validate_balances: true,
    validate_contract_status: true,
    validate_transaction_history: true,
    addresses_to_check: vec![wallet_address, contract_address],
    transactions_to_check: vec![tx_hash1, tx_hash2],
    expected_transaction_count: 10,
};

let validation_results = data_validator
    .validate_comprehensive_tui_output(tui_output, validation_config)
    .await?;

for result in validation_results {
    if result.matches {
        println!("✅ {} validation passed", result.data_type);
    } else {
        println!("❌ {} validation failed: {}",
                result.data_type,
                result.error_message.unwrap_or_default());
    }
}
```

### 4. Integration with TUI Functionality Tester

```rust
// Set up TUI functionality tester with data validator
let mut tui_tester = TuiFunctionalityTester::new(anvil_url, contract_address_str);
tui_tester.set_data_validator(anvil_client.clone())?;

// Execute TUI command with comprehensive validation
let command_result = tui_tester.execute_command("query_balances").await?;

// Validate the output
let validation_results = tui_tester
    .validate_comprehensive_output(
        &command_result.output_captured,
        vec![wallet_address],
        vec![tx_hash]
    )
    .await?;

for result in validation_results {
    println!("Validation: {} - {}", result.data_type,
             if result.matches { "PASS" } else { "FAIL" });
}
```

## Data Types Validated

### 1. Balance Display Validation

- **Purpose**: Verify TUI balance displays match on-chain balances
- **Method**: Direct blockchain queries for ETH and token balances
- **Tolerance**: Configurable decimal tolerance for balance comparisons
- **Patterns Detected**:
  - `ETH: 1.234567`
  - `USDC: 1000.50`
  - `Balance: 1.234 ETH`
  - `Available: 1000.00 USDC`
  - `$1,234.56 USD`

### 2. Contract Status Validation

- **Purpose**: Verify TUI contract status displays match on-chain state
- **Method**: Contract state queries for paused, emergency stopped, etc.
- **Status Indicators**:
  - Paused status
  - Emergency stopped status
  - Connection status
  - Contract address display

### 3. Transaction Status Validation

- **Purpose**: Verify TUI transaction status displays match blockchain receipts
- **Method**: Transaction receipt queries and status comparison
- **Status Types**:
  - Confirmed/Success
  - Failed/Reverted
  - Pending
  - Not Found
- **Additional Data**:
  - Confirmation count
  - Gas used
  - Block number

### 4. Transaction History Validation

- **Purpose**: Verify TUI transaction history matches blockchain transaction log
- **Method**: Block scanning for contract transactions
- **Validation Points**:
  - Transaction count accuracy
  - Transaction hash presence
  - Status consistency
  - Chronological ordering

## Error Handling and Tolerance

### Balance Tolerance

```rust
// Set custom balance tolerance
let mut data_validator = TuiDataValidator::new(anvil_client, contract_address);
data_validator.set_balance_tolerance(Decimal::new(1, 3)); // 0.001 tolerance
```

### Validation Results

```rust
pub struct DataValidationResult {
    pub data_type: String,           // Type of data validated
    pub expected_value: String,      // Expected value from blockchain
    pub actual_value: String,        // Actual value from TUI
    pub matches: bool,               // Whether values match within tolerance
    pub validation_method: String,   // Method used for validation
    pub tolerance: Option<Decimal>,  // Tolerance used (if applicable)
    pub error_message: Option<String>, // Detailed error information
}
```

## Integration Test Example

```rust
use super::test_data_validation_integration::TuiDataValidationIntegrationTest;

#[tokio::test]
async fn test_comprehensive_data_validation() -> Result<()> {
    // Initialize integration test
    let mut integration_test = TuiDataValidationIntegrationTest::new(
        "http://localhost:8545".to_string(),
        "******************************************".to_string(),
    ).await?;

    // Run comprehensive validation test
    let test_results = integration_test.run_comprehensive_test().await?;

    // Check results
    assert!(test_results.success_rate() > 0.8, "Validation success rate should be > 80%");
    assert!(test_results.errors.is_empty(), "No critical errors should occur");

    println!("Test completed with {:.1}% success rate",
             test_results.success_rate() * 100.0);

    Ok(())
}
```

## Best Practices

### 1. Environment Setup

- Ensure Anvil is running with proper Base fork
- Deploy contracts before running validation tests
- Fund test accounts with sufficient balances

### 2. Validation Configuration

- Use appropriate tolerance levels for balance comparisons
- Include relevant addresses and transactions in validation config
- Set realistic expected transaction counts

### 3. Error Handling

- Always check validation results for errors
- Log detailed error messages for debugging
- Implement retry logic for transient network issues

### 4. Performance Considerations

- Use validation caching to avoid redundant queries
- Batch validation requests when possible
- Set appropriate timeouts for blockchain queries

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify Anvil is running and accessible
   - Check network connectivity
   - Validate contract address format

2. **Balance Mismatches**
   - Check if transactions are pending
   - Verify token contract addresses
   - Adjust balance tolerance if needed

3. **Transaction Validation Failures**
   - Ensure transactions exist on blockchain
   - Check for recent block reorganizations
   - Verify transaction hash format

4. **Contract Status Mismatches**
   - Confirm contract is deployed
   - Check for recent state changes
   - Verify contract ABI compatibility

### Debug Mode

```rust
// Enable detailed logging
use tracing::{info, debug, warn, error};
use tracing_subscriber;

tracing_subscriber::fmt::init();

// Validation will now output detailed debug information
```

## Requirements Satisfied

This implementation satisfies the task requirements:

✅ **Write functions to query on-chain contract state directly**

- `query_contract_state()` - Comprehensive contract state queries
- `query_balance_data()` - Direct balance queries for ETH and tokens
- `query_transaction_status()` - Transaction receipt and status queries
- `query_transaction_history()` - Historical transaction data

✅ **Create data comparison logic between TUI display and blockchain state**

- `validate_balance_display()` - Compare TUI balances with on-chain data
- `validate_contract_status_display()` - Compare TUI status with contract state
- `validate_transaction_status_display()` - Compare TUI tx status with receipts
- `validate_transaction_history_display()` - Compare TUI history with blockchain

✅ **Implement balance verification for contract and wallet addresses**

- Support for ETH, USDC, WETH, and other ERC20 tokens
- Configurable tolerance for balance comparisons
- Multiple balance format pattern recognition
- Address-specific balance validation

✅ **Write validation functions for transaction status and history displays**

- Transaction status normalization and comparison
- Confirmation count validation with tolerance
- Transaction history count and hash verification
- Comprehensive transaction lifecycle validation

The system provides a complete data validation framework that ensures TUI displays accurate on-chain data through direct blockchain queries and intelligent comparison logic.
