import { HardhatUserConfig } from 'hardhat/config';
import '@nomicfoundation/hardhat-toolbox';
import * as dotenv from 'dotenv';

dotenv.config();

const config: HardhatUserConfig = {
  solidity: {
    version: '0.8.28',
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
      viaIR: true, // Enable IR optimizer to fix stack too deep issues
    },
  },
  paths: {
    sources: './contracts',
  },
  networks: {
    localhost: {
      url: 'http://127.0.0.1:8545',
      chainId: 8453, // Base chain ID for the fork
      accounts: [
        '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', // First Anvil account
      ],
    },
    base: {
      url: process.env.BASE_RPC_URL || 'https://mainnet.base.org',
      chainId: 8453,
      accounts: process.env.BASILISK_EXECUTION_PRIVATE_KEY
        ? [process.env.BASILISK_EXECUTION_PRIVATE_KEY]
        : [],
      gasPrice: **********, // 1 gwei
    },
    'base-sepolia': {
      url: 'https://sepolia.base.org',
      chainId: 84532,
      accounts: process.env.BASILISK_EXECUTION_PRIVATE_KEY
        ? [process.env.BASILISK_EXECUTION_PRIVATE_KEY]
        : [],
    },
  },
  etherscan: {
    apiKey: {
      base: process.env.BASESCAN_API_KEY || '',
      'base-sepolia': process.env.BASESCAN_API_KEY || '',
    },
    customChains: [
      {
        network: 'base',
        chainId: 8453,
        urls: {
          apiURL: 'https://api.basescan.org/api',
          browserURL: 'https://basescan.org',
        },
      },
      {
        network: 'base-sepolia',
        chainId: 84532,
        urls: {
          apiURL: 'https://api-sepolia.basescan.org/api',
          browserURL: 'https://sepolia.basescan.org',
        },
      },
    ],
  },
};

export default config;
