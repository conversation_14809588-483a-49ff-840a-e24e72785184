# Zen Geometer Multi-Chain Setup Guide

This comprehensive guide covers the setup and configuration of the Zen Geometer autonomous trading system across multiple blockchain networks: Base, Arbitrum, and Degen Chain.

## Table of Contents

1. [Overview](#overview)
2. [Supported Networks](#supported-networks)
3. [Base Network Setup](#base-network-setup)
4. [Degen Chain Setup](#degen-chain-setup)
5. [Arbitrum Setup (Future)](#arbitrum-setup-future)
6. [Cross-Chain Bridge Configuration](#cross-chain-bridge-configuration)
7. [Multi-Chain Validation](#multi-chain-validation)
8. [Operational Procedures](#operational-procedures)
9. [Troubleshooting](#troubleshooting)

## Overview

The Zen Geometer implements a **Hub and Spoke** multi-chain architecture:

- **Base Network (Hub)**: Primary settlement layer for cross-chain operations
- **Degen Chain (Spoke)**: High-frequency execution layer for specialized operations
- **Arbitrum (Future Spoke)**: Additional arbitrage opportunities and liquidity

### Architecture Benefits

- **Risk Distribution**: Spread operations across multiple chains
- **Latency Optimization**: Use fastest chain for time-sensitive operations
- **Liquidity Access**: Access diverse liquidity pools across ecosystems
- **Cost Optimization**: Route transactions to most cost-effective chains

## Supported Networks

### Network Status Matrix

| Network     | Chain ID  | Status              | Role            | Native Currency | Primary Use Case                               |
| ----------- | --------- | ------------------- | --------------- | --------------- | ---------------------------------------------- |
| Base        | 8453      | ✅ Production Ready | Settlement Hub  | ETH             | Cross-chain arbitrage, primary operations      |
| Degen Chain | 666666666 | ✅ Production Ready | Execution Spoke | DEGEN           | High-frequency trading, specialized strategies |
| Arbitrum    | 42161     | 🚧 Future Support   | Arbitrage Spoke | ETH             | Additional liquidity, L2 arbitrage             |

### Network Characteristics

#### Base Network

- **Type**: Optimistic Rollup (L2)
- **Settlement**: Ethereum mainnet
- **Block Time**: ~2 seconds
- **Gas Token**: ETH
- **Strengths**: Low fees, Coinbase ecosystem, high liquidity

#### Degen Chain

- **Type**: Custom L3 (Arbitrum Orbit)
- **Settlement**: Base Network
- **Block Time**: ~1 second
- **Gas Token**: DEGEN
- **Strengths**: Ultra-low fees, meme token ecosystem, fast execution

#### Arbitrum (Future)

- **Type**: Optimistic Rollup (L2)
- **Settlement**: Ethereum mainnet
- **Block Time**: ~1 second
- **Gas Token**: ETH
- **Strengths**: High throughput, mature DeFi ecosystem

## Base Network Setup

Base serves as the primary settlement layer and hub for cross-chain operations.

### 1. RPC Configuration

Configure multiple RPC endpoints for redundancy and performance:

```toml
[chains.8453]
name = "Base"
enabled = true
native_currency = "ETH"

# Primary RPC endpoint
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

# Backup endpoints for redundancy
[[chains.8453.rpc_endpoints]]
url = "https://base.publicnode.com"
priority = 1

[[chains.8453.rpc_endpoints]]
url = "https://base.drpc.org"
priority = 2

[[chains.8453.rpc_endpoints]]
url = "https://1rpc.io/base"
priority = 3
```

### 2. Token Configuration

Configure production token addresses on Base:

```toml
[chains.8453.tokens]
# Native USDC on Base (most liquid stablecoin)
USDC = "******************************************"

# Wrapped ETH on Base
WETH = "******************************************"

# DAI stablecoin
DAI = "******************************************"

# Tether USD
USDT = "******************************************"

# Additional tokens for arbitrage opportunities
CBETH = "******************************************"  # Coinbase Wrapped Staked ETH
```

### 3. DEX Configuration

Configure decentralized exchanges on Base:

```toml
[chains.8453.dex]
# Uniswap V3 (primary DEX)
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
uniswap_universal_router = "******************************************"

# Aerodrome (Base-native DEX with high liquidity)
aerodrome_factory = "******************************************"
aerodrome_router = "******************************************"
aerodrome_weth_usdc_pool = "******************************************"

# SushiSwap
sushiswap_factory = "******************************************"
sushiswap_router = "******************************************"

# Balancer V2
balancer_vault = "******************************************"
```

### 4. Smart Contract Configuration

Configure essential smart contracts on Base:

```toml
[chains.8453.contracts]
# Multicall for batch operations
multicall = "******************************************"

# Wrapped ETH contract
weth9 = "******************************************"

# Permit2 for gasless approvals
permit2 = "******************************************"

# Aave V3 lending protocol
aave_pool_addresses_provider = "******************************************"
aave_pool = "******************************************"

# Stargate V1 for cross-chain operations
stargate_router = "******************************************"
stargate_factory = "******************************************"

# Zen Geometer custom contract (deployed)
stargate_compass_v1 = "******************************************"
```

### 5. Base Network Validation

```bash
# Validate Base network configuration
cargo run -- config validate --chain 8453 --comprehensive

# Test connectivity to all RPC endpoints
cargo run -- utils ping-nodes --chain base --all-endpoints

# Check wallet balance on Base
cargo run -- utils balances --chain base --detailed

# Test DEX connectivity
cargo run -- utils test-dex-connectivity --chain base --all-dexs

# Validate smart contract addresses
cargo run -- utils validate-contracts --chain base
```

## Degen Chain Setup

Degen Chain serves as the high-frequency execution layer for specialized trading strategies.

### 1. RPC Configuration

```toml
[chains.666666666]
name = "Degen"
enabled = true
native_currency = "DEGEN"

# Primary Degen Chain RPC
[[chains.666666666.rpc_endpoints]]
url = "https://rpc.degen.tips"
priority = 0

# Backup RPC endpoint
[[chains.666666666.rpc_endpoints]]
url = "https://rpc-degen-mainnet-1.t.conduit.xyz"
priority = 1
```

### 2. Token Configuration

```toml
[chains.666666666.tokens]
# Native DEGEN token
DEGEN = "******************************************"

# Bridged USDC from Base
USDC = "******************************************"

# Wrapped ETH
WETH = "******************************************"

# Additional meme tokens for specialized strategies
HIGHER = "******************************************"
TOSHI = "******************************************"
```

### 3. DEX Configuration

```toml
[chains.666666666.dex]
# DegenSwap (Uniswap V2 fork - primary DEX on Degen Chain)
degen_swap_router = "******************************************"
degen_swap_factory = "******************************************"

# Additional DEXs as they become available
# pancakeswap_router = "TBD"
# sushiswap_router = "TBD"
```

### 4. Smart Contract Configuration

```toml
[chains.666666666.contracts]
# Multicall for batch operations
multicall = "******************************************"

# Wrapped ETH contract
weth9 = "******************************************"

# Stargate V1 for cross-chain operations
stargate_router = "******************************************"

# Future: Zen Geometer contracts on Degen Chain
# stargate_compass_v1 = "TBD"
```

### 5. Degen Chain Validation

```bash
# Validate Degen Chain configuration
cargo run -- config validate --chain 666666666 --comprehensive

# Test connectivity
cargo run -- utils ping-nodes --chain degen --latency-test

# Check DEGEN token balance
cargo run -- utils balances --chain degen --native-token

# Test DEX functionality
cargo run -- utils test-dex-connectivity --chain degen

# Validate gas estimation (DEGEN token)
cargo run -- utils test-gas-estimation --chain degen
```

## Arbitrum Setup (Future)

Arbitrum support is planned for future releases to expand arbitrage opportunities.

### 1. Planned RPC Configuration

```toml
[chains.42161]
name = "Arbitrum"
enabled = false  # Enable when ready for deployment
native_currency = "ETH"

# Primary Arbitrum RPC
[[chains.42161.rpc_endpoints]]
url = "https://arb1.arbitrum.io/rpc"
priority = 0

# Backup endpoints
[[chains.42161.rpc_endpoints]]
url = "https://arbitrum.publicnode.com"
priority = 1

[[chains.42161.rpc_endpoints]]
url = "https://arbitrum.drpc.org"
priority = 2
```

### 2. Planned Token Configuration

```toml
[chains.42161.tokens]
# Native USDC on Arbitrum
USDC = "******************************************"

# Wrapped ETH
WETH = "******************************************"

# Arbitrum native token
ARB = "******************************************"

# Additional tokens
USDT = "******************************************"
DAI = "******************************************"
```

### 3. Planned DEX Configuration

```toml
[chains.42161.dex]
# Uniswap V3
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"

# SushiSwap
sushiswap_factory = "******************************************"
sushiswap_router = "******************************************"

# Camelot (Arbitrum-native DEX)
camelot_factory = "0x6EcCab422D763aC031210895C81787E87B91425"
camelot_router = "******************************************"
```

## Cross-Chain Bridge Configuration

Configure cross-chain bridges for arbitrage opportunities between networks.

### 1. Stargate V1 Configuration

Stargate V1 is the primary bridge protocol for cross-chain operations:

```toml
[bridges]
# Bridge routes: [from_chain, to_chain, cost_usd, latency_seconds]
routes = [
    [8453, 666666666, 5.0, 180],    # Base -> Degen: $5 cost, 3min latency
    [666666666, 8453, 5.0, 180],    # Degen -> Base: $5 cost, 3min latency
    # Future Arbitrum routes
    # [8453, 42161, 8.0, 600],      # Base -> Arbitrum: $8 cost, 10min latency
    # [42161, 8453, 8.0, 600],      # Arbitrum -> Base: $8 cost, 10min latency
]

# Stargate pool configurations
[bridges.stargate]
# Base Network Stargate pools
base_usdc_pool_id = 1
base_eth_pool_id = 13

# Degen Chain Stargate pools
degen_usdc_pool_id = 1
degen_eth_pool_id = 13

# Bridge limits and parameters
min_bridge_amount_usd = 10.0
max_bridge_amount_usd = 100000.0
bridge_slippage_tolerance = 0.005  # 0.5%
```

### 2. Bridge Validation

```bash
# Test Stargate bridge connectivity
cargo run -- utils test-bridge --from base --to degen --amount 0.01

# Validate all bridge routes
cargo run -- utils validate-routes --comprehensive

# Test bridge cost estimation
cargo run -- utils estimate-bridge-costs --all-routes

# Check bridge pool liquidity
cargo run -- utils check-bridge-liquidity --all-pools
```

## Multi-Chain Validation

Comprehensive validation procedures for multi-chain setup.

### 1. Configuration Validation

```bash
# Validate all chain configurations
cargo run -- config validate --all-chains --strict

# Check configuration consistency
cargo run -- config validate --cross-chain-consistency

# Validate token address consistency
cargo run -- utils validate-token-addresses --all-chains
```

### 2. Connectivity Testing

```bash
# Test connectivity to all chains
cargo run -- utils ping-nodes --all-chains --comprehensive

# Test RPC endpoint failover
cargo run -- utils test-rpc-failover --all-chains

# Measure cross-chain latency
cargo run -- utils measure-latency --cross-chain
```

### 3. Wallet and Balance Validation

```bash
# Check wallet balances on all chains
cargo run -- utils balances --all-chains --detailed

# Validate wallet connectivity
cargo run -- utils validate-wallet --all-chains

# Test transaction signing on all chains
cargo run -- utils test-signing --all-chains --dry-run
```

### 4. Smart Contract Validation

```bash
# Validate all smart contract addresses
cargo run -- utils validate-contracts --all-chains

# Test contract interactions
cargo run -- utils test-contract-calls --all-chains --read-only

# Verify contract ABIs
cargo run -- utils verify-abis --all-chains
```

## Operational Procedures

### Daily Multi-Chain Operations

#### Morning Multi-Chain Health Check

```bash
# Comprehensive multi-chain health check
cargo run -- utils multi-chain-health --comprehensive

# Check balances across all chains
cargo run -- utils balances --all-chains --alert-thresholds

# Validate cross-chain connectivity
cargo run -- utils ping-nodes --all-chains --latency-check

# Review overnight cross-chain activity
cargo run -- utils cross-chain-report --since yesterday
```

#### Cross-Chain Monitoring

```bash
# Monitor cross-chain arbitrage opportunities
cargo run -- utils monitor-cross-chain-arb --live

# Track bridge utilization
cargo run -- utils bridge-utilization --real-time

# Monitor gas prices across chains
cargo run -- utils gas-prices --all-chains --comparative
```

### Multi-Chain Risk Management

#### Risk Distribution

```toml
[risk.multi_chain]
# Maximum exposure per chain (percentage of total capital)
max_exposure_base = 0.6        # 60% max on Base (primary hub)
max_exposure_degen = 0.3       # 30% max on Degen Chain
max_exposure_arbitrum = 0.2    # 20% max on Arbitrum (future)

# Cross-chain position limits
max_cross_chain_exposure = 0.4  # 40% max in cross-chain positions
min_chain_balance_usd = 100.0   # Minimum balance per chain
```

#### Emergency Procedures

```bash
# Emergency shutdown across all chains
cargo run -- utils emergency-shutdown --all-chains

# Consolidate funds to primary chain (Base)
cargo run -- utils consolidate-funds --to-base --emergency

# Check stuck cross-chain transactions
cargo run -- utils check-stuck-bridges --all-routes
```

### Performance Optimization

#### Chain Selection Logic

The system automatically selects optimal chains based on:

1. **Gas Costs**: Choose lowest cost chain for execution
2. **Liquidity**: Route to chain with best liquidity for trade size
3. **Latency**: Use fastest chain for time-sensitive operations
4. **Opportunity Size**: Match chain capabilities to opportunity requirements

#### Cross-Chain Arbitrage Strategy

```toml
[strategies.cross_chain_arbitrage]
enabled = true
min_profit_after_bridge_costs = 15.0  # Minimum profit after bridge fees
max_bridge_time_minutes = 10          # Maximum acceptable bridge time
preferred_bridge_protocol = "stargate" # Primary bridge protocol
fallback_bridge_protocols = ["layerzero", "hyperlane"]  # Future options
```

## Troubleshooting

### Common Multi-Chain Issues

#### RPC Endpoint Failures

**Symptoms**: Connection timeouts, failed transactions, stale data

**Diagnosis**:

```bash
cargo run -- utils diagnose-rpc --chain [chain_id] --comprehensive
```

**Solutions**:

1. Automatic failover to backup RPC endpoints
2. RPC endpoint health monitoring and rotation
3. Load balancing across multiple providers

#### Cross-Chain Bridge Issues

**Symptoms**: Stuck transactions, failed bridges, high costs

**Diagnosis**:

```bash
cargo run -- utils diagnose-bridge --route [from_chain]-[to_chain]
```

**Solutions**:

1. Monitor bridge pool liquidity
2. Adjust bridge parameters based on network conditions
3. Use alternative bridge routes when available

#### Gas Price Volatility

**Symptoms**: Failed transactions, high costs, timing issues

**Diagnosis**:

```bash
cargo run -- utils gas-analysis --all-chains --comparative
```

**Solutions**:

1. Dynamic gas strategy adjustment per chain
2. Chain selection based on gas efficiency
3. Transaction timing optimization

#### Token Address Mismatches

**Symptoms**: Failed swaps, incorrect pricing, transaction reverts

**Diagnosis**:

```bash
cargo run -- utils validate-token-addresses --cross-chain
```

**Solutions**:

1. Regular token address validation
2. Automated address verification against official sources
3. Fallback to canonical token lists

### Multi-Chain Monitoring

#### Key Metrics to Monitor

1. **Cross-Chain Latency**: Time for cross-chain operations
2. **Bridge Utilization**: Usage of bridge capacity
3. **Gas Price Ratios**: Relative gas costs across chains
4. **Liquidity Distribution**: Token liquidity across chains
5. **Success Rates**: Transaction success rates per chain

#### Alerting Thresholds

```toml
[alerting.multi_chain]
# Cross-chain operation alerts
max_bridge_time_minutes = 15
min_bridge_success_rate = 0.95
max_gas_price_deviation = 2.0  # 2x normal gas price

# Chain health alerts
min_rpc_success_rate = 0.98
max_rpc_latency_ms = 1000
min_chain_balance_usd = 50.0
```

### Recovery Procedures

#### Stuck Cross-Chain Transactions

```bash
# Identify stuck transactions
cargo run -- utils find-stuck-bridges --all-chains

# Attempt transaction recovery
cargo run -- utils recover-stuck-tx --tx-hash [hash] --chain [chain_id]

# Manual bridge completion (if needed)
cargo run -- utils manual-bridge-complete --bridge-id [id]
```

#### Chain Synchronization Issues

```bash
# Check chain synchronization
cargo run -- utils check-sync-status --all-chains

# Force resync if needed
cargo run -- utils force-resync --chain [chain_id]

# Validate state consistency
cargo run -- utils validate-state --cross-chain
```

This comprehensive multi-chain setup guide ensures robust, secure, and efficient operation across Base, Degen Chain, and future Arbitrum integration. Regular validation and monitoring procedures maintain optimal performance and minimize operational risks.
