use async_trait::async_trait;
use ethers::providers::{Provider, Http, Middleware};
use ethers::types::{U256, H256, TransactionRequest};
use std::sync::Arc;
use crate::error::{BasiliskError, ExecutionError, Result};
use super::{ChainAdapter, ChainFinalityStatus};

pub struct BaseAdapter {
    provider: Arc<Provider<Http>>,
}

impl BaseAdapter {
    pub fn new(provider: Arc<Provider<Http>>) -> Self {
        Self { provider }
    }
}

#[async_trait]
impl ChainAdapter for BaseAdapter {
    async fn get_gas_price(&self) -> Result<U256> {
        self.provider.get_gas_price().await.map_err(|e| BasiliskError::Execution(ExecutionError::GasEstimationFailed { 
            operation: "gas_price".to_string(),
            reason: format!("Failed to get gas price for Base: {}", e) 
        }))
    }

    async fn get_finality_status(&self, tx_hash: H256) -> Result<ChainFinalityStatus> {
        // For Base, finality is tied to L1 finality. This is a simplified check.
        // A more robust implementation would check L1 for state root publication.
        let receipt = self.provider.get_transaction_receipt(tx_hash).await.map_err(|e| BasiliskError::Execution(ExecutionError::RpcError(format!("Failed to get transaction receipt for Base: {}", e))))?;

        if let Some(receipt) = receipt {
            if receipt.block_number.is_some() {
                // Transaction is mined on L2, consider it L2 confirmed.
                // True L1 finality would require more complex logic.
                Ok(ChainFinalityStatus::L2Confirmed)
            } else {
                Ok(ChainFinalityStatus::Pending)
            }
        } else {
            Ok(ChainFinalityStatus::Pending)
        }
    }

    async fn submit_transaction(&self, tx: TransactionRequest) -> Result<H256> {
        let pending_tx = self.provider.send_transaction(tx, None).await.map_err(|e| BasiliskError::Execution(ExecutionError::SubmissionFailed { reason: format!("Failed to submit transaction to Base: {}", e) }))?;
        Ok(pending_tx.tx_hash())
    }
}
