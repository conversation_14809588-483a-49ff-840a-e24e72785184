use anyhow::Result;
use rust_decimal::Decimal;
use rust_decimal::prelude::FromStr;
use std::collections::HashMap;
use std::sync::Arc;
use ethers::providers::{Provider, Http};
use ethers::types::Address;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc, Duration};

use crate::contracts::ChainlinkAggregatorV3;
use crate::token_registry::TokenRegistry;

/// Price cache entry with timestamp for freshness validation
#[derive(Debug, Clone)]
struct PriceCacheEntry {
    price: Decimal,
    timestamp: DateTime<Utc>,
}

pub struct PriceOracle {
    chainlink_feeds: HashMap<String, ChainlinkAggregatorV3<Provider<Http>>>,
    provider: Arc<Provider<Http>>,
    // AUDIT-FIX: Add token registry for proper address resolution
    token_registry: TokenRegistry,
    // AUDIT-FIX: Add price cache with freshness validation
    price_cache: Arc<tokio::sync::Mutex<HashMap<String, PriceCacheEntry>>>,
    cache_duration: Duration,
}

impl PriceOracle {
    pub fn new(provider: Arc<Provider<Http>>, chainlink_feed_addresses: HashMap<String, String>) -> Self {
        let mut chainlink_feeds = HashMap::new();
        for (token_symbol, address_str) in chainlink_feed_addresses {
            match address_str.parse::<Address>() {
                Ok(address) => {
                    let aggregator = ChainlinkAggregatorV3::new(address, provider.clone());
                    chainlink_feeds.insert(token_symbol, aggregator);
                },
                Err(e) => {
                    error!("Failed to parse Chainlink address for {}: {}", token_symbol, e);
                }
            }
        }
        
        Self { 
            chainlink_feeds, 
            provider,
            // AUDIT-FIX: Initialize token registry with real addresses
            token_registry: TokenRegistry::new(),
            // AUDIT-FIX: Initialize price cache for freshness validation
            price_cache: Arc::new(tokio::sync::Mutex::new(HashMap::new())),
            cache_duration: Duration::minutes(5), // 5-minute cache
        }
    }
    
    /// AUDIT-FIX: Get token address by symbol using TokenRegistry
    pub fn get_token_address(&self, symbol: &str) -> Option<Address> {
        self.token_registry.get_address(symbol)
    }
    
    /// AUDIT-FIX: Check if token is registered in the registry
    pub fn has_token(&self, symbol: &str) -> bool {
        self.token_registry.has_token(symbol)
    }
    
    /// AUDIT-FIX: Get all registered tokens
    pub fn get_all_tokens(&self) -> &HashMap<String, Address> {
        self.token_registry.get_all_tokens()
    }

    /// AUDIT-FIX: Get price with freshness validation and caching
    pub async fn get_price(&self, token_symbol: &str) -> Result<Decimal> {
        // Check cache first for fresh data
        {
            let cache = self.price_cache.lock().await;
            if let Some(entry) = cache.get(token_symbol) {
                let age = Utc::now() - entry.timestamp;
                if age < self.cache_duration {
                    info!("Using cached price for {}: ${} (age: {}s)", 
                          token_symbol, entry.price, age.num_seconds());
                    return Ok(entry.price);
                }
            }
        }
        
        // Try to fetch fresh price from Chainlink
        let price = if let Some(aggregator) = self.chainlink_feeds.get(token_symbol) {
            match aggregator.latest_round_data().call().await {
                Ok(data) => {
                    // AUDIT-FIX: Validate price freshness from Chainlink
                    let price_raw = Decimal::from_str(&data.1.to_string())?;
                    let price = price_raw / Decimal::new(100_000_000, 0); 
                    
                    // Validate price is reasonable (not zero or negative)
                    if price <= Decimal::ZERO {
                        warn!("Invalid price from Chainlink for {}: ${}, using fallback", token_symbol, price);
                        self.get_hardcoded_price(token_symbol)?
                    } else {
                        info!("Fetched fresh price for {}: ${}", token_symbol, price);
                        price
                    }
                },
                Err(e) => {
                    error!("Failed to fetch price from Chainlink for {}: {}", token_symbol, e);
                    self.get_hardcoded_price(token_symbol)?
                }
            }
        } else {
            // No Chainlink feed configured, use hardcoded fallback
            self.get_hardcoded_price(token_symbol)?
        };
        
        // Update cache with fresh price
        {
            let mut cache = self.price_cache.lock().await;
            cache.insert(token_symbol.to_string(), PriceCacheEntry {
                price,
                timestamp: Utc::now(),
            });
        }
        
        Ok(price)
    }

    fn get_hardcoded_price(&self, token_symbol: &str) -> Result<Decimal> {
        match token_symbol {
            "DEGEN" => Ok(Decimal::new(1, 2)), // $0.01
            "ETH" => Ok(Decimal::new(3000, 0)), // $3000
            "USDC" => Ok(Decimal::new(1, 0)),      // $1
            _ => {
                warn!("No Chainlink feed or hardcoded price for {}. Defaulting to $1.", token_symbol);
                Ok(Decimal::new(1, 0))
            }
        }
    }

    pub async fn get_usd_value(&self, amount: Decimal, token_symbol: String) -> Result<Decimal> {
        let price = self.get_price(&token_symbol).await?;
        Ok(amount * price)
    }
}