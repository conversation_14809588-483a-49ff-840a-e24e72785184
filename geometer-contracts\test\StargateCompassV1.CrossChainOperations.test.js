const { expect } = require('chai');
const { ethers } = require('hardhat');
const { loadFixture } = require('@nomicfoundation/hardhat-network-helpers');

describe('StargateCompassV1 - Cross-Chain Operations Tests', function () {
  // Test fixture for contract deployment and setup
  async function deployCrossChainFixture() {
    const [owner, otherAccount] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    const mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    const mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    const mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    const mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    const stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with ETH for LayerZero fees
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('2'), // More ETH for cross-chain testing
    });

    // Set up mock USDC balance for flash loan simulation
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('100000', 6)); // 100k USDC

    return {
      stargateCompass,
      owner,
      otherAccount,
      mockAaveProvider,
      mockStargateRouter,
      mockPool,
      mockUSDC,
    };
  }

  // Test constants
  const LOAN_AMOUNT = ethers.parseUnits('1000', 6); // 1000 USDC
  const EXPECTED_PROFIT = ethers.parseUnits('10', 6); // 10 USDC profit
  const MIN_AMOUNT_OUT = ethers.parseUnits('980', 6); // 2% slippage tolerance
  const REMOTE_CALLDATA = '0x1234567890abcdef';
  const LAYERZERO_FEE = ethers.parseEther('0.01'); // 0.01 ETH

  // Stargate constants from contract
  const SG_CHAIN_ID_DEGEN = 204;
  const SG_POOL_ID_USDC_BASE = 1;
  const SG_POOL_ID_USDC_DEGEN = 13;
  const USDC_ON_BASE = '******************************************';

  describe('LayerZero Fee Handling', function () {
    it('Should quote LayerZero fees correctly for cross-chain operations', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const customFee = ethers.parseEther('0.025'); // 0.025 ETH
      await mockStargateRouter.setQuoteLayerZeroFee(customFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;

      // Verify the fee was used in the swap call
      // The mock router should have received the correct fee amount
    });

    it('Should handle variable LayerZero fee scenarios', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const testFees = [
        ethers.parseEther('0.001'), // Very low fee
        ethers.parseEther('0.01'), // Standard fee
        ethers.parseEther('0.05'), // Higher fee
        ethers.parseEther('0.1'), // Maximum allowed fee
      ];

      await mockPool.setFlashLoanSuccess(true);

      for (const fee of testFees) {
        await mockStargateRouter.setQuoteLayerZeroFee(fee, 0);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            LOAN_AMOUNT,
            REMOTE_CALLDATA,
            owner.address,
            MIN_AMOUNT_OUT,
            EXPECTED_PROFIT
          )
        ).to.not.be.reverted;
      }
    });

    it('Should reject excessive LayerZero fees', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const excessiveFee = ethers.parseEther('0.2'); // Above 0.1 ETH limit
      await mockStargateRouter.setQuoteLayerZeroFee(excessiveFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ExcessiveFee')
        .withArgs(excessiveFee, ethers.parseEther('0.1'));
    });

    it('Should reject ZRO fees (currently unsupported)', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const normalFee = ethers.parseEther('0.01');
      const zroFee = ethers.parseUnits('100', 18); // Any ZRO fee
      await mockStargateRouter.setQuoteLayerZeroFee(normalFee, zroFee);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ZROFeesNotSupported')
        .withArgs(zroFee);
    });

    it('Should apply fee buffer for volatility protection', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const baseFee = ethers.parseEther('0.01');
      const expectedBuffer = (baseFee * BigInt(200)) / BigInt(10000); // 2% buffer
      const bufferedFee = baseFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      // Ensure contract has enough ETH for buffered fee
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      if (currentBalance < bufferedFee) {
        await owner.sendTransaction({
          to: stargateCompass.target,
          value: bufferedFee - currentBalance + ethers.parseEther('0.1'),
        });
      }

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });

  describe('Stargate Protocol Integration', function () {
    it('Should execute cross-chain swaps with correct Stargate parameters', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      await stargateCompass.executeRemoteDegenSwap(
        LOAN_AMOUNT,
        REMOTE_CALLDATA,
        owner.address,
        MIN_AMOUNT_OUT,
        EXPECTED_PROFIT
      );

      // Verify Stargate swap was called with correct chain and pool IDs
      // This would be validated through the mock router's state
    });

    it('Should handle different loan amounts with proportional cross-chain operations', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const testAmounts = [
        ethers.parseUnits('100', 6), // 100 USDC
        ethers.parseUnits('1000', 6), // 1,000 USDC
        ethers.parseUnits('10000', 6), // 10,000 USDC
        ethers.parseUnits('50000', 6), // 50,000 USDC
      ];

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      for (const amount of testAmounts) {
        const proportionalProfit = (amount * BigInt(100)) / BigInt(10000); // 1% profit
        const proportionalMinOut = (amount * BigInt(9800)) / BigInt(10000); // 2% slippage

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            amount,
            REMOTE_CALLDATA,
            owner.address,
            proportionalMinOut,
            proportionalProfit
          )
        ).to.not.be.reverted;
      }
    });

    it('Should handle various remote calldata sizes', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const testCalldata = [
        '0x', // Empty calldata
        '0x1234', // Minimal calldata
        '0x' + '00'.repeat(100), // Medium calldata (100 bytes)
        '0x' + '00'.repeat(1000), // Large calldata (1000 bytes)
        '0x' + '00'.repeat(5000), // Very large calldata (5000 bytes)
      ];

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      for (const calldata of testCalldata) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            LOAN_AMOUNT,
            calldata,
            owner.address,
            MIN_AMOUNT_OUT,
            EXPECTED_PROFIT
          )
        ).to.not.be.reverted;
      }
    });

    it('Should reject calldata exceeding maximum size limit', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployCrossChainFixture
      );

      // Create calldata that exceeds MAX_CALLDATA_SIZE (10,000 bytes)
      const oversizedCalldata = '0x' + '00'.repeat(10001); // 10,001 bytes

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          oversizedCalldata,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'CalldataTooLarge');
    });
  });

  describe('Cross-Chain Failure Scenarios', function () {
    it('Should handle Stargate router failures gracefully', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      // Simulate router failure by setting insufficient fee
      await mockStargateRouter.setQuoteLayerZeroFee(ethers.parseEther('1'), 0); // Very high fee

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'ExcessiveFee');
    });

    it('Should handle insufficient ETH balance for cross-chain fees', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      // Drain ETH from contract
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const withdrawAmount = currentBalance - ethers.parseEther('0.001'); // Leave minimal ETH
      await stargateCompass.withdrawETH(withdrawAmount);

      const highFee = ethers.parseEther('0.05'); // More than remaining balance
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(highFee, 0);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });

    it('Should handle LayerZero fee quote failures', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);

      // Test with zero fees (potential edge case)
      await mockStargateRouter.setQuoteLayerZeroFee(0, 0);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted; // Zero fees should be acceptable
    });
  });

  describe('Cross-Chain Security Validations', function () {
    it('Should validate remote swap router addresses', async function () {
      const { stargateCompass, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      // Test with zero address
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          ethers.ZeroAddress, // Invalid zero address
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InvalidAddress');
    });

    it('Should validate cross-chain operation parameters', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      // Test with zero loan amount
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          0, // Invalid zero amount
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount');

      // Test with zero minimum amount out
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          0, // Invalid zero minimum amount
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InvalidAmount');
    });

    it('Should apply slippage protection in cross-chain operations', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      // Test with different slippage tolerances
      const slippageTests = [
        { slippage: 100, description: '1% slippage' }, // 100 BPS = 1%
        { slippage: 200, description: '2% slippage' }, // 200 BPS = 2%
        { slippage: 500, description: '5% slippage' }, // 500 BPS = 5%
        { slippage: 1000, description: '10% slippage' }, // 1000 BPS = 10%
      ];

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      for (const test of slippageTests) {
        await stargateCompass.setMaxSlippage(test.slippage);

        await expect(
          stargateCompass.executeRemoteDegenSwap(
            LOAN_AMOUNT,
            REMOTE_CALLDATA,
            owner.address,
            MIN_AMOUNT_OUT,
            EXPECTED_PROFIT
          )
        ).to.not.be.reverted;
      }
    });

    it('Should reject slippage tolerance exceeding maximum', async function () {
      const { stargateCompass } = await loadFixture(deployCrossChainFixture);

      const excessiveSlippage = 1500; // 15% - above 10% maximum

      await expect(stargateCompass.setMaxSlippage(excessiveSlippage))
        .to.be.revertedWithCustomError(
          stargateCompass,
          'SlippageExceedsMaximum'
        )
        .withArgs(excessiveSlippage, 1000);
    });
  });

  describe('Cross-Chain ETH Management', function () {
    it('Should maintain sufficient ETH balance for cross-chain operations', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      // Test multiple operations to ensure ETH balance is managed properly
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      const initialBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );

      // Execute multiple operations
      for (let i = 0; i < 3; i++) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            LOAN_AMOUNT,
            REMOTE_CALLDATA,
            owner.address,
            MIN_AMOUNT_OUT,
            EXPECTED_PROFIT
          )
        ).to.not.be.reverted;
      }

      const finalBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      expect(finalBalance).to.be.lt(initialBalance); // Balance should decrease due to fees
    });

    it('Should allow ETH deposits for cross-chain fee payments', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployCrossChainFixture
      );

      const depositAmount = ethers.parseEther('0.5');
      const initialBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );

      await expect(stargateCompass.depositETH({ value: depositAmount }))
        .to.emit(stargateCompass, 'ETHDeposited')
        .withArgs(depositAmount, owner.address);

      const finalBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      expect(finalBalance).to.equal(initialBalance + depositAmount);
    });

    it('Should prevent ETH withdrawal below minimum reserve during active operations', async function () {
      const { stargateCompass, owner } = await loadFixture(
        deployCrossChainFixture
      );

      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const minReserve = ethers.parseEther('0.05'); // MIN_ETH_BALANCE
      const excessiveWithdrawal =
        currentBalance - minReserve + ethers.parseEther('0.01');

      await expect(stargateCompass.withdrawETH(excessiveWithdrawal))
        .to.be.revertedWithCustomError(stargateCompass, 'InsufficientReserve')
        .withArgs(excessiveWithdrawal, currentBalance, minReserve);
    });
  });

  describe('Cross-Chain Operation Monitoring', function () {
    it('Should emit appropriate events during cross-chain operations', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      const flashLoanCosts = (LOAN_AMOUNT * 5n) / 10000n;
      const minimumProfit = (LOAN_AMOUNT * 50n) / 10000n;
      const totalRequiredProfit = flashLoanCosts + minimumProfit;

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          REMOTE_CALLDATA,
          owner.address,
          MIN_AMOUNT_OUT,
          EXPECTED_PROFIT
        )
      )
        .to.emit(stargateCompass, 'ProfitabilityValidated')
        .withArgs(LOAN_AMOUNT, EXPECTED_PROFIT, totalRequiredProfit);
    });

    it('Should provide visibility into cross-chain operation costs', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const customFee = ethers.parseEther('0.03'); // 0.03 ETH
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(customFee, 0);

      const initialBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );

      await stargateCompass.executeRemoteDegenSwap(
        LOAN_AMOUNT,
        REMOTE_CALLDATA,
        owner.address,
        MIN_AMOUNT_OUT,
        EXPECTED_PROFIT
      );

      const finalBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const actualCost = initialBalance - finalBalance;

      // The actual cost should be at least the quoted fee
      expect(actualCost).to.be.gte(customFee);
    });
  });

  describe('Cross-Chain Integration Edge Cases', function () {
    it('Should handle edge case with minimum viable loan amounts', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const minLoanAmount = ethers.parseUnits('1', 6); // 1 USDC
      const minProfit = ethers.parseUnits('0.01', 6); // 0.01 USDC

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.001'),
        0
      ); // Very low fee

      // This should fail due to insufficient profit
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          minLoanAmount,
          REMOTE_CALLDATA,
          owner.address,
          ethers.parseUnits('0.98', 6), // 2% slippage
          minProfit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
    });

    it('Should handle maximum viable loan amounts', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      const maxLoanAmount = ethers.parseUnits('100000', 6); // 100k USDC (pool limit)
      const maxProfit = ethers.parseUnits('1000', 6); // 1k USDC profit

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          maxLoanAmount,
          REMOTE_CALLDATA,
          owner.address,
          ethers.parseUnits('98000', 6), // 2% slippage
          maxProfit
        )
      ).to.not.be.reverted;
    });

    it('Should handle concurrent cross-chain operation attempts', async function () {
      const { stargateCompass, owner, mockPool, mockStargateRouter } =
        await loadFixture(deployCrossChainFixture);

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(LAYERZERO_FEE, 0);

      // Execute operations sequentially (contract doesn't support concurrent operations)
      for (let i = 0; i < 3; i++) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            LOAN_AMOUNT,
            REMOTE_CALLDATA,
            owner.address,
            MIN_AMOUNT_OUT,
            EXPECTED_PROFIT
          )
        ).to.not.be.reverted;
      }
    });
  });
});
