# Configuration Centralization & Dynamic Risk Hardening Implementation

## Overview

This document details the implementation of **Phase 3: Configuration Centralization** and **Phases 4-5: Dynamic Risk Hardening** for the Zen Geometer bot. These enhancements eliminate hardcoded values, implement real-time slippage protection, and enable regime-aware dynamic risk management.

## Phase 3: Configuration Centralization

### 3.1 Hardcoded Values Audit

**Identified and Centralized:**

1. **Strategy Decision Parameters**
   - `min_execution_score`: Moved from hardcoded `7.5` to `config.strategies.min_execution_score`
   - `quality_ratio_floor`: Moved from hardcoded `2.5` to `config.strategies.quality_ratio_floor`

2. **Risk Management Parameters**
   - `max_daily_loss_usd`: Centralized daily loss circuit breaker
   - `kelly_fraction_config`: Configurable Kelly criterion fraction
   - `default_max_position_size_usd`: Baseline position size for dynamic adjustment

3. **Scanner-Specific Thresholds**
   - **GazeScanner**: `min_price_deviation_pct`, `block_check_delay_ms`
   - **PilotFishScanner**: `min_whale_trade_usd`, `profit_multiplier`, `default_volatility`

4. **Execution Parameters**
   - `default_slippage_tolerance`: Pre-flight slippage protection
   - `fallback_base_fee_gwei`: Gas estimation fallback
   - Gas buffer multipliers for different urgency levels
   - Priority fee configurations

### 3.2 Configuration Structure

```rust
// Enhanced configuration structs in src/config.rs

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StrategiesConfig {
    pub unified: UnifiedStrategyConfig,
    pub basilisk_gaze: BasiliskGazeConfig,
    // Phase 3: Centralized strategy decision-making parameters
    pub min_execution_score: Decimal,
    pub quality_ratio_floor: Decimal,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RiskConfig {
    // Existing fields...
    // Phase 3: Centralized risk parameters
    pub max_daily_loss_usd: Decimal,
    pub kelly_fraction_config: Decimal,
    pub default_max_position_size_usd: Decimal,
}
```

### 3.3 Configuration File Updates

**config/default.toml** now includes:

```toml
# Core strategy decision-making parameters
[strategies]
min_execution_score = "7.5"        # Minimum score from ScoringEngine
quality_ratio_floor = "2.5"        # Minimum quality ratio for opportunities

# Risk management parameters
[risk]
max_daily_loss_usd = "-1000.0"     # Maximum daily loss before circuit breaker
kelly_fraction_config = "0.25"     # Quarter-Kelly for conservative sizing
default_max_position_size_usd = "50000.0"  # Baseline for dynamic adjustment

# Phase 4: Execution configuration
[execution]
default_slippage_tolerance = "0.005"  # 0.5% default slippage tolerance
fallback_base_fee_gwei = "20.0"       # Fallback base fee for gas estimation

[execution.gas_buffer_multipliers]
low = "1.1"      # 1.1x for low urgency
medium = "1.5"   # 1.5x for medium urgency
high = "2.0"     # 2.0x for high urgency
critical = "3.0" # 3.0x for critical urgency
```

## Phase 4: Real-time Slippage Protection

### 4.1 Enhanced SlippageCalculator

**Location**: `src/execution/slippage_calculator.rs`

```rust
impl SlippageCalculator {
    pub fn calculate_min_amount_out(
        &self,
        expected_output_amount: U256,
        slippage_tolerance: Decimal,
    ) -> Result<U256, ExecutionError> {
        // Validates slippage tolerance is between 0.0 and 1.0
        // Calculates: min_out = expected_out * (1.0 - slippage_tolerance)
        // Returns minimum acceptable output amount for on-chain enforcement
    }
}
```

### 4.2 Pre-flight Check Integration

**Location**: `src/execution/manager.rs`

```rust
impl ExecutionManager {
    /// Phase 4: Pre-flight check that returns the required on-chain parameter
    pub async fn pre_execution_check(
        &self,
        opportunity: &Opportunity,
    ) -> Result<ethers::types::U256, anyhow::Error> {
        // Extract expected output amount from opportunity
        let expected_output_amount = match opportunity {
            Opportunity::DexArbitrage { data, .. } => {
                // Calculate from arbitrage path
            },
            Opportunity::PilotFish { data, .. } => {
                // Calculate from capital requirement
            },
            // ... other opportunity types
        };

        // Calculate minimum amount out using configured slippage tolerance
        let min_amount_out = self.slippage_calculator.calculate_min_amount_out(
            expected_output_amount,
            self.execution_config.default_slippage_tolerance,
        )?;

        Ok(min_amount_out)
    }
}
```

### 4.3 On-chain Parameter Enforcement

The calculated `min_amount_out` is now passed to transaction builders:

```rust
// In process_unified_opportunity
let min_amount_out = self.pre_execution_check(&opportunity).await?;

// Pass to dispatcher for on-chain enforcement
self.dispatch_simple_swap(&opportunity, predicted_bid, min_amount_out, &event_builder).await?;
```

## Phase 5: Dynamic Risk Management

### 5.1 Enhanced RiskManager

**Location**: `src/risk/manager.rs`

```rust
pub struct RiskManager {
    nats_client: NatsClient,
    baseline_settings: RiskConfig,
    // Dynamic (current) settings - thread-safe with Arc<Mutex>
    pub max_position_size_usd: Arc<Mutex<Decimal>>,
    pub max_daily_loss: Arc<Mutex<Decimal>>,
    pub kelly_fraction: Arc<Mutex<Decimal>>,
}
```

### 5.2 Regime-Aware Risk Adjustment

```rust
async fn adjust_risk_limits(&self, regime: &MarketRegime) {
    let (new_pos_size, new_loss_limit, new_kelly) = match regime {
        MarketRegime::HighVolatilityCorrection | MarketRegime::BotGasWar => {
            // High-risk regime: Reduce all limits
            (
                baseline.default_max_position_size_usd * dec!(0.25), // 25% of normal
                baseline.max_daily_loss_usd * dec!(0.5),             // 50% of normal
                baseline.kelly_fraction_config * dec!(0.5)          // Half Kelly
            )
        },
        MarketRegime::CalmOrderly => {
            // Restore to baseline
            (baseline.default_max_position_size_usd, baseline.max_daily_loss_usd, baseline.kelly_fraction_config)
        },
        MarketRegime::RetailFomoSpike => {
            // Slightly increase limits for favorable conditions
            (
                baseline.default_max_position_size_usd * dec!(1.2), // 120% of normal
                baseline.max_daily_loss_usd * dec!(1.5),             // 150% of normal
                baseline.kelly_fraction_config * dec!(1.1)          // 110% of normal
            )
        },
    };
    
    // Update dynamic values atomically
    *self.max_position_size_usd.lock().await = new_pos_size;
    *self.max_daily_loss.lock().await = new_loss_limit;
    *self.kelly_fraction.lock().await = new_kelly;
}
```

### 5.3 Kelly Criterion Implementation

```rust
/// Calculate optimal trade size using Kelly criterion
pub async fn calculate_trade_size(
    &self,
    win_probability: Decimal,
    win_loss_ratio: Decimal,
    portfolio_value_usd: Decimal,
) -> Result<Decimal, anyhow::Error> {
    let current_kelly_fraction = *self.kelly_fraction.lock().await;
    let max_position_usd = *self.max_position_size_usd.lock().await;

    // Kelly formula: f = (bp - q) / b
    let loss_probability = dec!(1.0) - win_probability;
    let kelly_fraction_optimal = (win_loss_ratio * win_probability - loss_probability) / win_loss_ratio;

    // Apply configured Kelly fraction multiplier (e.g., quarter-Kelly for safety)
    let kelly_fraction_adjusted = kelly_fraction_optimal * current_kelly_fraction;

    // Calculate and cap position size
    let calculated_size = portfolio_value_usd * kelly_fraction_adjusted;
    let final_size = calculated_size.min(max_position_usd);

    Ok(final_size.max(dec!(0.0)))
}
```

## Error Handling Enhancements

### Enhanced ExecutionError Types

**Location**: `src/error.rs`

```rust
#[derive(Error, Debug)]
pub enum ExecutionError {
    // Existing errors...
    
    // Phase 4: Enhanced pre-flight check errors
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    
    #[error("Slippage calculation failed: {0}")]
    SlippageCalculationFailed(String),
    
    #[error("Pre-flight check failed: {0}")]
    PreFlightCheckFailed(String),
    
    #[error("Risk check failed: {0}")]
    RiskCheckFailed(String),
}
```

## Integration Points

### 5.1 StrategyManager Integration

The StrategyManager now uses centralized configuration:

```rust
// Use centralized min_execution_score instead of hardcoded values
if score >= self.min_execution_score {
    // Approve opportunity
}
```

### 5.2 ExecutionManager Integration

```rust
// Pre-flight check before execution
let min_amount_out = self.pre_execution_check(&opportunity).await?;

// Risk management check
if !self.risk_manager.is_trade_size_acceptable(trade_amount_usd).await {
    return Err(anyhow::anyhow!("Trade size exceeds risk limits"));
}
```

## Testing Strategy

### Comprehensive Test Suite

**Location**: `tests/test_configuration_centralization_v2.rs`

1. **Configuration Loading Tests**
   - Verify all parameters load correctly from config files
   - Validate parameter ranges and constraints

2. **Slippage Calculator Tests**
   - Test calculation accuracy with various tolerances
   - Verify error handling for invalid inputs

3. **Dynamic Risk Management Tests**
   - Test regime-based risk adjustment
   - Verify Kelly criterion calculations
   - Test thread-safety of dynamic parameters

4. **Integration Tests**
   - End-to-end pre-flight check workflow
   - Risk manager integration with execution flow

## Configuration Validation

### Validation Rules

```rust
impl Settings {
    fn validate(&self) -> Result<(), ConfigError> {
        // Strategy validation
        if self.strategies.min_execution_score <= dec!(0.0) {
            return Err(ConfigError::InvalidValue {
                field: "strategies.min_execution_score".to_string(),
                value: self.strategies.min_execution_score.to_string(),
            });
        }

        // Risk validation
        if self.risk.kelly_fraction_config <= dec!(0.0) || self.risk.kelly_fraction_config > dec!(1.0) {
            return Err(ConfigError::InvalidValue {
                field: "risk.kelly_fraction_config".to_string(),
                value: self.risk.kelly_fraction_config.to_string(),
            });
        }

        // Execution validation
        if self.execution.default_slippage_tolerance <= dec!(0.0) || self.execution.default_slippage_tolerance >= dec!(1.0) {
            return Err(ConfigError::InvalidValue {
                field: "execution.default_slippage_tolerance".to_string(),
                value: self.execution.default_slippage_tolerance.to_string(),
            });
        }

        Ok(())
    }
}
```

## Benefits Achieved

### 3.1 Maintainability
- **No More Magic Numbers**: All thresholds and parameters are explicitly configured
- **Easy Tuning**: Parameters can be adjusted without code recompilation
- **Environment-Specific Configs**: Different settings for development, testing, and production

### 3.2 Risk Management
- **Real-time Slippage Protection**: Pre-flight checks prevent excessive slippage
- **Dynamic Risk Adjustment**: Risk limits automatically adjust to market conditions
- **Kelly Criterion Sizing**: Mathematically optimal position sizing

### 3.3 Operational Excellence
- **Configuration Validation**: Invalid configurations are caught at startup
- **Thread-Safe Updates**: Dynamic parameters can be safely updated during operation
- **Comprehensive Testing**: All configuration paths are thoroughly tested

## Future Enhancements

### 5.1 Hot Configuration Reloading
- Implement configuration file watching
- Allow parameter updates without restart

### 5.2 Advanced Risk Models
- Implement portfolio-level risk metrics
- Add correlation-based risk adjustments

### 5.3 Machine Learning Integration
- Dynamic parameter optimization based on historical performance
- Regime detection using ML models

## Conclusion

The configuration centralization and dynamic risk hardening implementation provides:

1. **Complete elimination** of hardcoded values in critical paths
2. **Real-time slippage protection** with configurable tolerances
3. **Dynamic risk management** that adapts to market conditions
4. **Robust error handling** with specific error types for different failure modes
5. **Comprehensive testing** ensuring reliability and correctness

This implementation follows Rust best practices, maintains thread safety, and provides a solid foundation for future enhancements to the Zen Geometer bot's risk management capabilities.