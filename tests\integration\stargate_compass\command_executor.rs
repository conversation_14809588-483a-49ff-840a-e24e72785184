// MISSION: Command Executor - Low-level TUI Process Management and Communication
// WHY: Handle process spawning, input/output capture, and timeout management
// HOW: Tokio process management with async I/O and proper cleanup

use anyhow::{Context, Result};
use std::process::Stdio;
use std::time::{Duration, Instant};
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::process::{Child, Command};
use tokio::sync::mpsc;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};

/// Command execution configuration
#[derive(Debug, Clone)]
pub struct CommandConfig {
    pub binary_path: String,
    pub args: Vec<String>,
    pub env_vars: std::collections::HashMap<String, String>,
    pub working_directory: Option<String>,
    pub timeout_seconds: u64,
    pub capture_stderr: bool,
}

/// Command execution result
#[derive(Debug)]
pub struct CommandExecutionResult {
    pub success: bool,
    pub exit_code: Option<i32>,
    pub stdout: String,
    pub stderr: String,
    pub execution_time: Duration,
    pub timed_out: bool,
}

/// Process output line with metadata
#[derive(Debug, Clone)]
pub struct OutputLine {
    pub content: String,
    pub timestamp: Instant,
    pub source: OutputSource,
}

#[derive(Debug, Clone)]
pub enum OutputSource {
    Stdout,
    Stderr,
}

/// Command executor for TUI processes
pub struct CommandExecutor {
    config: CommandConfig,
    output_buffer: Vec<OutputLine>,
    process_handle: Option<Child>,
}

impl CommandExecutor {
    pub fn new(config: CommandConfig) -> Self {
        Self {
            config,
            output_buffer: Vec::new(),
            process_handle: None,
        }
    }

    /// Create a new command executor for TUI harness
    pub fn new_tui_harness(anvil_url: String, contract_address: String) -> Self {
        let mut env_vars = std::collections::HashMap::new();
        env_vars.insert("NATS_URL".to_string(), "nats://localhost:4222".to_string());
        env_vars.insert("ANVIL_URL".to_string(), anvil_url);
        env_vars.insert("CONTRACT_ADDRESS".to_string(), contract_address);
        env_vars.insert("RUST_LOG".to_string(), "info".to_string());

        let config = CommandConfig {
            binary_path: "cargo".to_string(),
            args: vec!["run".to_string(), "--bin".to_string(), "tui_harness".to_string()],
            env_vars,
            working_directory: None,
            timeout_seconds: 30,
            capture_stderr: true,
        };

        Self::new(config)
    }

    /// Spawn the command process
    pub async fn spawn(&mut self) -> Result<()> {
        info!("Spawning command: {} {:?}", self.config.binary_path, self.config.args);

        let mut cmd = Command::new(&self.config.binary_path);
        cmd.args(&self.config.args)
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(if self.config.capture_stderr { Stdio::piped() } else { Stdio::null() })
            .kill_on_drop(true);

        // Set environment variables
        for (key, value) in &self.config.env_vars {
            cmd.env(key, value);
        }

        // Set working directory if specified
        if let Some(ref working_dir) = self.config.working_directory {
            cmd.current_dir(working_dir);
        }

        let child = cmd.spawn()
            .context("Failed to spawn command process")?;

        self.process_handle = Some(child);

        // Wait for process to initialize
        tokio::time::sleep(Duration::from_millis(2000)).await;

        info!("Command process spawned successfully");
        Ok(())
    }

    /// Execute command with input and capture output
    pub async fn execute_with_input(&mut self, input: &str) -> Result<CommandExecutionResult> {
        let start_time = Instant::now();

        if self.process_handle.is_none() {
            self.spawn().await?;
        }

        let process = self.process_handle.as_mut()
            .ok_or_else(|| anyhow::anyhow!("No process handle available"))?;

        // Setup output capture
        let (output_tx, mut output_rx) = mpsc::channel(1000);
        let mut stdout_lines = Vec::new();
        let mut stderr_lines = Vec::new();

        // Capture stdout
        if let Some(stdout) = process.stdout.take() {
            let tx = output_tx.clone();
            tokio::spawn(async move {
                let mut reader = BufReader::new(stdout);
                let mut line = String::new();
                while reader.read_line(&mut line).await.is_ok() {
                    if line.trim().is_empty() {
                        continue;
                    }
                    let output_line = OutputLine {
                        content: line.trim().to_string(),
                        timestamp: Instant::now(),
                        source: OutputSource::Stdout,
                    };
                    if tx.send(output_line).await.is_err() {
                        break;
                    }
                    line.clear();
                }
            });
        }

        // Capture stderr if enabled
        if self.config.capture_stderr {
            if let Some(stderr) = process.stderr.take() {
                let tx = output_tx.clone();
                tokio::spawn(async move {
                    let mut reader = BufReader::new(stderr);
                    let mut line = String::new();
                    while reader.read_line(&mut line).await.is_ok() {
                        if line.trim().is_empty() {
                            continue;
                        }
                        let output_line = OutputLine {
                            content: line.trim().to_string(),
                            timestamp: Instant::now(),
                            source: OutputSource::Stderr,
                        };
                        if tx.send(output_line).await.is_err() {
                            break;
                        }
                        line.clear();
                    }
                });
            }
        }

        // Send input to process
        if let Some(stdin) = process.stdin.as_mut() {
            debug!("Sending input to process: {}", input);
            stdin.write_all(input.as_bytes()).await
                .context("Failed to write input to process")?;
            stdin.flush().await
                .context("Failed to flush input to process")?;
        }

        // Collect output with timeout
        let timeout_duration = Duration::from_secs(self.config.timeout_seconds);
        let output_result = timeout(timeout_duration, async {
            while let Some(output_line) = output_rx.recv().await {
                match output_line.source {
                    OutputSource::Stdout => stdout_lines.push(output_line),
                    OutputSource::Stderr => stderr_lines.push(output_line),
                }
                
                // Store in buffer for later analysis
                self.output_buffer.push(output_line);
            }
        }).await;

        let execution_time = start_time.elapsed();
        let timed_out = output_result.is_err();

        if timed_out {
            warn!("Command execution timed out after {:?}", timeout_duration);
        }

        // Try to get exit status
        let exit_code = if let Ok(status) = process.try_wait() {
            status.and_then(|s| s.code())
        } else {
            None
        };

        let stdout = stdout_lines.iter()
            .map(|line| line.content.clone())
            .collect::<Vec<_>>()
            .join("\n");

        let stderr = stderr_lines.iter()
            .map(|line| line.content.clone())
            .collect::<Vec<_>>()
            .join("\n");

        let success = !timed_out && exit_code.unwrap_or(1) == 0;

        Ok(CommandExecutionResult {
            success,
            exit_code,
            stdout,
            stderr,
            execution_time,
            timed_out,
        })
    }

    /// Send raw input to the process (for TUI key sequences)
    pub async fn send_raw_input(&mut self, input: &[u8]) -> Result<()> {
        if let Some(process) = &mut self.process_handle {
            if let Some(stdin) = process.stdin.as_mut() {
                stdin.write_all(input).await
                    .context("Failed to send raw input to process")?;
                stdin.flush().await
                    .context("Failed to flush raw input")?;
                debug!("Sent {} bytes of raw input to process", input.len());
            }
        }
        Ok(())
    }

    /// Send a sequence of key inputs with timing
    pub async fn send_key_sequence(&mut self, keys: &[KeyInput]) -> Result<()> {
        for key in keys {
            match key {
                KeyInput::Raw(bytes) => {
                    self.send_raw_input(bytes).await?;
                }
                KeyInput::Char(c) => {
                    self.send_raw_input(&[*c as u8]).await?;
                }
                KeyInput::SpecialKey(special) => {
                    let bytes = special.to_bytes();
                    self.send_raw_input(&bytes).await?;
                }
                KeyInput::Wait(duration) => {
                    tokio::time::sleep(*duration).await;
                }
            }
            
            // Small delay between key presses for TUI processing
            tokio::time::sleep(Duration::from_millis(50)).await;
        }
        Ok(())
    }

    /// Capture output for a specified duration
    pub async fn capture_output(&mut self, duration: Duration) -> Result<Vec<OutputLine>> {
        let (tx, mut rx) = mpsc::channel(1000);
        let mut captured_lines = Vec::new();

        // Start capture task
        if let Some(process) = &mut self.process_handle {
            if let Some(stdout) = process.stdout.take() {
                let tx_clone = tx.clone();
                tokio::spawn(async move {
                    let mut reader = BufReader::new(stdout);
                    let mut line = String::new();
                    while reader.read_line(&mut line).await.is_ok() {
                        if !line.trim().is_empty() {
                            let output_line = OutputLine {
                                content: line.trim().to_string(),
                                timestamp: Instant::now(),
                                source: OutputSource::Stdout,
                            };
                            if tx_clone.send(output_line).await.is_err() {
                                break;
                            }
                        }
                        line.clear();
                    }
                });
            }
        }

        // Capture for specified duration
        let capture_result = timeout(duration, async {
            while let Some(line) = rx.recv().await {
                captured_lines.push(line);
            }
        }).await;

        if capture_result.is_err() {
            debug!("Output capture completed after timeout");
        }

        Ok(captured_lines)
    }

    /// Check if process is still running
    pub async fn is_running(&mut self) -> bool {
        if let Some(process) = &mut self.process_handle {
            match process.try_wait() {
                Ok(Some(_)) => false, // Process has exited
                Ok(None) => true,     // Process is still running
                Err(_) => false,      // Error checking status
            }
        } else {
            false
        }
    }

    /// Terminate the process
    pub async fn terminate(&mut self) -> Result<()> {
        if let Some(mut process) = self.process_handle.take() {
            info!("Terminating command process");
            
            // Try graceful shutdown first
            if let Some(stdin) = process.stdin.as_mut() {
                let _ = stdin.write_all(&[3]).await; // Ctrl+C
                let _ = stdin.flush().await;
            }

            // Wait a moment for graceful shutdown
            tokio::time::sleep(Duration::from_millis(1000)).await;

            // Force kill if still running
            if let Err(e) = process.kill().await {
                warn!("Failed to kill process: {}", e);
            }

            // Wait for process to exit
            if let Err(e) = process.wait().await {
                warn!("Failed to wait for process exit: {}", e);
            }

            info!("Command process terminated");
        }
        Ok(())
    }

    /// Get captured output buffer
    pub fn get_output_buffer(&self) -> &[OutputLine] {
        &self.output_buffer
    }

    /// Clear output buffer
    pub fn clear_output_buffer(&mut self) {
        self.output_buffer.clear();
    }

    /// Search output buffer for patterns
    pub fn search_output(&self, pattern: &str) -> Vec<&OutputLine> {
        self.output_buffer.iter()
            .filter(|line| line.content.contains(pattern))
            .collect()
    }

    /// Get recent output lines
    pub fn get_recent_output(&self, duration: Duration) -> Vec<&OutputLine> {
        let cutoff = Instant::now() - duration;
        self.output_buffer.iter()
            .filter(|line| line.timestamp > cutoff)
            .collect()
    }
}

/// Key input types for TUI interaction
#[derive(Debug, Clone)]
pub enum KeyInput {
    Raw(Vec<u8>),
    Char(char),
    SpecialKey(SpecialKey),
    Wait(Duration),
}

/// Special key definitions
#[derive(Debug, Clone)]
pub enum SpecialKey {
    Tab,
    Enter,
    Escape,
    Up,
    Down,
    Left,
    Right,
    CtrlC,
    CtrlD,
    Backspace,
    Delete,
}

impl SpecialKey {
    pub fn to_bytes(&self) -> Vec<u8> {
        match self {
            SpecialKey::Tab => vec![9],
            SpecialKey::Enter => vec![13],
            SpecialKey::Escape => vec![27],
            SpecialKey::Up => vec![27, 91, 65],    // ESC[A
            SpecialKey::Down => vec![27, 91, 66],  // ESC[B
            SpecialKey::Left => vec![27, 91, 68],  // ESC[D
            SpecialKey::Right => vec![27, 91, 67], // ESC[C
            SpecialKey::CtrlC => vec![3],
            SpecialKey::CtrlD => vec![4],
            SpecialKey::Backspace => vec![8],
            SpecialKey::Delete => vec![127],
        }
    }
}

impl Drop for CommandExecutor {
    fn drop(&mut self) {
        if self.process_handle.is_some() {
            // Note: In async drop, we can't await, so we'll just try to kill
            if let Some(mut process) = self.process_handle.take() {
                let _ = process.start_kill();
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_command_executor_creation() {
        let executor = CommandExecutor::new_tui_harness(
            "http://localhost:8545".to_string(),
            "0x1234567890123456789012345678901234567890".to_string(),
        );

        assert_eq!(executor.config.binary_path, "cargo");
        assert!(executor.config.args.contains(&"tui_harness".to_string()));
        assert!(executor.config.env_vars.contains_key("ANVIL_URL"));
    }

    #[test]
    fn test_special_key_bytes() {
        assert_eq!(SpecialKey::Tab.to_bytes(), vec![9]);
        assert_eq!(SpecialKey::Enter.to_bytes(), vec![13]);
        assert_eq!(SpecialKey::Up.to_bytes(), vec![27, 91, 65]);
        assert_eq!(SpecialKey::CtrlC.to_bytes(), vec![3]);
    }

    #[tokio::test]
    async fn test_key_input_sequence() {
        let mut executor = CommandExecutor::new_tui_harness(
            "http://localhost:8545".to_string(),
            "0x1234567890123456789012345678901234567890".to_string(),
        );

        let key_sequence = vec![
            KeyInput::Char('1'),
            KeyInput::SpecialKey(SpecialKey::Tab),
            KeyInput::Wait(Duration::from_millis(100)),
            KeyInput::SpecialKey(SpecialKey::Enter),
        ];

        // This test would require a running process, so we'll just verify the sequence is valid
        assert_eq!(key_sequence.len(), 4);
    }
}