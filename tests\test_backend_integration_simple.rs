// Simple test for Backend Integration Tester
// This test verifies that the Backend Integration Tester component works correctly

use std::time::Duration;
use anyhow::Result;

// Import the integration testing modules directly
use basilisk_bot::*;

// Include the integration test modules
#[path = "integration/stargate_compass/core.rs"]
mod core;

#[path = "integration/stargate_compass/backend_tester.rs"]
mod backend_tester;

#[path = "integration/stargate_compass/anvil_client.rs"]
mod anvil_client;

use core::*;
use backend_tester::BackendIntegrationTester;

#[tokio::test]
async fn test_backend_integration_tester_creation() -> Result<()> {
    println!("🧪 Testing Backend Integration Tester creation...");
    
    // Test that BackendIntegrationTester can be created
    let backend_tester = BackendIntegrationTester::new();
    assert_eq!(backend_tester.component_name(), "BackendIntegration");
    
    println!("✅ Backend Integration Tester created successfully");
    println!("   Component name: {}", backend_tester.component_name());
    
    Ok(())
}

#[tokio::test]
async fn test_backend_integration_tester_readiness() -> Result<()> {
    println!("🧪 Testing Backend Integration Tester readiness...");
    
    let backend_tester = BackendIntegrationTester::new();
    
    // Test that it reports readiness status
    let is_ready = backend_tester.is_ready().await?;
    println!("✅ Backend tester ready status: {}", is_ready);
    
    // Test setup
    backend_tester.setup().await?;
    println!("✅ Backend tester setup completed");
    
    // Test cleanup
    backend_tester.cleanup().await?;
    println!("✅ Backend tester cleanup completed");
    
    Ok(())
}

#[tokio::test]
async fn test_backend_integration_tester_execution() -> Result<()> {
    println!("🧪 Testing Backend Integration Tester execution...");
    
    let backend_tester = BackendIntegrationTester::new();
    
    // Run the integration test
    let test_result = backend_tester.run_test().await?;
    
    println!("📊 Test Result:");
    println!("  - Component: {}", test_result.component_name);
    println!("  - Success: {}", test_result.success);
    println!("  - Execution Time: {:?}", test_result.execution_time);
    println!("  - Errors: {}", test_result.errors.len());
    println!("  - Warnings: {}", test_result.warnings.len());
    
    // Verify the test structure
    match test_result.details {
        TestDetails::Backend(backend_result) => {
            println!("📈 Backend Test Details:");
            println!("  - ExecutionManager tests: {}", backend_result.execution_manager_tests.len());
            println!("  - ExecutionDispatcher tests: {}", backend_result.execution_dispatcher_tests.len());
            println!("  - Transaction validations: {}", backend_result.transaction_validations.len());
            println!("  - Opportunities tested: {}", backend_result.opportunities_tested);
            println!("  - Successful transactions: {}", backend_result.successful_transactions);
            
            // Verify that tests were actually run
            assert!(!backend_result.execution_manager_tests.is_empty(), "ExecutionManager tests should be present");
            assert!(!backend_result.execution_dispatcher_tests.is_empty(), "ExecutionDispatcher tests should be present");
            
            println!("✅ Backend integration tests executed successfully");
        }
        _ => panic!("Expected Backend test details"),
    }
    
    Ok(())
}

#[test]
fn test_backend_integration_data_structures() {
    println!("🧪 Testing Backend Integration data structures...");
    
    // Test TestResult creation
    let success_result = TestResult::success(
        "TestBackend".to_string(),
        TestDetails::Backend(BackendTestResult {
            success: true,
            execution_manager_tests: vec![],
            execution_dispatcher_tests: vec![],
            strategy_manager_tests: vec![],
            transaction_validations: vec![],
            contract_interactions: vec![],
            opportunities_tested: 1,
            successful_transactions: 1,
        })
    );
    
    assert!(success_result.success);
    assert_eq!(success_result.component_name, "TestBackend");
    println!("✅ TestResult creation works correctly");
    
    // Test error creation
    let error = TestError::new(
        IntegrationTestError::BackendIntegrationError {
            component: "ExecutionManager".to_string(),
            function: "process_opportunity".to_string(),
            error_type: BackendErrorType::ExecutionFailed,
            message: "Test error".to_string(),
        },
        "Test context".to_string(),
        true
    );
    
    assert!(error.recoverable);
    assert_eq!(error.context, "Test context");
    println!("✅ TestError creation works correctly");
    
    println!("✅ Backend Integration data structures test passed!");
}

#[test]
fn test_component_test_result_structure() {
    println!("🧪 Testing ComponentTestResult structure...");
    
    use std::collections::HashMap;
    
    let mut test_data = HashMap::new();
    test_data.insert("test_key".to_string(), "test_value".to_string());
    
    let component_result = ComponentTestResult {
        component_name: "ExecutionManager".to_string(),
        function_name: "process_opportunity".to_string(),
        success: true,
        execution_time: Duration::from_millis(100),
        error_message: None,
        test_data,
    };
    
    assert_eq!(component_result.component_name, "ExecutionManager");
    assert_eq!(component_result.function_name, "process_opportunity");
    assert!(component_result.success);
    assert!(component_result.error_message.is_none());
    assert_eq!(component_result.test_data.get("test_key"), Some(&"test_value".to_string()));
    
    println!("✅ ComponentTestResult structure works correctly");
}

#[test]
fn test_transaction_validation_result_structure() {
    println!("🧪 Testing TransactionValidationResult structure...");
    
    use ethers::types::{H256, U256};
    
    let validation_result = TransactionValidationResult {
        transaction_hash: H256::zero(),
        success: true,
        reverted: false,
        gas_used: U256::from(21000),
        gas_price: U256::from(20_000_000_000u64), // 20 gwei
        return_values: vec!["RemoteSwapExecuted".to_string()],
        events_emitted: vec!["CrossChainSwapInitiated".to_string()],
        validation_errors: vec![],
        execution_time: Duration::from_millis(500),
    };
    
    assert!(validation_result.success);
    assert!(!validation_result.reverted);
    assert_eq!(validation_result.gas_used, U256::from(21000));
    assert!(!validation_result.return_values.is_empty());
    assert!(!validation_result.events_emitted.is_empty());
    assert!(validation_result.validation_errors.is_empty());
    
    println!("✅ TransactionValidationResult structure works correctly");
}