use tera::{Context, Tera};
use plotters::prelude::*;
use anyhow::Result;
use std::fs::File;
use std::io::Write;

// Assume TradeLogEntry and KpiSummary structs exist
// For now, let's define simple versions for compilation
pub struct TradeLogEntry {
    pub timestamp: i64,
    pub pnl: f64,
}

pub struct KpiSummary {
    pub total_pnl: f64,
    pub sharpe_ratio: f64,
    // Add other KPIs as needed
}

pub struct HtmlReporter {
    tera: Tera,
}

impl HtmlReporter {
    pub fn new() -> Result<Self> {
        let mut tera = Tera::new("templates/**/*.html")?;
        tera.autoescape_on(vec![]); // Allow raw SVG content
        Ok(Self { tera })
    }

    pub fn generate_report(
        &self,
        trade_log: &[TradeLogEntry],
        kpis: &KpiSummary,
        output_path: &str,
    ) -> Result<()> {
        let mut context = Context::new();
        context.insert("kpis", kpis);

        // Generate Equity Curve Chart
        let equity_curve_svg = self.generate_equity_curve_chart(trade_log)?;
        context.insert("equity_curve_svg", &equity_curve_svg);

        // Generate PnL Histogram Chart (placeholder for now)
        let pnl_histogram_svg = self.generate_pnl_histogram_chart(trade_log)?;
        context.insert("pnl_histogram_svg", &pnl_histogram_svg);

        let rendered = self.tera.render("report_template.html", &context)?;
        let mut file = File::create(output_path)?;
        file.write_all(rendered.as_bytes())?;

        Ok(())
    }

    fn generate_equity_curve_chart(&self, trade_log: &[TradeLogEntry]) -> Result<String> {
        let root_area = SVGBackend::with_string((800, 600)).into_drawing_area();
        root_area.fill(&WHITE)?;

        let mut chart = ChartBuilder::on(&root_area)
            .margin(5)
            .set_all_label_area_size(50)
            .build_cartesian_2d(
                (trade_log.first().ok_or_else(|| anyhow::anyhow!("Trade log is empty"))?.timestamp as f64)..(trade_log.last().ok_or_else(|| anyhow::anyhow!("Trade log is empty"))?.timestamp as f64),
                -100.0..100.0, // Placeholder for PnL range
            )?;

        chart
            .configure_mesh()
            .x_desc("Time")
            .y_desc("PnL")
            .draw()?;

        let pnl_series: Vec<(f64, f64)> = trade_log
            .iter()
            .map(|entry| (entry.timestamp as f64, entry.pnl))
            .collect();

        chart.draw_series(LineSeries::new(pnl_series, &RED))?;

        root_area.present()?;
        Ok(root_area.into_inner())
    }

    fn generate_pnl_histogram_chart(&self, _trade_log: &[TradeLogEntry]) -> Result<String> {
        // Placeholder for PnL Histogram
        let root_area = SVGBackend::with_string((800, 600)).into_drawing_area();
        root_area.fill(&WHITE)?;
        root_area.present()?;
        Ok(root_area.into_inner())
    }
}
