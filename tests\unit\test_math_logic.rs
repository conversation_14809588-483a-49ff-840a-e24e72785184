// Unit Tests for Mathematical Functions
// Tests FractalAnalyzer, decimal operations, and core mathematical logic

use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sqrt_precision() {
        let analyzer = FractalAnalyzer::new();
        let test_cases = vec![
            (dec!(4), dec!(2)),
            (dec!(9), dec!(3)),
            (dec!(16), dec!(4)),
            (dec!(25), dec!(5)),
            (dec!(2), dec!(1.41421356237)),
            (dec!(0.5), dec!(0.70710678118)),
        ];

        for (input, expected) in test_cases {
            let result = analyzer.sqrt(input);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(0.00001),
                "sqrt({}) failed. Result: {}, Expected: {}, Diff: {}",
                input,
                result,
                expected,
                difference
            );
        }
    }

    #[test]
    fn test_natural_log_precision() {
        let analyzer = FractalAnalyzer::new();
        let test_cases = vec![
            (dec!(1), dec!(0)),
            (dec!(2), dec!(0.69314718056)),
            (dec!(0.5), dec!(-0.69314718056)),
            (dec!(1.1), dec!(0.0953101798)),
        ];

        for (input, expected) in test_cases {
            let result = analyzer.natural_log(input);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(0.0001),
                "natural_log({}) failed. Result: {}, Expected: {}, Diff: {}",
                input,
                result,
                expected,
                difference
            );
        }
    }

    #[test]
    fn test_decimal_edge_cases() {
        let analyzer = FractalAnalyzer::new();
        
        // Test zero handling
        assert_eq!(analyzer.sqrt(dec!(0)), dec!(0));
        
        // Test very small numbers
        let small_result = analyzer.sqrt(dec!(0.000001));
        assert!(small_result > dec!(0));
        assert!(small_result < dec!(0.01));
        
        // Test very large numbers
        let large_result = analyzer.sqrt(dec!(1000000));
        assert_eq!(large_result, dec!(1000));
    }

    #[test]
    fn test_mathematical_invariants() {
        let analyzer = FractalAnalyzer::new();
        
        // Test sqrt(x^2) = x for positive x
        let test_values = vec![dec!(1), dec!(2.5), dec!(10), dec!(100.75)];
        for x in test_values {
            let squared = x * x;
            let sqrt_result = analyzer.sqrt(squared);
            let difference = (sqrt_result - x).abs();
            assert!(
                difference < dec!(0.00001),
                "sqrt({}^2) != {}, got {}",
                x,
                x,
                sqrt_result
            );
        }
    }
}