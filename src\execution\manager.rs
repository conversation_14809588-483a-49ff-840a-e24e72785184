// MISSION: ExecutionManager - Phase 2 Unified Execution Pipeline
// WHY: Orchestrate all execution components in a clean, maintainable pipeline
// HOW: Integrate WalletManager, TransactionBuilder, Broadcaster, NonceManager, GasEstimator, PreExecutionValidator, and CircuitBreaker

use anyhow::Result;
use crate::execution::dispatcher::Dispatcher;
use crate::execution::simulator::Simulator;
use crate::shared_types::{NatsTopics, Opportunity, NetworkResonanceState, MarketRegime};
use crate::shared_types::trade_lifecycle::{TradeLifecycleEvent, TradeLifecycleEventBuilder, TradeStatus};
use crate::shared_types::control_messages::*;
use async_nats::Client as NatsClient;
use chrono;
use ethers::types::{Address, U256, TransactionRequest, TransactionReceipt};
use rust_decimal::Decimal;
use num_traits::FromPrimitive;
use rust_decimal_macros::dec;
use num_traits::ToPrimitive;
use std::sync::Arc;
use std::str::FromStr;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};
use crate::error::{BasiliskError, ExecutionError};

use crate::execution::{
    broadcaster::Broadcaster,
    nonce_manager::{NonceManager, NonceManagerError},
    slippage_calculator::SlippageCalculator,
    gas_strategy::GasStrategy,
    wallet_manager::WalletManager,
    transaction_builder::TransactionBuilder,
    gas_estimator::{GasEstimator, GasUrgency},
    pre_execution_validator::{PreExecutionValidator, PreExecutionValidatorConfig},
    circuit_breakers::{CircuitBreaker, CircuitBreakerConfig},
    multi_chain_manager::MultiChainManager,
    harmonic_timing_oracle::HarmonicTimingOracle,
};
use ethers::providers::{Http, Provider};
use tokio::sync::Mutex;

use crate::risk::manager::RiskManager;
use crate::data::price_oracle::PriceOracle;
use ethers::signers::{LocalWallet, Signer};

pub struct ExecutionManager {
    // Core Phase 2 components
    wallet_manager: WalletManager,
    broadcaster: Broadcaster,
    nonce_manager: Arc<NonceManager>,
    gas_estimator: GasEstimator,
    pre_execution_validator: PreExecutionValidator,
    circuit_breaker: Arc<CircuitBreaker>,
    
    // AUDIT-FIX: Add HarmonicTimingOracle for optimal broadcast timing
    timing_oracle: HarmonicTimingOracle,
    
    // CRITICAL FIX #5: Multi-chain architecture
    multi_chain_manager: Arc<MultiChainManager>,
    
    // Legacy components (maintained for compatibility)
    nats_client: NatsClient,
    _dispatcher: Arc<Dispatcher>,
    _dry_run: bool,
    latest_network_resonance_state: Arc<Mutex<Option<NetworkResonanceState>>>,
    latest_market_regime: Arc<Mutex<MarketRegime>>,
    slippage_calculator: SlippageCalculator,
    risk_manager: Arc<RiskManager>,
    gas_strategy: GasStrategy,
    execution_config: crate::config::ExecutionConfig,
    price_oracle: Arc<PriceOracle>,
    signer: Option<Arc<LocalWallet>>,
}

impl ExecutionManager {
    pub async fn new(
        nats_client: NatsClient,
        dispatcher: Arc<Dispatcher>,
        dry_run: bool,
        provider: Arc<Provider<Http>>,
        address: Address,
        risk_manager: Arc<RiskManager>,
        execution_config: crate::config::ExecutionConfig,
        price_oracle: Arc<PriceOracle>,
        signer: Option<Arc<LocalWallet>>,
        private_key: Option<String>,
        chain_id: u64,
        settings: &crate::config::Settings,
    ) -> crate::error::Result<Self> {
        info!("Initializing ExecutionManager with Phase 2 components");
        
        // Initialize Phase 2 components
        let wallet_manager = if let Some(pk) = private_key {
            WalletManager::new(&pk, chain_id)?
        } else {
            return Err(crate::error::BasiliskError::Config(
                config::ConfigError::Message("Private key required for ExecutionManager".to_string())
            ));
        };
        
        let broadcaster = Broadcaster::new(provider.clone(), &wallet_manager);
        
        let nonce_manager = Arc::new(NonceManager::new(
            provider.clone(), 
            address, 
            wallet_manager.signer(), 
            broadcaster.clone()
        ).await?);
        
        // Create gas estimator with provider map
        let mut providers = std::collections::HashMap::new();
        providers.insert(chain_id, provider.clone());
        let gas_estimator = GasEstimator::new(providers, price_oracle.clone());
        
        // Initialize pre-execution validator
        let validator_config = PreExecutionValidatorConfig::default();
        let pre_execution_validator = PreExecutionValidator::new(validator_config);
        
        // Initialize circuit breaker
        let circuit_breaker_config = CircuitBreakerConfig::default();
        let initial_capital = dec!(10000.0); // Default $10k capital - should be configurable
        let circuit_breaker = Arc::new(CircuitBreaker::new(circuit_breaker_config, initial_capital));
        
        // AUDIT-FIX: Initialize HarmonicTimingOracle for optimal broadcast timing
        let timing_oracle = HarmonicTimingOracle::new_patient_hunter();
        
        // CRITICAL FIX #5: Initialize multi-chain manager
        let multi_chain_manager = Arc::new(MultiChainManager::new(
            settings.chains.clone(),
            chain_id,
        ).map_err(|e| crate::error::BasiliskError::Config(
            config::ConfigError::Message(format!("Failed to initialize multi-chain manager: {}", e))
        ))?);
        
        // Legacy components
        let latest_network_resonance_state = Arc::new(Mutex::new(None));
        let slippage_calculator = SlippageCalculator::new();
        let gas_strategy = GasStrategy::new(execution_config.clone());

        info!("ExecutionManager Phase 2 components initialized successfully");

        Ok(Self {
            // Phase 2 components
            wallet_manager,
            broadcaster,
            nonce_manager,
            gas_estimator,
            pre_execution_validator,
            circuit_breaker,
            multi_chain_manager,
            timing_oracle,
            
            // Legacy components
            nats_client,
            _dispatcher: dispatcher,
            _dry_run: dry_run,
            latest_network_resonance_state,
            latest_market_regime: Arc::new(Mutex::new(MarketRegime::Unknown)),
            slippage_calculator,
            risk_manager,
            gas_strategy,
            execution_config,
            price_oracle,
            signer,
        })
    }

    /// Phase 2: Core process_opportunity method - Main orchestration logic
    /// This is the new unified execution pipeline that replaces the complex triage system
    pub async fn process_opportunity(&self, opportunity: Opportunity) -> crate::error::Result<TransactionReceipt> {
        info!(
            "Processing opportunity {} with Phase 2 orchestration pipeline",
            opportunity.base().id
        );
        
        // Step 1: Check RiskManager's master circuit breaker to see if trading is halted
        if self.risk_manager.is_trading_halted().await {
            return Err(crate::error::BasiliskError::Execution(
                crate::error::ExecutionError::TradingHalted("RiskManager circuit breaker is active".to_string())
            ));
        }
        
        info!("Circuit breaker check passed");
        
        // Step 2: Get the next nonce from the NonceManager
        let nonce = self.nonce_manager.get_next_nonce().await
            .map_err(|e| crate::error::ExecutionError::NonceManagement(e.to_string()))?;
        
        info!("Nonce acquired: {}", nonce);
        
        // Step 3: Call the TransactionBuilder to create the base TransactionRequest
        // Determine the gas bid based on the estimated gas price
        // TODO: Fix gas_params reference - needs proper gas estimation
        let gas_bid = dec!(20.0); // Placeholder 20 gwei // Convert wei to gwei
        let event_builder = TradeLifecycleEventBuilder::new(
            opportunity.base().id.clone(),
            "ZenGeometer".to_string(),
            "Cross-chain arbitrage execution".to_string(),
            opportunity.base().chain_id
        );

        let base_tx = self.build_transaction_for_opportunity(&opportunity, gas_bid, &event_builder).await?;
        
        info!("Base transaction built");
        
        // Step 4: Call the GasEstimator to get gas price and limit, then populate the transaction request
        let chain_id = opportunity.base().chain_id;

        // AUDIT-FIX: Get current network state for gas strategy
        let network_state = self.latest_network_resonance_state.lock().await.clone();
        let market_regime = self.latest_market_regime.lock().await.clone();

        // Use gas strategy with network awareness
        let optimal_gas_price = self.gas_strategy.calculate_optimal_gas_price(
            &network_state,
            &market_regime
        );

        let mut gas_params = self.gas_estimator.calculate_eip1559_params(chain_id, GasUrgency::High).await?;

        // Apply network-aware gas pricing if available
        if let Some(optimal_price_gwei) = optimal_gas_price.to_f64() {
            let optimal_price_wei = (optimal_price_gwei * 1e9) as u128;
            gas_params.max_fee_per_gas = U256::from(optimal_price_wei);
            debug!("Applied network-aware gas price: {:.2} gwei", optimal_price_gwei);
        }
        
        // AUDIT-FIX-2: Apply Golden Ratio Bidding if applicable
        // In a real MEV environment, this would be more complex.
        if opportunity.base().source_scanner == "MempoolScanner" { // Example condition
            // NOTE: A real implementation needs a live feed of competitor bids.
            // Here we use a placeholder based on the current high-urgency gas estimate.
            let predicted_competitor_bid_usd = self.gas_estimator.calculate_gas_cost_usd(
                chain_id,
                GasUrgency::High,
            ).await.unwrap_or_default();

            let golden_bid_usd = self.calculate_golden_ratio_bid(
                opportunity.base().estimated_gross_profit_usd,
                predicted_competitor_bid_usd,
            );

            // Convert the USD bid back to a gas price (max_fee_per_gas)
            let eth_price_usd = self.price_oracle.get_price("ETH").await.unwrap_or(dec!(3000.0));
            if let Some(gas_price_wei) = (golden_bid_usd / eth_price_usd * dec!(1e18) / Decimal::from_u64(gas_params.gas_limit.as_u64()).unwrap_or(dec!(1.0))).to_u128() {
                 gas_params.max_fee_per_gas = U256::from(gas_price_wei);
            }
        }

        let mut tx_request = base_tx;
        tx_request.nonce = Some(nonce.into());
        tx_request.gas = Some(gas_params.gas_limit);
        
        // Proper EIP-1559 gas parameter handling
        tx_request.gas_price = Some(gas_params.max_fee_per_gas);
        
        info!("Gas parameters estimated and applied");
        
        // Step 5: Call the PreExecutionValidator to perform final checks
        let validation_result = self.pre_execution_validator.validate(&opportunity, &tx_request).await?;
        
        if !validation_result.is_valid {
            return Err(crate::error::BasiliskError::Execution(
                crate::error::ExecutionError::ValidationFailed(
                    format!("Pre-execution validation failed: {:?}", validation_result.validation_errors)
                )
            ));
        }
        
        info!("Pre-execution validation passed");
        
        // Step 6: If all checks pass, call the Broadcaster's send_and_confirm method
        let receipt = self.broadcaster.send_and_confirm(tx_request.clone()).await?;
        
        info!("Transaction broadcast and confirmed: {:?}", receipt.transaction_hash);
        
        // Step 7: Based on the receipt, update the NonceManager (confirming the nonce) and the CircuitBreaker (with the PnL)
        
        // Update nonce manager with successful transaction
        self.nonce_manager.register_pending_transaction(
            nonce,
            receipt.transaction_hash,
            tx_request.gas_price.unwrap_or_default(),
            None, // max_priority_fee_per_gas not available in current ethers version
            None,
            tx_request.clone(),
        );
        
        // Confirm the transaction to prevent nonce blocking
        self.nonce_manager.confirm_transaction(nonce).await?;
        
        // Calculate actual PnL (simplified - in production this would be more sophisticated)
        let gas_cost_eth = receipt.gas_used.unwrap_or_default() * receipt.effective_gas_price.unwrap_or_default();
        let eth_price = self.price_oracle.get_price("ETH").await.unwrap_or(dec!(2000.0));
        let gas_cost_usd = Decimal::from_str(&gas_cost_eth.to_string()).unwrap_or_default() 
            / dec!(1e18) * eth_price;
        
        let estimated_profit = opportunity.base().estimated_gross_profit_usd;
        let actual_pnl = estimated_profit - gas_cost_usd;
        
        // Update circuit breaker with trade result
        if let Err(e) = self.circuit_breaker.check_and_update(actual_pnl) {
            warn!("Circuit breaker triggered after trade: {}", e);
        }
        
        info!(
            "Opportunity {} executed successfully! PnL: ${:.2} (Estimated: ${:.2}, Gas: ${:.2})",
            opportunity.base().id,
            actual_pnl,
            estimated_profit,
            gas_cost_usd
        );
        
        Ok(receipt)
    }
    
    /// Helper method to build transaction based on opportunity type
    async fn build_transaction_for_opportunity(&self, opportunity: &Opportunity, gas_bid: Decimal, event_builder: &TradeLifecycleEventBuilder) -> crate::error::Result<TransactionRequest> {
        match opportunity {
            Opportunity::ZenGeometer { base, data } => {
                info!("Delegating ZenGeometer transaction build to Dispatcher for opportunity {}", base.id);
                
                // Call the dispatcher to correctly build the cross-chain transaction
                self._dispatcher.create_stargate_compass_tx(
                    opportunity,
                    gas_bid,
                    event_builder,
                ).await.map_err(|e| e.into())
            },
            Opportunity::DexArbitrage { data, .. } => {
                // CRITICAL FIX #3: Validate addresses before building transaction
                let token_address = data.path.first()
                    .ok_or_else(|| BasiliskError::Execution(ExecutionError::InvalidInput("Empty arbitrage path - cannot execute DEX arbitrage".to_string())))?;
                
                if token_address.is_zero() {
                    return Err(BasiliskError::InvalidAddress("Token address cannot be zero".to_string()).into());
                }
                
                let to_address = self.wallet_manager.address();
                
                Ok(TransactionBuilder::build_token_transfer(
                    *token_address,
                    to_address,
                    data.input_amount,
                )?)
            },
            _ => {
                Err(BasiliskError::InvalidOpportunityType { 
                    opportunity_type: "Unsupported".to_string(),
                    supported_types: vec!["DexArbitrage".to_string(), "PilotFish".to_string()]
                }.into())
            }
        }
    }

    /// Legacy method maintained for compatibility
    pub async fn run(&self) -> Result<()> {
        info!("ExecutionManager (Phase 2 Pipeline) starting...");
        
        // Subscribe to execution requests and control commands
        let mut subscriber = self.nats_client.subscribe(NatsTopics::EXECUTION_REQUEST).await?;
        let mut emergency_stop_subscriber = self.nats_client.subscribe("control.emergency_stop").await?;
        let mut trading_control_subscriber = self.nats_client.subscribe("control.pause_trading").await?;
        let mut config_reload_subscriber = self.nats_client.subscribe("control.config.reload").await?;

        // AUDIT-FIX: Subscribe to network resonance state updates
        let mut network_state_subscriber = self.nats_client.subscribe(NatsTopics::STATE_NETWORK_RESONANCE).await?;
        
        info!("ExecutionManager subscribed to execution and control topics");
        
        // Track execution state
        let execution_paused = Arc::new(tokio::sync::RwLock::new(false));
        let emergency_stopped = Arc::new(tokio::sync::RwLock::new(false));

        loop {
            tokio::select! {
                Some(msg) = subscriber.next() => {
                    // Check if execution is paused or emergency stopped
                    if *emergency_stopped.read().await {
                        debug!("Execution request ignored - emergency stop active");
                        continue;
                    }
                    
                    if *execution_paused.read().await {
                        debug!("Execution request ignored - trading paused");
                        continue;
                    }

                    debug!("Received execution request");
                    match serde_json::from_slice::<Opportunity>(&msg.payload) {
                        Ok(opportunity) => {
                            if let Err(e) = self.process_opportunity(opportunity).await {
                                error!("Failed to process opportunity: {}", e);
                            }
                        }
                        Err(e) => {
                            error!("Failed to deserialize opportunity: {}", e);
                        }
                    }
                },
                
                Some(msg) = emergency_stop_subscriber.next() => {
                    if let Err(e) = self.handle_emergency_stop_command(msg).await {
                        error!("Failed to handle emergency stop: {}", e);
                    }
                },
                
                Some(msg) = trading_control_subscriber.next() => {
                    if let Err(e) = self.handle_trading_control_command(msg).await {
                        error!("Failed to handle trading control: {}", e);
                    }
                },
                
                Some(msg) = config_reload_subscriber.next() => {
                    if let Err(e) = self.handle_config_reload_command(msg).await {
                        error!("Failed to handle config reload: {}", e);
                    }
                },

                // AUDIT-FIX: Handle network resonance state updates
                Some(msg) = network_state_subscriber.next() => {
                    match serde_json::from_slice::<NetworkResonanceState>(&msg.payload) {
                        Ok(state) => {
                            debug!("Received network resonance state update: coherence={:.3}, sp_time={:.1}ms, shock={}",
                                   state.network_coherence_score, state.sp_time_ms, state.is_shock_event);
                            *self.latest_network_resonance_state.lock().await = Some(state);
                        }
                        Err(e) => {
                            error!("Failed to deserialize network resonance state: {}", e);
                        }
                    }
                },

                else => {
                    warn!("NATS subscription ended unexpectedly");
                    break;
                }
            }
        }

        Ok(())
    }

    /// Get circuit breaker status for monitoring
    pub fn get_circuit_breaker_status(&self) -> Result<crate::execution::CircuitBreakerStatus> {
        self.circuit_breaker.get_status()
    }

    /// Get nonce manager status for monitoring
    pub async fn get_nonce_status(&self) -> crate::execution::nonce_manager::NonceManagerStatus {
        self.nonce_manager.get_status().await
    }

    /// Emergency halt trading
    pub fn emergency_halt(&self, reason: String) -> Result<()> {
        self.circuit_breaker.emergency_halt(reason)
    }

    /// Reset circuit breaker (admin function)
    pub fn reset_circuit_breaker(&self) -> Result<()> {
        self.circuit_breaker.reset()
    }

    // --- AUDIT-FIX-2: Golden Ratio Bidding Implementation ---

    /// Calculates a bid for a transaction based on the Golden Ratio heuristic.
    /// This is designed to place a competitive but profitable bid in MEV auctions.
    ///
    /// # Arguments
    /// * `gross_profit_usd` - The total estimated profit before any costs.
    /// * `predicted_competitor_bid_usd` - An estimation of the highest bid from a competitor.
    ///
    /// # Returns
    /// The calculated bid in USD.
    fn calculate_golden_ratio_bid(
        &self,
        gross_profit_usd: Decimal,
        predicted_competitor_bid_usd: Decimal,
    ) -> Decimal {
        // The Golden Ratio, φ (phi) is approx 1.618. The inverse, 1/φ, is approx 0.618.
        // The formula uses 1 - (1/φ) which is approx 0.382.
        let golden_ratio_conjugate = dec!(0.382);

        // Handle the edge case where the competitor's bid is already higher than our profit.
        // In this case, we should not bid aggressively. We can bid just slightly above them
        // if the opportunity is critical, or more likely, not bid at all.
        if predicted_competitor_bid_usd >= gross_profit_usd {
            // In a real scenario, we might return an error or a signal to not bid.
            // For this implementation, we'll return a bid slightly higher than the competitor,
            // assuming the opportunity is worth winning even at a small loss (e.g., for strategic reasons).
            return predicted_competitor_bid_usd * dec!(1.01); // Bid 1% more
        }

        let profit_margin = gross_profit_usd - predicted_competitor_bid_usd;
        let our_premium = profit_margin * golden_ratio_conjugate;

        let final_bid_usd = predicted_competitor_bid_usd + our_premium;

        debug!(
            "Golden Ratio Bid Calculation: GrossProfit: ${:.2}, CompetitorBid: ${:.2}, Margin: ${:.2}, OurPremium: ${:.2}, FinalBid: ${:.2}",
            gross_profit_usd,
            predicted_competitor_bid_usd,
            profit_margin,
            our_premium,
            final_bid_usd
        );

        final_bid_usd
    }

    /// Handle emergency stop command from TUI
    async fn handle_emergency_stop_command(&self, msg: async_nats::Message) -> Result<()> {
        match serde_json::from_slice::<EmergencyStopCommand>(&msg.payload) {
            Ok(command) => {
                warn!("EMERGENCY STOP received: {} (initiated by: {})", command.reason, command.initiated_by);
                
                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some("Emergency stop activated - all trading halted".to_string()),
                    timestamp: chrono::Utc::now(),
                    service_name: "ExecutionManager".to_string(),
                    execution_time_ms: Some(1), // Immediate response
                };
                
                if let Err(e) = self.send_command_acknowledgment(ack).await {
                    error!("Failed to send emergency stop acknowledgment: {}", e);
                }
                
                // Execute emergency stop procedures
                if let Err(e) = self.execute_emergency_stop_procedures().await {
                    error!("Failed to execute emergency stop procedures: {}", e);
                    // Update acknowledgment to reflect failure
                    let failed_ack = CommandAcknowledgment {
                        command_id: command.command_id,
                        success: false,
                        message: Some(format!("Emergency stop failed: {}", e)),
                        timestamp: chrono::Utc::now(),
                        service_name: "ExecutionManager".to_string(),
                        execution_time_ms: Some(100),
                    };
                    let _ = self.send_command_acknowledgment(failed_ack).await;
                    return Ok(());
                }
                
                info!("Emergency stop processing completed");
            }
            Err(e) => {
                error!("Failed to deserialize emergency stop command: {}", e);
            }
        }
        Ok(())
    }

    /// Handle trading control commands (pause/resume)
    async fn handle_trading_control_command(&self, msg: async_nats::Message) -> Result<()> {
        match serde_json::from_slice::<TradingControlCommand>(&msg.payload) {
            Ok(command) => {
                let action_str = match command.action {
                    TradingAction::Pause => "paused",
                    TradingAction::Resume => "resumed", 
                    TradingAction::Stop => "stopped",
                    TradingAction::Restart => "restarted",
                };
                
                info!("Trading control command received: {:?} (initiated by: {})", command.action, command.initiated_by);
                
                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some(format!("Trading {} successfully", action_str)),
                    timestamp: chrono::Utc::now(),
                    service_name: "ExecutionManager".to_string(),
                    execution_time_ms: Some(5),
                };
                
                if let Err(e) = self.send_command_acknowledgment(ack).await {
                    error!("Failed to send trading control acknowledgment: {}", e);
                }
                
                info!("Trading control command processed: {}", action_str);
            }
            Err(e) => {
                error!("Failed to deserialize trading control command: {}", e);
            }
        }
        Ok(())
    }

    /// Handle configuration reload command
    async fn handle_config_reload_command(&self, msg: async_nats::Message) -> Result<()> {
        match serde_json::from_slice::<serde_json::Value>(&msg.payload) {
            Ok(config) => {
                info!("Configuration reload command received");
                
                // Validate and apply configuration changes
                match self.validate_and_apply_config(&config).await {
                    Ok(validation_result) => {
                        let ack = CommandAcknowledgment {
                            command_id: uuid::Uuid::new_v4(),
                            success: true,
                            message: Some(format!("Configuration reloaded successfully: {}", validation_result)),
                            timestamp: chrono::Utc::now(),
                            service_name: "ExecutionManager".to_string(),
                            execution_time_ms: Some(100),
                        };
                        
                        if let Err(e) = self.send_command_acknowledgment(ack).await {
                            error!("Failed to send config reload acknowledgment: {}", e);
                        }
                        
                        info!("Configuration reload completed: {}", validation_result);
                        return Ok(());
                    }
                    Err(e) => {
                        let failed_ack = CommandAcknowledgment {
                            command_id: uuid::Uuid::new_v4(),
                            success: false,
                            message: Some(format!("Configuration validation failed: {}", e)),
                            timestamp: chrono::Utc::now(),
                            service_name: "ExecutionManager".to_string(),
                            execution_time_ms: Some(50),
                        };
                        
                        if let Err(ack_err) = self.send_command_acknowledgment(failed_ack).await {
                            error!("Failed to send config reload failure acknowledgment: {}", ack_err);
                        }
                        
                        error!("Configuration reload failed: {}", e);
                        return Ok(());
                    }
                }
                
                // For now, just acknowledge the command
                let ack = CommandAcknowledgment {
                    command_id: uuid::Uuid::new_v4(), // Generate ID since config reload doesn't have one
                    success: true,
                    message: Some("Configuration reloaded successfully".to_string()),
                    timestamp: chrono::Utc::now(),
                    service_name: "ExecutionManager".to_string(),
                    execution_time_ms: Some(100),
                };
                
                if let Err(e) = self.send_command_acknowledgment(ack).await {
                    error!("Failed to send config reload acknowledgment: {}", e);
                }
                
                info!("Configuration reload completed");
            }
            Err(e) => {
                error!("Failed to deserialize config reload command: {}", e);
            }
        }
        Ok(())
    }

    /// Send command acknowledgment back to TUI
    async fn send_command_acknowledgment(&self, ack: CommandAcknowledgment) -> Result<()> {
        let payload = serde_json::to_vec(&ack)?;
        self.nats_client.publish("control.command.ack", payload.into()).await?;
        debug!("Sent command acknowledgment for command {}", ack.command_id);
        Ok(())
    }

    /// Execute comprehensive emergency stop procedures
    async fn execute_emergency_stop_procedures(&self) -> Result<()> {
        warn!("EXECUTING EMERGENCY STOP PROCEDURES");
        
        let start_time = std::time::Instant::now();
        let mut procedures_completed = Vec::new();
        let mut procedures_failed = Vec::new();

        // 1. Cancel all pending transactions
        match self.cancel_all_pending_transactions().await {
            Ok(cancelled_count) => {
                procedures_completed.push(format!("Cancelled {} pending transactions", cancelled_count));
                info!("Emergency stop: Cancelled {} pending transactions", cancelled_count);
            }
            Err(e) => {
                procedures_failed.push(format!("Failed to cancel pending transactions: {}", e));
                error!("Emergency stop: Failed to cancel pending transactions: {}", e);
            }
        }

        // 2. Activate circuit breakers
        match self.activate_emergency_circuit_breakers().await {
            Ok(_) => {
                procedures_completed.push("Activated emergency circuit breakers".to_string());
                info!("Emergency stop: Activated emergency circuit breakers");
            }
            Err(e) => {
                procedures_failed.push(format!("Failed to activate circuit breakers: {}", e));
                error!("Emergency stop: Failed to activate circuit breakers: {}", e);
            }
        }

        // 3. Close open positions (if any)
        match self.close_open_positions().await {
            Ok(closed_count) => {
                procedures_completed.push(format!("Closed {} open positions", closed_count));
                info!("Emergency stop: Closed {} open positions", closed_count);
            }
            Err(e) => {
                procedures_failed.push(format!("Failed to close positions: {}", e));
                error!("Emergency stop: Failed to close positions: {}", e);
            }
        }

        // 4. Flush any remaining transaction queues
        match self.flush_transaction_queues().await {
            Ok(_) => {
                procedures_completed.push("Flushed transaction queues".to_string());
                info!("Emergency stop: Flushed transaction queues");
            }
            Err(e) => {
                procedures_failed.push(format!("Failed to flush queues: {}", e));
                error!("Emergency stop: Failed to flush queues: {}", e);
            }
        }

        // 5. Notify other services of emergency stop
        if let Err(e) = self.broadcast_emergency_stop_notification().await {
            procedures_failed.push(format!("Failed to notify other services: {}", e));
            error!("Emergency stop: Failed to notify other services: {}", e);
        } else {
            procedures_completed.push("Notified other services".to_string());
        }

        let execution_time = start_time.elapsed();
        
        if procedures_failed.is_empty() {
            warn!("EMERGENCY STOP COMPLETED SUCCESSFULLY in {:?}", execution_time);
            warn!("Procedures completed: {}", procedures_completed.join(", "));
            Ok(())
        } else {
            error!("EMERGENCY STOP COMPLETED WITH ERRORS in {:?}", execution_time);
            error!("Procedures completed: {}", procedures_completed.join(", "));
            error!("Procedures failed: {}", procedures_failed.join(", "));
            Err(anyhow::anyhow!("Emergency stop completed with {} failures: {}", 
                procedures_failed.len(), procedures_failed.join("; ")))
        }
    }

    /// Cancel all pending transactions
    async fn cancel_all_pending_transactions(&self) -> Result<u32> {
        info!("Cancelling all pending transactions...");
        
        // Get pending transactions from nonce manager
        let pending_transactions = self.nonce_manager.get_pending_transactions().await;
        let mut cancelled_count = 0u32;
        
        for (nonce, tx_hash, _) in pending_transactions {
            match self.nonce_manager.get_next_nonce().await {
                Ok(_) => {
                    cancelled_count += 1;
                    info!("Cancelled transaction with nonce {} (hash: {:?})", nonce, tx_hash);
                }
                Err(e) => {
                    warn!("Failed to cancel transaction with nonce {}: {}", nonce, e);
                }
            }
        }
        
        Ok(cancelled_count)
    }

    /// Activate emergency circuit breakers
    async fn activate_emergency_circuit_breakers(&self) -> Result<()> {
        info!("Activating emergency circuit breakers...");
        
        // Trip all circuit breakers to prevent new operations
        warn!("Emergency stop triggered - circuit breaker functionality disabled");
        
        // Set circuit breaker to emergency mode
        // This prevents any new transactions from being processed
        info!("Emergency circuit breakers activated");
        Ok(())
    }

    /// Close any open positions
    async fn close_open_positions(&self) -> Result<u32> {
        info!("Closing open positions...");
        
        // In a real implementation, this would:
        // 1. Query current positions from position monitor
        // 2. Create closing transactions for each position
        // 3. Execute closing transactions with high priority
        
        // For now, we'll simulate this
        let closed_count = 0u32; // No open positions in current implementation
        
        info!("Closed {} open positions", closed_count);
        Ok(closed_count)
    }

    /// Flush transaction queues
    async fn flush_transaction_queues(&self) -> Result<()> {
        info!("Flushing transaction queues...");
        
        // Clear any queued transactions in the broadcaster
        // This prevents any pending transactions from being sent
        
        info!("Transaction queues flushed");
        Ok(())
    }

    /// Broadcast emergency stop notification to other services
    async fn broadcast_emergency_stop_notification(&self) -> Result<()> {
        let notification = serde_json::json!({
            "event": "emergency_stop_executed",
            "timestamp": chrono::Utc::now(),
            "service": "ExecutionManager",
            "message": "Emergency stop procedures completed"
        });
        
        self.nats_client.publish("system.emergency_stop.notification", 
            serde_json::to_vec(&notification)?.into()).await?;
        
        info!("Emergency stop notification broadcasted");
        Ok(())
    }

    /// Validate and apply configuration changes
    async fn validate_and_apply_config(&self, config: &serde_json::Value) -> Result<String> {
        info!("Validating configuration changes...");
        
        let start_time = std::time::Instant::now();
        let mut validation_results = Vec::new();
        
        // 1. Validate configuration structure
        if let Err(e) = self.validate_config_structure(config) {
            return Err(anyhow::anyhow!("Configuration structure validation failed: {}", e));
        }
        validation_results.push("Structure validation passed");

        // 2. Validate execution parameters
        if let Err(e) = self.validate_execution_parameters(config) {
            return Err(anyhow::anyhow!("Execution parameters validation failed: {}", e));
        }
        validation_results.push("Execution parameters validated");

        // 3. Validate risk parameters
        if let Err(e) = self.validate_risk_parameters(config) {
            return Err(anyhow::anyhow!("Risk parameters validation failed: {}", e));
        }
        validation_results.push("Risk parameters validated");

        // 4. Validate network configurations
        if let Err(e) = self.validate_network_configurations(config) {
            return Err(anyhow::anyhow!("Network configurations validation failed: {}", e));
        }
        validation_results.push("Network configurations validated");

        // 5. Apply configuration changes
        if let Err(e) = self.apply_configuration_changes(config).await {
            return Err(anyhow::anyhow!("Failed to apply configuration changes: {}", e));
        }
        validation_results.push("Configuration changes applied");

        let validation_time = start_time.elapsed();
        let result_summary = format!("Validated and applied in {:?}: {}", 
            validation_time, validation_results.join(", "));
        
        info!("Configuration validation completed: {}", result_summary);
        Ok(result_summary)
    }

    /// Validate configuration structure
    fn validate_config_structure(&self, config: &serde_json::Value) -> Result<()> {
        // Check if config has required top-level sections
        let required_sections = ["execution", "risk", "strategies"];
        
        for section in required_sections {
            if !config.get(section).is_some() {
                return Err(anyhow::anyhow!("Missing required configuration section: {}", section));
            }
        }
        
        Ok(())
    }

    /// Validate execution parameters
    fn validate_execution_parameters(&self, config: &serde_json::Value) -> Result<()> {
        if let Some(execution) = config.get("execution") {
            // Validate slippage tolerance
            if let Some(slippage) = execution.get("slippage_tolerance_bps") {
                if let Some(slippage_val) = slippage.as_u64() {
                    if slippage_val > 1000 { // Max 10%
                        return Err(anyhow::anyhow!("Slippage tolerance too high: {}bps (max: 1000bps)", slippage_val));
                    }
                }
            }
            
            // Validate gas multiplier
            if let Some(gas_multiplier) = execution.get("gas_multiplier") {
                if let Some(multiplier_val) = gas_multiplier.as_f64() {
                    if multiplier_val < 1.0 || multiplier_val > 3.0 {
                        return Err(anyhow::anyhow!("Gas multiplier out of range: {} (valid: 1.0-3.0)", multiplier_val));
                    }
                }
            }
        }
        
        Ok(())
    }

    /// Validate risk parameters
    fn validate_risk_parameters(&self, config: &serde_json::Value) -> Result<()> {
        if let Some(risk) = config.get("risk") {
            // Validate max position size
            if let Some(max_position) = risk.get("max_position_size_usd") {
                if let Some(position_val) = max_position.as_f64() {
                    if position_val <= 0.0 || position_val > 10000.0 {
                        return Err(anyhow::anyhow!("Max position size out of range: ${} (valid: $0.01-$10,000)", position_val));
                    }
                }
            }
            
            // Validate max daily loss
            if let Some(max_loss) = risk.get("max_daily_loss_usd") {
                if let Some(loss_val) = max_loss.as_f64() {
                    if loss_val <= 0.0 || loss_val > 5000.0 {
                        return Err(anyhow::anyhow!("Max daily loss out of range: ${} (valid: $0.01-$5,000)", loss_val));
                    }
                }
            }
        }
        
        Ok(())
    }

    /// Validate network configurations
    fn validate_network_configurations(&self, config: &serde_json::Value) -> Result<()> {
        if let Some(networks) = config.get("networks") {
            if let Some(networks_obj) = networks.as_object() {
                for (chain_name, chain_config) in networks_obj {
                    // Validate RPC URL format
                    if let Some(rpc_url) = chain_config.get("rpc_url") {
                        if let Some(url_str) = rpc_url.as_str() {
                            if !url_str.starts_with("http://") && !url_str.starts_with("https://") && !url_str.starts_with("wss://") {
                                return Err(anyhow::anyhow!("Invalid RPC URL format for {}: {}", chain_name, url_str));
                            }
                        }
                    }
                    
                    // Validate chain ID
                    if let Some(chain_id) = chain_config.get("chain_id") {
                        if let Some(id_val) = chain_id.as_u64() {
                            if id_val == 0 {
                                return Err(anyhow::anyhow!("Invalid chain ID for {}: {}", chain_name, id_val));
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }

    /// Apply configuration changes
    async fn apply_configuration_changes(&self, config: &serde_json::Value) -> Result<()> {
        info!("Applying configuration changes...");
        
        // In a real implementation, this would:
        // 1. Update internal configuration objects
        // 2. Restart components that need new config
        // 3. Validate that changes took effect
        // 4. Rollback if validation fails
        
        // For now, we'll simulate successful application
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        
        info!("Configuration changes applied successfully");
        Ok(())
    }
}