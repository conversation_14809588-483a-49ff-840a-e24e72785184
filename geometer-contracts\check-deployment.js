// Script to check if contract is deployed
async function main() {
  console.log("🔍 Checking contract deployment status...");
  
  // Get the contract address
  const contractAddress = "******************************************";
  console.log(`Contract address: ${contractAddress}`);
  
  // Check if the address has code
  const code = await ethers.provider.getCode(contractAddress);
  console.log(`Bytecode length: ${(code.length - 2) / 2} bytes`);
  
  if (code === "0x") {
    console.log("❌ No bytecode found at this address. Contract is not deployed.");
  } else {
    console.log("✅ Contract is deployed!");
    console.log(`First 100 bytes: ${code.substring(0, 100)}...`);
  }
  
  // Get current block number
  const blockNumber = await ethers.provider.getBlockNumber();
  console.log(`Current block number: ${blockNumber}`);
  
  // Check balance
  const balance = await ethers.provider.getBalance(contractAddress);
  console.log(`Contract balance: ${ethers.formatEther(balance)} ETH`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });