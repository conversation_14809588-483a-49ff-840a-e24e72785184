// MISSION: Console Notification Channel - Development & Debug Alerts
// WHY: Immediate console output for development and debugging
// HOW: Structured logging with tactical language and color coding

use async_trait::async_trait;
use crate::shared_types::{Alert, AlertLevel};
use crate::alerting::notifiers::Notifier;
use anyhow::Result;
use tracing::{info, warn, error};

pub struct ConsoleNotifier {
    enabled: bool,
    min_level: AlertLevel,
}

impl ConsoleNotifier {
    pub fn new(min_level: AlertLevel) -> Self {
        Self {
            enabled: true,
            min_level,
        }
    }

    fn should_send(&self, alert: &Alert) -> bool {
        if !self.enabled {
            return false;
        }

        match (&self.min_level, &alert.level) {
            (AlertLevel::INFO, _) => true,
            (AlertLevel::WARNING, AlertLevel::WARNING | AlertLevel::SEVERE | AlertLevel::CRITICAL) => true,
            (AlertLevel::SEVERE, AlertLevel::SEVERE | AlertLevel::CRITICAL) => true,
            (AlertLevel::CRITICAL, AlertLevel::CRITICAL) => true,
            _ => false,
        }
    }

    fn format_alert(&self, alert: &Alert) -> String {
        let emoji = match alert.level {
            AlertLevel::INFO => "INFO",
            AlertLevel::WARNING => "WARN",
            AlertLevel::SEVERE => "SEVERE",
            AlertLevel::CRITICAL => "CRITICAL",
        };

        let category_icon = match alert.category {
            crate::shared_types::AlertCategory::Performance => "PERF",
            crate::shared_types::AlertCategory::Risk => "RISK",
            crate::shared_types::AlertCategory::Operational => "OPS",
            crate::shared_types::AlertCategory::Profitability => "PROFIT",
        };

        let mut output = format!(
            "\n{} {} ZEN GEOMETER ALERT {} {}\n",
            "=".repeat(20),
            emoji,
            emoji,
            "=".repeat(20)
        );

        output.push_str(&format!("TITLE: {}\n", alert.title));
        output.push_str(&format!("{} CATEGORY: {:?}\n", category_icon, alert.category));
        output.push_str(&format!("LEVEL: {}\n", alert.level_str()));
        output.push_str(&format!("SOURCE: {}\n", alert.source));
        output.push_str(&format!("TIME: {}\n", 
            chrono::DateTime::from_timestamp(alert.timestamp as i64, 0)
                .unwrap_or_else(|| chrono::Utc::now())
                .format("%Y-%m-%d %H:%M:%S UTC")
        ));
        
        output.push_str("\nMESSAGE:\n");
        output.push_str(&format!("{}\n", alert.message));

        if !alert.context.is_empty() {
            output.push_str("\nCONTEXT:\n");
            for (key, value) in &alert.context {
                output.push_str(&format!("  • {}: {}\n", key, value));
            }
        }

        output.push_str(&format!("\n{}\n", "=".repeat(60)));
        output
    }
}

#[async_trait]
impl Notifier for ConsoleNotifier {
    async fn send(&self, alert: &Alert) -> Result<()> {
        if !self.should_send(alert) {
            return Ok(());
        }

        let formatted = self.format_alert(alert);

        match alert.level {
            AlertLevel::INFO => info!("{}", formatted),
            AlertLevel::WARNING => warn!("{}", formatted),
            AlertLevel::SEVERE => error!("{}", formatted),
            AlertLevel::CRITICAL => error!("{}", formatted),
        }

        Ok(())
    }

    fn name(&self) -> &'static str {
        "Console"
    }

    fn is_enabled(&self) -> bool {
        self.enabled
    }
}