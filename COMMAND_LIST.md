# Zen Geometer Bot - Command Reference

This document provides a comprehensive list of all commands and subcommands available in the Zen Geometer bot, along with their descriptions and usage.

## Table of Contents

- [Main Commands](#main-commands)
- [Run Command](#run-command)
- [Simulate Command](#simulate-command)
- [Validate Command](#validate-command)
- [Configuration Commands](#configuration-commands)
  - [Profile Management](#profile-management)
- [Utility Commands](#utility-commands)
- [TUI Command](#tui-command)

## Main Commands

```
basilisk_bot [OPTIONS] <COMMAND>
```

| Command | Description |
|---------|-------------|
| `run` | Run the bot in a specific operational mode |
| `simulate` | Run the bot in educational simulation mode against live mainnet data |
| `validate` | Validate configuration and connections |
| `config` | Configuration management commands |
| `utils` | Utility commands |
| `tui` | Launch the Terminal User Interface (TUI) |
| `help` | Print help information |

**Options:**
- `-c, --config <FILE>`: Optional path to config file
- `-h, --help`: Print help
- `-V, --version`: Print version

## Run Command

```
basilisk_bot run [OPTIONS]
```

Run the bot in a specific operational mode.

**Options:**
- `--mode <MODE>`: Operational mode for the bot [default: simulate]
  - `simulate`: Educational simulation - connects to live data but never broadcasts transactions. Perfect for learning how the bot analyzes real opportunities.
  - `shadow`: Live simulation with on-chain verification - tests transactions on forked state. Validates profitability without broadcasting to mainnet.
  - `sentinel`: Live on-chain event monitoring only - monitors deployed contracts. Tests contract functionality with minimal capital.
  - `low-capital`: Live trading with very low risk limits - first real money mode. Overrides risk parameters with hardcoded safe values.
  - `live`: Full production live trading - uses configured risk parameters. Only use after thorough testing in all other modes.
- `--verbose`: Enable verbose output (shows detailed transaction information)
- `-h, --help`: Print help

## Simulate Command

```
basilisk_bot simulate [OPTIONS]
```

Run the bot in educational simulation mode against live mainnet data. This is a shortcut for `run --mode simulate`.

**Options:**
- `--detailed`: Show detailed lifecycle reports for every opportunity
- `--scanner <SCANNER>`: Filter to only show opportunities from specific scanner
- `--min-profit <MIN_PROFIT>`: Minimum profit threshold to show in reports (USD)
- `-h, --help`: Print help

## Validate Command

```
basilisk_bot validate [OPTIONS]
```

Validate configuration and connections. Checks that all configured services, RPC endpoints, and settings are valid and accessible.

**Options:**
- `--reason`: Show detailed reasoning for each validation check
- `-h, --help`: Print help

## Configuration Commands

```
basilisk_bot config <COMMAND>
```

Configuration management commands.

| Command | Description |
|---------|-------------|
| `show` | Show current configuration |
| `validate` | Validate configuration file with detailed analysis |
| `wizard` | Run the configuration wizard for guided setup |
| `profile` | Profile management commands |
| `set-gaze` | Set Basilisk Gaze parameters |
| `edit` | Interactive configuration editor |
| `templates` | Generate configuration templates |
| `import` | Import a configuration profile from a file |
| `export` | Export a configuration profile to a file or stdout |
| `help` | Print help information |

### Profile Management

```
basilisk_bot config profile <COMMAND>
```

Profile management commands.

| Command | Description |
|---------|-------------|
| `list` | List all available profiles |
| `show` | Show profile information |
| `create` | Create a new profile |
| `delete` | Delete a profile |
| `copy` | Copy a profile |
| `search` | Search profiles by tags or description |
| `stats` | Show profile statistics |
| `validate` | Validate a specific profile |
| `help` | Print help information |

## Utility Commands

```
basilisk_bot utils <COMMAND>
```

Utility commands for various operational tasks.

| Command | Description |
|---------|-------------|
| `balances` | Check balances of configured wallets |
| `ping-nodes` | Ping all configured nodes and services |
| `show-config` | Show current Basilisk Gaze configuration |
| `test-gaze` | Test Basilisk Gaze strategy with current configuration |
| `help` | Print help information |

## TUI Command

```
basilisk_bot tui
```

Launch the Terminal User Interface (TUI) for interactive monitoring and control.

## Examples

### Basic Usage

```bash
# Run in simulation mode (default)
./target/release/basilisk_bot run

# Run in shadow mode with verbose output
./target/release/basilisk_bot run --mode shadow --verbose

# Run with a specific config file
./target/release/basilisk_bot -c config/production.toml run --mode live

# Launch the TUI
./target/release/basilisk_bot tui
```

### Configuration Management

```bash
# Show current configuration
./target/release/basilisk_bot config show

# Run the configuration wizard
./target/release/basilisk_bot config wizard

# List all available profiles
./target/release/basilisk_bot config profile list

# Export configuration to a file
./target/release/basilisk_bot config export > my_config.toml
```

### Utilities

```bash
# Check wallet balances
./target/release/basilisk_bot utils balances

# Ping all configured nodes
./target/release/basilisk_bot utils ping-nodes

# Test Basilisk Gaze strategy
./target/release/basilisk_bot utils test-gaze
```

## Advanced Usage

### Development Workflow

```bash
# Validate configuration
./target/release/basilisk_bot validate

# Run in simulation mode
./target/release/basilisk_bot run --mode simulate

# Test with shadow mode
./target/release/basilisk_bot run --mode shadow

# Monitor with TUI
./target/release/basilisk_bot tui
```

### Production Deployment

```bash
# Final validation
./target/release/basilisk_bot validate

# Run with low capital first
./target/release/basilisk_bot run --mode low-capital

# Full production mode
./target/release/basilisk_bot run --mode live
```