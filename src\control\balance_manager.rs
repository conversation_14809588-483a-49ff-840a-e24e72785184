// DEGEN CHAIN: Balance Manager Service
// WHY: Track wallet balances across Base and Degen chains for Mission Control
// HOW: Periodic queries to both chains with NATS publishing

use anyhow::Result;
use async_nats::Client as NatsClient;
use ethers::{
    providers::{Http, Provider, Middleware},
    types::{Address, U256},
    utils::format_units,
};
use rust_decimal::Decimal;
use serde_json::json;
use std::{sync::Arc, time::Duration};
use tokio::time::interval;
use tracing::{debug, error, info, warn};

use crate::prelude::BasiliskError;

/// Balance Manager - Tracks wallet balances across multiple chains
pub struct BalanceManager {
    nats_client: NatsClient,
    base_provider: Arc<Provider<Http>>,
    degen_provider: Arc<Provider<Http>>,
    wallet_address: Address,
    usdc_address_base: Address,
    usdc_address_degen: Address,
    degen_token_address: Address,
    fob_contract_address: Address,
    update_interval: Duration,
}

impl BalanceManager {
    pub fn new(
        nats_client: NatsClient,
        base_rpc_url: String,
        degen_rpc_url: String,
        wallet_address: Address,
        usdc_address_base: Address,
        usdc_address_degen: Address,
        degen_token_address: Address,
        fob_contract_address: Address,
    ) -> Result<Self> {
        let base_provider = Arc::new(
            Provider::<Http>::try_from(base_rpc_url)
                .map_err(|e| BasiliskError::DataIngestion { 
                    message: format!("Invalid Base RPC URL: {}", e) 
                })?,
        );

        let degen_provider = Arc::new(
            Provider::<Http>::try_from(degen_rpc_url)
                .map_err(|e| BasiliskError::DataIngestion { 
                    message: format!("Invalid Degen RPC URL: {}", e) 
                })?,
        );

        Ok(Self {
            nats_client,
            base_provider,
            degen_provider,
            wallet_address,
            usdc_address_base,
            usdc_address_degen,
            degen_token_address,
            fob_contract_address,
            update_interval: Duration::from_secs(30), // Update every 30 seconds
        })
    }

    /// Start the balance monitoring service
    pub async fn run(&self) -> Result<()> {
        info!("Starting Balance Manager service");
        let mut interval = interval(self.update_interval);

        loop {
            interval.tick().await;

            if let Err(e) = self.update_balances().await {
                error!("Failed to update balances: {}", e);
                // Continue running even if one update fails
            }
        }
    }

    /// Update all wallet balances and publish to NATS
    async fn update_balances(&self) -> Result<()> {
        debug!("Updating wallet balances");

        // Query Base chain balances
        let (base_usdc, base_eth) = self.query_base_balances().await?;

        // Query Degen chain balances
        let (degen_usdc, degen_degen) = self.query_degen_balances().await?;

        // Query FOB contract balance
        let fob_balance = self.query_fob_balance().await?;

        // Publish balance update to NATS
        let balance_update = json!({
            "base_usdc": base_usdc,
            "base_eth": base_eth,
            "degen_usdc": degen_usdc,
            "degen_degen": degen_degen,
            "fob_balance": fob_balance,
            "timestamp": chrono::Utc::now().timestamp(),
            "wallet_address": format!("{:?}", self.wallet_address)
        });

        self.nats_client
            .publish(crate::shared_types::NatsTopics::STATE_BALANCES, serde_json::to_vec(&balance_update)?.into())
            .await
            .map_err(|e| BasiliskError::DataIngestion { message: format!("Failed to publish balance update: {}", e) })?;

        debug!(
            "Published balance update: Base USDC: {}, ETH: {}, Degen USDC: {}, DEGEN: {}, FOB: {}",
            base_usdc, base_eth, degen_usdc, degen_degen, fob_balance
        );

        Ok(())
    }

    /// Query balances on Base chain
    async fn query_base_balances(&self) -> Result<(f64, f64)> {
        // Get ETH balance
        let eth_balance = self
            .base_provider
            .get_balance(self.wallet_address, None)
            .await
            .map_err(|e| BasiliskError::DataIngestion { message: format!("Failed to get ETH balance: {}", e) })?;

        let eth_balance_formatted = format_units(eth_balance, 18)
            .map_err(|e| BasiliskError::DataIngestion { message: format!("Failed to format ETH balance: {}", e) })?
            .parse::<f64>()
            .unwrap_or(0.0);

        // Get USDC balance (would need ERC20 contract call in real implementation)
        // For now, using placeholder values
        let usdc_balance = 50000.0; // Placeholder

        Ok((usdc_balance, eth_balance_formatted))
    }

    /// Query balances on Degen chain
    async fn query_degen_balances(&self) -> Result<(f64, f64)> {
        // Get USDC balance on Degen chain
        let usdc_balance = 500.0; // Placeholder - would need ERC20 contract call

        // Get DEGEN token balance
        let degen_balance = 15000.0; // Placeholder - would need ERC20 contract call

        Ok((usdc_balance, degen_balance))
    }

    /// Query FOB contract balance
    async fn query_fob_balance(&self) -> Result<f64> {
        // Query the Stargate Compass FOB contract balance
        // This would involve calling the contract's balance method
        Ok(0.0) // Placeholder
    }

    /// Set update interval for testing
    pub fn set_update_interval(&mut self, interval: Duration) {
        self.update_interval = interval;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_balance_manager_creation() {
        // Test that BalanceManager can be created with valid parameters
        let wallet_address = Address::zero();
        let usdc_address = Address::zero();
        let degen_address = Address::zero();
        let fob_address = Address::zero();

        // This test will fail if RPC URLs are invalid, but that's expected behavior
        // In a real test environment, you'd use mock providers
        let result = BalanceManager::new(
            async_nats::connect("nats://localhost:4222").await.unwrap(),
            "http://localhost:8545".to_string(),
            "http://localhost:8546".to_string(),
            wallet_address,
            usdc_address,
            usdc_address,
            degen_address,
            fob_address,
        );

        // For now, we just test that the function doesn't panic
        // In a production environment, you'd want proper mocking
        assert!(result.is_ok() || result.is_err()); // Either outcome is acceptable for this basic test
    }
}