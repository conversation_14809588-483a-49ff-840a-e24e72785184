// src/shared_types/graceful_degradation.rs

use std::fmt;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{warn, error, info, debug};
use async_trait::async_trait;
use thiserror::Error;

/// AUDIT-FIX: Graceful degradation patterns for Task 5.1
/// This trait provides consistent error handling and fallback mechanisms for all components

#[async_trait]
pub trait ComponentWithFallback<T> {
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Primary operation that may fail
    async fn primary_operation(&self) -> Result<T, Self::Error>;
    
    /// Fallback operation when primary fails
    async fn fallback_operation(&self) -> Result<T, Self::Error>;
    
    /// Execute with graceful degradation
    async fn execute_with_fallback(&self) -> Result<T, Self::Error> {
        match self.primary_operation().await {
            Ok(result) => {
                debug!("Primary operation succeeded");
                Ok(result)
            }
            Err(primary_error) => {
                warn!("Primary operation failed: {}, attempting fallback", primary_error);
                
                match self.fallback_operation().await {
                    Ok(fallback_result) => {
                        info!("Fallback operation succeeded");
                        Ok(fallback_result)
                    }
                    Err(fallback_error) => {
                        error!("Both primary and fallback operations failed. Primary: {}, Fallback: {}", 
                               primary_error, fallback_error);
                        Err(primary_error) // Return original error
                    }
                }
            }
        }
    }
    
    /// Check if component is healthy
    async fn health_check(&self) -> ComponentHealth;
    
    /// Get component name for logging
    fn component_name(&self) -> &'static str;
}

/// Component health status for monitoring
#[derive(Debug, Clone, PartialEq)]
pub enum ComponentHealth {
    Healthy,
    Degraded(String),    // Reason for degradation
    Unhealthy(String),   // Reason for being unhealthy
    Down(String),        // Reason for being down
}

impl fmt::Display for ComponentHealth {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ComponentHealth::Healthy => write!(f, "Healthy"),
            ComponentHealth::Degraded(reason) => write!(f, "Degraded: {}", reason),
            ComponentHealth::Unhealthy(reason) => write!(f, "Unhealthy: {}", reason),
            ComponentHealth::Down(reason) => write!(f, "Down: {}", reason),
        }
    }
}

/// AUDIT-FIX: System-wide degradation manager
pub struct DegradationManager {
    component_states: Arc<RwLock<std::collections::HashMap<String, ComponentHealth>>>,
    degradation_config: DegradationConfig,
}

#[derive(Debug, Clone)]
pub struct DegradationConfig {
    pub max_unhealthy_components: usize,
    pub max_degraded_components: usize,
    pub health_check_interval_seconds: u64,
    pub enable_emergency_mode: bool,
}

impl Default for DegradationConfig {
    fn default() -> Self {
        Self {
            max_unhealthy_components: 2,
            max_degraded_components: 5,
            health_check_interval_seconds: 30,
            enable_emergency_mode: true,
        }
    }
}

impl DegradationManager {
    pub fn new(config: DegradationConfig) -> Self {
        Self {
            component_states: Arc::new(RwLock::new(std::collections::HashMap::new())),
            degradation_config: config,
        }
    }
    
    /// Register a component for health monitoring
    pub async fn register_component(&self, name: String, initial_health: ComponentHealth) {
        let mut states = self.component_states.write().await;
        states.insert(name.clone(), initial_health.clone());
        info!("Registered component '{}' with health: {}", name, initial_health);
    }
    
    /// Update component health status
    pub async fn update_component_health(&self, name: String, health: ComponentHealth) {
        let mut states = self.component_states.write().await;
        let previous_health = states.get(&name).cloned();
        states.insert(name.clone(), health.clone());
        
        // Log health changes
        match (previous_health, &health) {
            (Some(prev), current) if prev != *current => {
                warn!("Component '{}' health changed: {} -> {}", name, prev, current);
            }
            (None, current) => {
                info!("Component '{}' health initialized: {}", name, current);
            }
            _ => {
                debug!("Component '{}' health unchanged: {}", name, health);
            }
        }
    }
    
    /// Get overall system health
    pub async fn get_system_health(&self) -> SystemHealth {
        let states = self.component_states.read().await;
        
        let mut healthy_count = 0;
        let mut degraded_count = 0;
        let mut unhealthy_count = 0;
        let mut down_count = 0;
        
        for health in states.values() {
            match health {
                ComponentHealth::Healthy => healthy_count += 1,
                ComponentHealth::Degraded(_) => degraded_count += 1,
                ComponentHealth::Unhealthy(_) => unhealthy_count += 1,
                ComponentHealth::Down(_) => down_count += 1,
            }
        }
        
        // Determine overall system health
        if down_count > 0 {
            SystemHealth::Critical(format!("{} components down", down_count))
        } else if unhealthy_count > self.degradation_config.max_unhealthy_components {
            SystemHealth::Unhealthy(format!("{} components unhealthy (max: {})", 
                                          unhealthy_count, self.degradation_config.max_unhealthy_components))
        } else if degraded_count > self.degradation_config.max_degraded_components {
            SystemHealth::Degraded(format!("{} components degraded (max: {})", 
                                         degraded_count, self.degradation_config.max_degraded_components))
        } else if unhealthy_count > 0 || degraded_count > 0 {
            SystemHealth::Degraded(format!("{} degraded, {} unhealthy components", 
                                         degraded_count, unhealthy_count))
        } else {
            SystemHealth::Healthy
        }
    }
    
    /// Get detailed component status
    pub async fn get_component_status(&self) -> std::collections::HashMap<String, ComponentHealth> {
        self.component_states.read().await.clone()
    }
    
    /// Check if system should enter emergency mode
    pub async fn should_enter_emergency_mode(&self) -> bool {
        if !self.degradation_config.enable_emergency_mode {
            return false;
        }
        
        let system_health = self.get_system_health().await;
        matches!(system_health, SystemHealth::Critical(_))
    }
}

/// Overall system health status
#[derive(Debug, Clone, PartialEq)]
pub enum SystemHealth {
    Healthy,
    Degraded(String),
    Unhealthy(String),
    Critical(String),
}

impl fmt::Display for SystemHealth {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SystemHealth::Healthy => write!(f, "Healthy"),
            SystemHealth::Degraded(reason) => write!(f, "Degraded: {}", reason),
            SystemHealth::Unhealthy(reason) => write!(f, "Unhealthy: {}", reason),
            SystemHealth::Critical(reason) => write!(f, "Critical: {}", reason),
        }
    }
}

/// AUDIT-FIX: Common degradation errors
#[derive(Error, Debug)]
pub enum DegradationError {
    #[error("Primary operation failed: {0}")]
    PrimaryFailed(String),
    #[error("Fallback operation failed: {0}")]
    FallbackFailed(String),
    #[error("Component is down: {0}")]
    ComponentDown(String),
    #[error("System in emergency mode: {0}")]
    EmergencyMode(String),
}

#[cfg(test)]
mod tests {
    use super::*;
    
    struct TestComponent {
        should_fail_primary: bool,
        should_fail_fallback: bool,
    }
    
    #[async_trait]
    impl ComponentWithFallback<String> for TestComponent {
        type Error = DegradationError;
        
        async fn primary_operation(&self) -> Result<String, Self::Error> {
            if self.should_fail_primary {
                Err(DegradationError::PrimaryFailed("Test failure".to_string()))
            } else {
                Ok("Primary result".to_string())
            }
        }
        
        async fn fallback_operation(&self) -> Result<String, Self::Error> {
            if self.should_fail_fallback {
                Err(DegradationError::FallbackFailed("Test fallback failure".to_string()))
            } else {
                Ok("Fallback result".to_string())
            }
        }
        
        async fn health_check(&self) -> ComponentHealth {
            if self.should_fail_primary && self.should_fail_fallback {
                ComponentHealth::Down("Both operations failing".to_string())
            } else if self.should_fail_primary {
                ComponentHealth::Degraded("Primary failing".to_string())
            } else {
                ComponentHealth::Healthy
            }
        }
        
        fn component_name(&self) -> &'static str {
            "TestComponent"
        }
    }
    
    #[tokio::test]
    async fn test_primary_success() {
        let component = TestComponent {
            should_fail_primary: false,
            should_fail_fallback: false,
        };
        
        let result = component.execute_with_fallback().await.unwrap();
        assert_eq!(result, "Primary result");
    }
    
    #[tokio::test]
    async fn test_fallback_success() {
        let component = TestComponent {
            should_fail_primary: true,
            should_fail_fallback: false,
        };
        
        let result = component.execute_with_fallback().await.unwrap();
        assert_eq!(result, "Fallback result");
    }
    
    #[tokio::test]
    async fn test_both_fail() {
        let component = TestComponent {
            should_fail_primary: true,
            should_fail_fallback: true,
        };
        
        let result = component.execute_with_fallback().await;
        assert!(result.is_err());
    }
    
    #[tokio::test]
    async fn test_degradation_manager() {
        let manager = DegradationManager::new(DegradationConfig::default());
        
        // Register components
        manager.register_component("test1".to_string(), ComponentHealth::Healthy).await;
        manager.register_component("test2".to_string(), ComponentHealth::Degraded("Test".to_string())).await;
        
        let system_health = manager.get_system_health().await;
        assert!(matches!(system_health, SystemHealth::Degraded(_)));
    }
}