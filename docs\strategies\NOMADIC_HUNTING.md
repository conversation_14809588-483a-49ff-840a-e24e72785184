### **The Doctrine of Nomadic Hunting**

**The Philosophy:** The bot should not be "rebuilt" for each chain. The bot's **brain and soul are chain-agnostic**. The only things that change are its **"eyes" (RPC endpoints)** and its **"map" (contract addresses)**. The migration process is therefore not a complex redeployment, but a graceful, hot-swappable change of its operational context.

**The Process (Low Technical Overhead):**

1.  **Survey (Automated):** The `MarketStateAnalyzer` continuously surveys all potential territories.
2.  **Advise (Automated):** The TUI presents this survey to the operator, recommending the most fertile hunting ground.
3.  **Command (Manual):** The operator makes the strategic decision and gives a single command: "Migrate."
4.  **Execute (Automated):** The system handles the complex tasks of pausing, bridging assets, reconfiguring, and resuming.

---

### **Implementation Guide: Forging the "Nomadic Predator"**

This guide uses the multi-chain reference list you provided to build the system.

#### **Phase 1: The `Atlas` - A Chain-Agnostic Configuration**

**Objective:** Refactor the configuration to be inherently multi-chain. This is the foundation of the entire system.

**File to Edit:** `src/config.rs` and `config.toml`

1.  **Rust Feature: `HashMap` for Chain-Specific Data.** We will use `HashMap`s keyed by `chain_id` to store all chain-specific information. This makes adding a new chain as simple as adding a new entry to the config file.

2.  **Action: Refactor `config.toml`.**

    ```toml
    # config.toml

    # The currently active hunting ground.
    # The bot will load the config for this chain_id on startup.
    active_chain_id = 8453 # Base Mainnet

    # The 'Atlas' - a map of all known territories.
    [chains]

    # --- Territory: Base Mainnet ---
    [chains.8453]
    name = "Base"
    rpc_endpoints = [
        "https://mainnet.base.org",
        "https://base.publicnode.com",
    ]
    block_explorer_url = "https://basescan.org/tx/"
    # Key contracts for this territory
    [chains.8453.contracts]
    weth = "******************************************"
    usdc = "******************************************"
    uniswap_v3_factory = "******************************************"
    aerodrome_weth_usdc_pool = "******************************************"
    # Bridge contract for migrating AWAY from this chain
    bridge_contract = "0x..."

    # --- Territory: Polygon PoS ---
    [chains.137]
    name = "Polygon"
    rpc_endpoints = [
        "https://polygon-rpc.com",
        "https://polygon.drpc.org",
    ]
    block_explorer_url = "https://polygonscan.com/tx/"
    [chains.137.contracts]
    weth = "******************************************"
    usdc = "******************************************"
    quickswap_router = "******************************************"
    bridge_contract = "0x..."

    # ... add entries for Arbitrum, Optimism, etc. ...
    ```

3.  **Action: Refactor `config.rs`.**

    ```rust
    // src/config.rs
    use serde::Deserialize;
    use std::collections::HashMap;

    #[derive(Deserialize)]
    pub struct Config {
        pub active_chain_id: u64,
        pub chains: HashMap<u64, ChainConfig>,
        // ... other global settings
    }

    #[derive(Deserialize)]
    pub struct ChainConfig {
        pub name: String,
        pub rpc_endpoints: Vec<String>,
        pub block_explorer_url: String,
        pub contracts: HashMap<String, String>, // Flexible key-value for contract addresses
    }
    ```

**The Edge:** The bot's entire personality is now decoupled from a specific chain. To switch hunting grounds, we only need to change one number: `active_chain_id`.

#### **Phase 2: The `EcologicalSurveyor`**

**Objective:** Upgrade the `MarketStateAnalyzer` to monitor all configured territories.

**File to Edit:** `src/control/analyzer.rs`

1.  **Rust Feature: `Arc<Provider>` for Shared Connections.** The analyzer will hold a `HashMap<u64, Arc<Provider<Http>>>` to maintain lightweight connections to all chains simultaneously.

2.  **Action: Implement the Survey Loop.**

    ```rust
    // In MarketStateAnalyzer
    async fn run_ecological_survey(&self) {
        loop {
            let mut health_scores = Vec::new();
            // This loop runs concurrently for all chains in the config.
            for (chain_id, provider) in &self.chain_providers {
                let score = self.calculate_territory_health(*chain_id, provider).await;
                health_scores.push(score);
            }

            // Publish the full list to NATS
            self.nats_client.publish(
                "intelligence.ecosystem.health",
                &serde_json::to_vec(&health_scores).unwrap()
            ).await.unwrap();

            tokio::time::sleep(Duration::from_secs(300)).await; // Survey every 5 minutes
        }
    }
    ```

**The Edge:** The bot gains "god-mode" awareness of the entire multi-chain ecosystem, always knowing where the most fertile hunting ground is.

#### **Phase 3: The "Migration" Workflow (Operator-Driven)**

**Objective:** Implement the TUI and backend logic for a safe, operator-commanded migration.

**Files to Edit:** `src/tui/app.rs`, `src/control/migration.rs`

1.  **Action: Build the TUI "Ecosystem" Tab.**

    - The TUI subscribes to `intelligence.ecosystem.health` and displays the list of territories and their health scores.
    - It clearly highlights the `ACTIVE` territory and marks another as `RECOMMENDED` if its score is significantly higher.
    - Pressing `[M]` on a new territory triggers the confirmation dialog.

2.  **Action: Create the `MigrationController`.**
    - This new, lean service (`src/control/migration.rs`) subscribes to `control.system.migrate`.
    - **Rust Feature: Enum for State Machine.** The migration process is a state machine, which is perfectly modeled by a Rust enum.
      ```rust
      enum MigrationState {
          Paused,
          Bridging { from: u64, to: u64, tx_hash: H256 },
          Confirming,
          Complete,
      }
      ```
    - **The Workflow:**
      1.  Receive migrate command. Set state to `Paused`.
      2.  Publish a `control.system.pause_all` message to NATS. All scanners must obey.
      3.  Call the configured `bridge_contract` on the source chain to initiate the asset transfer.
      4.  Set state to `Bridging`, storing the transaction hash.
      5.  Poll a bridge oracle or the destination chain to confirm funds have arrived. Set state to `Confirming`.
      6.  Once confirmed, **this is the critical step:** It does _not_ try to hot-swap the bot's config. Instead, it **publishes a high-priority message to the operator in the TUI** and then **gracefully shuts down the bot**.
          > `SYSTEM: MIGRATION COMPLETE. Assets successfully bridged to Polygon. Please restart the bot with 'active_chain_id = 137' in your config.toml to resume hunting.`
      7.  Set state to `Complete`.

**The Edge (Elegance & Safety):** This design is **profoundly safer and lower overhead** than a complex, in-memory, hot-swappable multi-chain system. A hot-swap is fraught with risk (stale state, provider mismatches). A **clean restart** is a simple, robust, and foolproof way to change the bot's fundamental context. It forces a clean slate, which is the safest way to move between complex environments. The operator is involved in the final step, providing a crucial layer of human verification. This is the epitome of the Basilisk's doctrine: favoring robustness and precision over unnecessary complexity.

---

### **The Doctrine of the "Nomad's Knapsack"**

Think of the Basilisk not as an empire with outposts on every chain, but as a **lone, nomadic hunter**. A nomad doesn't build a fortress in every valley. They travel light, carrying only what is essential. Their strength is their mobility, not their distributed presence.

For our bot, this "knapsack" contains two things:

1.  **A single wallet** (one private key).
2.  **A single core asset** for liquidity and gas.

**Why This is the Superior Strategy:**

- **Dramatically Reduced Complexity:** Managing capital and wallets across multiple chains simultaneously is an operational nightmare. You have to worry about maintaining gas on every chain, tracking P&L across different asset pools, and securing multiple wallets. The "single knapsack" approach eliminates all of this. The bot's entire net worth is in one place, easy to track and secure.
- **Maximum Capital Efficiency:** Arbitrage requires capital. Splitting your $10,000 of capital across five chains means you only have $2,000 to work with in any given territory. This severely limits the size and profitability of the opportunities you can capture. By migrating your **entire capital base** to the most fertile hunting ground, you can deploy 100% of your power where it matters most.
- **Lower Security Footprint:** One wallet means one private key to protect. Managing keys for five different chains dramatically increases your attack surface.
- **Perfect Alignment with the "Territory Hopping" Behavior:** The goal is not to be everywhere at once. The goal is to be in the **best place at the right time**. The single wallet/asset model forces this strategic discipline.

---

### **How it Works in Practice: The Migration Cycle**

Here is the operational cycle for migrating the bot's single wallet and asset pool from **Base** to **Polygon**.

**Prerequisites:**

- The bot's wallet holds its capital primarily in **WETH** and/or a native stablecoin like **USDC**. We will use WETH as the example as it's the most versatile for both liquidity and gas.
- The `config.toml` has been populated with the addresses for a trusted **Bridge Protocol** (e.g., the official Base Bridge, Stargate, Synapse) for both the source and destination chains.

**The Cycle:**

1.  **The Survey (`MarketStateAnalyzer`):**

    - The analyzer's "Ecological Survey" runs. It calculates a `Territory Health Score` for all configured chains.
    - It determines that Polygon's score (e.g., `120.5`) is significantly higher than Base's score (e.g., `85.2`).
    - **TUI Output:** The "Ecosystem" tab in the TUI updates:
      ```
      TERRITORY   | HEALTH SCORE | STATUS
      ------------|--------------|-----------
      Polygon     | 120.5        | RECOMMENDED
      Base        | 85.2         | ACTIVE
      ```

2.  **The Command (Operator):**

    - You see the recommendation. You decide the hunt on Base has grown stale and the competition fierce. You want to move.
    - In the TUI, you select "Polygon" and press `[M]` to migrate.
    - A confirmation appears: `Begin migration to Polygon? This will bridge all WETH from your wallet. The bot will shut down upon completion.` You confirm with `[Y]`.

3.  **The Execution (`MigrationController`):**

    - The `MigrationController` receives the `control.system.migrate` command.
    - **Step A: Pause & Consolidate.**
      - It publishes `control.system.pause_all` to NATS. All scanners stop looking for new opportunities.
      - It queries the bot's wallet balance on **Base**. It finds `5.0 WETH`. It reserves a tiny fraction for gas (e.g., `0.01 WETH`).
    - **Step B: The Bridge.**
      - It constructs a transaction to call the `bridge_contract` on **Base**.
      - The transaction calls a function like `bridge(destinationChainId: 137, amount: 4.99 WETH)`.
      - It dispatches this transaction. The TUI updates to show: `STATUS: MIGRATING (Bridging 4.99 WETH to Polygon)... Tx: 0x...`
    - **Step C: Confirmation.**
      - The controller now enters a loop, polling the bridge's status or the Polygon chain directly, waiting for the `4.99 WETH` to appear in the same wallet address on the destination chain.
    - **Step D: The Final Order.**
      - Once the funds are confirmed on Polygon, the migration is successful.
      - The `MigrationController` publishes a final message to the TUI:
        > `SYSTEM: MIGRATION COMPLETE. 4.99 WETH successfully bridged to Polygon. Please update your config.toml to 'active_chain_id = 137' and restart the bot.`
      - The controller then triggers a **graceful shutdown** of the entire bot application.

4.  **The Relaunch (Operator):**
    - You open `config.toml`, change `active_chain_id = 8453` to `active_chain_id = 137`.
    - You save the file and run `cargo run`.
    - The bot now starts up, loads the configuration for **Polygon**, connects to Polygon RPCs, targets Polygon DEXs, and resumes its hunt in the new, more fertile territory with its full capital base intact.

This "single asset, single wallet" nomadic approach is the epitome of efficiency. It's lean, secure, and allows the Basilisk to bring its full predatory power to bear on whichever ecosystem offers the easiest hunt, perfectly embodying its core personality.

----------

### **Doctrine of Integrated Resilience: The Self-Healing Nomad**

**The New Philosophy:** The bot now has two distinct levels of resilience, mirroring a real predator:

1.  **Tactical Resilience (`Protocol: OROBOROS`):** If the predator's immediate watering hole (primary RPC) dries up, it doesn't immediately abandon the entire territory. It first automatically seeks out the *next closest watering hole* (backup RPC) within the same territory to continue its hunt with minimal disruption.
2.  **Strategic Resilience (`Nomadic Hunting`):** If the *entire territory* becomes barren or too dangerous (as determined by the `EcologicalSurveyor`), the operator can command a full migration to a new, more fertile territory.

`OROBOROS` is the automated, short-term survival instinct. `Nomadic Hunting` is the long-term, operator-guided strategic relocation. Together, they make the bot exceptionally robust.

---

### **Implementation Guide: Integrating OROBOROS with the Nomadic Hunter**

This integration requires no new services. It is an enhancement of the existing `config.toml` structure and the `SystemMonitor`'s (`OROBOROS`) logic.

#### **Phase 1: Upgrade the "Atlas" for Deep Redundancy**

**Objective:** The bot's map (`config.toml`) must be updated to not just list territories, but to list multiple, prioritized "watering holes" within each territory.

**File to Edit:** `config.toml`

1.  **Action: Structure RPCs for Prioritized Failover.**
    The `rpc_endpoints` key for each chain will no longer be a simple list. It will now be an array of tables, allowing us to add metadata, specifically a `priority`.

    ```toml
    # config.toml
    
    active_chain_id = 8453

    [chains]

    # --- Territory: Base Mainnet ---
    [chains.8453]
    name = "Base"
    block_explorer_url = "https://basescan.org/tx/"
    
    # OROBOROS will try these in order of priority.
    # We use a list of tables to structure this.
    rpc_endpoints = [
        { url = "https://your-dedicated-alchemy-url.g.alchemy.com/...", priority = 0 }, # Primary
        { url = "https://your-dedicated-quicknode-url.quiknode.pro/...", priority = 1 }, # Backup 1
        { url = "https://base.publicnode.com", priority = 2 },                       # Backup 2 (Public)
        { url = "https://base.drpc.org", priority = 3 },                             # Backup 3 (Public)
    ]
    # ... contracts ...

    # --- Territory: Polygon PoS ---
    [chains.137]
    name = "Polygon"
    block_explorer_url = "https://polygonscan.com/tx/"
    rpc_endpoints = [
        { url = "https://your-polygon-dedicated-url.quiknode.pro/...", priority = 0 },
        { url = "https://polygon-rpc.com", priority = 1 },
        { url = "https://polygon.drpc.org", priority = 2 },
    ]
    # ... contracts ...
    ```

2.  **Action: Update the `Config` Structs.**
    *   **File to Edit:** `src/config.rs`
    *   **Logic:** The Rust structs must be updated to deserialize this new, richer format.

    ```rust
    // src/config.rs
    use serde::Deserialize;

    // ... Config and ChainConfig structs ...

    #[derive(Deserialize, Clone)] // Add Clone
    pub struct RpcEndpoint {
        pub url: String,
        pub priority: u32,
    }

    // The field in ChainConfig changes:
    // pub rpc_endpoints: Vec<String>  ->  pub rpc_endpoints: Vec<RpcEndpoint>
    ```

**The Edge:** The bot's configuration now contains a rich, prioritized list of failover targets for every territory it knows. This is the ammunition for OROBOROS.

#### **Phase 2: Upgrade `Protocol: OROBOROS` to be a "Pathfinder"**

**Objective:** The `SystemMonitor`'s logic must be upgraded. It will no longer just switch to a single backup; it will intelligently cycle through the prioritized list.

**File to Edit:** `src/control/monitor.rs`

1.  **Action: Enhance the Failover Logic.**
    *   The `SystemMonitor` needs access to the global `Config`.
    *   When it detects a failure for a service, it needs to know the service's *current* RPC URL. This will require the service's heartbeat message to be slightly enriched (e.g., `state.health.gaze { status: Error("..."), current_rpc: "..." }`).
    *   **New OROBOROS Logic:**
        1.  Detects failure from `GazeScanner` using `https://mainnet.base.org`.
        2.  It looks up the `rpc_endpoints` list for the `active_chain_id`.
        3.  It finds the entry for `https://mainnet.base.org` and sees its priority is `0`.
        4.  It searches the list for the next highest priority, which is `1`.
        5.  It finds the URL for priority `1`: `https://base.publicnode.com`.
        6.  It constructs and publishes the targeted `control.config.update` message with this new URL.
        7.  If, later, the connection to `base.publicnode.com` also fails, OROBOROS will repeat the process and failover to the priority `2` endpoint, `base.drpc.org`.

**The Edge:** `OROBOROS` is now a truly resilient self-healing protocol. It can withstand multiple, sequential provider failures, automatically "hopping" between RPC endpoints within its current territory to stay alive and continue the hunt.

#### **Phase 3: Update the TUI for Enhanced Status Reporting**

**Objective:** The operator must be fully aware of the bot's resilience status.

**File to Edit:** `src/tui/app.rs` and `src/tui/ui.rs`

1.  **Action: Enhance the Dashboard's System Health Panel.**
    *   The TUI's state needs to track not just the status of the RPC connection, but also which endpoint is currently in use.
    *   **New TUI Display:**
        ```
        SYSTEM HEALTH:
        RPC Node: [!] WARNING (Operating on Backup #2: drpc.org)
        NATS Bus: [● OK]
        Database: [● OK]
        ```

2.  **Action: Add OROBOROS Events to the Narrative Log.**
    *   The `SystemMonitor` must publish clear, thematic logs to the TUI feed when OROBOROS activates.
        > `SYSTEM  [19:45:10] OROBOROS: Primary RPC failed. Attempting tactical failover.`
        > `SYSTEM  [19:45:11] OROBOROS: Connection established with Backup 1 (publicnode.com). Hunt continues.`
        > `ERROR   [19:50:22] OROBOROS: Backup 1 failed. Attempting tactical failover.`
        > `SYSTEM  [19:50:23] OROBOROS: Connection established with Backup 2 (drpc.org). Hunt continues on degraded connection.`

**The Final, Unified Behavior:**

With this integration, the Basilisk's resilience is now multi-layered and perfectly aligned with its nomadic personality:

*   **Immediate Threat (A single RPC fails):** The bot doesn't panic. `OROBOROS` activates, shedding the broken connection and automatically moving to the next best watering hole **within its current territory**. It heals itself and continues hunting.
*   **Systemic Threat (The whole territory is barren):** The `EcologicalSurveyor` reports a consistently low `Health Score` for the entire territory. The operator sees this, makes a strategic decision, and commands a full **Nomadic Migration** to a new, more fertile ecosystem.

The bot is now not just a predator; it is a master survivalist.