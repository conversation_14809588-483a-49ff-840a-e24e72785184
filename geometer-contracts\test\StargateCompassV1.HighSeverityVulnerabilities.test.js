const { expect } = require('chai');
const { ethers } = require('hardhat');

describe('StargateCompassV1 - High Severity Vulnerability Tests', function () {
  let stargateCompass;
  let owner;
  let attacker;
  let mockAaveProvider;
  let mockStargateRouter;
  let mockPool;
  let mockUSDC;

  const USDC_ADDRESS = '******************************************';
  const LOAN_AMOUNT = ethers.parseUnits('1000', 6); // 1000 USDC
  const EXPECTED_PROFIT = ethers.parseUnits('10', 6); // 10 USDC profit

  beforeEach(async function () {
    [owner, attacker] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory('MockERC20');
    mockUSDC = await MockERC20.deploy('USD Coin', 'USDC', 6);

    const MockAaveProvider = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockAaveProvider'
    );
    mockAaveProvider = await MockAaveProvider.deploy();

    const MockPool = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockPool'
    );
    mockPool = await MockPool.deploy();

    const MockStargateRouter = await ethers.getContractFactory(
      'contracts/mocks/MockDependencies.sol:MockStargateRouter'
    );
    mockStargateRouter = await MockStargateRouter.deploy();

    // Set up mock relationships
    await mockAaveProvider.setPool(mockPool.target);

    // Deploy StargateCompassV1
    const StargateCompassV1 = await ethers.getContractFactory(
      'StargateCompassV1'
    );
    stargateCompass = await StargateCompassV1.deploy(
      mockAaveProvider.target,
      mockStargateRouter.target
    );

    // Fund the contract with ETH for fees
    await owner.sendTransaction({
      to: stargateCompass.target,
      value: ethers.parseEther('1'),
    });

    // Set up mock USDC balance for flash loan simulation
    await mockUSDC.mint(mockPool.target, ethers.parseUnits('10000', 6));
  });

  describe('H-1: Fee Limits Prevent Excessive LayerZero Fees', function () {
    it('should reject operations with fees exceeding maximum limit (0.1 ETH)', async function () {
      const excessiveFee = ethers.parseEther('0.2'); // 0.2 ETH - above 0.1 ETH limit
      await mockStargateRouter.setQuoteLayerZeroFee(excessiveFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ExcessiveFee')
        .withArgs(excessiveFee, ethers.parseEther('0.1'));
    });

    it('should accept fees within the maximum limit', async function () {
      const acceptableFee = ethers.parseEther('0.05'); // 0.05 ETH - within limit
      await mockStargateRouter.setQuoteLayerZeroFee(acceptableFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should validate fees at the exact maximum limit', async function () {
      const maxFee = ethers.parseEther('0.1'); // Exactly at limit
      await mockStargateRouter.setQuoteLayerZeroFee(maxFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should reject ZRO fees (currently unsupported)', async function () {
      const normalFee = ethers.parseEther('0.01');
      const zroFee = ethers.parseUnits('100', 18); // Any ZRO fee
      await mockStargateRouter.setQuoteLayerZeroFee(normalFee, zroFee);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      )
        .to.be.revertedWithCustomError(stargateCompass, 'ZROFeesNotSupported')
        .withArgs(zroFee);
    });

    it('should validate ETH balance is sufficient for fee payment', async function () {
      // Drain most ETH from contract
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const withdrawAmount = currentBalance - ethers.parseEther('0.01'); // Leave minimal ETH
      await stargateCompass.withdrawETH(withdrawAmount);

      const highFee = ethers.parseEther('0.05'); // More than remaining balance
      await mockStargateRouter.setQuoteLayerZeroFee(highFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(
        stargateCompass,
        'InsufficientETHBalance'
      );
    });

    it('should apply fee buffer for volatility protection', async function () {
      const baseFee = ethers.parseEther('0.01');
      const expectedBuffer = (baseFee * BigInt(200)) / BigInt(10000); // 2% buffer
      const bufferedFee = baseFee + expectedBuffer;

      await mockStargateRouter.setQuoteLayerZeroFee(baseFee, 0);
      await mockPool.setFlashLoanSuccess(true);

      // Ensure contract has enough ETH for buffered fee
      const currentBalance = await ethers.provider.getBalance(
        stargateCompass.target
      );
      if (currentBalance < bufferedFee) {
        await owner.sendTransaction({
          to: stargateCompass.target,
          value: bufferedFee - currentBalance + ethers.parseEther('0.1'),
        });
      }

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });
  });

  describe('H-2: Pre-execution Profitability Validation', function () {
    it('should validate profitability before flash loan initiation', async function () {
      // Test with various profit scenarios
      const testCases = [
        {
          profit: ethers.parseUnits('0', 6),
          shouldFail: true,
          description: 'zero profit',
        },
        {
          profit: ethers.parseUnits('1', 6),
          shouldFail: true,
          description: 'minimal profit',
        },
        {
          profit: ethers.parseUnits('5', 6),
          shouldFail: true,
          description: 'insufficient profit',
        },
        {
          profit: ethers.parseUnits('10', 6),
          shouldFail: false,
          description: 'sufficient profit',
        },
      ];

      for (const testCase of testCases) {
        if (testCase.shouldFail) {
          await expect(
            stargateCompass.executeRemoteDegenSwap(
              LOAN_AMOUNT,
              '0x1234',
              owner.address,
              ethers.parseUnits('980', 6),
              testCase.profit
            )
          ).to.be.revertedWithCustomError(
            stargateCompass,
            'InsufficientProfit'
          );
        } else {
          await mockPool.setFlashLoanSuccess(true);
          await mockStargateRouter.setQuoteLayerZeroFee(
            ethers.parseEther('0.01'),
            0
          );

          await expect(
            stargateCompass.executeRemoteDegenSwap(
              LOAN_AMOUNT,
              '0x1234',
              owner.address,
              ethers.parseUnits('980', 6),
              testCase.profit
            )
          ).to.not.be.reverted;
        }
      }
    });

    it('should calculate total required profit correctly', async function () {
      const loanAmount = ethers.parseUnits('1000', 6);

      // Flash loan cost: 1000 * 0.05% = 0.5 USDC
      const expectedFlashLoanCost = (loanAmount * BigInt(5)) / BigInt(10000);

      // Minimum profit: 1000 * 0.5% = 5 USDC
      const expectedMinProfit = (loanAmount * BigInt(50)) / BigInt(10000);

      // Total required: 0.5 + 5 = 5.5 USDC
      const totalRequired = expectedFlashLoanCost + expectedMinProfit;

      // Just below required should fail
      const insufficientProfit = totalRequired - BigInt(1);
      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          insufficientProfit
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');

      // Exactly required should succeed
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          loanAmount,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          totalRequired
        )
      ).to.not.be.reverted;
    });

    it('should perform runtime validation in executeOperation', async function () {
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      const sufficientProfit = ethers.parseUnits('10', 6);

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          sufficientProfit
        )
      ).to.emit(stargateCompass, 'ProfitabilityValidated');
    });

    it('should prevent flash loan defaults through comprehensive validation', async function () {
      // Test edge cases that could lead to defaults
      const edgeCases = [
        {
          amount: ethers.parseUnits('1', 6),
          profit: ethers.parseUnits('0.1', 6),
        }, // Small amounts
        {
          amount: ethers.parseUnits('100000', 6),
          profit: ethers.parseUnits('100', 6),
        }, // Large amounts
        {
          amount: ethers.parseUnits('999', 6),
          profit: ethers.parseUnits('5', 6),
        }, // Odd amounts
      ];

      for (const testCase of edgeCases) {
        await expect(
          stargateCompass.executeRemoteDegenSwap(
            testCase.amount,
            '0x1234',
            owner.address,
            ethers.parseUnits('980', 6),
            testCase.profit
          )
        ).to.be.revertedWithCustomError(stargateCompass, 'InsufficientProfit');
      }
    });
  });

  describe('H-3: Emergency Controls Provide Operational Safety', function () {
    it('should allow owner to pause contract operations', async function () {
      await expect(stargateCompass.emergencyPause())
        .to.emit(stargateCompass, 'EmergencyPaused')
        .withArgs(owner.address);

      expect(await stargateCompass.emergencyPaused()).to.be.true;
    });

    it('should prevent operations when contract is paused', async function () {
      await stargateCompass.emergencyPause();

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.be.revertedWithCustomError(stargateCompass, 'ContractPaused');
    });

    it('should allow owner to unpause contract operations', async function () {
      await stargateCompass.emergencyPause();

      await expect(stargateCompass.emergencyUnpause())
        .to.emit(stargateCompass, 'EmergencyUnpaused')
        .withArgs(owner.address);

      expect(await stargateCompass.emergencyPaused()).to.be.false;
    });

    it('should allow operations after unpausing', async function () {
      await stargateCompass.emergencyPause();
      await stargateCompass.emergencyUnpause();

      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should prevent non-owners from pausing/unpausing', async function () {
      await expect(
        stargateCompass.connect(attacker).emergencyPause()
      ).to.be.revertedWithCustomError(stargateCompass, 'NotOwner');

      await stargateCompass.emergencyPause();

      await expect(
        stargateCompass.connect(attacker).emergencyUnpause()
      ).to.be.revertedWithCustomError(stargateCompass, 'NotOwner');
    });

    it('should handle double pause/unpause gracefully', async function () {
      await stargateCompass.emergencyPause();

      await expect(stargateCompass.emergencyPause()).to.be.revertedWith(
        'Contract is already paused'
      );

      await stargateCompass.emergencyUnpause();

      await expect(stargateCompass.emergencyUnpause()).to.be.revertedWith(
        'Contract is not paused'
      );
    });

    it('should allow emergency asset recovery during pause', async function () {
      // Add some USDC to the contract
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('100', 6));

      await stargateCompass.emergencyPause();

      // Emergency functions should still work during pause
      await expect(stargateCompass.emergencyWithdraw(mockUSDC.target)).to.emit(
        stargateCompass,
        'EmergencyWithdraw'
      );
    });

    it('should provide comprehensive emergency recovery', async function () {
      // Add assets to recover
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('100', 6));

      const initialETH = await ethers.provider.getBalance(
        stargateCompass.target
      );
      const initialUSDC = await mockUSDC.balanceOf(stargateCompass.target);

      await expect(stargateCompass.emergencyRecoverAll()).to.emit(
        stargateCompass,
        'EmergencyRecoverAll'
      );

      // Verify assets were recovered
      const finalETH = await ethers.provider.getBalance(stargateCompass.target);
      expect(finalETH).to.equal(0);
    });
  });

  describe('Operational Safety Tests', function () {
    it('should maintain operational integrity under stress conditions', async function () {
      // Test rapid pause/unpause cycles
      for (let i = 0; i < 5; i++) {
        await stargateCompass.emergencyPause();
        await stargateCompass.emergencyUnpause();
      }

      // Contract should still function normally
      await mockPool.setFlashLoanSuccess(true);
      await mockStargateRouter.setQuoteLayerZeroFee(
        ethers.parseEther('0.01'),
        0
      );

      await expect(
        stargateCompass.executeRemoteDegenSwap(
          LOAN_AMOUNT,
          '0x1234',
          owner.address,
          ethers.parseUnits('980', 6),
          EXPECTED_PROFIT
        )
      ).to.not.be.reverted;
    });

    it('should handle emergency scenarios with multiple asset types', async function () {
      // Add multiple token types
      const MockToken1 = await ethers.getContractFactory('MockERC20');
      const token1 = await MockToken1.deploy('Token1', 'TK1', 18);
      const token2 = await MockToken1.deploy('Token2', 'TK2', 8);

      await token1.mint(stargateCompass.target, ethers.parseUnits('50', 18));
      await token2.mint(stargateCompass.target, ethers.parseUnits('25', 8));
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('100', 6));

      // Emergency recovery should handle all assets
      await expect(stargateCompass.emergencyRecoverAll()).to.not.be.reverted;
    });

    it('should provide accurate asset inventory for emergency planning', async function () {
      await mockUSDC.mint(stargateCompass.target, ethers.parseUnits('100', 6));

      const [tokens, balances] = await stargateCompass.getRecoverableAssets();

      expect(tokens.length).to.equal(2); // ETH + USDC
      expect(balances.length).to.equal(2);

      // Verify ETH balance
      const ethIndex = tokens.findIndex(
        (token) => token === ethers.ZeroAddress
      );
      expect(ethIndex).to.not.equal(-1);
      expect(balances[ethIndex]).to.be.gt(0);

      // Verify USDC balance
      const usdcIndex = tokens.findIndex((token) => token === USDC_ADDRESS);
      expect(usdcIndex).to.not.equal(-1);
      expect(balances[usdcIndex]).to.equal(ethers.parseUnits('100', 6));
    });

    it('should maintain security during emergency operations', async function () {
      // Non-owner should not be able to perform emergency operations
      await expect(
        stargateCompass.connect(attacker).emergencyWithdraw(mockUSDC.target)
      ).to.be.revertedWithCustomError(stargateCompass, 'NotOwner');

      await expect(
        stargateCompass.connect(attacker).emergencyRecoverAll()
      ).to.be.revertedWithCustomError(stargateCompass, 'NotOwner');

      await expect(
        stargateCompass.connect(attacker).emergencyWithdrawETH()
      ).to.be.revertedWithCustomError(stargateCompass, 'NotOwner');
    });
  });
});
