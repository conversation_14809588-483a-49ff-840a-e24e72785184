# Operational Flow Tracing - Aetheric Resonance Engine

## Executive Summary

This document traces the complete operational flow of the Aetheric Resonance Engine (ARE) from opportunity discovery through execution, mapping data flow through StrategyManager scoring and synthesis, documenting ExecutionManager timing and network condition integration, and identifying critical decision points and potential bottlenecks.

## 1. System Initialization and Entry Points

### 1.1 Application Startup (`src/main.rs`)

The system begins with command-line argument parsing and configuration loading:

```rust
// Parse command line arguments
let args = Cli::parse();

// Load configuration
let config = Arc::new(Settings::new(args.config.as_deref())?);
```

**Entry Points:**

- **TUI Mode**: `basilisk_bot::cli_handlers::handle_tui_command(config).await`
- **Run Mode**: Various operational modes (Simulate, Shadow, Sentinel, LowCapital, Live)

### 1.2 Operational Mode Selection (`src/operational_modes.rs`)

The system supports five operational modes, each with different risk profiles:

1. **Simulate**: Educational mode with no real transactions
2. **Shadow**: Live simulation with fork validation
3. **Sentinel**: Live monitoring with minimal capital
4. **LowCapital**: Conservative live trading
5. **Live**: Full production trading

## 2. Opportunity Discovery Flow

### 2.1 Scanner Initialization

Multiple scanners operate concurrently to detect opportunities:

**Primary Scanners:**

- **GazeScanner** (`src/strategies/scanners/gaze.rs`): DEX-DEX arbitrage on Degen Chain
- **AmmOpportunityScanner**: AMM-based opportunities
- **LiquidationScanner**: Liquidation opportunities
- **PilotFishScanner**: Flash loan opportunities
- **SwapScanner**: Swap-based arbitrage

### 2.2 GazeScanner Operational Flow

The GazeScanner demonstrates the typical scanner pattern:

```rust
pub async fn scan_for_opportunities(&self) -> Result<Vec<Opportunity>> {
    // 1. Get popular token pairs
    let token_pairs = self.get_popular_token_pairs().await?;

    // 2. Safety check tokens
    for (token_a, token_b) in token_pairs {
        if !self.are_tokens_safe(&token_a, &token_b).await? {
            continue;
        }

        // 3. Get prices across DEXes
        let prices = self.get_prices_across_dexes(&token_a, &token_b).await?;

        // 4. Analyze price differences
        if let Some(opportunity) = self.analyze_price_differences(&token_a, &token_b, prices).await? {
            opportunities.push(opportunity);
        }
    }
}
```

**Critical Decision Point 1**: Token safety validation via HoneypotChecker
**Potential Bottleneck 1**: Multi-DEX price fetching via RPC calls

### 2.3 Opportunity Structure

All opportunities are converted to the unified `Opportunity` enum:

```rust
pub enum Opportunity {
    DexArbitrage { base: OpportunityBase, data: DexArbitrageData },
    ZenGeometer { base: OpportunityBase, data: ZenGeometerData },
    PilotFish { base: OpportunityBase, data: PilotFishData },
    // ... other types
}
```

## 3. Three-Pillar Analysis Architecture

### 3.1 Chronos Sieve (Temporal Analysis)

**Location**: `src/data/fractal_analyzer.rs`

**Data Flow:**

1. Price points ingested via `add_price_point()`
2. Kalman filtering applied for noise reduction
3. Multi-timeframe analysis (1m, 15m, 1h)
4. FFT analysis for dominant cycles
5. Hurst exponent calculation for market character
6. Market regime classification via HMM

**Output**: `TemporalHarmonics` containing:

- `dominant_cycles_minutes`: Vec<(f64, f64)>
- `market_rhythm_stability`: f64
- `wavelet_features`: Vec<Decimal>

**Critical Decision Point 2**: Market regime classification affects strategy scoring multipliers

### 3.2 Mandorla Gauge (Geometric Analysis)

**Location**: `src/math/geometry.rs`, `src/math/vesica.rs`

**Data Flow:**

1. Arbitrage path converted to geometric coordinates
2. Pool reserves converted to USD using price oracle
3. Convexity ratio calculated using convex hull analysis
4. Harmonic path score based on anchor asset presence
5. Vesica Piscis depth calculation for opportunity sizing

**Key Formula** (Vesica Piscis):

```rust
// Amount_To_Equalize = Pool_Reserves_X * (sqrt(Price_B / Price_A) - 1)
let amount_to_equalize = cheaper_pool_reserves * (sqrt_price_ratio - Decimal::ONE);
```

**Output**: `GeometricScore` containing:

- `convexity_ratio`: Decimal
- `liquidity_centroid_bias`: Decimal
- `harmonic_path_score`: Decimal

**Potential Bottleneck 2**: Price oracle calls for USD conversion

### 3.3 Network Seismology

**Location**: `src/bin/network_observer.rs`, `src/bin/seismic_analyzer.rs`

**Data Flow:**

1. **Network Observer**: Multi-endpoint WebSocket connections monitor block propagation
2. **P-Wave Detection**: First timestamp across all endpoints
3. **S-Wave Detection**: 80th percentile timestamp
4. **S-P Time Calculation**: Network latency measurement
5. **Coherence Analysis**: Reorg detection and chain stability
6. **Seismic Analyzer**: Processes samples into NetworkResonanceState

**Output**: `NetworkResonanceState` containing:

- `sp_time_ms`: f64 (S-P wave time delta)
- `network_coherence_score`: f64 (0.0 to 1.0)
- `is_shock_event`: bool
- `sequencer_status`: String
- `censorship_detected`: bool

**Critical Decision Point 3**: Network shock events can halt trading

## 4. Strategy Manager Synthesis

### 4.1 Opportunity Processing Flow (`src/strategies/manager.rs`)

The StrategyManager acts as the central brain, synthesizing all three pillars:

```rust
pub async fn process_opportunity(&mut self, mut opportunity: Opportunity) -> Result<()> {
    // 1. Cross-chain cost analysis
    let final_net_profit_usd = self.calculate_cross_chain_net_profit(&opportunity).await?;

    // 2. Honeypot detection
    if self.honeypot_detector.is_honeypot(token_address).await {
        return Ok(()); // Reject opportunity
    }

    // 3. Calculate Aetheric Resonance Score
    let mut score = self.scoring_engine.calculate_opportunity_score(
        &opportunity,
        &self.latest_market_regime,
        &self.latest_temporal_harmonics,
        &self.latest_network_resonance_state,
        &self.centrality_scores,
    ).await;

    // 4. Apply SIGINT strategic bias
    score = self.apply_strategic_bias(&opportunity, score);

    // 5. Decision logic
    if score >= self.min_execution_score {
        // Forward to execution
        self.nats_client.publish(NatsTopics::EXECUTION_REQUEST, payload).await?;
    }
}
```

### 4.2 Aetheric Resonance Score Calculation

**Location**: `src/strategies/scoring.rs` (ScoringEngine)

The final score synthesis combines all three pillars:

```rust
final_score = (temporal_weight * temporal_score) +
              (geometric_weight * geometric_score) +
              (network_weight * network_score)
```

**Critical Decision Point 4**: Score threshold comparison determines execution

### 4.3 SIGINT Intelligence Override System

The system supports intelligence officer directives that can modify scoring:

```rust
fn apply_strategic_bias(&self, opportunity: &Opportunity, base_score: Decimal) -> Decimal {
    if self.autonomous_mode {
        return base_score; // No bias in autonomous mode
    }

    for active_directive in &self.active_directives {
        // Apply multiplier based on directive
        if matches_bias_target {
            final_score *= multiplier_decimal;
        }
    }
}
```

## 5. Execution Manager Flow

### 5.1 Phase 2 Orchestration Pipeline (`src/execution/manager.rs`)

The ExecutionManager implements a clean orchestration pipeline:

```rust
pub async fn process_opportunity(&self, opportunity: Opportunity) -> Result<TransactionReceipt> {
    // Step 1: Circuit breaker check
    if self.risk_manager.is_trading_halted().await {
        return Err(TradingHalted);
    }

    // Step 2: Nonce management
    let nonce = self.nonce_manager.get_next_nonce().await?;

    // Step 3: Transaction building
    let base_tx = self.build_transaction_for_opportunity(&opportunity, gas_bid).await?;

    // Step 4: Gas estimation with Golden Ratio Bidding
    let mut gas_params = self.gas_estimator.calculate_eip1559_params(chain_id, GasUrgency::High).await?;

    // Step 5: Pre-execution validation
    let validation_result = self.pre_execution_validator.validate(&opportunity, &tx_request).await?;

    // Step 6: Transaction broadcast
    let receipt = self.broadcaster.send_and_confirm(tx_request).await?;

    // Step 7: Post-execution updates
    self.nonce_manager.confirm_transaction(nonce).await?;
    self.circuit_breaker.check_and_update(actual_pnl)?;
}
```

### 5.2 Golden Ratio Bidding Implementation

For MEV competition, the system implements Golden Ratio bidding:

```rust
fn calculate_golden_ratio_bid(&self, gross_profit_usd: Decimal, predicted_competitor_bid_usd: Decimal) -> Decimal {
    let golden_ratio_conjugate = dec!(0.382); // 1 - (1/φ)
    let profit_margin = gross_profit_usd - predicted_competitor_bid_usd;
    let our_premium = profit_margin * golden_ratio_conjugate;
    predicted_competitor_bid_usd + our_premium
}
```

### 5.3 Network Condition Integration

The ExecutionManager integrates network conditions for optimal timing:

**HarmonicTimingOracle**: Uses NetworkResonanceState to determine optimal broadcast timing
**Gas Strategy**: Adjusts gas parameters based on network congestion
**Circuit Breakers**: Halt trading during network shock events

## 6. Critical Decision Points

### 6.1 Opportunity Filtering

1. **Token Safety**: HoneypotChecker validation
2. **Profitability**: Minimum profit thresholds
3. **Network Conditions**: Shock event detection
4. **Risk Limits**: Circuit breaker status

### 6.2 Scoring Thresholds

1. **Temporal Analysis**: Market regime compatibility
2. **Geometric Analysis**: Convexity and centrality requirements
3. **Network Analysis**: Coherence score minimums
4. **Combined Score**: Final resonance threshold

### 6.3 Execution Gates

1. **Risk Manager**: Master circuit breaker
2. **Pre-execution Validation**: Final safety checks
3. **Nonce Management**: Transaction ordering
4. **Gas Strategy**: MEV competition readiness

## 7. Potential Bottlenecks

### 7.1 Data Ingestion Bottlenecks

1. **Multi-endpoint RPC calls**: Price fetching across DEXes
2. **WebSocket connections**: Block propagation monitoring
3. **Price oracle queries**: USD conversion for geometric analysis

### 7.2 Analysis Bottlenecks

1. **FFT calculations**: Temporal harmonics analysis
2. **Geometric computations**: Convex hull and polygon area calculations
3. **Market regime classification**: HMM processing

### 7.3 Execution Bottlenecks

1. **Nonce management**: Sequential transaction ordering
2. **Gas estimation**: Real-time network condition analysis
3. **Transaction confirmation**: Network propagation delays

### 7.4 Communication Bottlenecks

1. **NATS message passing**: Inter-component communication
2. **Database queries**: Historical data retrieval
3. **Configuration reloading**: Dynamic parameter updates

## 8. Data Flow Summary

```
Scanners → Opportunities → StrategyManager → ExecutionManager → Blockchain
    ↓           ↓              ↓                ↓
Price Data → Temporal → Aetheric Resonance → Transaction
Market Data → Geometric → Score Calculation → Broadcast
Network Data → Network → Decision Logic → Confirmation
```

## 9. Performance Characteristics

### 9.1 Latency Requirements

- **Opportunity Detection**: < 100ms per scan cycle
- **Score Calculation**: < 50ms per opportunity
- **Transaction Building**: < 20ms
- **Network Broadcast**: < 500ms

### 9.2 Throughput Expectations

- **Scanner Frequency**: 1-5 second intervals
- **Opportunity Processing**: 10-50 opportunities/second
- **Execution Capacity**: 1-5 transactions/second

## 10. Recommendations

### 10.1 Optimization Opportunities

1. **Caching**: Price oracle responses and geometric calculations
2. **Batching**: Multi-opportunity analysis
3. **Parallelization**: Independent scanner execution
4. **Connection Pooling**: RPC endpoint management

### 10.2 Monitoring Requirements

1. **Latency Tracking**: End-to-end opportunity processing time
2. **Bottleneck Detection**: Component-level performance metrics
3. **Error Rate Monitoring**: Failed opportunity processing
4. **Resource Utilization**: CPU, memory, and network usage

This operational flow analysis reveals a sophisticated three-pillar architecture with clear data flow patterns, well-defined decision points, and identifiable performance bottlenecks. The system demonstrates good separation of concerns with the StrategyManager acting as the central synthesis point for all analysis components.
