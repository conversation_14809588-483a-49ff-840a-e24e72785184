# Quick Cross-Reference Validation Runner
# Simple interface for running cross-reference validation

param(
    [switch]$Quick = $false,
    [switch]$Detailed = $false,
    [switch]$JsonOutput = $false
)

Write-Host "Cross-Reference Validation Runner" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

if ($Quick) {
    Write-Host "Running quick validation..." -ForegroundColor Yellow
    & powershell -ExecutionPolicy Bypass -File "scripts/validate_cross_references.ps1"
} elseif ($Detailed) {
    Write-Host "Running detailed validation with reporting..." -ForegroundColor Yellow
    $Args = @("-Mode", "all")
    if ($JsonOutput) { $Args += "-JsonOutput" }
    & powershell -ExecutionPolicy Bypass -File "scripts/cross_reference_validation_system.ps1" @Args
} else {
    Write-Host "Running broken link detection..." -ForegroundColor Yellow
    $Args = @("-OutputFile", "validation_reports/quick_validation_report.md")
    if ($JsonOutput) { $Args += "-JsonOutput" }
    & powershell -ExecutionPolicy Bypass -File "scripts/broken_link_detector.ps1" @Args
}

Write-Host ""
Write-Host "Validation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Available options:" -ForegroundColor Cyan
Write-Host "  -Quick     : Run quick cross-reference validation" -ForegroundColor White
Write-Host "  -Detailed  : Run comprehensive validation with full reporting" -ForegroundColor White
Write-Host "  -JsonOutput: Generate JSON reports in addition to markdown" -ForegroundColor White