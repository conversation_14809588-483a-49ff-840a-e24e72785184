# The Zen Geometer: Mathematical Implementation Reference

**To:** Operations Team
**From:** The Architect
**Subject:** Production Mathematical Models - Implementation Complete

This document serves as the mathematical reference for the Zen Geometer's production implementation. All formulas and algorithms have been successfully implemented and are operational in the live system. This is now a reference guide for the mathematical foundations underlying the trading strategies.

---

### **1. DEX-DEX Pathfinding: The Graph as a Weapon**

Our core on-chain arbitrage capability relies on modeling the entire DEX landscape as a weighted, directed graph. This is the foundational doctrine for the **Liquidity Scavenger**.

#### **1.1. Doctrine: The Logarithmic Transformation**

-   **The Problem:** An arbitrage opportunity is a multiplicative cycle: `Rate₁ * Rate₂ * ... * Rateₙ > 1`. Standard algorithms require additive weights.
-   **The Solution:** We apply the natural logarithm (`ln`) to convert multiplication into addition.
-   **The Edge Weight Formula:** To use algorithms that find negative-weight cycles (like Bellman-Ford or MMBF), we define the edge weight `w` as the **negative natural logarithm of the exchange rate**.
    > `w(A→B) = -ln(Rate(A→B))`
-   **The Conclusion:** A profitable arbitrage cycle is mathematically equivalent to a path whose sum of weights is negative. We are now hunting for negative cycles.

#### **1.2. Rust Implementation: The `ArbitrageGraph` (`strategies/liquidity_scavenger.rs`)**

-   **Crate:** `petgraph`. Its `DiGraph` is our foundational data structure.
-   **Data Structures:**

    ```rust
    use petgraph::graph::{DiGraph, NodeIndex};
    use std::collections::HashMap;
    use ethers::types::{Address, U256};

    // Represents a token. It is the node in our graph.
    #[derive(Debug, Clone, PartialEq, Eq, Hash)]
    pub struct Token {
        pub address: Address,
        pub symbol: String,
        pub decimals: u8,
    }

    // The in-memory model of the entire market.
    pub struct ArbitrageGraph {
        // The graph itself: Nodes are Tokens, Edges are weighted by -ln(rate).
        pub graph: DiGraph<Token, f64>,
        // A high-speed lookup map from a token's on-chain address to its internal graph index.
        pub token_map: HashMap<Address, NodeIndex>,
        // A map from an edge (source_idx, target_idx) to the pool that facilitates the trade.
        // This is CRITICAL for reconstructing the path for execution.
        pub edge_to_pool: HashMap<(NodeIndex, NodeIndex), Address>,
    }
    ```

-   **Edge Weight Calculation:** This function is the heart of the graph's accuracy. It must be called on every `Swap` event.

    ```rust
    // Inside ArbitrageGraph impl
    pub fn update_edge_weight(&mut self, pool_addr: Address, token_a: &Token, token_b: &Token, reserves_a: U256, reserves_b: U256, fee: f64) {
        if reserves_a.is_zero() || reserves_b.is_zero() { return; }

        // WARNING: Using f64 for U256 math is a necessary evil for `ln`. It introduces
        // potential precision loss. For production, this is a known and accepted trade-off,
        // but it must be monitored. Using a dedicated high-precision decimal library
        // would be too slow for our high-frequency update loop.
        let reserves_a_f64 = reserves_a.to_string().parse::<f64>().unwrap_or(0.0);
        let reserves_b_f64 = reserves_b.to_string().parse::<f64>().unwrap_or(0.0);

        // Rate(A→B) = (Reserves_B * (1 - Fee)) / Reserves_A
        let rate_a_to_b = (reserves_b_f64 * (1.0 - fee)) / reserves_a_f64;
        if rate_a_to_b > 0.0 {
            let weight = -rate_a_to_b.ln();
            let node_a = self.token_map[&token_a.address];
            let node_b = self.token_map[&token_b.address];
            self.graph.update_edge(node_a, node_b, weight);
            self.edge_to_pool.insert((node_a, node_b), pool_addr);
        }

        // --- Repeat for the B->A direction ---
        let rate_b_to_a = (reserves_a_f64 * (1.0 - fee)) / reserves_b_f64;
        if rate_b_to_a > 0.0 {
            let weight = -rate_b_to_a.ln();
            let node_a = self.token_map[&token_a.address];
            let node_b = self.token_map[&token_b.address];
            self.graph.update_edge(node_b, node_a, weight);
            self.edge_to_pool.insert((node_b, node_a), pool_addr);
        }
    }
    ```

---

### **2. Optimal Sizing: From Path to Payload**

Finding a path is the signal. Calculating the optimal trade size is what generates profit. This is used by all strategies.

#### **2.1. Formula: Two-Pool `x*y=k` Arbitrage (The Closed-Form Relic)**

This formula is elegant but increasingly rare in a CL-dominated world. It serves as a baseline and a sanity check.

-   **Variables:** `x₁`, `y₁` (reserves in Pool 1); `x₂`, `y₂` (reserves in Pool 2); `f₁`, `f₂` (fees).
-   **Optimal Input (`Δa`):**
    > `Numerator = sqrt(x₁*y₁*x₂*y₂ * (1-f₁)*(1-f₂)) - (x₁*y₂)`
    > `Denominator = y₂*(1-f₁) + y₁`
    > `Δa = Numerator / Denominator`

#### **2.2. Algorithm: Concentrated Liquidity (The Iterative Hunt)**

-   **The Problem:** Concentrated Liquidity has no closed-form sizing solution. The profit function `Profit(AmountIn)` is a complex, non-linear curve. We must find its peak.
-   **The Algorithm (Ternary Search):** This is a ruthlessly efficient search algorithm for finding the maximum of a unimodal function. We iteratively narrow the search space `[low, high]` until we converge on the optimal input amount.

-   **Rust Implementation (`strategies/sizing.rs`):**

    ```rust
    use crate::execution::Simulator; // Your Anvil wrapper
    use std::sync::Arc;

    // This is not a "calculator." It is an iterative hunter.
    pub struct V3SizingOptimizer {
        simulator: Arc<Simulator>,
        pools: Vec<Address>, // The sequence of pools in the arbitrage path
    }

    impl V3SizingOptimizer {
        // The profit function. We feed it a potential trade size (the bait) and
        // it returns the simulated profit by calling out to our Anvil fork.
        async fn calculate_profit(&self, amount_in: U256) -> i128 { // Signed for profit/loss
            if amount_in.is_zero() { return 0; }
            match self.simulator.simulate_swap_path(&self.pools, amount_in).await {
                Ok(amount_out) => amount_out.as_i128() - amount_in.as_i128(),
                Err(_) => i128::MIN, // Penalize simulation errors heavily
            }
        }

        // The hunt itself.
        pub async fn find_optimal_amount(&self) -> U256 {
            let mut low = U256::zero();
            // A realistic upper bound. Don't boil the ocean. 50 WETH is a sane start.
            let mut high = U256::from(50) * U256::from(10).pow(U256::from(18));

            // ~100 iterations is more than enough precision for our purposes.
            for _ in 0..100 {
                // Should not happen in a unimodal function, but a good guard.
                if high < low || high.saturating_sub(low) < U256::from(2) { break; }
                
                let mid1 = low + (high - low) / 3;
                let mid2 = high - (high - low) / 3;

                if self.calculate_profit(mid1).await > self.calculate_profit(mid2).await {
                    high = mid2;
                } else {
                    low = mid1;
                }
            }
            // Return the midpoint of the final, narrow range.
            (low + high) / 2
        }
    }
    ```

---

### **3. CEX-DEX Latency: Statistical Triggers**

We weaponize the latency between CEX order books and on-chain state. The Z-Score tells us when a price deviation is a real signal, not just market noise.

#### **3.1. Formula: The Rolling Z-Score**

-   **Purpose:** To identify if the current CEX-DEX spread is a statistically significant deviation from its recent history.
-   **Formula:**
    > `Z_Score = (Current_Spread - SMA(Spreads, n)) / STDEV(Spreads, n)`
-   **Logic:** Trigger a trade if `|Z_Score| > Threshold` (e.g., `3.0`). A score of 3 means the current spread is 3 standard deviations away from the mean—a rare event.

#### **3.2. Rust Implementation (`strategies/cex_dex.rs`)**

-   **Data Structure:** `std::collections::VecDeque` is the perfect Rust type for a high-performance, fixed-size rolling window.

    ```rust
    use std::collections::VecDeque;

    const Z_SCORE_WINDOW_SIZE: usize = 100;
    const Z_SCORE_TRIGGER_THRESHOLD: f64 = 3.0;

    pub struct CexDexSpreadMonitor {
        // A deque to hold the last N spread measurements.
        spreads: VecDeque<f64>,
    }

    impl CexDexSpreadMonitor {
        pub fn new() -> Self { Self { spreads: VecDeque::with_capacity(Z_SCORE_WINDOW_SIZE) } }

        /// Adds a new spread measurement, updates statistics, and returns the Z-Score.
        pub fn update(&mut self, spread: f64) -> Option<f64> {
            // Maintain the rolling window size.
            if self.spreads.len() == Z_SCORE_WINDOW_SIZE {
                self.spreads.pop_front();
            }
            self.spreads.push_back(spread);

            // Don't calculate until the window is full to avoid skewed stats.
            if self.spreads.len() < Z_SCORE_WINDOW_SIZE { return None; }

            // Calculate statistics.
            let sum: f64 = self.spreads.iter().sum();
            let mean = sum / (Z_SCORE_WINDOW_SIZE as f64);
            let variance: f64 = self.spreads.iter().map(|val| (val - mean).powi(2)).sum();
            let std_dev = (variance / (Z_SCORE_WINDOW_SIZE as f64)).sqrt();

            // Guard against division by zero if the spread is flat.
            if std_dev < 1e-9 { return Some(0.0); }

            Some((spread - mean) / std_dev)
        }
    }
    ```

---

### **4. MEV State Modeling: The Tick Raider's Battlefield**

This is the most advanced doctrine in the grimoire. The previous sections dealt with prices. Here, we deal with the very structure of liquidity itself. For the **CL Tick Raider**, the price is an output; the liquidity distribution is the input. This model is its weapon.

#### **4.1. Doctrine: Beyond Price, Into Depth**

-   **The Problem:** In a Concentrated Liquidity pool, price is not a single number but a step function defined by discrete ticks. A large swap doesn't move a price along a curve; it consumes finite chunks of liquidity, jumping from one tick to the next. To exploit this, we cannot simply track the current price. We must maintain a perfect, real-time model of the liquidity at every relevant tick.
-   **The Solution:** We construct an in-memory data structure that mirrors the on-chain state of the CL pool. This allows us to simulate, with perfect fidelity, the effect of a large swap and calculate the exact intra-block arbitrage it creates.

#### **4.2. Rust Implementation: The `MonitoredCLPool` (`strategies/cl_tick_raider.rs`)**

-   **Data Structures:** The choice of data structure is paramount for performance. A `BTreeMap` is ideal for storing ticks because it maintains sorted keys, allowing for efficient iteration and finding the "next initialized tick" during a simulation.

    ```rust
    use std::collections::BTreeMap;
    use ethers::types::{Address, U256};

    // Represents the on-chain state of a single liquidity tick.
    // This is the atomic unit of our CL model.
    #[derive(Debug, Clone, Copy, Default)]
    pub struct Tick {
        // The total liquidity that is added or removed when this tick is crossed.
        pub liquidity_gross: u128,
        // The net change in liquidity when crossing this tick from left to right.
        pub liquidity_net: i128,
    }

    // The in-memory, high-fidelity model of a single CL pool we are targeting.
    // This entire struct must be kept perfectly synchronized with on-chain state.
    pub struct MonitoredCLPool {
        pub address: Address,
        // The core of the model. A map from a tick's index (i32) to its state.
        // BTreeMap is chosen for its efficient, ordered iteration.
        pub ticks: BTreeMap<i32, Tick>,
        // The current active tick the pool is trading in.
        pub current_tick: i32,
        // The current sqrt(price) * 2^96. This is the "current price."
        pub sqrt_price_x96: U256,
        // The amount of liquidity currently active in the pool.
        pub current_liquidity: u128,
        // Other necessary state like fee, token addresses, etc.
    }
    ```

-   **State Synchronization Logic:** The Tick Raider service must subscribe to `Swap` events for its target pools. Each swap updates the `MonitoredCLPool`.

    ```rust
    // Inside the CLTickRaider service impl
    fn on_swap_event(&mut self, pool_addr: Address, new_sqrt_price: U256, new_tick: i32, new_liquidity: u128) {
        if let Some(pool) = self.target_pools.get_mut(&pool_addr) {
            pool.sqrt_price_x96 = new_sqrt_price;
            pool.current_tick = new_tick;
            pool.current_liquidity = new_liquidity;
            // Note: A real implementation also needs to handle tick updates
            // when new liquidity is added or removed (Mint/Burn events).
        }
    }
    ```

---

### **5. DEX Parasite Doctrine: The Litmus Test**

The **DEX Parasite** does not require a complex new mathematical model. Its "formula" is a simple, procedural, and brutal litmus test for profitability in a hostile environment.

-   **The Formula:** `IsProfitable = (UntrustedPrice - OraclePrice) > (GasCost + RiskPremium)`
-   **The Logic:**
    1.  Detect a `PoolCreated` event.
    2.  Instantiate the new pool contract at runtime.
    3.  Calculate its implicit price from reserves: `Price = ReserveA / ReserveB`.
    4.  Fetch a trusted price from a primary oracle (e.g., a NATS topic published by the `MarketStateAnalyzer`).
    5.  If the deviation is positive and exceeds a high threshold that accounts for gas and an extreme `RiskPremium`, publish the opportunity.
-   **Implementation:** The code for this resides in the service's procedural logic (`strategies/dex_parasite.rs`), not in a reusable formula module. Its risk is managed by the `ExecutionManager`, not by its own mathematical complexity.