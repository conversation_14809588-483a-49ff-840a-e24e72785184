#!/bin/bash

# Zen Geometer Unified Testing Suite
# Runs all tests across the entire project: Rust, Solidity, and Fuzz tests
# Usage: ./scripts/run_all_tests.sh [--verbose] [--skip-fuzz]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Parse command line arguments
VERBOSE=false
SKIP_FUZZ=false

for arg in "$@"; do
    case $arg in
        --verbose)
            VERBOSE=true
            shift
            ;;
        --skip-fuzz)
            SKIP_FUZZ=true
            shift
            ;;
        --help)
            echo "Zen Geometer Unified Testing Suite"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --verbose    Show detailed test output"
            echo "  --skip-fuzz  Skip fuzz testing (recommended for CI)"
            echo "  --help       Show this help message"
            echo ""
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to print colored output
print_header() {
    echo -e "${BOLD}${CYAN}🧪 $1${NC}"
}

print_step() {
    echo -e "${BLUE}✅ [$2] $1${NC}"
}

print_success() {
    echo -e "${GREEN}SUCCESS${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}WARNING${NC} $1"
}

print_error() {
    echo -e "${RED}ERROR${NC} $1"
}

print_info() {
    echo -e "${CYAN}INFO${NC} $1"
}

# Track test results
RUST_TESTS_PASSED=false
SOLIDITY_TESTS_PASSED=false
FUZZ_TESTS_AVAILABLE=false

print_header "Starting Zen Geometer Unified Testing Suite..."
echo ""

# ============================================================================
# [1/3] Rust Tests (Unit & Integration)
# ============================================================================
print_step "Running Rust unit and integration tests..." "1/3"

print_info "Executing cargo test with all features..."

if [ "$VERBOSE" = true ]; then
    echo ""
    echo "--- Rust Test Output ---"
    if cargo test --all-features; then
        RUST_TESTS_PASSED=true
        print_success "All Rust tests passed"
    else
        print_error "Some Rust tests failed"
    fi
    echo "--- End Rust Test Output ---"
    echo ""
else
    if cargo test --all-features --quiet; then
        RUST_TESTS_PASSED=true
        print_success "All Rust tests passed"
    else
        print_error "Some Rust tests failed. Use --verbose for details."
    fi
fi

# Run specific test categories if available
print_info "Running specific test categories..."

# Test simulation mode
if cargo test --test "*simulation*" --quiet >/dev/null 2>&1; then
    print_success "Simulation tests passed"
fi

# Test integration with Anvil
if cargo test test_integration_anvil --quiet >/dev/null 2>&1; then
    print_success "Anvil integration tests passed"
fi

# Test mathematical properties
if cargo test math_properties --quiet >/dev/null 2>&1; then
    print_success "Mathematical property tests passed"
fi

echo ""

# ============================================================================
# [2/3] Solidity Tests (Smart Contracts)
# ============================================================================
print_step "Running Solidity smart contract tests..." "2/3"

if [ -d "geometer-contracts" ] && command -v npx >/dev/null 2>&1; then
    print_info "Changing to geometer-contracts directory..."
    cd geometer-contracts
    
    if [ -f "package.json" ]; then
        print_info "Installing dependencies..."
        if npm install --silent >/dev/null 2>&1; then
            print_success "Dependencies installed"
        else
            print_warning "Failed to install dependencies"
        fi
        
        print_info "Running Hardhat tests..."
        if [ "$VERBOSE" = true ]; then
            echo ""
            echo "--- Solidity Test Output ---"
            if npx hardhat test; then
                SOLIDITY_TESTS_PASSED=true
                print_success "All Solidity tests passed"
            else
                print_error "Some Solidity tests failed"
            fi
            echo "--- End Solidity Test Output ---"
            echo ""
        else
            if npx hardhat test --silent >/dev/null 2>&1; then
                SOLIDITY_TESTS_PASSED=true
                print_success "All Solidity tests passed"
            else
                print_error "Some Solidity tests failed. Use --verbose for details."
            fi
        fi
    else
        print_warning "No package.json found in geometer-contracts/"
        SOLIDITY_TESTS_PASSED=true  # Don't fail if no Solidity tests
    fi
    
    cd ..
else
    print_warning "Solidity tests skipped (missing geometer-contracts/ or npx)"
    SOLIDITY_TESTS_PASSED=true  # Don't fail if no Solidity environment
fi

echo ""

# ============================================================================
# [3/3] Fuzz Tests
# ============================================================================
print_step "Checking fuzz test availability..." "3/3"

if [ "$SKIP_FUZZ" = true ]; then
    print_info "Fuzz testing skipped (--skip-fuzz flag)"
    FUZZ_TESTS_AVAILABLE=true
elif [ -d "fuzz" ]; then
    print_info "Fuzz testing directory found..."
    cd fuzz
    
    if command -v cargo-fuzz >/dev/null 2>&1; then
        print_info "Listing available fuzz targets..."
        FUZZ_TARGETS=$(cargo fuzz list 2>/dev/null || echo "")
        
        if [ -n "$FUZZ_TARGETS" ]; then
            FUZZ_TESTS_AVAILABLE=true
            print_success "Fuzz targets available:"
            echo "$FUZZ_TARGETS" | while read -r target; do
                if [ -n "$target" ]; then
                    echo "  • $target"
                fi
            done
            echo ""
            print_info "To run fuzz tests manually:"
            echo "$FUZZ_TARGETS" | while read -r target; do
                if [ -n "$target" ]; then
                    echo "  cargo fuzz run $target -- -max_total_time=60"
                fi
            done
            echo ""
            print_warning "Fuzz tests are time-consuming and not run automatically."
            print_info "Use the commands above to run specific fuzz targets when needed."
        else
            print_warning "No fuzz targets found"
            FUZZ_TESTS_AVAILABLE=true  # Don't fail if no fuzz targets
        fi
    else
        print_warning "cargo-fuzz not installed. Install with: cargo install cargo-fuzz"
        FUZZ_TESTS_AVAILABLE=true  # Don't fail if cargo-fuzz not available
    fi
    
    cd ..
else
    print_warning "No fuzz testing directory found"
    FUZZ_TESTS_AVAILABLE=true  # Don't fail if no fuzz directory
fi

echo ""

# ============================================================================
# Test Summary
# ============================================================================
print_header "Testing Suite Summary"
echo ""

# Calculate overall result
OVERALL_SUCCESS=true

if [ "$RUST_TESTS_PASSED" = true ]; then
    echo "✅ Rust Tests: PASSED"
else
    echo "❌ Rust Tests: FAILED"
    OVERALL_SUCCESS=false
fi

if [ "$SOLIDITY_TESTS_PASSED" = true ]; then
    echo "✅ Solidity Tests: PASSED"
else
    echo "❌ Solidity Tests: FAILED"
    OVERALL_SUCCESS=false
fi

if [ "$FUZZ_TESTS_AVAILABLE" = true ]; then
    echo "✅ Fuzz Tests: AVAILABLE"
else
    echo "❌ Fuzz Tests: NOT AVAILABLE"
    OVERALL_SUCCESS=false
fi

echo ""

if [ "$OVERALL_SUCCESS" = true ]; then
    print_success "🎉 ALL TESTS COMPLETED SUCCESSFULLY!"
    echo ""
    echo "✅ Unit Tests: All Rust unit tests passed"
    echo "✅ Integration Tests: All integration tests passed"
    echo "✅ Smart Contracts: All Solidity tests passed"
    echo "✅ Fuzz Testing: Available for manual execution"
    echo ""
    echo "🚀 The Zen Geometer is ready for deployment!"
    echo ""
    exit 0
else
    print_error "❌ SOME TESTS FAILED"
    echo ""
    echo "Please review the failed tests above and fix any issues."
    echo "All tests must pass before proceeding with deployment."
    echo ""
    exit 1
fi