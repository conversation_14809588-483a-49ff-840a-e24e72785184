# Zen Geometer Installation & Setup Guide

## Mission Brief: Complete Installation and Configuration Guide

This comprehensive tactical guide covers everything needed to install, configure, and deploy the **Zen Geometer** (Binary: `basilisk_bot`) autonomous DeFi trading system. Follow these operational procedures for a complete setup from scratch to production deployment.

**Mission Objective**: Deploy a fully operational autonomous trading system with Hub and Spoke architecture  
**Tactical Approach**: Progressive installation with validation checkpoints at each phase  
**Production Status**: ✅ **DEPLOYMENT READY** - All installation procedures validated and tested

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Prerequisites Installation](#prerequisites-installation)
3. [Environment Setup](#environment-setup)
4. [Multi-Chain Configuration](#multi-chain-configuration)
5. [Infrastructure Services](#infrastructure-services)
6. [Smart Contract Deployment](#smart-contract-deployment)
7. [Configuration Validation](#configuration-validation)
8. [First Run & Testing](#first-run--testing)
9. [Production Deployment](#production-deployment)
10. [Troubleshooting](#troubleshooting)

## 🎯 System Requirements

### Minimum Hardware Requirements

- **CPU**: 4+ cores (Intel i5/AMD Ryzen 5 or better)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB available space (SSD recommended)
- **Network**: Stable internet connection with low latency

### Supported Operating Systems

- **Linux**: Ubuntu 20.04+, Debian 11+, CentOS 8+
- **macOS**: 10.15+ (Catalina or newer)
- **Windows**: Windows 10/11 with WSL2

### Network Requirements

- **Outbound HTTPS**: Port 443 (RPC endpoints)
- **Outbound WSS**: Port 443 (WebSocket connections)
- **Local Services**: Ports 4222 (NATS), 5432 (PostgreSQL), 6379 (Redis)

## ⚙️ Prerequisites Installation

### 1. Rust 2021 Edition Installation

The Zen Geometer requires **Rust 1.75+** with the 2021 edition.

#### Linux/macOS Installation

```bash
# Install Rust using rustup (recommended)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Follow the prompts, then reload your shell
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version

# Should show Rust 1.75+ and Cargo 1.75+
```

#### Windows Installation

1. Download and run [rustup-init.exe](https://rustup.rs/)
2. Follow the installation wizard
3. Open a new Command Prompt or PowerShell
4. Verify installation:

```powershell
rustc --version
cargo --version
```

#### Update Existing Rust Installation

```bash
# Update to latest stable
rustup update stable
rustup default stable

# Verify version
rustc --version  # Should be 1.75+
```

### 2. Foundry Installation

Foundry is required for Anvil blockchain simulation and smart contract operations.

#### Linux/macOS Installation

```bash
# Install Foundry
curl -L https://foundry.paradigm.xyz | bash

# Reload your shell or run:
source ~/.bashrc  # or ~/.zshrc

# Install the latest version
foundryup

# Verify installation
forge --version
anvil --version
cast --version
```

#### Windows Installation

```powershell
# Using PowerShell
iwr -useb https://foundry.paradigm.xyz | iex

# Or download the installer from https://github.com/foundry-rs/foundry/releases
# Then run foundryup.exe
```

#### Verify Foundry Installation

```bash
# Test Anvil (should start a local blockchain)
anvil --version

# Should show: anvil 0.2.0+
```

### 3. Docker & Docker Compose Installation

Docker is required for infrastructure services (PostgreSQL, Redis, NATS, monitoring).

#### Linux Installation (Ubuntu/Debian)

```bash
# Update package index
sudo apt-get update

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add your user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose V2
sudo apt-get install docker-compose-plugin

# Logout and login again, then verify
docker --version
docker compose version
```

#### macOS Installation

```bash
# Using Homebrew (recommended)
brew install --cask docker

# Or download Docker Desktop from https://docker.com/products/docker-desktop
# Start Docker Desktop application

# Verify installation
docker --version
docker compose version
```

#### Windows Installation

1. Download [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
2. Install and restart your computer
3. Start Docker Desktop
4. Verify in PowerShell:

```powershell
docker --version
docker compose version
```

### 4. Node.js Installation (for Smart Contracts)

Node.js is required for Hardhat smart contract compilation and deployment.

#### Using Node Version Manager (Recommended)

```bash
# Install nvm (Linux/macOS)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reload shell
source ~/.bashrc

# Install and use Node.js LTS
nvm install --lts
nvm use --lts

# Verify installation
node --version  # Should be v18+ or v20+
npm --version
```

#### Direct Installation

- **Linux**: Use your package manager (`sudo apt install nodejs npm`)
- **macOS**: `brew install node`
- **Windows**: Download from [nodejs.org](https://nodejs.org/)

## 🌐 Environment Setup

### 1. Clone the Repository

```bash
# Clone the Zen Geometer repository
git clone https://github.com/your-org/zen-geometer.git
cd zen-geometer

# Verify project structure
ls -la
# Should see: Cargo.toml, src/, config/, docs/, etc.
```

### 2. Create Environment Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Edit the environment file with your settings
nano .env  # or use your preferred editor
```

### 3. Complete Environment Variables Guide

Edit your `.env` file with the following configuration:

```bash
# =============================================================================
# ZEN GEOMETER ENVIRONMENT CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# CRITICAL: EXECUTION PRIVATE KEY
# -----------------------------------------------------------------------------
# This is your wallet's private key for transaction execution
# SECURITY: Use a dedicated wallet with limited funds for testing
# FORMAT: 64-character hex string WITHOUT 0x prefix
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here_without_0x_prefix

# -----------------------------------------------------------------------------
# RPC ENDPOINTS - REQUIRED
# -----------------------------------------------------------------------------
# Base L2 (Primary settlement layer)
RPC_URL_BASE=https://mainnet.base.org
BASE_RPC_API_KEY=your_base_api_key_optional

# Arbitrum L2 (Secondary execution venue)
RPC_URL_ARBITRUM=https://arb1.arbitrum.io/rpc
ARBITRUM_RPC_API_KEY=your_arbitrum_api_key_optional

# Degen Chain L3 (Primary execution venue)
RPC_URL_DEGEN=https://rpc.degen.tips

# Ethereum L1 (For reference data)
RPC_URL_ETHEREUM=https://eth.public-rpc.com
ETHEREUM_RPC_API_KEY=your_ethereum_api_key_optional

# -----------------------------------------------------------------------------
# INFRASTRUCTURE SERVICES
# -----------------------------------------------------------------------------
# Database connection (PostgreSQL/TimescaleDB)
DATABASE_URL=postgresql://basilisk_bot:basilisk_bot_secure_password@localhost:5432/basilisk_bot

# Redis cache
REDIS_URL=redis://localhost:6379

# NATS message bus
NATS_URL=nats://localhost:4222

# -----------------------------------------------------------------------------
# OPTIONAL: CEX INTEGRATION
# -----------------------------------------------------------------------------
# Coinbase Pro API (for price reference)
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret
COINBASE_API_PASSPHRASE=your_coinbase_passphrase

# -----------------------------------------------------------------------------
# OPTIONAL: MEV PROTECTION
# -----------------------------------------------------------------------------
# Flashbots relay for MEV protection
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
TITAN_RELAY_URL=https://rpc.titanbuilder.xyz

# -----------------------------------------------------------------------------
# SECURITY & MONITORING
# -----------------------------------------------------------------------------
# GoPlus API for honeypot detection
HONEYPOT_API_KEY=your_goplus_api_key

# Discord webhook for alerts
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# Telegram bot for notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# -----------------------------------------------------------------------------
# DEVELOPMENT SETTINGS
# -----------------------------------------------------------------------------
# Logging level (trace, debug, info, warn, error)
RUST_LOG=info

# Enable backtrace for debugging
RUST_BACKTRACE=1

# Environment identifier
ENVIRONMENT=production

# Enable dry run mode (recommended for testing)
DRY_RUN=false

# -----------------------------------------------------------------------------
# PERFORMANCE TUNING
# -----------------------------------------------------------------------------
# Maximum concurrent operations
MAX_CONCURRENT_OPERATIONS=10

# Request timeout in seconds
REQUEST_TIMEOUT_SECONDS=30

# WebSocket reconnection attempts
WS_MAX_RECONNECT_ATTEMPTS=5
```

### 4. Secure Your Private Key

**CRITICAL SECURITY STEPS:**

```bash
# Create a dedicated wallet for the bot
# NEVER use your main wallet's private key

# For testing, you can generate a test wallet:
cast wallet new

# Fund the test wallet with small amounts:
# - Base: 0.01 ETH for gas
# - Arbitrum: 0.01 ETH for gas
# - Degen Chain: 1000 DEGEN for gas

# Set restrictive permissions on .env file
chmod 600 .env

# Add .env to .gitignore (should already be there)
echo ".env" >> .gitignore
```

## 🔗 Multi-Chain Configuration

### 1. Base L2 Configuration

Base serves as the primary settlement layer with access to Aave flash loans.

```toml
# config/default.toml - Base configuration
[chains.8453]  # Base Mainnet
name = "Base"
enabled = true
native_currency = "ETH"

[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[[chains.8453.rpc_endpoints]]
url = "https://base-rpc.publicnode.com"
priority = 1

[chains.8453.tokens]
WETH = "******************************************"
USDC = "******************************************"
DEGEN = "******************************************"

[chains.8453.dex]
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
aerodrome_factory = "******************************************"

[chains.8453.contracts]
multicall = "******************************************"
aave_pool = "******************************************"
stargate_router = "******************************************"
```

### 2. Arbitrum L2 Configuration

```toml
# Arbitrum configuration
[chains.42161]  # Arbitrum One
name = "Arbitrum"
enabled = true
native_currency = "ETH"

[[chains.42161.rpc_endpoints]]
url = "https://arb1.arbitrum.io/rpc"
priority = 0

[chains.42161.tokens]
WETH = "******************************************"
USDC = "******************************************"

[chains.42161.dex]
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
```

### 3. Degen Chain L3 Configuration

```toml
# Degen Chain configuration
[chains.666666666]  # Degen Chain
name = "Degen"
enabled = true
native_currency = "DEGEN"

[[chains.666666666.rpc_endpoints]]
url = "https://rpc.degen.tips"
priority = 0

[chains.666666666.tokens]
WETH = "******************************************"
USDC = "******************************************"
DEGEN = "******************************************"

[chains.666666666.dex]
degen_swap_router = "******************************************"
```

### 4. Cross-Chain Bridge Configuration

```toml
# Bridge routes for cross-chain operations
[bridges]
routes = [
    # [source_chain, dest_chain, cost_usd, latency_seconds]
    [8453, 666666666, 2.0, 180],    # Base -> Degen Chain
    [666666666, 8453, 2.0, 180],    # Degen Chain -> Base
    [8453, 42161, 5.0, 300],        # Base -> Arbitrum
    [42161, 8453, 5.0, 300],        # Arbitrum -> Base
]
```

## 🏗️ Infrastructure Services

### 1. Start Infrastructure Services

```bash
# Start all required services using Docker Compose
docker compose up -d

# Verify services are running
docker compose ps

# Should show: nats, timescaledb, redis, prometheus, grafana
```

### 2. Verify Service Health

```bash
# Check NATS server
curl http://localhost:8222/healthz
# Should return: ok

# Check PostgreSQL
docker compose exec timescaledb pg_isready -U basilisk_bot -d basilisk_bot
# Should return: accepting connections

# Check Redis
docker compose exec redis redis-cli ping
# Should return: PONG

# Check all services
docker compose ps
# All services should show "healthy" status
```

### 3. Initialize Database Schema

```bash
# The database schema is automatically initialized
# Verify tables were created
docker compose exec timescaledb psql -U basilisk_bot -d basilisk_bot -c "\dt"

# Should show tables: trades, opportunities, metrics, etc.
```

### 4. Configure Monitoring (Optional)

```bash
# Access Grafana dashboard
open http://localhost:3000
# Login: admin / basilisk_bot_admin

# Access Prometheus metrics
open http://localhost:9090

# Import pre-configured dashboards
# Dashboards are automatically provisioned from config/grafana/
```

## 📜 Smart Contract Deployment

### 1. Setup Smart Contract Environment

```bash
# Navigate to contracts directory
cd geometer-contracts

# Install Node.js dependencies
npm install

# Verify Hardhat installation
npx hardhat --version
# Should show: 2.25.0+
```

### 2. Compile Smart Contracts

```bash
# Compile all contracts
npx hardhat compile

# Verify compilation
ls artifacts/contracts/
# Should show: StargateCompassV1.sol/
```

### 3. Deploy to Local Network (Testing)

```bash
# Start local Anvil fork of Base
anvil --fork-url https://mainnet.base.org --chain-id 8453 &

# Deploy contracts to local network
npx hardhat ignition deploy ignition/modules/StargateCompassV1.ts --network localhost

# Note the deployed contract address
# Example output: StargateCompassV1 deployed to: ******************************************
```

### 4. Deploy to Testnet (Base Sepolia)

```bash
# Deploy to Base Sepolia testnet
npx hardhat ignition deploy ignition/modules/StargateCompassV1.ts --network base-sepolia

# Verify deployment on BaseScan
npx hardhat verify --network base-sepolia <CONTRACT_ADDRESS>
```

### 5. Update Configuration with Contract Addresses

```bash
# Edit config/default.toml
nano config/default.toml

# Add deployed contract addresses:
[chains.8453.contracts]
stargate_compass_v1 = "0x10fb5800FA746C592f013c51941F28b2D8Fb2c6B"  # Your deployed address

# For testnet:
[chains.84532.contracts]
stargate_compass_v1 = "0x76D78a65E873f749AE5575C6fB47ca18e8789b62"  # Testnet address
```

## ✅ Configuration Validation

### 1. Build the Project

```bash
# Return to main project directory
cd ..

# Build all binaries
cargo build --release

# This may take 5-10 minutes on first build
# Subsequent builds will be faster
```

### 2. Validate Configuration

```bash
# Run comprehensive configuration validation
cargo run -- config validate --strict --check-network

# Expected output:
# ✅ Configuration syntax valid
# ✅ Network connectivity confirmed
# ✅ Smart contracts accessible
# ✅ Risk parameters within safe ranges
# ✅ All required services available
```

### 3. Test System Connectivity

```bash
# Test RPC endpoints
cargo run -- utils ping-nodes

# Test wallet access
cargo run -- utils balances

# Test infrastructure services
cargo run -- utils health-check
```

### 4. Validate Smart Contracts

```bash
# Test contract interaction
cargo run -- utils test-contracts

# Should show successful contract calls
```

## 🚀 First Run & Testing

### 1. Start with Simulation Mode

```bash
# Start in simulation mode (no real transactions)
cargo run -- run --mode simulate --verbose

# This will:
# - Connect to live market data
# - Analyze real opportunities
# - Show what trades would be executed
# - Never broadcast actual transactions
```

### 2. Monitor with TUI

```bash
# In another terminal, start the TUI
cargo run -- tui

# Navigate through tabs:
# - Dashboard: Real-time market analysis
# - Operations: Trade monitoring
# - Systems: Service health
# - Config: Configuration management
```

### 3. Test Binary Tools

```bash
# Test data ingestion
cargo run --bin data_ingestor -- --help

# Test feature analysis
cargo run --bin feature_exporter -- --cycles 5

# Test graph analysis
cargo run --bin graph_analyzer -- --help

# Test TUI harness
cargo run --bin tui_harness
```

### 4. Run Integration Tests

```bash
# Run all tests
cargo test

# Run specific integration tests
cargo test --test backend_integration_test

# Run with Anvil blockchain simulation
./scripts/test_complete_system.sh
```

## 🚀 Production Deployment

### 1. Use the Deployment Ladder

```bash
# Run the guided deployment ladder
./scripts/deployment_ladder.sh

# This will walk you through:
# 1. Simulate mode (no risk)
# 2. Shadow mode (fork testing)
# 3. Sentinel mode (minimal risk)
# 4. Low-capital mode (limited risk)
# 5. Live mode (full production)
```

### 2. Production Configuration

```bash
# Create production configuration
cp config/default.toml config/production.toml

# Edit production settings
nano config/production.toml

# Key production settings:
dry_run = false
active_chain_id = 8453  # Base mainnet

[risk]
max_daily_loss_usd = 500.0
kelly_fraction = 0.25
max_position_size_usd = 2000.0

[strategies.unified]
min_net_profit_usd = 5.0
risk_aversion_k = 0.5
```

### 3. Production Deployment

```bash
# Run preflight checks
./scripts/preflight.sh

# Start in low-capital mode first
cargo run -- run --mode low-capital --config config/production.toml

# Monitor performance for 24-48 hours
# Then upgrade to live mode:
cargo run -- run --mode live --config config/production.toml
```

### 4. Production Monitoring

```bash
# Set up log rotation
sudo logrotate -f /etc/logrotate.d/zen-geometer

# Monitor system resources
htop

# Monitor application logs
tail -f logs/application.log

# Monitor via TUI
cargo run -- tui
```

## 🛠️ Troubleshooting

### Common Installation Issues

#### Rust Installation Problems

```bash
# If rustc version is too old:
rustup update stable
rustup default stable

# If cargo build fails with linking errors:
sudo apt-get install build-essential pkg-config libssl-dev
# On macOS: xcode-select --install
```

#### Docker Issues

```bash
# If Docker daemon not running:
sudo systemctl start docker
sudo systemctl enable docker

# If permission denied:
sudo usermod -aG docker $USER
# Then logout and login again

# If Docker Compose not found:
sudo apt-get install docker-compose-plugin
```

#### Foundry Installation Issues

```bash
# If foundryup fails:
curl -L https://foundry.paradigm.xyz | bash
source ~/.bashrc
foundryup

# If anvil command not found:
export PATH="$HOME/.foundry/bin:$PATH"
```

### Configuration Issues

#### RPC Connection Failures

```bash
# Test RPC endpoint manually:
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  https://mainnet.base.org

# If fails, try alternative endpoints:
# Base: https://base-rpc.publicnode.com
# Arbitrum: https://arbitrum-one.publicnode.com
```

#### Database Connection Issues

```bash
# Check if PostgreSQL is running:
docker compose ps timescaledb

# Test connection manually:
docker compose exec timescaledb psql -U basilisk_bot -d basilisk_bot

# If connection fails, check DATABASE_URL in .env
```

#### Smart Contract Deployment Issues

```bash
# If deployment fails with gas errors:
# Increase gas limit in hardhat.config.ts:
gas: 5000000,
gasPrice: 2000000000,  # 2 gwei

# If verification fails:
# Check BASESCAN_API_KEY in .env
# Wait 1-2 minutes after deployment before verifying
```

### Runtime Issues

#### High Gas Costs

```bash
# Adjust gas strategy in config:
[execution.priority_fee_gwei]
low_gwei = 0.5      # Reduce for lower costs
medium_gwei = 1.0
high_gwei = 2.0
```

#### Low Profitability

```bash
# Reduce minimum profit threshold:
[strategies.unified]
min_net_profit_usd = 1.0  # Lower threshold

# Increase position sizes (carefully):
[risk]
max_position_size_usd = 5000.0
```

#### Circuit Breaker Activation

```bash
# Check risk parameters:
cargo run -- utils risk-status

# Adjust if too conservative:
[risk]
max_consecutive_failures = 10  # Increase tolerance
```

### Getting Help

1. **Check Logs**: `tail -f logs/application.log`
2. **Run Diagnostics**: `cargo run -- utils diagnose`
3. **Validate Config**: `cargo run -- config validate --strict`
4. **Test Connectivity**: `cargo run -- utils ping-nodes`
5. **Check Service Health**: `docker compose ps`

### Support Resources

- **Documentation**: Complete guides in `/docs` directory
- **Configuration Examples**: Sample configs in `/config` directory
- **Test Scripts**: Validation scripts in `/scripts` directory
- **Integration Tests**: Comprehensive test suite in `/tests` directory

---

## 🎉 Installation Complete!

Your **Zen Geometer** installation is now complete. The system is ready for tactical deployment across all operational modes:

- **✅ Simulation Mode**: Risk-free learning and strategy validation
- **✅ Shadow Mode**: Fork-based transaction testing with live data
- **✅ Progressive Deployment**: Safe scaling through the 5-tier deployment ladder
- **✅ Production Trading**: Full autonomous DeFi trading capabilities

**Next Steps - Mission Execution:**

1. **Start with simulation mode**: `cargo run -- run --mode simulate`
2. **Monitor with TUI**: `cargo run -- tui`
3. **Follow the deployment ladder**: `./scripts/deployment_ladder.sh`

---

_"Where autonomous intelligence meets strategic guidance in the pursuit of geometric perfection."_ - **The Zen Geometer**
