# SIGINT Workflow

This directory is the command center for the SIGINT (Signal Intelligence) workflow, which allows for human-in-the-loop strategic adjustments to the Basilisk Bot's operation.

## Table of Contents

- [Introduction](#introduction)
- [How It Works](#how-it-works)
- [Creating a SIGINT Report](#creating-a-sigint-report)
- [Directive Types](#directive-types)
- [Monitoring](#monitoring)
- [Best Practices](#best-practices)

## Introduction

The SIGINT workflow allows a human operator to provide strategic guidance to the bot by creating a `sigint_report.json` file. This file contains a set of directives that override the bot's autonomous behavior for a specified period.

## How It Works

1.  **Create a `sigint_report.json` file:** The operator creates a JSON file containing a set of directives.
2.  **Place the file in the `sigint/` directory:** The bot automatically detects and processes new reports in this directory.
3.  **The bot enters "Override" mode:** The bot applies the directives from the report, overriding its autonomous decision-making process.
4.  **The report expires:** The directives are active until the expiration time specified in the report. After the report expires, the bot reverts to its autonomous mode.

## Creating a SIGINT Report

To create a SIGINT report, follow these steps:

1.  **Analyze the market:** Use the `feature_exporter` tool to generate a quantitative analysis of the market:
    ```bash
    cargo run --bin feature_exporter
    ```

2.  **Create the report file:** Create a JSON file with the following structure:

    ```json
    {
      "report_id": "sigint-20250709-1000",
      "expires_at": "2025-07-09T14:00:00Z",
      "directives": [
        {
          "directive_type": "SET_MARKET_CONTEXT",
          "regime": "High_Volatility_Correction",
          "character": "Trending",
          "reason": "Manual override based on external analysis",
          "duration_hours": 4.0
        },
        {
          "directive_type": "APPLY_STRATEGIC_BIAS",
          "bias_target": "asset:0x...",
          "multiplier_adjustment": 2.0,
          "reason": "Increased confidence in this asset"
        }
      ]
    }
    ```

3.  **Deploy the report:** Save the file in the `sigint/` directory. The bot will automatically process it.

## Directive Types

-   **`SET_MARKET_CONTEXT`**: Overrides the bot's autonomous market classification.
    -   `regime`: The market regime (e.g., `High_Volatility_Correction`).
    -   `character`: The market character (e.g., `Trending`).
-   **`APPLY_STRATEGIC_BIAS`**: Applies a multiplier to specific opportunities.
    -   `bias_target`: The asset or scanner to apply the bias to.
    -   `multiplier_adjustment`: The multiplier to apply.

## Monitoring

The TUI dashboard provides a real-time view of the SIGINT workflow, including the current mode (autonomous or override), the number of active directives, and the last time the `sigint/` directory was checked.

## Best Practices

-   **Use SIGINT sparingly:** The bot is designed to operate autonomously. Use SIGINT only when you have a high degree of confidence that your strategic guidance will improve performance.
-   **Set clear expiration times:** Always set a reasonable expiration time for your directives.
-   **Provide clear reasons:** Use the `reason` field to document why you are issuing a directive.
-   **Monitor performance:** Use the TUI and other monitoring tools to track the performance of the bot while it is in override mode.
