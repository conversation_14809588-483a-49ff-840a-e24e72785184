// MISSION: Strategy Inspector - Deep Analysis of Aetheric Resonance Scoring
// WHY: Provide operators with complete transparency into ARE decision-making
// HOW: Detailed breakdown of how each pillar contributes to the final score

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use rust_decimal::Decimal;

/// Detailed scoring information from a single analytical pillar
#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PillarScoreDetail {
    pub pillar_name: String,
    pub metric_name: String,
    pub metric_value: String, // Flexible formatting (e.g., "120 ms", "0.92")
    pub score_contribution: f64,
    pub weight: f64, // How much this pillar is weighted in final calculation
    pub explanation: String, // Human-readable explanation of the score
}

/// Collection of scoring details from all analytical pillars
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScoreBreakdown {
    pub details: Vec<PillarScoreDetail>,
    pub total_weighted_score: f64,
    pub confidence_level: f64, // Overall confidence in the analysis (0.0-1.0)
    pub risk_factors: Vec<String>, // Identified risk factors
    pub opportunity_strengths: Vec<String>, // Identified strengths
}

/// Complete inspectable opportunity with full scoring breakdown
#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct InspectableOpportunity {
    pub id: Uuid,
    pub final_score: f64,
    pub opportunity_summary: String, // e.g., "ARB: 1.5 ETH -> WETH -> USDC -> ETH"
    pub breakdown: ScoreBreakdown,
    pub timestamp: u64, // Unix timestamp
    pub execution_status: ExecutionStatus,
    pub profit_estimate_usd: Decimal,
    pub gas_estimate_usd: Decimal,
    pub net_profit_usd: Decimal,
    pub execution_time_estimate_ms: u64,
}

/// Status of opportunity execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStatus {
    Staged,           // Opportunity identified but not executed
    Executing,        // Currently being executed
    Completed,        // Successfully executed
    Failed(String),   // Failed with reason
    Rejected(String), // Rejected with reason
}

impl ExecutionStatus {
    pub fn color(&self) -> ratatui::style::Color {
        match self {
            ExecutionStatus::Staged => ratatui::style::Color::Cyan,
            ExecutionStatus::Executing => ratatui::style::Color::Yellow,
            ExecutionStatus::Completed => ratatui::style::Color::Green,
            ExecutionStatus::Failed(_) => ratatui::style::Color::Red,
            ExecutionStatus::Rejected(_) => ratatui::style::Color::Gray,
        }
    }
    
    pub fn as_str(&self) -> &str {
        match self {
            ExecutionStatus::Staged => "STAGED",
            ExecutionStatus::Executing => "EXECUTING",
            ExecutionStatus::Completed => "COMPLETED",
            ExecutionStatus::Failed(_) => "FAILED",
            ExecutionStatus::Rejected(_) => "REJECTED",
        }
    }
}

/// Helper functions for creating pillar score details
impl PillarScoreDetail {
    pub fn new_mandorla_gauge(
        geometric_score: f64,
        vesica_piscis_ratio: f64,
        path_efficiency: f64,
    ) -> Self {
        let score_contribution = geometric_score * 0.4; // 40% weight for geometric analysis
        let explanation = format!(
            "Vesica Piscis ratio: {:.3}, Path efficiency: {:.2}%, Geometric harmony assessment",
            vesica_piscis_ratio, path_efficiency * 100.0
        );
        
        Self {
            pillar_name: "💠 Mandorla Gauge".to_string(),
            metric_name: "Geometric Score".to_string(),
            metric_value: format!("{:.4}", geometric_score),
            score_contribution,
            weight: 0.4,
            explanation,
        }
    }
    
    pub fn new_chronos_sieve(
        rhythm_stability: f64,
        dominant_cycle_minutes: f64,
        market_phase: &str,
    ) -> Self {
        let score_contribution = rhythm_stability * 0.3; // 30% weight for temporal analysis
        let explanation = format!(
            "Market phase: {}, Dominant cycle: {:.1}min, Temporal harmony analysis",
            market_phase, dominant_cycle_minutes
        );
        
        Self {
            pillar_name: "⌛ Chronos Sieve".to_string(),
            metric_name: "Rhythm Stability".to_string(),
            metric_value: format!("{:.3}", rhythm_stability),
            score_contribution,
            weight: 0.3,
            explanation,
        }
    }
    
    pub fn new_network_seismology(
        coherence_score: f64,
        sp_time_ms: u64,
        network_state: &str,
    ) -> Self {
        let score_contribution = coherence_score * 0.3; // 30% weight for network analysis
        let explanation = format!(
            "Network state: {}, S-P propagation time: {}ms, Network timing analysis",
            network_state, sp_time_ms
        );
        
        Self {
            pillar_name: "🌊 Network Seismology".to_string(),
            metric_name: "Coherence Score".to_string(),
            metric_value: format!("{:.3}", coherence_score),
            score_contribution,
            weight: 0.3,
            explanation,
        }
    }
}

/// Helper functions for creating score breakdowns
impl ScoreBreakdown {
    pub fn new(details: Vec<PillarScoreDetail>) -> Self {
        let total_weighted_score = details.iter().map(|d| d.score_contribution).sum();
        
        // Calculate confidence based on score consistency
        let scores: Vec<f64> = details.iter().map(|d| d.score_contribution / d.weight).collect();
        let mean_score = scores.iter().sum::<f64>() / scores.len() as f64;
        let variance = scores.iter().map(|s| (s - mean_score).powi(2)).sum::<f64>() / scores.len() as f64;
        let confidence_level = (1.0 - variance.sqrt()).max(0.0).min(1.0);
        
        // Identify risk factors and strengths
        let mut risk_factors = Vec::new();
        let mut opportunity_strengths = Vec::new();
        
        for detail in &details {
            let normalized_score = detail.score_contribution / detail.weight;
            if normalized_score < 0.3 {
                risk_factors.push(format!("Low {}: {:.2}", detail.metric_name, normalized_score));
            } else if normalized_score > 0.8 {
                opportunity_strengths.push(format!("High {}: {:.2}", detail.metric_name, normalized_score));
            }
        }
        
        Self {
            details,
            total_weighted_score,
            confidence_level,
            risk_factors,
            opportunity_strengths,
        }
    }
}

/// Helper functions for creating inspectable opportunities
impl InspectableOpportunity {
    pub fn new(
        id: Uuid,
        opportunity_summary: String,
        breakdown: ScoreBreakdown,
        profit_estimate_usd: Decimal,
        gas_estimate_usd: Decimal,
    ) -> Self {
        let final_score = breakdown.total_weighted_score;
        let net_profit_usd = profit_estimate_usd - gas_estimate_usd;
        
        Self {
            id,
            final_score,
            opportunity_summary,
            breakdown,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            execution_status: ExecutionStatus::Staged,
            profit_estimate_usd,
            gas_estimate_usd,
            net_profit_usd,
            execution_time_estimate_ms: 2000, // Default 2 second estimate
        }
    }
    
    pub fn update_status(&mut self, status: ExecutionStatus) {
        self.execution_status = status;
    }
    
    pub fn should_execute(&self, min_score_threshold: f64, min_profit_usd: Decimal) -> bool {
        self.final_score >= min_score_threshold && self.net_profit_usd >= min_profit_usd
    }
    
    pub fn risk_level(&self) -> &'static str {
        match self.breakdown.confidence_level {
            c if c > 0.8 => "LOW",
            c if c > 0.5 => "MEDIUM", 
            _ => "HIGH",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;
    
    #[test]
    fn test_pillar_score_detail_creation() {
        let mandorla = PillarScoreDetail::new_mandorla_gauge(0.85, 1.618, 0.92);
        assert_eq!(mandorla.pillar_name, "💠 Mandorla Gauge");
        assert_eq!(mandorla.weight, 0.4);
        assert!((mandorla.score_contribution - 0.34).abs() < 0.01);
    }
    
    #[test]
    fn test_score_breakdown_calculation() {
        let details = vec![
            PillarScoreDetail::new_mandorla_gauge(0.8, 1.618, 0.9),
            PillarScoreDetail::new_chronos_sieve(0.7, 12.5, "HARMONIC"),
            PillarScoreDetail::new_network_seismology(0.9, 120, "COHERENT"),
        ];
        
        let breakdown = ScoreBreakdown::new(details);
        assert!(breakdown.total_weighted_score > 0.0);
        assert!(breakdown.confidence_level >= 0.0 && breakdown.confidence_level <= 1.0);
    }
    
    #[test]
    fn test_inspectable_opportunity_creation() {
        let details = vec![
            PillarScoreDetail::new_mandorla_gauge(0.8, 1.618, 0.9),
        ];
        let breakdown = ScoreBreakdown::new(details);
        
        let opportunity = InspectableOpportunity::new(
            Uuid::new_v4(),
            "ARB: ETH -> USDC -> ETH".to_string(),
            breakdown,
            dec!(100.0),
            dec!(25.0),
        );
        
        assert_eq!(opportunity.net_profit_usd, dec!(75.0));
        assert!(opportunity.should_execute(0.2, dec!(50.0)));
    }
}