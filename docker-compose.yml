# version: '3.8'  # Removed obsolete version field

# Basilisk Bot - High-Frequency DeFi Trading Bot Infrastructure

services:
  # NATS Message Bus - Core communication backbone
  nats:
    image: nats:2.10-alpine
    container_name: basilisk_bot_nats
    ports:
      - "4222:4222"  # Client connections
      - "8222:8222"  # HTTP monitoring
      - "6222:6222"  # Cluster connections
    command: [
      "nats-server",
      "--jetstream",
      "--store_dir=/data",
      "--http_port=8222"
    ]
    volumes:
      - nats_data:/data
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.basilisk_bot.service=message_bus"
      - "com.basilisk_bot.component=infrastructure"

  # TimescaleDB - Time-series data storage for market analysis
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: basilisk_bot_timescaledb
    environment:
      POSTGRES_DB: basilisk_bot
      POSTGRES_USER: basilisk_bot
      POSTGRES_PASSWORD: basilisk_bot_secure_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_timescaledb.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U basilisk_bot -d basilisk_bot"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.basilisk_bot.service=database"
      - "com.basilisk_bot.component=infrastructure"

  # Redis - Caching and state management
  redis:
    image: redis:7-alpine
    container_name: basilisk_bot_redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.basilisk_bot.service=cache"
      - "com.basilisk_bot.component=infrastructure"

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: basilisk_bot_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    labels:
      - "com.basilisk_bot.service=metrics"
      - "com.basilisk_bot.component=monitoring"

  # Grafana - Metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: basilisk_bot_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: basilisk_bot_admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.basilisk_bot.service=visualization"
      - "com.basilisk_bot.component=monitoring"

  # Zen Geometer Main Application
  zen-geometer:
    build: .
    container_name: zen_geometer_app
    ports:
      - "9091:9090"  # Metrics endpoint (avoiding conflict with Prometheus)
    environment:
      - RUST_LOG=info
      - RUST_BACKTRACE=1
      - DATABASE_URL=*********************************************************************/basilisk_bot
      - REDIS_URL=redis://redis:6379
      - NATS_URL=nats://nats:4222
    volumes:
      - ./config:/app/config
      - ./sigint:/app/sigint
      - ./logs:/app/logs
    networks:
      - basilisk_bot_network
    restart: unless-stopped
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
      nats:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9090/metrics"]
      interval: 30s
      timeout: 10s
      start_period: 60s
      retries: 3
    labels:
      - "com.basilisk_bot.service=trading_engine"
      - "com.basilisk_bot.component=core"

volumes:
  nats_data:
    driver: local
    labels:
      - "com.basilisk_bot.volume=nats_storage"
  postgres_data:
    driver: local
    labels:
      - "com.basilisk_bot.volume=database_storage"
  redis_data:
    driver: local
    labels:
      - "com.basilisk_bot.volume=cache_storage"
  prometheus_data:
    driver: local
    labels:
      - "com.basilisk_bot.volume=metrics_storage"
  grafana_data:
    driver: local
    labels:
      - "com.basilisk_bot.volume=dashboard_storage"

networks:
  basilisk_bot_network:
    driver: bridge
    labels:
      - "com.basilisk_bot.network=autonomous_intelligence"
