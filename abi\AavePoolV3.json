[{"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes", "name": "params", "type": "bytes"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "flashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}], "name": "borrow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "rateMode", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}], "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "supply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]