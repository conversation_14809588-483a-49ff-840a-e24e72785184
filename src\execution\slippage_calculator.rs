use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use ethers::types::U256;
use std::str::FromStr;
use crate::error::ExecutionError;

#[derive(Clone)]
pub struct SlippageCalculator {}

impl SlippageCalculator {
    pub fn new() -> Self {
        Self {}
    }

    /// Calculates the minimum acceptable output amount for a swap.
    /// `slippage_tolerance` is a percentage (e.g., 0.005 for 0.5%).
    pub fn calculate_min_amount_out(
        &self,
        expected_output_amount: U256,
        slippage_tolerance: Decimal,
    ) -> Result<U256, ExecutionError> {
        if !(slippage_tolerance > dec!(0.0) && slippage_tolerance < dec!(1.0)) {
            return Err(ExecutionError::InvalidInput(
                "Slippage tolerance must be between 0.0 and 1.0".to_string(),
            ));
        }

        let expected_out_dec = Decimal::from_str(&expected_output_amount.to_string())
            .map_err(|e| ExecutionError::SlippageCalculationFailed(e.to_string()))?;
        
        let min_out_dec = expected_out_dec * (dec!(1.0) - slippage_tolerance);
        
        U256::from_dec_str(&min_out_dec.floor().to_string())
            .map_err(|e| ExecutionError::SlippageCalculationFailed(e.to_string()))
    }
}